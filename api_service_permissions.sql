-- =============================================
-- Grant service role permissions for API profile creation
-- Description: Ensure the API can create user profiles using service role
-- =============================================

-- Grant comprehensive permissions to service_role
GRANT ALL ON public.users TO service_role;
GRANT ALL ON public.workforce_invitations TO service_role;

-- Ensure service role can bypass RLS
ALTER TABLE public.users FORCE ROW LEVEL SECURITY;

-- Create or update service role policy
DROP POLICY IF EXISTS "Service role can manage all users" ON public.users;
CREATE POLICY "Service role can manage all users" ON public.users
  FOR ALL TO service_role USING (true) WITH CHECK (true);

-- Also ensure service role can read invitations for validation
DROP POLICY IF EXISTS "Service role can read invitations" ON public.workforce_invitations;
CREATE POLICY "Service role can read invitations" ON public.workforce_invitations
  FOR SELECT TO service_role USING (true);