{"mcpServers": {"@magicuidesign/mcp": {"command": "npx", "args": ["-y", "@magicuidesign/mcp@latest"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "type": "stdio"}, "supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=nwwynkkigyahrjumqmrj"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}, "type": "stdio"}}}