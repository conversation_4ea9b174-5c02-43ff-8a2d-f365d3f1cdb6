please read and analyse the files Claude.md and ./local/reports/design-inconsistency-report.md to get a picture of what we are working on. Also read the file ./local/errors/css-error-chat.md to see what 
efforts have already been made to resolve this css issue. Please note we are trying to implement a unified and token based design system so make sure you do not break this system and and destroy months
of work already done. Just try to fix the error and make the new design system work on the whole site without error. Use the playwright mcp tool to visit the site at http://192.168.1.25:3000/ and see the error
for yourself, debug it, fix it and test until it is resolved. Please note that the server is already running on port 3000 so do not try start the server yourself. If you can't get the server started, 
let me know so I can stop it and let you start it from your end.