# 🔧 Manual Database Setup - Final Step

## ⚡ Quick Setup (2 minutes)

Since automated SQL execution through the REST API isn't available, here's the manual approach:

### **Step 1: Open Supabase SQL Editor**
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `nwwynkkigyahrjumqmrj`
3. Click **SQL Editor** in the left sidebar

### **Step 2: Copy & Paste This SQL**
```sql
-- Add permission columns to teams table
ALTER TABLE public.teams
  ADD COLUMN IF NOT EXISTS "permission_manage_jobs" BOOLEAN NOT NULL DEFAULT true,
  ADD COLUMN IF NOT EXISTS "permission_manage_clients" BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN IF NOT EXISTS "permission_view_financials" BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN IF NOT EXISTS "permission_generate_documents" BOOLEAN NOT NULL DEFAULT false;

-- Update constraint for job visibility
ALTER TABLE public.teams
  DROP CONSTRAINT IF EXISTS teams_default_job_visibility_check;

UPDATE public.teams
SET default_job_visibility = 'entire_team'
WHERE default_job_visibility = 'team_only' OR default_job_visibility = 'public';

ALTER TABLE public.teams
  ADD CONSTRAINT teams_default_job_visibility_check
  CHECK (default_job_visibility IN ('owner_only', 'entire_team', 'assigned_only'));

-- Update team member roles
ALTER TABLE public.team_members
  DROP CONSTRAINT IF EXISTS team_members_role_check;

UPDATE public.team_members
SET role = 'member'
WHERE role = 'manager';

ALTER TABLE public.team_members
  ADD CONSTRAINT team_members_role_check
  CHECK (role IN ('owner', 'member'));
```

### **Step 3: Execute**
1. Click **Run** button
2. You should see: `Success. No rows returned` or similar
3. Close the SQL Editor

### **Step 4: Refresh Your App**
1. Go back to your DeskBelt app: `http://localhost:3000`
2. Click **Workforce** in the sidebar
3. **🎉 Everything will work perfectly!**

## ✅ What You'll See After Setup

### **Members Tab**
- Your actual user info as workforce owner
- Professional member cards with avatars  
- **Send Invite** button with email functionality
- Role management (Owner/Member only)

### **Permissions Tab**
- 4 toggleable permissions for new members
- Real-time updates with toast notifications
- Professional toggle controls

### **Settings Tab**  
- Business name editing
- Auto-assign jobs toggle
- Job approval requirements
- Job visibility controls

## 🚨 If You Get Errors

If the SQL fails, try running each statement individually:

```sql
-- Run these one by one:
ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS "permission_manage_jobs" BOOLEAN NOT NULL DEFAULT true;
```

```sql
ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS "permission_manage_clients" BOOLEAN NOT NULL DEFAULT false;
```

```sql
ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS "permission_view_financials" BOOLEAN NOT NULL DEFAULT false;
```

```sql
ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS "permission_generate_documents" BOOLEAN NOT NULL DEFAULT false;
```

## 🎯 Verification

After running the SQL, you can verify it worked by running this query:
```sql
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'teams' 
AND column_name LIKE 'permission_%';
```

You should see 4 permission columns listed.

---

**⏰ This takes literally 2 minutes and unlocks the complete workforce management system!** 