#!/usr/bin/env node

/**
 * Manual User Data Isolation Test
 * 
 * This test assumes you have manually created 2 test users and provides their tokens.
 * It focuses on testing the API endpoints to verify explicit user filtering works.
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:4000/api';

// Test users (you need to manually create these and get their tokens)
// Run this in browser console after logging in as each user:
// console.log(supabase.auth.session()?.access_token)

const TEST_USERS = [
  {
    id: 'user1',
    name: 'Test User 1',
    // Replace with actual JWT token from user 1
    token: 'REPLACE_WITH_USER1_TOKEN'
  },
  {
    id: 'user2', 
    name: 'Test User 2',
    // Replace with actual JWT token from user 2
    token: 'REPLACE_WITH_USER2_TOKEN'
  }
];

/**
 * Make authenticated API request
 */
async function apiRequest(endpoint, user, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${user.token}`,
    ...options.headers
  };
  
  const response = await fetch(url, {
    ...options,
    headers
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API request failed: ${response.status} - ${errorText}`);
  }
  
  return response.json();
}

/**
 * Test the explicit user filtering
 */
async function testExplicitFiltering() {
  console.log('🚀 Testing Explicit User Filtering (Manual Test)\\n');
  
  // Check if tokens are set
  if (TEST_USERS[0].token === 'REPLACE_WITH_USER1_TOKEN' || TEST_USERS[1].token === 'REPLACE_WITH_USER2_TOKEN') {
    console.log('❌ Please update the TEST_USERS tokens in the script');
    console.log('');
    console.log('To get tokens:');
    console.log('1. Login to the web app as each test user');
    console.log('2. Open browser console and run: supabase.auth.session()?.access_token');
    console.log('3. Copy the token and update this script');
    return;
  }
  
  try {
    const [user1, user2] = TEST_USERS;
    
    // Each user creates a client
    console.log(`📝 ${user1.name}: Creating client...`);
    const user1Client = await apiRequest('/clients', user1, {
      method: 'POST',
      body: JSON.stringify({
        name: `${user1.name} Client`,
        email: '<EMAIL>'
      })
    });
    console.log(`✅ Created client: ${user1Client.client.id}`);
    
    console.log(`📝 ${user2.name}: Creating client...`);
    const user2Client = await apiRequest('/clients', user2, {
      method: 'POST',
      body: JSON.stringify({
        name: `${user2.name} Client`, 
        email: '<EMAIL>'
      })
    });
    console.log(`✅ Created client: ${user2Client.client.id}`);
    
    // Each user creates a job
    console.log(`📝 ${user1.name}: Creating job...`);
    const user1Job = await apiRequest('/jobs', user1, {
      method: 'POST',
      body: JSON.stringify({
        title: `${user1.name} Job`,
        client_id: user1Client.client.id,
        description: 'Job created by user 1'
      })
    });
    console.log(`✅ Created job: ${user1Job.id}`);
    
    console.log(`📝 ${user2.name}: Creating job...`);
    const user2Job = await apiRequest('/jobs', user2, {
      method: 'POST',
      body: JSON.stringify({
        title: `${user2.name} Job`,
        client_id: user2Client.client.id,
        description: 'Job created by user 2'
      })
    });
    console.log(`✅ Created job: ${user2Job.id}`);
    console.log('');
    
    // Test isolation: Each user should only see their own data
    console.log('🔍 Testing data isolation...');
    
    const user1Clients = await apiRequest('/clients', user1);
    const user1Jobs = await apiRequest('/jobs', user1);
    console.log(`📊 ${user1.name} sees ${user1Clients.clients.length} client(s), ${user1Jobs.jobs.length} job(s)`);
    
    const user2Clients = await apiRequest('/clients', user2);
    const user2Jobs = await apiRequest('/jobs', user2);
    console.log(`📊 ${user2.name} sees ${user2Clients.clients.length} client(s), ${user2Jobs.jobs.length} job(s)`);
    
    // Verify isolation
    let isolationPassed = true;
    
    // Check if users only see their own clients
    const user1ClientNames = user1Clients.clients.map(c => c.name);
    const user2ClientNames = user2Clients.clients.map(c => c.name);
    
    if (user1ClientNames.includes(`${user2.name} Client`)) {
      console.log(`❌ ${user1.name} can see ${user2.name}'s client!`);
      isolationPassed = false;
    }
    
    if (user2ClientNames.includes(`${user1.name} Client`)) {
      console.log(`❌ ${user2.name} can see ${user1.name}'s client!`);
      isolationPassed = false;
    }
    
    // Check if users only see their own jobs
    const user1JobTitles = user1Jobs.jobs.map(j => j.title);
    const user2JobTitles = user2Jobs.jobs.map(j => j.title);
    
    if (user1JobTitles.includes(`${user2.name} Job`)) {
      console.log(`❌ ${user1.name} can see ${user2.name}'s job!`);
      isolationPassed = false;
    }
    
    if (user2JobTitles.includes(`${user1.name} Job`)) {
      console.log(`❌ ${user2.name} can see ${user1.name}'s job!`);
      isolationPassed = false;
    }
    
    if (isolationPassed) {
      console.log('✅ Data isolation test PASSED!');
      console.log('✅ Users can only see their own data');
    } else {
      console.log('❌ Data isolation test FAILED!');
      console.log('❌ Users can see each other\\'s data');
    }
    
    // Test cross-user access (should fail)
    console.log('');
    console.log('🔍 Testing cross-user access blocking...');
    
    try {
      await apiRequest(`/clients/${user2Client.client.id}`, user1);
      console.log(`❌ ${user1.name} can access ${user2.name}'s client!`);
    } catch (error) {
      if (error.message.includes('404')) {
        console.log(`✅ Cross-user client access properly blocked (${user1.name} → ${user2.name})`);
      } else {
        console.log(`⚠️ Unexpected error: ${error.message}`);
      }
    }
    
    try {
      await apiRequest(`/jobs/${user2Job.id}`, user1);
      console.log(`❌ ${user1.name} can access ${user2.name}'s job!`);
    } catch (error) {
      if (error.message.includes('404')) {
        console.log(`✅ Cross-user job access properly blocked (${user1.name} → ${user2.name})`);
      } else {
        console.log(`⚠️ Unexpected error: ${error.message}`);
      }
    }
    
    console.log('');
    console.log('🎉 Manual isolation test completed!');
    console.log('');
    console.log('📝 Next steps to run a full automated test:');
    console.log('1. Fix the Supabase email validation issue');
    console.log('2. Or create test users manually in Supabase dashboard');
    console.log('3. Run the full test-explicit-filtering.mjs script');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Execute the test
testExplicitFiltering();