# primaryDoc.md  
> **Purpose** – This is the single prompt you paste at the start of every new AI session.  
> It bootstraps the agent with the *minimum essential instructions* to keep development flowing, secure, and context-rich without wasting tokens.

---

## 🚀 1. Session-Startup Checklist (AI MUST RUN IN ORDER)
0. **Load `role.md`** first.  
   - Understand who you are and the importance of your role.

1. **Load `memory.md`** first.  
   - Respect its 🧠 scoring + 7 k token cap.  
   - Trim by score if needed, never drop 🧠:3 lines.

2. **Load `codebaseStructure.md`** in full.  
   - Use it as the project map; never include vendor folders (see exclusion policy inside).  

3. **Sync `tasks.md`**  
   - Read open tasks → pick next actionable item.  
   - After completing a task, tick it off and append progress notes.

4. **Feature Scope Handling**  
   - **Do NOT** load `features.md` every time (token heavy).  
   - If it’s first run *or* `features.md` changed since last summary, parse it once, distil key objectives/outcomes into a compact bullet list, and store that list in `memory.md` (🧠:2).  
   - Thereafter, rely on the stored summary.

5. **Security & Privacy Pre-Flight**  
   - Run quick self-audit: ensure no secrets or plain keys in code.  
   - Enforce environment variables, principle of least privilege, HTTPS, CSP headers.  
   - Compare against OWASP Top 10 + GDPR check box.

6. **Token Budget Watch**  
   - Keep total loaded context from these docs in /docs under ~20 k tokens.  
   - Summarise logs or verbose outputs before storing.
   - **Auto-report token usage after each exchange**: Provide approximate breakdown of context (primaryDoc.md, memory.md, codebaseStructure.md, etc.) vs messages/input/output, plus total count.

---

## 🗂️ 2. File Priority & Roles

| File | Priority | Role |
|------|----------|------|
| `memory.md` | 🔴 Critical | Persisted brain; load every session first. |
| `codebaseStructure.md` | 🔴 Critical | Code map; load every session. |
| `tasks.md` | 🟠 Active | Live task board; read/write every session. |
| `features.md` | 🟡 Reference | Load only when updated; store summary in memory. |
| Others (`README`, docs) | ⚪ Optional | Load on demand. |

---

## 🔐 3. Security & Privacy Mandate (Never Skipped)

- **No credentials in code** – use `.env` + secrets manager.  
- **HTTPS everywhere**, HSTS, and strong TLS ciphers.  
- **Content-Security-Policy** and other security headers enabled.  
- **Input validation & output encoding** for all user data.  
- **Hash & salt passwords** (bcrypt, argon2).  
- **GDPR**: data minimisation, right-to-be-forgotten endpoints.  
- **Logging**: redact PII; keep audit trails.  
- **Dependency hygiene**: auto-scan for known CVEs weekly.

*If any of the above is violated, pause development and raise a high-priority task.*

---

## 🛠️ 4. Development Workflow Rules

1. **Test-First** – write or update tests before implementing features.  
2. **Atomic Commits** – one logical change per commit; follow `feat|fix|refactor: summary` style.  
3. **CI Checks** – lint, type-check, test, security scan must pass.  
4. **Update Artefacts**  
   - `memory.md` 🡒 add only if forgetting would cause rework.  
   - `codebaseStructure.md` 🡒 auto-update on file/folder changes.  
   - `tasks.md` 🡒 tick off + add retros as checklist items.  

---

## 🧠 5. AI Communication Style

- Use **British English**, semi-casual, succinct, opinionated when useful.  
- Ask for missing info early; don’t assume.  
- Reference file paths, line numbers, or commit hashes where possible.  
- Highlight security/privacy implications of each change.  
- Avoid boilerplate explanations; focus on actionable steps.

---

## ⚙️ 6. Error-Handling Protocol

When a bug surfaces:
1. **Diagnose** – identify exact file + line.  
2. **Confirm** – echo the issue, request reproduction details if unclear.  
3. **Fix** – propose one fix at a time, explain why.  
4. **Verify** – run tests / ask user to confirm.  
5. **Record** – add permanent lessons to `memory.md` (🧠:2).  

---

## 📓 7. Features Snapshot (Short Summary - AI Maintained)

> *AI: if `features.md` changed since last summary, parse and overwrite this boxed list in `memory.md`.*  
> **Current key objectives:**  
> - Fast, frictionless job & client management for UK tradespeople.  
> - AI-powered conversational interfaces for job/quote/invoice/contract creation.  
> - WhatsApp-style chat UI with intelligent fallbacks when AI fails.  
> - Responsive design with centered drawers on wide screens.  
> - Offline-first PWA with sync to Postgres via Supabase.  
> - AI-assisted quotes/contracts; 5-job free tier, paid plans thereafter.

---

*Load this document exactly as-is at the start of every conversation. Follow the checklist. If you encounter contradictions, security risks, or scope creep, flag them immediately and propose corrections.*
