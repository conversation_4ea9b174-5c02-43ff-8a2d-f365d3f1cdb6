# AI-Powered Drawer Implementation Guide

## Overview

This guide provides detailed implementation instructions for creating AI-powered drawers (Quote, Invoice, Contract, Client) based on the successful pattern established in `NewJobDrawer.tsx`. All drawers follow the same conversational UI pattern with trades-focused AI assistance.

**Based on Production Implementation**: This guide reflects the actual working implementation from `NewJobDrawer.tsx` which includes centered layout, intelligent AI prompts, smart fallback system, and proper security measures.

## Core Architecture Pattern

### 1. Drawer Structure
All AI drawers follow this standard structure:
- **Centered Layout**: Drawer appears in middle of screen on wide displays, responsive on mobile
- **Chat Interface**: WhatsApp-style conversation between user and "Dex" AI assistant
- **Multi-stage Flow**: Conversation → Data Collection → Confirmation → Creation
- **Error Handling**: Graceful fallbacks when AI fails, smart local responses

### 2. State Management Pattern
```typescript
// Core state interfaces
interface ConversationState = 'collecting-info' | 'selecting-options' | 'ready-to-create';

interface ChatMessage {
  id: string;
  type: 'dex' | 'user';
  content: string;
  timestamp: Date;
}

// Drawer state
const [conversationState, setConversationState] = useState<ConversationState>('collecting-info');
const [messages, setMessages] = useState<ChatMessage[]>([]);
const [isTyping, setIsTyping] = useState(false);
const [showOptionsDropdown, setShowOptionsDropdown] = useState(false);
```

### 3. AI Prompt Templates

Each drawer has a specialized AI prompt following this structure:

```typescript
const generateIntelligentResponse = async (userInput: string, context: string) => {
  const prompt = `You are Dex, a sharp, no-fuss assistant that helps UK tradespeople with [SPECIFIC_TASK].

OBJECTIVE:
Your goal is to help the user get to a [QUALITY_STANDARD] quickly. [QUALITY_CRITERIA]

[TASK_SPECIFIC_FORMAT]:
When input is valid and enough, provide this exact format:
[EXPECTED_OUTPUT_FORMAT]

IF IT'S VAGUE:
If the [TASK] is too vague, ask one quick follow-up. Only ask one or two questions.

IF IT'S GIBBERISH:
Stay cool, give a quick witty nudge, wait for real input.

STYLE & TONE:
- Sound like a friendly UK tradesman
- Plain UK English, no corporate tone
- One emoji max per response
- Keep things moving toward creation

Current context: "${context}"
User's input: "${userInput}"`;

  // API call with fallback handling
};
```

## Drawer-Specific Implementations

### Quote Drawer (`CreateQuoteDrawer.tsx`)

**Purpose**: AI-assisted quote creation for jobs
**Location**: Nested drawer from JobDrawer → "Create Quote" button

#### AI Prompt Configuration
```typescript
const quotePrompt = `You are Dex, a sharp, no-fuss assistant that helps UK tradespeople create accurate quotes for their jobs.

OBJECTIVE:
Help the user create a proper quote with:
- Clear description of work
- Fair pricing for UK trade standards
- Materials and labour breakdown (if relevant)

QUOTE FORMAT:
When input is complete, provide this exact format:

Quote Description: [Clear work description]
Materials: £[amount] [brief description]
Labour: £[amount] [time estimate]
Total: £[total amount]

That signals the system to move to quote confirmation.

IF IT'S VAGUE:
If pricing seems off or description unclear:
- "What's the rough size/scope?" 
- "Materials included or supply only?"
- "How many days work roughly?"

PRICING GUIDANCE:
- Suggest realistic UK trade rates
- Account for materials, labour, VAT
- Flag if pricing seems too low/high

Current job: "${jobTitle}"
Job description: "${jobDescription}"
User input: "${userInput}"`;
```

#### Key Features
- **Job Context**: Auto-populate with job title and description
- **Pricing Intelligence**: AI suggests realistic UK trade rates
- **Materials/Labour Split**: Automatic breakdown calculation
- **VAT Handling**: Optional 20% VAT calculation
- **Quote Templates**: Smart defaults based on job type

#### Implementation Steps
1. **Setup State**: Quote data, conversation state, job context
2. **Initialize Chat**: Greeting with job context
3. **AI Processing**: Parse user input for quote components
4. **Validation**: Ensure pricing is reasonable for UK market
5. **Confirmation**: Show quote summary with edit options
6. **Creation**: Save to database, generate PDF

### Invoice Drawer (`CreateInvoiceDrawer.tsx`)

**Purpose**: Convert quotes to invoices or create standalone invoices
**Location**: Nested drawer from JobDrawer → "Create Invoice" button

#### AI Prompt Configuration
```typescript
const invoicePrompt = `You are Dex, helping UK tradespeople create invoices from completed work.

OBJECTIVE:
Create proper invoice with:
- Payment terms (usually 30 days for trades)
- Completed work description
- Final amounts including VAT

INVOICE FORMAT:
When ready, provide:

Work Completed: [Description of finished work]
Net Amount: £[amount before VAT]
VAT (20%): £[vat amount]
Total Due: £[total amount]
Payment Terms: [Net 30 / Due on receipt / etc]

IF IT'S VAGUE:
- "Work finished or partially complete?"
- "Any extras or changes from original quote?"
- "Payment terms - 30 days or due on receipt?"

INVOICE SMARTS:
- Default to Net 30 for established clients
- Suggest "Due on receipt" for new clients
- Auto-populate from existing quotes where possible

Current job: "${jobTitle}"
Available quote: ${quoteData ? "£" + quoteData.total : "None"}
User input: "${userInput}"`;
```

#### Key Features
- **Quote Integration**: Auto-populate from existing quotes
- **VAT Calculation**: Automatic 20% VAT handling
- **Payment Terms**: Smart defaults (Net 30 for regular clients)
- **Status Tracking**: Draft → Sent → Paid workflow
- **Late Payment**: Automatic reminders system

### Contract Drawer (`CreateContractDrawer.tsx`)

**Purpose**: Generate UK-compliant trade contracts
**Location**: Nested drawer from JobDrawer → "Create Contract" button

#### AI Prompt Configuration
```typescript
const contractPrompt = `You are Dex, helping UK tradespeople create proper contracts that protect both parties.

OBJECTIVE:
Generate UK-compliant contract covering:
- Scope of work (detailed)
- Timeline and milestones
- Payment schedule
- Terms and conditions

CONTRACT FORMAT:
When ready, provide:

Scope of Work: [Detailed work description]
Timeline: [Start date, duration, key milestones]
Payment Schedule: [Deposit, progress payments, final payment]
Key Terms: [Warranty, liability, variations, cancellation]

IF IT'S VAGUE:
- "How long roughly for completion?"
- "Deposit required upfront?"
- "Any specific client requirements?"

UK COMPLIANCE:
- Include Consumer Rights Act provisions
- Standard trade warranty periods
- Fair payment terms (late payment legislation)
- Clear variation procedures

Current job: "${jobTitle}"
Client type: ${isCommercial ? "Commercial" : "Residential"}
User input: "${userInput}"`;
```

#### Key Features
- **Legal Compliance**: UK Consumer Rights Act, late payment legislation
- **Contract Templates**: Different templates for residential vs commercial
- **Digital Signatures**: Integration with digital signing platform
- **Version Control**: Track contract revisions and amendments
- **Automatic Clauses**: Standard warranty, liability, payment terms

### Client Drawer (`NewClientDrawer.tsx`)

**Purpose**: AI-assisted client onboarding and data collection
**Location**: Global "+ New Client" button or from job creation

#### AI Prompt Configuration
```typescript
const clientPrompt = `You are Dex, helping UK tradespeople add new clients efficiently.

OBJECTIVE:
Collect essential client info:
- Business/person name
- Address for jobs
- Contact details
- Property type (residential/commercial)

CLIENT FORMAT:
When you have enough info, provide:

Client Name: [Business or person name]
Address: [Full address including postcode]
Contact: [Phone number]
Email: [Email if provided]
Type: [Residential/Commercial]

IF IT'S VAGUE:
- "Business or residential property?"
- "Full address including postcode?"
- "Best contact number?"

CLIENT SMARTS:
- Validate UK postcodes
- Suggest property type from address
- Check for existing similar clients

User input: "${userInput}"`;
```

#### Key Features
- **Duplicate Detection**: Check for existing similar clients
- **Address Validation**: UK postcode validation and formatting
- **Contact Verification**: Phone number format validation
- **Property Intelligence**: Suggest commercial/residential from address
- **Integration**: Link immediately to job creation if originated from job

## Technical Implementation Details

### 1. Drawer Layout (Consistent Across All)

```tsx
const DrawerComponent = ({ isOpen, onClose, contextData }) => {
  return (
    <div className={`fixed inset-0 z-50 ${isOpen ? 'block' : 'hidden'}`}>
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      {/* Drawer Container - Centered on wide screens */}
      <div className="relative flex items-center justify-center min-h-screen p-4">
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
          
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-lg font-medium">Create [Type] for '{contextData.title}'</h2>
            <button onClick={onClose}>×</button>
          </div>
          
          {/* Chat Messages - Scrollable */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4">
            {messages.map(message => (
              <ChatMessage key={message.id} message={message} />
            ))}
            {isTyping && <TypingIndicator />}
          </div>
          
          {/* Input Area */}
          <div className="border-t p-6">
            <ChatInput onSend={processUserInput} />
          </div>
          
        </div>
      </div>
    </div>
  );
};
```

### 2. Auto-Scroll Implementation

```typescript
const scrollToBottom = () => {
  // Use requestAnimationFrame for proper timing
  requestAnimationFrame(() => {
    const chatContainer = document.getElementById('chat-messages');
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
  });
};

// Call after every message add
useEffect(() => {
  scrollToBottom();
}, [messages]);
```

### 3. API Integration Pattern

```typescript
const generateIntelligentResponse = async (userInput: string, context: string) => {
  try {
    const response = await fetch('/api/ai/chat-response', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        prompt: [DRAWER_SPECIFIC_PROMPT],
        input: userInput,
        context: context
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      return result.message;
    }
  } catch (error) {
    console.error('AI response error:', error);
  }
  
  // Smart fallback for each drawer type
  return getSmartFallbackResponse(userInput, context);
};
```

### 4. Smart Fallback System

Each drawer implements intelligent fallbacks when AI fails:

```typescript
const getSmartFallbackResponse = (userInput: string, context: string) => {
  const input = userInput.toLowerCase();
  
  // Quote Drawer Fallbacks
  if (drawerType === 'quote') {
    if (input.includes('electrical') || input.includes('rewire')) {
      return "For electrical work, I'd typically suggest £30-50/hour labour plus materials. What's the scope - full rewire or specific circuits?";
    }
    if (input.includes('plumbing')) {
      return "Plumbing rates around £35-60/hour usually. Is this a repair, installation, or full bathroom?";
    }
    // ... more intelligent fallbacks
  }
  
  // Default fallback
  return "I'm having connection issues. Could you try describing that in a bit more detail?";
};
```

## Security & Privacy Considerations

### 1. API Key Protection
- ✅ All AI API keys stored in backend environment variables only
- ✅ No API keys exposed in frontend bundle
- ✅ All AI calls routed through backend `/api/ai/*` endpoints

### 2. Data Validation
```typescript
// Validate all user inputs before processing
const validateInput = (input: string): boolean => {
  if (input.length > 2000) return false; // Max message length
  if (input.trim().length === 0) return false; // No empty messages
  // Add more validation as needed
  return true;
};
```

### 3. Error Handling
```typescript
// Graceful error handling with user-friendly messages
const handleError = (error: Error, context: string) => {
  console.error(`${context} error:`, error);
  addMessage('dex', "Something went wrong on my end. Please try again!", 500);
};
```

## Common Pitfalls & Solutions

### 1. **AI Response Parsing**
**Problem**: AI returns inconsistent format
**Solution**: Use regex patterns to extract key information
```typescript
const extractDataFromAI = (response: string) => {
  const patterns = {
    title: /(?:title|name):\s*(.+)/i,
    amount: /(?:total|amount):\s*£?(\d+(?:\.\d{2})?)/i,
    // Add more patterns
  };
  
  // Extract and validate each field
};
```

### 2. **State Management Complexity**
**Problem**: Multiple conversation states get confusing
**Solution**: Clear state machine with explicit transitions
```typescript
const stateTransitions = {
  'collecting-info': ['selecting-options', 'ready-to-create'],
  'selecting-options': ['collecting-info', 'ready-to-create'],
  'ready-to-create': ['collecting-info']
};
```

### 3. **Mobile Responsiveness**
**Problem**: Drawer too tall on mobile
**Solution**: Use viewport-aware sizing
```tsx
<div className="max-h-[90vh] sm:max-h-[80vh] md:max-h-[85vh]">
```

### 4. **Auto-scroll Issues**
**Problem**: Scroll doesn't work with dynamic content
**Solution**: Use requestAnimationFrame and intersection observer
```typescript
useEffect(() => {
  requestAnimationFrame(() => {
    setTimeout(scrollToBottom, 100); // Extra delay for dynamic content
  });
}, [messages]);
```

## Testing Strategy

### 1. Manual Testing Checklist
- [ ] AI conversation flows naturally
- [ ] Fallbacks work when AI fails
- [ ] Mobile responsive on all screen sizes
- [ ] Auto-scroll works properly
- [ ] Data validation prevents errors
- [ ] Error messages are user-friendly
- [ ] Security: No API keys in frontend
- [ ] Performance: No memory leaks

### 2. Test Scenarios
- Happy path: Complete conversation → creation
- AI failure: Test fallback responses
- Edge cases: Very long messages, special characters
- Mobile: Touch interactions, small screens
- Network: Slow connections, timeouts

## Implementation Checklist

### Pre-Implementation
- [ ] Review this guide thoroughly
- [ ] Understand the job drawer pattern
- [ ] Prepare AI prompts for specific drawer type
- [ ] Design fallback responses
- [ ] Plan data validation rules

### During Implementation
- [ ] Copy drawer structure from `NewJobDrawer.tsx`
- [ ] Adapt AI prompt for specific use case
- [ ] Implement smart fallback system
- [ ] Add proper TypeScript interfaces
- [ ] Test conversation flow
- [ ] Implement auto-scroll
- [ ] Add error handling
- [ ] Secure API endpoints

### Post-Implementation
- [ ] Test all conversation paths
- [ ] Verify mobile responsiveness
- [ ] Check security (no exposed keys)
- [ ] Remove debug/console.log statements
- [ ] Update documentation
- [ ] Add to main navigation

## File Locations & Naming

```
apps/web/src/components/
├── CreateQuoteDrawer.tsx      # Quote creation drawer
├── CreateInvoiceDrawer.tsx    # Invoice creation drawer
├── CreateContractDrawer.tsx   # Contract creation drawer
├── NewClientDrawer.tsx        # Client creation drawer (already exists)
└── NewJobDrawer.tsx          # Reference implementation

api/src/routes/
├── ai.ts                     # AI chat endpoints
├── quotes.ts                 # Quote CRUD operations
├── invoices.ts               # Invoice CRUD operations
├── contracts.ts              # Contract CRUD operations
└── clients.ts                # Client CRUD operations
```

## Summary

This implementation guide provides a proven pattern for creating AI-powered drawers that:
- Provide excellent UX with conversational interfaces
- Handle AI failures gracefully with smart fallbacks
- Maintain security by keeping API keys server-side
- Work responsively across all device sizes
- Follow consistent design patterns

Use this guide as a blueprint for implementing Quote, Invoice, Contract, and other AI-assisted drawers. The pattern is proven, tested, and ready for production use. 