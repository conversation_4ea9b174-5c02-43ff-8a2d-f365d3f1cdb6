# AI Integration Guide for DeskBelt

This document explains how to integrate AI-powered features into DeskBelt for automated document workflows. The system uses OpenRouter with LLaMA 3.3 8B as the primary model and Mistral 7B as fallback to provide intelligent parsing and generation capabilities with trades-focused responses.

## 🔧 **Current Implementation Status**

### ✅ **Implemented**
- **Job Creation AI Parsing** - Integrated into `NewJobDrawer` component
- **AI Service Layer** - Complete TypeScript service at `apps/web/src/lib/ai.ts`
- **API Endpoints** - Backend routes at `api/src/routes/ai.ts`
- **AI Configuration Page** - Settings interface at `apps/web/src/app/settings/ai/page.tsx`
- **AI Assistant "Dex"** - Conversational AI in NewJobDrawer with proper fallback handling
- **Rate Limiting Handling** - Discovered 50 requests/day limit, implemented proper fallback modes

### 🚧 **Ready for Implementation**
- **Dex Prompt Specification Update** - User provided detailed new prompt requirements
- Client Creation AI Parsing
- Quote Generation AI
- Invoice Generation AI
- Contract Generation AI
- Pricing Validation AI

### ⚠️ **Critical Issues Resolved**
- **Hardcoded Fallback Responses** - Removed fake AI responses that were overriding real AI
- **Rate Limit Discovery** - OpenRouter free tier: 50 requests/day, returns 429 errors when exceeded
- **Manual Fallback Mode** - "Dex is not feeling well" message when AI completely fails

---

## 🏆 **Model Selection & Performance**

### **Head-to-Head Comparison Results (2025-01-28)**

We tested multiple models to find the best AI for trades-focused conversations:

| Model | Connection | Response Quality | Trades Context | Winner |
|-------|------------|------------------|----------------|--------|
| `meta-llama/llama-3.3-8b-instruct:free` | ✅ | ⭐⭐⭐⭐⭐ Excellent | ✅ Perfect | 🥇 **PRIMARY** |
| `mistralai/mistral-7b-instruct:free` | ✅ | ⭐⭐⭐⭐ Good | ✅ Good | 🥈 **FALLBACK** |
| `google/gemma-3n-e4b-it:free` | ✅ | ❌ 500 Errors | ❌ Non-functional | ❌ |
| `mistralai/devstral-small:free` | ✅ | ❌ Poor | ❌ Confused "rewire" with software | ❌ |

### **LLaMA 3.3 8B Response Example (Winner):**
> *"A rewire, okay. What kind of rewire are we talking about? Is it a full rewire of an entire house, or just a specific area or room? And what's the reason for the rewire, is it due to an old or faulty electrical system, or are you making some changes to the layout or adding new electrical devices?"*

**Why LLaMA 3.3 8B Won:**
- ✅ **Natural conversational flow** - "okay" feels authentic  
- ✅ **Perfect length** - Not overwhelming, gets to the point
- ✅ **Trades-focused voice** - Sounds like a real electrician
- ✅ **Smart diagnostic questions** - Focuses on scope + cause

### **Fallback System Implementation**

The system now automatically tries the primary model first, then falls back to Mistral 7B if the primary fails:

```typescript
// API implements automatic fallback
const tryModel = async (modelName: string): Promise<string | null> => {
  // Returns response or null if failed
}

// Try primary first, fallback if needed
let response = await tryModel(PRIMARY_MODEL)
if (!response) {
  response = await tryModel(FALLBACK_MODEL)
}
```

**Files Updated:**
- `api/src/routes/ai.ts` - Primary/fallback model configuration
- `api/src/index.ts` - Updated test endpoints  
- Chat response endpoint includes model fallback logic

---

## 🛠 **Technical Architecture**

### **API Configuration**
```typescript
// OpenRouter configuration
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY // Secured in environment

// Primary model (winner of comparison testing)
const PRIMARY_MODEL = 'meta-llama/llama-3.3-8b-instruct:free'
// Fallback model (backup)
const FALLBACK_MODEL = 'mistralai/mistral-7b-instruct:free'

const OPENROUTER_MODEL = PRIMARY_MODEL // Using winner as primary
const BASE_URL = 'https://openrouter.ai/api/v1'
```

### **Service Layer Structure**
```
apps/web/src/lib/ai.ts
├── Types (ParsedClient, ParsedJob, etc.)
├── Default Prompts
├── Core Processing Function
├── Specific Functions (parseClientInformation, parseJobDescription, etc.)
└── AIService Class (with rate limiting)
```

### **API Endpoints Structure**
```
api/src/routes/ai.ts
├── POST /api/ai/parse-job ✅ IMPLEMENTED
├── POST /api/ai/parse-client (ready to implement)
├── POST /api/ai/parse-quote (ready to implement)
├── POST /api/ai/generate-invoice (ready to implement)
├── POST /api/ai/generate-contract (ready to implement)
├── POST /api/ai/validate-pricing (ready to implement)
└── GET /api/ai/test-connection ✅ IMPLEMENTED
```

---

## 📝 **Implementation Guide**

### **Step 1: Setting Up AI for a New Component**

1. **Add AI State to Component**
```typescript
const [aiInput, setAiInput] = useState('')
const [isParsingAI, setIsParsingAI] = useState(false)
const [hasUsedAI, setHasUsedAI] = useState(false)
```

2. **Create AI Parse Handler**
```typescript
const handleAIParse = async () => {
  if (!aiInput.trim()) return;
  
  setIsParsingAI(true);
  try {
    const response = await fetch('/api/ai/parse-[type]', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        input: aiInput,
        // Add relevant context
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.details || errorData.error);
    }

    const parsed = await response.json();
    // Update form data with parsed values
    
    setHasUsedAI(true);
  } catch (error) {
    console.error('AI parsing error:', error);
    alert(`Failed to parse: ${error.message}`);
  } finally {
    setIsParsingAI(false);
  }
};
```

3. **Add AI UI Elements**
```tsx
<div className="space-y-4">
  <Textarea
    value={aiInput}
    onChange={(e) => setAiInput(e.target.value)}
    placeholder="Paste job description, business card info, or describe the work..."
    rows={4}
  />
  <Button 
    onClick={handleAIParse}
    disabled={!aiInput.trim() || isParsingAI}
    className="flex items-center gap-2"
  >
    <SparklesIcon className="w-4 h-4" />
    {isParsingAI ? 'Parsing...' : 'Parse with AI'}
  </Button>
</div>
```

### **Step 2: Creating New API Endpoints**

Use this template for new AI endpoints in `api/src/routes/ai.ts`:

```typescript
// POST /api/ai/parse-[type]
router.post('/parse-[type]', async (req: Request, res: Response) => {
  try {
    const { input, context, customPrompt } = req.body

    // Validate input
    if (!input || typeof input !== 'string' || input.trim().length === 0) {
      return res.status(400).json({
        error: 'Invalid input',
        details: 'Input text is required'
      })
    }

    // Prepare context
    const contextString = context ? 
      `\n\nContext:\n${JSON.stringify(context, null, 2)}` : ''

    // Use custom or default prompt
    const prompt = customPrompt || DEFAULT_PROMPTS['[type]-parsing']

    // Call OpenRouter
    const completion = await openai.chat.completions.create({
      model: OPENROUTER_MODEL,
      messages: [
        { role: 'system', content: prompt },
        { role: 'user', content: `${input}${contextString}` }
      ],
      temperature: 0.1,
      max_tokens: 2000,
    })

    const response = completion.choices[0]?.message?.content?.trim()
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI'
      })
    }

    // Parse and validate JSON response
    let parsedResponse
    try {
      parsedResponse = JSON.parse(response)
    } catch (parseError) {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        parsedResponse = JSON.parse(jsonMatch[0])
      } else {
        return res.status(500).json({
          error: 'Invalid AI response format'
        })
      }
    }

    res.json(parsedResponse)

  } catch (error) {
    console.error('AI parsing error:', error)
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})
```

---

## 🎯 **Implementation Roadmap**

### **Phase 1: Client Creation AI (Next)**

1. **Add API Endpoint**
   - Create `POST /api/ai/parse-client` in `api/src/routes/ai.ts`
   - Use `CLIENT_PARSING_PROMPT` from AI settings

2. **Update Component**
   - Find client creation component (likely `CreateClientDrawer` or similar)
   - Add AI parsing step similar to job creation
   - Parse business cards, contact info, company details

3. **Expected Input/Output**
   ```typescript
   // Input: "John Smith Electrical Services, 123 Main St, London, <EMAIL>, 020 7946 0958"
   // Output: 
   {
     firstName: "John",
     lastName: "Smith", 
     email: "<EMAIL>",
     phone: "020 7946 0958",
     companyName: "John Smith Electrical Services",
     address: "123 Main St, London",
     isCommercial: true
   }
   ```

### **Phase 2: Quote Generation AI**

1. **Add API Endpoint**
   - Create `POST /api/ai/parse-quote`
   - Parse natural language pricing into structured line items

2. **Update Component**
   - Enhance `CreateQuoteDrawer` with AI parsing
   - Parse phrases like "£200 for labour, £150 materials, 2 days work"

3. **Expected Input/Output**
   ```typescript
   // Input: "Kitchen rewiring - £300 labour for 2 days, £150 materials (cables, outlets), £50 misc"
   // Output:
   {
     lineItems: [
       { description: "Labour (Kitchen Rewiring)", quantity: 2, rate: 150, unit: "day", total: 300 },
       { description: "Materials (cables, outlets)", quantity: 1, rate: 150, unit: "item", total: 150 },
       { description: "Miscellaneous", quantity: 1, rate: 50, unit: "item", total: 50 }
     ],
     subtotal: 500,
     suggestedAddOns: ["GFCI outlet upgrade", "Smart switches"],
     notes: ["All work includes parts and labour", "2-year warranty on workmanship"]
   }
   ```

### **Phase 3: Invoice Generation AI**

1. **Add API Endpoint**
   - Create `POST /api/ai/generate-invoice`
   - Auto-populate invoice details based on quote and job data

2. **Update Component**
   - Enhance `CreateInvoiceDrawer` with smart defaults
   - Generate invoice numbers, payment terms, due dates

### **Phase 4: Contract Generation AI**

1. **Add API Endpoint**
   - Create `POST /api/ai/generate-contract`
   - Generate contract terms based on job type and value

2. **Update Component**
   - Enhance `CreateContractDrawer` with AI-generated terms
   - Include appropriate clauses, payment schedules, warranties

### **Phase 5: Pricing Validation AI**

1. **Add API Endpoint**
   - Create `POST /api/ai/validate-pricing`
   - Compare pricing against market rates

2. **Integration Points**
   - Add validation step to quote creation
   - Show pricing intelligence in job details
   - Alert for under/over-pricing

---

## 🔧 **Configuration & Customization**

### **Prompt Management**

Prompts can be customized via the AI Settings page (`/settings/ai`):

1. **Access Settings**: Navigate to Settings → AI Configuration
2. **Select Prompt**: Choose from client-parsing, job-parsing, quote-parsing, etc.
3. **Edit Prompt**: Modify the prompt text to fine-tune AI behavior
4. **Test Changes**: Use the test functionality to verify prompt effectiveness

### **Environment Variables**

```bash
# Required
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional (will use defaults)
OPENROUTER_MODEL=meta-llama/llama-3.3-8b-instruct:free
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
```

### **Rate Limiting**

The `AIService` class includes built-in rate limiting:
- **Default**: 20 requests per minute
- **Customizable**: Modify `maxRequestsPerMinute` in `apps/web/src/lib/ai.ts`
- **Error Handling**: Graceful degradation when limits exceeded

---

## 🚨 **Error Handling & Best Practices**

### **Error Types**
1. **Validation Errors** (400): Invalid input
2. **Authentication Errors** (401): Invalid API key
3. **Rate Limiting** (429): Too many requests
4. **AI Response Errors** (500): Invalid AI output
5. **Network Errors**: Connection issues

### **Best Practices**

1. **Always Validate Input**
   ```typescript
   if (!input || typeof input !== 'string' || input.trim().length === 0) {
     throw new Error('Input is required')
   }
   ```

2. **Provide Context**
   ```typescript
   const context = {
     clientInfo: selectedClient,
     previousJobs: jobHistory,
     location: userLocation
   }
   ```

3. **Handle Graceful Fallbacks**
   ```typescript
   try {
     const aiResult = await parseWithAI(input)
     setFormData(aiResult)
   } catch (error) {
     console.error('AI failed, using manual entry:', error)
     setManualEntryMode(true)
   }
   ```

4. **User-Friendly Error Messages**
   ```typescript
   catch (error) {
     const userMessage = error.message.includes('rate limit') 
       ? 'AI is temporarily busy. Please try again in a moment.'
       : 'AI parsing failed. You can enter information manually.'
     alert(userMessage)
   }
   ```

### **Performance Tips**

1. **Debounce AI Calls** - Avoid rapid-fire requests
2. **Cache Results** - Store successful parses to avoid re-processing
3. **Progressive Enhancement** - AI enhances but doesn't break core functionality
4. **Fallback Modes** - Always provide manual entry options

---

## 📊 **Testing & Monitoring**

### **Test AI Connection**
```bash
curl -X GET http://localhost:3000/api/ai/test-connection
```

### **Test Job Parsing**
```bash
curl -X POST http://localhost:3000/api/ai/parse-job \
  -H "Content-Type: application/json" \
  -d '{"input": "Need electrical work in kitchen - new outlets and lighting, about 2 days work, £400 budget"}'
```

### **Monitor Usage**
- Check API logs for errors
- Monitor OpenRouter usage dashboard
- Track success/failure rates
- User feedback on AI accuracy

---

## 🔗 **Related Files**

- **AI Service**: `apps/web/src/lib/ai.ts`
- **AI Settings**: `apps/web/src/app/settings/ai/page.tsx` 
- **API Routes**: `api/src/routes/ai.ts`
- **Job Creation**: `apps/web/src/components/NewJobDrawer.tsx`
- **Documentation**: `docs/AIWorkflow.md` (strategy overview)

---

## 🆘 **Troubleshooting**

### **Common Issues**

1. **"No response from AI"**
   - Check API key validity
   - Verify OpenRouter service status
   - Check network connectivity

2. **"Invalid JSON response"**
   - AI might be returning explanation text
   - Adjust prompt to emphasize "JSON only"
   - Check for prompt injection in user input

3. **"Rate limit exceeded"**
   - Wait before retrying
   - Implement request queuing
   - Consider upgrading OpenRouter plan

4. **Inconsistent AI Results**
   - Lower temperature setting (0.1)
   - More specific prompts
   - Add validation rules to prompts

### **Debug Mode**
Enable detailed logging by setting:
```typescript
const DEBUG_AI = process.env.NODE_ENV === 'development'
```

This will log full AI requests/responses for troubleshooting.

---

## 🤖 **AI Assistant "Dex" Implementation**

### **Current Status**
The AI assistant "Dex" is now fully functional in the NewJobDrawer component with proper conversation flow and fallback handling.

### **Key Features Implemented**
- ✅ **Natural Conversation Flow** - LLaMA 3.3 8B model provides trade-focused responses
- ✅ **Hardcoded Response Removal** - Eliminated fake AI responses that were overriding real AI
- ✅ **Proper Fallback Handling** - Returns null when AI fails, triggering manual mode
- ✅ **Rate Limit Awareness** - Handles 50 requests/day OpenRouter limit gracefully
- ✅ **Manual Override Mode** - "Dex is not feeling well" message with manual input processing

### **Example Working Response**
```
Input: "rewire"
Dex Response: "A rewire, that's a significant job. Can you tell me a bit more about what's driving the need for a rewire? Is it due to an old or faulty electrical system, or are you making some significant changes to the property, such as adding new rooms or extensions?"
```

### **Rate Limiting Behavior**
- **Free Tier Limit**: 50 requests per day
- **Error Response**: 429 "Rate limit exceeded: free-models-per-day"
- **Fallback Action**: Shows manual mode instead of fake AI responses
- **User Experience**: Clear indication when AI is unavailable

### **Pending Implementation: New Prompt Specification**

The user has provided a comprehensive new prompt specification for Dex that needs to be implemented. The new specification includes:

#### **Required Sections**
1. **Role Definition** - Dex as trade job logging assistant
2. **Input Acceptance/Rejection Rules** - What types of input to process
3. **Job Drafting Guidelines** - How to structure job information
4. **Clarification Rules** - When and how to ask follow-up questions
5. **Off-topic Handling** - How to redirect non-trade conversations
6. **Flow Control** - Conversation management and examples

#### **Implementation Location**
- **File**: `api/src/routes/ai.ts`
- **Function**: Update the prompt in the `/chat` endpoint
- **Current Prompt**: Basic trade-focused prompt
- **New Prompt**: Comprehensive specification with examples and formatting rules

#### **Next Steps**
1. Replace current basic prompt with user's detailed specification
2. Test conversation flow with new prompt structure
3. Verify all sections (acceptance, drafting, clarification, etc.) work correctly
4. Update any related documentation

---

**Last Updated**: January 2025  
**Version**: 1.0  
**Status**: Job Creation AI ✅ Implemented | Other Components 🚧 Ready for Implementation 