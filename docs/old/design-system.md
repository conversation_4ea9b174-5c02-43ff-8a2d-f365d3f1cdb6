# DeskBelt Design System
*Professional, mobile-first design system for UK tradespeople*

## 🎨 Design Philosophy

**Mobile-First**: Every component is designed for mobile and enhanced for desktop
**Accessibility-First**: WCAG 2.1 AA compliant with proper contrast ratios and keyboard navigation
**Performance-First**: Lightweight, optimized components that load fast on any connection
**Clarity-First**: Clear visual hierarchy and intuitive interactions

## 🌈 Color Palette

### Brand Colors
- **Jobs Blue**: `#1d4ed8` - Primary blue for all job-related features
- **Clients Green**: `#047857` - Primary green for all client-related features  
- **AI Orange**: `#d97706` - Accent orange for AI/Ask Dex features

### Neutral Colors
- **Background Light**: `#f8fafc`
- **Background Dark**: `#0f172a`
- **Text Primary**: `#1e293b` (light) / `#f1f5f9` (dark)
- **Text Secondary**: `#64748b` (light) / `#94a3b8` (dark)
- **Border**: `#e2e8f0` (light) / `#334155` (dark)

### Status Colors
- **Success**: `#047857` (Green)
- **Warning**: `#d97706` (Orange) 
- **Error**: `#b91c1c` (Red)
- **Info**: `#1d4ed8` (Blue)

## 📐 Typography Scale

### Font Family
**Primary**: Inter (fallback: system-ui, sans-serif)

### Type Scale
- **Display Large**: 3rem (48px) - Hero headings
- **Display Small**: 2.25rem (36px) - Page headings
- **Heading Large**: 1.875rem (30px) - Section headings
- **Heading Medium**: 1.5rem (24px) - Card headings
- **Heading Small**: 1.25rem (20px) - Sub-headings
- **Body Large**: 1.125rem (18px) - Prominent body text
- **Body Regular**: 1rem (16px) - Default body text
- **Body Small**: 0.875rem (14px) - Secondary text
- **Caption**: 0.75rem (12px) - Captions, timestamps

## 🏗️ Layout System

### Breakpoints
- **Mobile**: 0px - 639px
- **Tablet**: 640px - 1023px
- **Desktop**: 1024px+

### Grid System
- **Mobile**: Single column, full width with 16px padding
- **Tablet**: 2-3 columns with 24px gutters
- **Desktop**: 4-6 columns with 32px gutters

### Z-Index Scale
- **Background**: -1
- **Normal**: 1
- **Dropdown**: 10
- **Sticky**: 20
- **Header**: 30
- **Drawer**: 40
- **Modal**: 50
- **Tooltip**: 60

## 🧩 Component Specifications

### 1. Buttons

#### Primary Button
- **Background**: Brand color (jobs-700, clients-700, ai-600)
- **Text**: White
- **Padding**: 12px 24px (mobile) / 16px 32px (desktop)
- **Border Radius**: 8px
- **Font Weight**: 600
- **Min Height**: 44px (touch-friendly)
- **States**: Default, Hover (-100 darker), Active, Disabled (50% opacity)

#### Secondary Button  
- **Background**: Transparent
- **Border**: 2px solid brand color
- **Text**: Brand color
- **Same dimensions as primary**

#### Outline Button
- **Background**: Transparent  
- **Border**: 1px solid gray-300
- **Text**: gray-700
- **Hover**: gray-50 background

### 2. Form Controls

#### Input Fields
- **Height**: 44px minimum (mobile accessibility)
- **Padding**: 12px 16px
- **Border**: 1px solid gray-300
- **Border Radius**: 8px
- **Focus**: 2px solid brand color, remove default outline
- **Error**: Border becomes error-500, show error text below
- **Placeholder**: gray-500 text

#### Textarea
- **Min Height**: 88px (2 rows)
- **Max Length**: 2000 characters (as specified)
- **Resize**: Vertical only
- **Character counter** when approaching limit

#### Select Dropdown
- **Same styling as input**
- **Chevron icon**: Right-aligned, gray-500
- **Options**: White background, hover gray-50

### 3. Cards

#### Job Card
- **Background**: White (light) / gray-800 (dark)
- **Border**: 1px solid gray-200 (light) / gray-700 (dark)
- **Border Radius**: 12px
- **Padding**: 16px
- **Shadow**: soft (hover: medium)
- **Max Width**: 100% (mobile) / 400px (desktop)

#### Client Card
- **Same base styling as Job Card**
- **Accent**: Left border 4px solid clients-700

#### Layout Structure
```
┌─────────────────────────────────┐
│ Title               Status Badge │
│ Client Info         📅 Date     │
│ 💬 Notes  📞 Call  👤 Assigned │
│ [Action Buttons]                │
└─────────────────────────────────┘
```

### 4. Status Badges

#### Size: Small
- **Padding**: 4px 8px
- **Font Size**: 12px
- **Border Radius**: 12px (pill shape)

#### Colors by Status
- **New**: Blue (jobs-100 bg, jobs-700 text)
- **In Progress**: Green (success-100 bg, success-700 text)
- **On Hold**: Orange (warning-100 bg, warning-700 text)
- **Completed**: Teal (success-100 bg, success-700 text)
- **Archived**: Gray (gray-100 bg, gray-700 text)

### 5. Navigation

#### Top Bar
- **Height**: 64px
- **Background**: White (light) / gray-900 (dark)
- **Border Bottom**: 1px solid gray-200 (light) / gray-700 (dark)
- **Layout**: Hamburger (left) | Logo (center) | Theme + Profile (right)

#### Left Navigation Drawer
- **Width**: 280px (desktop) / 100% (mobile)
- **Background**: White (light) / gray-900 (dark)
- **Animation**: Slide from left, 300ms ease-out
- **Links**: 16px vertical padding, hover background gray-50/gray-800

#### Right Detail Drawer
- **Width**: 400px (desktop) / 100% (mobile)
- **Background**: White (light) / gray-50 (dark)
- **Animation**: Slide from right, 300ms ease-out

### 6. Chat Interface (WhatsApp-style)

#### Chat Bubbles
- **User (Right)**: jobs-100 background, jobs-900 text, right-aligned
- **System/AI (Left)**: gray-100 background, gray-900 text, left-aligned
- **Max Width**: 80% of container
- **Border Radius**: 16px
- **Padding**: 12px 16px
- **Timestamp**: Below bubble, 12px gray-500 text

#### Chat Input
- **Background**: White
- **Border**: 1px solid gray-300
- **Border Radius**: 24px
- **Padding**: 12px 16px
- **Send Button**: Circular, 40px, jobs-700 background, paper plane icon

### 7. Modals & Drawers

#### Modal Overlay
- **Background**: rgba(0, 0, 0, 0.5)
- **Backdrop Blur**: 4px
- **Animation**: Fade in 200ms

#### Modal Content
- **Background**: White (light) / gray-900 (dark)
- **Border Radius**: 12px (desktop) / 0 (mobile full-screen)
- **Max Width**: 600px (desktop)
- **Padding**: 24px
- **Shadow**: hard

### 8. Tables (Admin Portal)

#### Table Structure
- **Header**: gray-50 background, 600 font weight
- **Rows**: Hover gray-50, border-bottom gray-200
- **Cell Padding**: 12px 16px
- **Min Height**: 48px

#### Action Menus
- **Trigger**: Three dots icon, gray-500
- **Menu**: White background, shadow-medium, 8px border radius
- **Items**: 12px vertical padding, hover gray-50

## 🎭 Interaction States

### Hover Effects
- **Buttons**: Background darkens by 100 (e.g., 700 → 800)
- **Cards**: Shadow increases from soft to medium, slight scale (1.02)
- **Links**: Underline appears, color darkens by 100

### Focus States  
- **All Interactive Elements**: 2px solid brand color outline, 2px offset
- **Skip outline for mouse users**, show for keyboard users

### Loading States
- **Skeleton Loaders**: Gray-200 background with subtle pulse animation
- **Spinners**: Brand color, 24px size for buttons, 32px for page loading
- **Progress Bars**: Brand color fill, gray-200 background

### Error States
- **Form Fields**: Red border, red text below
- **Pages**: Centered card with error icon and retry button
- **Toast Messages**: Red background, white text, auto-dismiss 5s

## 📱 Responsive Behavior

### Mobile (< 640px)
- **Single column layouts**
- **Full-width cards** with 16px side margins
- **Bottom sheets** instead of dropdowns where appropriate
- **Larger touch targets** (minimum 44px)
- **Simplified navigation** with hamburger menu

### Tablet (640px - 1023px)
- **2-3 column layouts**
- **Grid cards** with gutters
- **Side-by-side forms** where space allows
- **Hybrid navigation** (drawer + horizontal tabs)

### Desktop (1024px+)
- **Multi-column layouts** (up to 6 columns)
- **Sidebar navigation** always visible
- **Right detail panels** for drill-down content
- **Hover states** and advanced interactions
- **Keyboard shortcuts** support

## 🌙 Dark Mode Specifications

### Color Adaptations
- **Backgrounds**: Invert to dark variants
- **Text**: High contrast white/light gray on dark
- **Borders**: Lighter grays on dark backgrounds
- **Shadows**: Darker, more subtle on dark themes

### Toggle Implementation
- **Position**: Top bar, right side, next to profile
- **Icon**: Sun/moon toggle
- **Persistence**: localStorage
- **Transition**: Smooth 200ms transition on color changes

## ♿ Accessibility Requirements

### Color Contrast
- **AA Compliance**: Minimum 4.5:1 ratio for normal text
- **AAA Compliance**: 7:1 ratio for small text when possible
- **Color Independence**: Never rely on color alone for meaning

### Keyboard Navigation
- **Tab Order**: Logical, visible focus indicators
- **Skip Links**: "Skip to main content" for screen readers
- **Escape Key**: Close modals, drawers, dropdowns

### Screen Reader Support
- **ARIA Labels**: All interactive elements
- **Landmark Roles**: header, main, navigation, aside
- **Live Regions**: For dynamic content updates

### Touch Accessibility  
- **Minimum Size**: 44px × 44px touch targets
- **Spacing**: 8px minimum between touch targets
- **Swipe Gestures**: Optional, always provide button alternatives

---

This design system ensures consistent, professional, and accessible user experience across all DeskBelt applications while maintaining the brand identity and usability requirements specified in the feature documentation. 