# DeskBelt Feature Specification  
*Ready for Architects, Devs & QA to break down into phases, tasks and sub-tasks; completely unambiguous, image-free, mobile-first and security-first.*

---

## 1. Project Overview

DeskBelt is a responsive, mobile-first web platform for UK tradespeople to manage jobs, clients, invoices, quotes and contracts in under 60 seconds. It has two “faces”:  
1. **End-User Frontend (Next.js + TailwindCSS + shadcn)**  
2. **Admin Portal (Next.js + TailwindCSS)**  

Behind the scenes, everything lives on **Supabase PostgreSQL** (via Supabase Auth & Row-Level Policies) and a set of custom Next.js API routes (for any business logic not handled by Supabase). The entire stack must be **dockerised**, **modular**, **scalable** and **secure by design**.

Key user-facing areas:  
- **Authentication & Registration** (email/password + OAuth via Google/Facebook)  
- **Main Dashboard** with four sections:  
  - **Jobs**  
  - **Clients**  
  - **Stats**  
  - **Archived**  
- **Left Nav Drawer** (hamburger menu) with:  
  - Dashboard (Jobs/Clients/Stats/Archived)  
  - Notifications  
  - Teams  
  - Settings  
  - Profile (with “Logged in as … / Logout”)  
- **Right-Side Drawers** (slide-in panels) for:  
  - Job details/actions  
  - Client details/actions  
  - Team details/actions  
  - Profile editing  
- **Ask Dex** (AI chat window for business advice + auto-filling quotes/invoices/contracts)  
- **Job & Client “Chat-Style” Notes** (WhatsApp-like interaction)  
- **Create Quote / Invoice / Contract** flows (AI-assisted, generated automatically)  
- **Team Management** on the user side (invite, roles, preferences)  
- **Admin Portal** (super-admin only):  
  - Dashboard (system health, counts)  
  - User Management (CRUD + status + risk flags)  
  - Team Management (view teams, members, settings)  
  - System Settings (feature toggles, security settings)  
  - Analytics (charts, tables)  
  - Audit Logs (user & system actions)  

Every single screen, button, form and interaction must be precisely defined and broken down into sub-features so that even someone who never saw the mocks can build it.

---

## 2. High-Level Phases & Sub-Phases

Below is a “Phase → Sub-Phase → Task → Sub-Task” breakdown. **Tests** occur at the end of each Sub-Phase (or, if especially granular, at Sub-Task level). When QA says “✅ This Sub-Phase works”, you move on.

> **Style convention**:  
> - **PHASE** (all-caps, major section)  
> - **Sub-Phase** (Title Case, grouping of related features)  
> - **Task** (bullet, short imperative)  
> - **Sub-Task** (indented bullet, specific steps)

---

### PHASE 1 — Project Kick-Off & Planning

#### 1.1 Requirements Finalisation & Review
- **Task 1.1.1** Gather all final UI screens and text annotations.  
  - Sub-Task 1.1.1.1: Confirm no additional views are missing (e.g. payment pages, error states, mobile-only flows).  
  - Sub-Task 1.1.1.2: Verify final AI integration expectations.  
  - Sub-Task 1.1.1.3: Confirm brand colours (blue → jobs; green → clients; yellow/orange → AI).  
- **Task 1.1.2** Review technology stack & architectural constraints.  
  - Sub-Task 1.1.2.1: Document Next.js version, TailwindCSS config, shadcn components.  
  - Sub-Task 1.1.2.2: Specify Supabase schema guidelines.  
  - Sub-Task 1.1.2.3: Confirm Docker base images (Node 18+ LTS).  
- **Task 1.1.3** Define security & compliance requirements.  
  - Sub-Task 1.1.3.1: List OWASP Top 10 protections needed (XSS, CSRF, Injection).  
  - Sub-Task 1.1.3.2: Outline GDPR/privacy requirements (UK-compliant).  
  - Sub-Task *******: Decide on password rules / rate-limiting / brute-force protections.  
- **Task 1.1.4** Create initial repository structure & naming conventions.  
  - Sub-Task *******: Establish monorepo vs. two repos (Front End + Admin).  
  - Sub-Task *******: Define folder layout:  
    - `/apps/web` (user frontend)  
    - `/apps/admin` (admin portal)  
    - `/libs/ui` (shared UI components)  
    - `/libs/db` (shared database models / migration scripts)  
    - `/docker` (Dockerfiles, docker-compose templates)  
- **Task 1.1.5** Set up project issue tracker & base CI pipeline stub.  
  - Sub-Task *******: Create JIRA/GitHub Projects board with Epics for each Phase.  
  - Sub-Task *******: Kickstart GitHub Actions (or equivalent) with “lint” and “type check” jobs.

**– End of Sub-Phase 1.1 Tests –**  
- Verify that all mocks/screens are accounted for in a single place.  
- Ensure initial repo structure has been created and CI passes its stub pipelines.  

---

### PHASE 2 — Authentication & Basic Routing (Frontend Skeleton)

#### 2.1 Build Layout & Routing Structure (Unauthenticated)
- **Task 2.1.1** Design and implement global layout.  
  - Sub-Task *******: Create `Layout.tsx` rendering a top-bar (DeskBelt logo center, Profile icon right, hamburger menu icon left).  
  - Sub-Task *******: Define responsive breakpoints (mobile < 640px, tablet 640–1023px, desktop 1024px+).  
  - Sub-Task *******: Implement left Nav Drawer (hidden by default, slides from left on small screens).  
    - Items: Dashboard link, Notifications link, Teams, Settings.  
    - Profile section at bottom with circular avatar (user initials), "Logged in as:" label, and company name.
    - Logout button with icon, styled as bordered button.
    - Help and About links below logout button as horizontal blue clickable links.
    - On mobile, it occupies full height; on desktop, can be toggled collapsed/expanded.  
  - Sub-Task *******: Implement sticky top navigation bar that remains fixed during scroll.
    - Only dashboard content scrolls, top bar with DeskBelt logo and profile icon stays visible.
  - Sub-Task *******: Implement right-side drawer container (empty placeholder) that will host Job/Client/Team/Settings drawers.
    - Supports nested drawers: when action buttons are clicked in right drawers, new drawers open in front.  
- **Task 2.1.2** Set up Next.js page routing.  
  - Sub-Task *******: `/login` (Login Page)  
  - Sub-Task *******: `/register` (Registration Page)  
  - Sub-Task *******: `/forgot-password` (Password recovery)  
  - Sub-Task *******: `/dashboard` (redirects to `/dashboard/jobs`)  
  - Sub-Task *******: `/dashboard/jobs` (Jobs Dashboard)  
  - Sub-Task *******: `/dashboard/clients` (Clients Dashboard)  
  - Sub-Task *******: `/dashboard/stats` (Stats Dashboard)  
  - Sub-Task *******: `/dashboard/archived` (Archived Dashboard)  
  - Sub-Task *******: `/profile` (User Profile)  
  - Sub-Task *******0: `/settings` (User Settings)  
  - Sub-Task *******1: `/teams` (Team Overview/List)  
  - Sub-Task *******2: `/team/[teamId]` (Team Details)  
  - Sub-Task *******3: `/ask-dex` (AI Chat Page – full-screen on mobile, drawer variant on desktop)  
  - Sub-Task *******4: (catch-all) Any undefined route → 404 Page (custom)  
- **Task 2.1.3** Stub in all TypeScript interfaces for props and pages.  
  - Sub-Task *******: Create `types/Job.ts`, `types/Client.ts`, `types/User.ts`, `types/Team.ts`, `types/Quote.ts`, `types/Invoice.ts`, `types/Contract.ts` with empty interfaces.  
  - Sub-Task *******: In each page file, import appropriate type and return skeleton `<div>Loading…</div>`.  

#### 2.2 Build Authentication UI Components
- **Task 2.2.1** Create `AuthLayout.tsx` (simple centred card).  
  - Sub-Task *******: Top-bar hidden (only show logo at top centre).  
  - Sub-Task *******: Card container: `max-w-md mx-auto p-6 shadow-lg rounded-xl`.  
- **Task 2.2.2** Implement **Login Page** (`/login`).  
  - Sub-Task *******: Email + Password fields (Tailwind styled, appropriate labels, placeholders).  
  - Sub-Task *******: “Login with Google” & “Login with Facebook” buttons (use official SVG icons, brand colours: Google button white background with Google logo; Facebook button dark-blue background with “f”).  
  - Sub-Task *******: “Forgot password?” link under form.  
  - Sub-Task 2.2.2.4: “Don’t have an account? Register” link.  
  - Sub-Task 2.2.2.5: Button “Sign In” disabled by default; enabled once form validates.  
- **Task 2.2.3** Implement **Registration Page** (`/register`).  
  - Sub-Task 2.2.3.1: Email + Password + Confirm Password fields.  
  - Sub-Task 2.2.3.2: “Register with Google” & “Register with Facebook” buttons (same styling as login).  
  - Sub-Task 2.2.3.3: “Already have an account? Login” link.  
  - Sub-Task 2.2.3.4: Show password strength meter under password field (weak, medium, strong).  
- **Task 2.2.4** Implement **Forgot Password Page** (`/forgot-password`).  
  - Sub-Task 2.2.4.1: Single email field + “Send Reset Link” button.  
  - Sub-Task 2.2.4.2: After submit, show success message: “If that email is in our system, you will receive a link shortly.”  
- **Task 2.2.5** Add basic **Auth Context** & client-side guard.  
  - Sub-Task 2.2.5.1: Create `context/AuthContext.tsx` that wraps `SessionProvider` from Supabase.  
  - Sub-Task 2.2.5.2: In `pages/_app.tsx`, wrap `<AuthContextProvider>`.  
  - Sub-Task 2.2.5.3: Add a `useRequireAuth()` hook that:  
    - If user not logged in → redirect to `/login`.  
    - If user logged in → allow.  
  - Sub-Task 2.2.5.4: Apply `useRequireAuth()` in every page under `/dashboard`, `/ask-dex`, `/profile`, `/settings`, `/teams`, etc.  
- **Task 2.2.6** Stub out **API routes** (Next.js API) for auth-related calls.  
  - Sub-Task 2.2.6.1: `/api/auth/login` (POST): accept email/password, return Supabase session.  
  - Sub-Task 2.2.6.2: `/api/auth/register` (POST): accept email/password, create Supabase user.  
  - Sub-Task 2.2.6.3: `/api/auth/forgot-password` (POST): accept email, call Supabase password reset.  
  - Sub-Task 2.2.6.4: `/api/auth/oauth-callback` (GET): handle Google/Facebook OAuth.  

**– End of Sub-Phase 2.1 & 2.2 Tests –**  
1. **Layout & Routing Tests** (Sub-Phase 2.1):  
   - Verify global layout renders on every page (logo, drawer icons), and correct placeholder content appears.  
   - Check that clicking “Dashboard → Jobs/Clients/Stats/Archived” updates URL without errors.  
   - Confirm 404 page appears for unknown routes.  
2. **Auth UI Tests** (Sub-Phase 2.2):  
   - Test form validation: empty fields disable submit, invalid email shows error.  
   - Clicking “Google” / “Facebook” buttons triggers stubbed OAuth (no actual redirect yet).  
   - Submitting “Forgot Password” shows the success message.  
   - Confirm `useRequireAuth()` actually redirects an unauthenticated user away from `/dashboard`.  

---

## 3. Database Schema Design & Supabase Setup

> **Note**: Devs must not write any backend logic until DB schema is finalised.

### 3.1 Design Entity-Relationship Model
- **Task 3.1.1** Define **Users** table (in Supabase).  
  - Sub-Task *******: Columns:  
    - `id` (UUID, primary key, default `auth.uid()`)  
    - `email` (text, unique, not null)  
    - `full_name` (text, null)  
    - `phone` (text, null)  
    - `company_name` (text, null)  
    - `address` (text, null)  
    - `website` (text, null)  
    - `country` (text, null)  
    - `role` (enum {'tradesperson','team_member','admin','super_admin'}, default  ='tradesperson')  
    - `created_at` (timestamp, default `now()`)  
    - `last_login` (timestamp, null)  
- **Task 3.1.2** Define **Teams** table.  
  - Sub-Task *******: Columns:  
    - `id` (UUID, primary key)  
    - `name` (text, not null)  
    - `owner_id` (UUID, references `users.id`, not null)  
    - `allow_invites` (boolean, default =false)  
    - `require_job_approval` (boolean, default =false)  
    - `auto_assign_jobs` (boolean, default =false)  
    - `default_job_visibility` (enum {'owner_only','team_only','public'}, default ='team_only')  
    - `created_at` (timestamp, default `now()`)  
- **Task 3.1.3** Define **TeamMembers** join table (many-to-many).  
  - Sub-Task *******: Columns:  
    - `user_id` (UUID, references `users.id`)  
    - `team_id` (UUID, references `teams.id`)  
    - `role` (enum {'member','manager','owner'}, default ='member')  
    - Composite primary key (`user_id`,`team_id`)  
- **Task 3.1.4** Define **Clients** table.  
  - Sub-Task *******: Columns:  
    - `id` (UUID, primary key)  
    - `name` (text, not null)  
    - `address` (text, null)  
    - `email` (text, null)  
    - `phone` (text, null)  
    - `created_at` (timestamp, default `now()`)  
    - `created_by` (UUID, references `users.id`, not null)  
    - `rating` (integer, 0–5, default =0)  
    - `notes` (text, null)  
- **Task 3.1.5** Define **Jobs** table.  
  - Sub-Task *******: Columns:  
    - `id` (UUID, primary key)  
    - `title` (text, not null)  
    - `client_id` (UUID, references `clients.id`, not null)  
    - `created_by` (UUID, references `users.id`, not null)  
    - `team_id` (UUID, references `teams.id`, null)  
    - `description` (text, null)  
    - `scheduled_at` (timestamp, null)  
    - `status` (enum {'new','quoted','in_progress','on_hold','completed','archived'}, default ='new')  
    - `created_at` (timestamp, default `now()`)  
    - `updated_at` (timestamp, default `now()`)  
- **Task 3.1.6** Define **JobNotes** table (WhatsApp-style chat).  
  - Sub-Task *******: Columns:  
    - `id` (UUID, PK)  
    - `job_id` (UUID, references `jobs.id`, not null)  
    - `author_id` (UUID, references `users.id`, not null)  
    - `message` (text, not null)  
    - `created_at` (timestamp, default `now()`)  
    - `message_type` (enum {'text','system','AI'}, default ='text')  
- **Task 3.1.7** Define **ClientNotes** table.  
  - Sub-Task *******: Columns:  
    - `id` (UUID, PK)  
    - `client_id` (UUID, references `clients.id`, not null)  
    - `author_id` (UUID, references `users.id`, not null)  
    - `message` (text, not null)  
    - `created_at` (timestamp, default `now()`)  
- **Task 3.1.8** Define **Quotes**, **Invoices**, **Contracts** tables.  
  - Sub-Task 3.1.8.1: **Quotes**:  
    - `id` (UUID, PK)  
    - `job_id` (UUID, references `jobs.id`, not null)  
    - `amount` (decimal, not null)  
    - `details` (text, null)  
    - `created_at` (timestamp, default `now()`)  
    - `created_by` (UUID, references `users.id`, not null)  
    - `status` (enum {'draft','sent','accepted','rejected'}, default ='draft')  
  - Sub-Task 3.1.8.2: **Invoices**:  
    - `id` (UUID, PK)  
    - `quote_id` (UUID, references `quotes.id`, null)  
    - `job_id` (UUID, references `jobs.id`, not null)  
    - `amount` (decimal, not null)  
    - `tax` (decimal, default =0)  
    - `details` (text, null)  
    - `due_date` (date, null)  
    - `created_at` (timestamp, default `now()`)  
    - `created_by` (UUID, references `users.id`, not null)  
    - `status` (enum {'draft','sent','paid','overdue'}, default ='draft')  
  - Sub-Task 3.1.8.3: **Contracts**:  
    - `id` (UUID, PK)  
    - `job_id` (UUID, references `jobs.id`, not null)  
    - `terms` (text, not null)  
    - `created_at` (timestamp, default `now()`)  
    - `created_by` (UUID, references `users.id`, not null)  
    - `signed_at` (timestamp, null)  
- **Task 3.1.9** Define **AuditLogs** table (for Admin Portal).  
  - Sub-Task 3.1.9.1: Columns:  
    - `id` (UUID, PK)  
    - `timestamp` (timestamp, default `now()`)  
    - `actor_id` (UUID, references `users.id`)  
    - `action` (text, not null)  
    - `target_type` (text, not null, e.g. “job”, “user”, “team”)  
    - `target_id` (UUID, null)  
    - `details` (jsonb, null)  
- **Task 3.1.10** Define **SystemSettings** table (global flags).  
  - Sub-Task 3.1.10.1: Columns:  
    - `key` (text, PK)  
    - `value` (jsonb, not null)  
    - `updated_at` (timestamp, default `now()`)  

### 3.2 Supabase Policies & Security
- **Task 3.2.1** Enable Supabase Auth and hook Postgres to Auth.  
- **Task 3.2.2** Set **Row-Level Policies** (RLS) for each table:  
  - Sub-Task *******: **Users**:  
    - Users can select their own row (`id = auth.uid()`).  
    - Only `super_admin` can view/edit other users.  
  - Sub-Task *******: **Teams**:  
    - Allow `owner` or `team_member` to select where `team_id` is in their membership.  
    - Disallow reads from non-members.  
    - Only `super_admin` can view all teams.  
  - Sub-Task *******: **TeamMembers**:  
    - Only allow each user to see rows where `user_id = auth.uid()` or if `auth.uid()` is owner/manager.  
  - Sub-Task *******: **Clients**:  
    - Only allow clients where `created_by = auth.uid()` or if they belong to the same team as `created_by`.  
    - `super_admin` can see all.  
  - Sub-Task *******: **Jobs**:  
    - Only allow jobs where (`created_by = auth.uid()`) OR (`team_id` is in teams of `auth.uid()`) OR (`super_admin`).  
    - Insert: any authenticated user may insert; the `created_by` field must be `auth.uid()`.  
    - Update: only if user is `created_by` or team owner/manager or `super_admin`.  
    - Delete: same as update.  
  - Sub-Task *******: **JobNotes/ClientNotes**:  
    - Only allow insert if `author_id = auth.uid()` AND that user can view the underlying job or client.  
    - Select: only if underlying job/client is visible.  
    - Delete/Update: only if `author_id = auth.uid()`.  
  - Sub-Task 3.2.2.7: **Quotes/Invoices/Contracts**:  
    - Only allow access if `job_id` is visible (per above).  
    - Only allow creation if user is `created_by = auth.uid()` or team owner/manager.  
    - Only allow update to status if same.  
  - Sub-Task *******: **AuditLogs**:  
    - Only `super_admin` can read.  
    - Insert via RPC from trusted service (e.g. an API).  
  - Sub-Task *******: **SystemSettings**:  
    - Only `super_admin` can read/write.  
- **Task 3.2.3** Create **Database Migrations** (SQL scripts).  
  - Sub-Task *******: Write each table DDL in a numbered migration file (e.g. `001_create_users.sql`, …).  
  - Sub-Task *******: Write RLS policies in subsequent migration files (e.g. `002_rls_users.sql`, …).  
  - Sub-Task *******: Document index requirements:  
    - Index on `jobs(client_id)`  
    - Index on `jobs(team_id)`  
    - Index on `jobnotes(job_id)`  
    - Index on `clients(created_by)`  
- **Task 3.2.4** Set up **Supabase** project & apply migrations.  
  - Sub-Task *******: Create new Supabase project “DeskBelt-Prod” with Postgres 14.  
  - Sub-Task *******: Run all migrations in order.  
  - Sub-Task *******: Seed dummy data (1 user, 1 team, 1 client, 1 job, 1 quote) for initial QA.  

**– End of Sub-Phase 3.1 & 3.2 Tests –**  
1. **ERD Validation**: A DBA or architect cross-checks the schema against the ERD.  
2. **RLS Policy Tests** (via Supabase SQL Editor):  
   - As a normal user (simulate by setting `auth.uid()`) attempt to read another user’s data → FAIL.  
   - Confirm a team member can see jobs of their team.  
   - Confirm a user cannot see or modify jobs they don’t own or belong to.  
3. **Migration Tests**:  
   - Wipe a fresh Supabase DB, re-apply migrations. Confirm no errors.  
   - Confirm seeded data appears.

---

## 3.3 Backend API Routes & Business Logic

> **Note**: All API routes are Next.js API routes under `/pages/api/...`. They use Supabase client/server SDK. Every route must validate the user’s session, check RLS policies, and sanitize inputs.

- **Task 3.3.1** Users & Auth API  
  - **Sub-Task ********* `/api/users/me` (GET)  
    - Description: Returns the currently authenticated user’s profile (id, email, full_name, phone, company_name, address, website, country, role, created_at, last_login).  
    - Access: Authenticated; internal RLS ensures only your own row.  
  - **Sub-Task ********* `/api/users/update-profile` (PUT)  
    - Body: `{ full_name?, phone?, company_name?, address?, website?, country? }`  
    - Description: Update fields in the `users` table for `auth.uid()`. Reject if payload is empty.  
    - Access: Authenticated.  
  - **Sub-Task ********* `/api/auth/logout` (POST)  
    - Description: Signs out the user from Supabase session (server‐side).  
    - Access: Authenticated.  
- **Task 3.3.2** Clients API  
  - **Sub-Task ********* `/api/clients` (GET)  
    - Query params (optional): `search`, `limit`, `offset`  
    - Returns: Paginated list of clients visible to the user (per RLS). Each item: `{ id, name, address, email, phone, rating, created_at, created_by }`  
    - Access: Authenticated.  
  - **Sub-Task ********* `/api/clients` (POST)  
    - Body: `{ name (required), address?, email?, phone?, rating? (default=0), notes? }`  
    - Creates a new client row with `created_by=auth.uid()`. Returns the new client ID.  
    - Access: Authenticated.  
  - **Sub-Task ********* `/api/clients/[clientId]` (GET)  
    - Path param: `clientId` (UUID)  
    - Returns full client details, including all `ClientNotes` messages (sorted ascending by `created_at`).  
    - Access: Authenticated (RLS: only if user can view).  
  - **Sub-Task ********* `/api/clients/[clientId]` (PUT)  
    - Body: `{ name?, address?, email?, phone?, rating?, notes? }`  
    - Updates client fields.  
    - Access: Authenticated (RLS: only if user can update).  
  - **Sub-Task ********* `/api/clients/[clientId]` (DELETE)  
    - Deletes the client (soft‐delete by changing status to “archived”? Or physically delete? Implementation: physically delete).  
    - Access: Authenticated (RLS: only if user can delete).  
  - **Sub-Task ********* `/api/clients/[clientId]/notes` (POST)  
    - Body: `{ message (text) }`  
    - Inserts a row into `ClientNotes` with `client_id=clientId, author_id=auth.uid(), message_type='text', created_at=now()`.  
    - Returns the new note.  
    - Access: Authenticated (RLS: only if user can view client).  
- **Task 3.3.3** Jobs API  
  - **Sub-Task ********* `/api/jobs` (GET)  
    - Query params: `search`, `status` (array), `limit`, `offset`.  
    - Returns a paginated list of jobs visible to the user: each item:  
      ```json
      {
        "id": "string",
        "title": "string",
        "description_snippet": "string",     // first 100 chars
        "client": { "id": "string", "name": "string" },
        "status": "new|quoted|in_progress|on_hold|completed|archived",
        "scheduled_at": "ISO timestamp or null",
        "created_at": "ISO timestamp",
        "team_id": "UUID or null",
        "created_by": "UUID"
      }
      ```  
    - Access: Authenticated (RLS).  
  - **Sub-Task ********* `/api/jobs` (POST)  
    - Body: `{ title (required), client_id (required), description?, scheduled_at?, team_id? }`  
    - Creates new job with `status='new'`, `created_by=auth.uid()`, `created_at=now()`.  
    - Returns new job ID.  
    - Access: Authenticated (RLS).  
  - **Sub-Task ********* `/api/jobs/[jobId]` (GET)  
    - Returns full job details:  
      ```json
      {
        "id": "string",
        "title": "string",
        "description": "string",
        "scheduled_at": "ISO timestamp or null",
        "status": "new|quoted|in_progress|on_hold|completed|archived",
        "client": { "id": "string", "name": "string", "address": "string", "phone": "string", "email": "string", "rating": 0-5 },
        "team_id": "UUID or null",
        "created_by": "UUID",
        "created_at": "ISO timestamp",
        "updated_at": "ISO timestamp",
        "jobNotes": [ { "id": "UUID", "author": { "id": "UUID", "full_name": "string", "role": "string" }, "message": "string", "message_type": "text|system|AI", "created_at": "ISO timestamp" } ],
        "quote_summary": { "amount": number, "status": "draft|sent|accepted|rejected" }
      }
      ```  
    - Access: Authenticated (RLS).  
  - **Sub-Task ********* `/api/jobs/[jobId]` (PUT)  
    - Body: any subset of `{ title?, description?, scheduled_at?, status?, team_id? }`.  
    - Updates job if permitted. If `status` changes, append a `JobNotes` record with `message_type='system'` and message `"Status changed from OLD → NEW"`.  
    - Access: Authenticated (RLS: only if user can update).  
  - **Sub-Task ********* `/api/jobs/[jobId]` (DELETE)  
    - Soft‐delete by setting `status='archived'`.  
    - Access: Authenticated (RLS).  
  - **Sub-Task ********* `/api/jobs/[jobId]/notes` (POST)  
    - Body: `{ message (text) }`  
    - Inserts into `JobNotes` with `message_type='text'`.  
    - Access: Authenticated (RLS).  
- **Task 3.3.4** Quotes, Invoices & Contracts API  
  - **Sub-Task ********* `/api/quotes` (POST)  
    - Body: `{ job_id (required), amount (required), details? }`  
    - Creates a `Quotes` row with `status='draft'`, `created_by=auth.uid()`, `created_at=now()`.  
    - Access: Authenticated (RLS: only if user can see job).  
  - **Sub-Task ********* `/api/quotes/[quoteId]` (PUT)  
    - Body: `{ amount?, details?, status? }`  
    - Allows editing only if `status='draft'` or `status='sent'` and user is `created_by` or team owner/manager.  
    - If `status` changes to `'sent'` → insert a `JobNotes` record `"Quote sent: £AMOUNT"`.  
  - **Sub-Task 3.3.4.3** `/api/quotes/[quoteId]` (GET)  
    - Returns full quote data plus basic job/client references.  
  - **Sub-Task 3.3.4.4** `/api/invoices` (POST)  
    - Body: `{ job_id (required), quote_id?, amount (required), tax?, details?, due_date? }`  
    - Inserts `Invoices` row; if `quote_id` provided, ensure quote exists & belongs to same job.  
    - Insert `JobNotes`: `"Invoice created: £AMOUNT, Due: DATE"`.  
  - **Sub-Task 3.3.4.5** `/api/invoices/[invoiceId]` (PUT)  
    - Body: `{ amount?, tax?, details?, due_date?, status? }`  
    - Only `created_by` or team owner/manager. If `status='paid'`, add `JobNotes`: `"Invoice marked paid"`.  
  - **Sub-Task 3.3.4.6** `/api/contracts` (POST)  
    - Body: `{ job_id (required), terms (required) }`  
    - Inserts `Contracts` row; sets `signed_at=null`.  
    - Add `JobNotes`: `"Contract created"`.  
  - **Sub-Task 3.3.4.7** `/api/contracts/[contractId]/sign` (POST)  
    - Marks `signed_at=now()`. Only `created_by` or team owner/manager.  
    - Add `JobNotes`: `"Contract signed"`.  
  - **Sub-Task 3.3.4.8** `/api/quotes/[quoteId]/generate-pdf` (GET)  
    - Generates PDF from quote data (serverless function or library). Returns a URL (signed).  
  - **Sub-Task 3.3.4.9** `/api/invoices/[invoiceId]/generate-pdf` (GET)  
    - Similar to quote.  
  - **Sub-Task *******0** `/api/contracts/[contractId]/generate-pdf` (GET)  
    - Similar.  
- **Task 3.3.5** Team & Notification API  
  - **Sub-Task 3.3.5.1** `/api/teams` (GET)  
    - Returns all teams that the user belongs to (RLS) with minimal fields: `{ id, name, role_in_team }`.  
  - **Sub-Task 3.3.5.2** `/api/teams` (POST)  
    - Body: `{ name }`  
    - Creates new team; sets `owner_id=auth.uid()`. Inserts a `TeamMembers` row with `role='owner'`.  
  - **Sub-Task 3.3.5.3** `/api/teams/[teamId]` (GET)  
    - Returns full team info: `{ id, name, owner_id, allow_invites, require_job_approval, auto_assign_jobs, default_job_visibility, created_at }` plus a list of members: `[ { user_id, full_name, role } ]`.  
  - **Sub-Task 3.3.5.4** `/api/teams/[teamId]` (PUT)  
    - Body: any subset of `{ name?, allow_invites?, require_job_approval?, auto_assign_jobs?, default_job_visibility? }`. Only team owner can update.  
  - **Sub-Task 3.3.5.5** `/api/teams/[teamId]/invite` (POST)  
    - Body: `{ email (required), role (optional, default='member') }`.  
    - Sends an invite email with a magic link. Creates a “PendingInvite” record in a separate `TeamInvites` table (columns: `id, team_id, email, role, token, expires_at, created_at`).  
    - Access: Authenticated, only if `role_in_team='owner'` or `manager` and `allow_invites=true`.  
  - **Sub-Task ********* `/api/teams/[teamId]/members` (DELETE)  
    - Query param or body: `{ user_id }`. Removes a member from the team. Only owner or manager (if team allows) can remove. Cannot remove yourself if owner (first transfer ownership).  
  - **Sub-Task ********* `/api/notifications` (GET)  
    - Returns a list of notifications for `auth.uid()`, sorted descending by `created_at`. Each: `{ id, type, message, link, is_read, created_at }`.  
  - **Sub-Task ********* `/api/notifications/mark-read` (POST)  
    - Body: `{ notification_id }`. Marks `is_read=true`.  
  - **Sub-Task ********* Real-time triggers:  
    - Fire a notification when:  
      - A team invite is accepted.  
      - A job is assigned to a team member (in `/api/jobs/[jobId]` when `team_id` changes).  
      - A quote is accepted.  
      - An invoice is paid.  

**– End of Sub-Phase 3.3 Tests –**  
1. Call each new API route with valid/invalid inputs; confirm correct HTTP status, RLS enforcement.  
2. Insert mock data, try RLS bypass (e.g. user A tries to fetch user B’s job) → 403.  
3. Notification triggers: Accept a quote via API → a new notification appears in `/api/notifications`.  

---

## 4. Frontend — Jobs Dashboard & Components

> These screens live under `/pages/dashboard/jobs`. All components must be modular (e.g. one file per component), styled with TailwindCSS + shadcn primitives, and fully responsive (mobile-first).

### 4.1 Jobs List Page (`/dashboard/jobs`)

- **Sub-Phase 4.1.1** Layout & Skeleton  
  - **Task 4.1.1.1** Page container with “Dashboard” heading and tab bar (Jobs | Clients | Stats | Archived).  
  - **Task 4.1.1.2** Top action bar:  
    - Large button “New Job” (blue background)  
    - Link/button “New Client” (green background)  
    - Link/button “Ask Dex” (yellow/orange background)  
  - **Task 4.1.1.3** Search bar on the right of actions, placeholder “Search jobs…”.  
  - **Task 4.1.1.4** If no jobs: show a friendly illustration + “No jobs found. Create one now.” with a “New Job” button.  
- **Sub-Phase 4.1.2** Job Card Component (`components/JobCard.tsx`)  
  - **Props**:  
    ```ts
    interface JobCardProps {
      job: {
        id: string;
        title: string;
        client: { id: string; name: string; };
        status: 'new' | 'quoted' | 'in_progress' | 'on_hold' | 'completed' | 'archived';
        scheduled_at: string | null; // ISO
        created_at: string; // ISO
        updated_at: string; // ISO
        hasNotes: boolean;
        assigned_to?: { id: string; full_name: string; } | null;
      };
    }
    ```  
  - **Rendering**:  
    - Card container: `bg-white rounded-lg shadow-sm p-4 flex flex-col justify-between`.  
    - Top row (flex):  
      - Left: Job title (text-xl font-semibold)  
      - Right: Status badge:  
        - `new` → blue circle + “New”  
        - `quoted` → indigo circle + “Quoted”  
        - `in_progress` → green circle + “In Progress”  
        - `on_hold` → orange circle + “On Hold”  
        - `completed` → teal circle + “Completed”  
        - `archived` → gray circle + “Archived”  
      - Badge styling: small pill shape, white text, background lightly tinted.  
    - Middle row (flex, mt-2):  
      - Left: Client icon (user icon) + client name (clickable → opens client drawer).  
      - Right: Calendar icon + formatted `scheduled_at` (e.g. “01 Jun 2025”). If `null`, show “Not scheduled”.  
    - Bottom row (flex, mt-2 items-center):  
      - **Notes icon**: if `hasNotes` is true, show a chat bubble icon (filled) with number of unread notes (if any). Clicking opens job details drawer.  
      - **Call icon**: phone icon. Clicking opens `tel:` link to the client’s phone (provided API returns client.phone). If phone missing, disable.  
      - **Assigned To**: user icon + team member name (if assigned). If not assigned, show “Unassigned”.  
      - Entire card has a right-arrow chevron on far right to indicate clicking anywhere opens details.  
  - **Interactivity**:  
    - Clicking the card (except icons) opens the Job Details Drawer (see section 4.2).  
    - Clicking client name icon opens Client Details Drawer.  
    - Clicking notes icon scrolls/opens to notes section in the drawer if open, or opens drawer.  
    - On hover (desktop), card slightly elevates (`shadow-md`) and border-blue-300. On mobile, no hover effect (touch‐friendly).  
- **Sub-Phase 4.1.3** Integrate with Data Fetch  
  - **Task 4.1.3.1** Create a hook `useJobs({ search, status, limit, offset })` that calls `/api/jobs`. Returns `{ data: Job[], isLoading, error, refetch }`.  
  - **Task 4.1.3.2** On page load, call `useJobs({ limit: 20, offset: 0 })`. Show skeleton loaders (`animate-pulse`) for 5 cards while loading.  
  - **Task 4.1.3.3** Implement infinite scroll or “Load more” button at bottom. For mobile, “Load more” is simpler.  
  - **Task 4.1.3.4** Implement search: typing in search bar calls `debounce` (500 ms) then refetch with `search` param.  
  - **Task 4.1.3.5** Show error placeholder if API fails: “Failed to load jobs. Tap to retry.” with a retry button.  
- **Sub-Phase 4.1.4** Job Status Filter Dropdown  
  - **Task 4.1.4.1** Place a filter icon next to search; clicking opens dropdown with checkboxes for each status.  
  - **Task 4.1.4.2** “Select All” / “Clear All” buttons.  
  - **Task 4.1.4.3** When statuses change, refetch jobs with `status=[…]` query.  

**– End of Sub-Phase 4.1 Tests –**  
1. Verify jobs render as per design.  
2. Test search & filter combos.  
3. Simulate no results / API failure.  

---

### 4.2 Job Details Drawer (`components/JobDrawer.tsx`)

> This drawer slides in from the right when a job card is clicked. Occupies full height, width ~100% on mobile, ~400px on desktop.

- **Sub-Phase 4.2.1** Drawer Layout & Structure  
  - **Task 4.2.1.1** Top Bar:  
    - Job title (text‐large) + Close (X) icon on right.  
    - Under title: `#JOBNUMBER • CLIENT_NAME` (small subdued text).  
  - **Task ********* Job Meta section (grid with two columns on desktop, one on mobile):  
    - **Schedule**: Label + value (formatted date or “Not scheduled”). Clicking date opens date‐picker to reschedule.  
    - **Status**: Current status badge (same colors as card). Clicking badge opens a small dropdown to change status. Dropdown options: New, Quoted, In Progress, On Hold, Completed, Archived.  
    - **Client**: Icon + client name (clickable → opens Client Drawer).  
  - **Task ********* Description block:  
    - Label “Description” + text area showing `description`. If user has edit rights, show an “Edit” pencil icon to switch into edit mode: text area to modify. On save, call `/api/jobs/[jobId]` with updated `description`. Provide cancel.  
  - **Task ********* “Create Documents” section (with heading). Show three buttons (large full‐width on mobile / breath between on desktop):  
    - **Create Quote** (document icon)  
    - **Create Invoice** (£ icon)  
    - **Create Contract** (file‐contract icon)  
    - Buttons use blue/indigo outline. On click, open respective modal or nested drawer (see Section 6).  
  - **Task ********* “Job Actions” section:  
    - Buttons (icon + text) stacked vertically:  
      1. **Mark as Completed** (green check icon). Disabled if already `status='completed'`. On click: update status via `/api/jobs/[jobId]`.  
      2. **Assign to Team Member** (people icon). Clicking opens a sub‐drawer listing available team members (see Section 5).  
      3. **Archive Job** (archive/trash icon). Clicking prompts: “Are you sure you want to archive this job?” (Yes/No). If yes, call `/api/jobs/[jobId]` with `status='archived'`.  
    - Each button uses a border and hover effect to indicate clickability.  
  - **Task 4.2.1.6** **Delete Job** button at bottom (red text + trash icon). Clicking shows confirmation: “This cannot be undone—delete permanently?” Only appears if job is not already archived (if archived, “Delete Permanently”). On confirm, call DELETE `/api/jobs/[jobId]`.  
  - **Task 4.2.1.7** **Job Activity Log** (chronological list of system notes):  
    - Render `JobNotes` of type `system` or `AI` or `text` sorted ascending.  
    - Each entry: a rounded bubble (`bg-blue-800 text-white` for system/AI; `bg-gray-100 text-gray-800` for user text), with timestamp (`DD MMM YYYY, HH:mm`).  
    - Below log: input field (“Message…”) + send icon (paper plane). Typing and sending calls POST `/api/jobs/[jobId]/notes`.  
  - **Task 4.2.1.8** **Call Client / Message Buttons** at bottom fixed:  
    - Phone icon button: calls `tel: client.phone`. Disabled if no phone.  
    - Chat icon button: opens a WhatsApp web link `https://wa.me/CLIENT_PHONE` if phone exists (country code included).  
    - Email icon button: `mailto:client.email`. Disabled if no email.  

- **Sub‐Phase 4.2.2** Data Fetch & State Management  
  - **Task 4.2.2.1** Create a hook `useJobDetails(jobId)` that fetches `/api/jobs/[jobId]`. Returns `job`, `notes`, `isLoading`, `error`, `refetch`.  
  - **Task ********* In `JobDrawer`, call `useJobDetails(jobId)`. If loading: show spinner within drawer. If error: show error message + retry.  
  - **Task ********* Handle status change: clicking badge → dropdown → selects new status → call PUT `/api/jobs/[jobId]` with `{ status: newStatus }`. On success: refetch job details.  
  - **Task ********* Handle edit description: open text area, on save call PUT. On cancel, discard.  
  - **Task ********* Handle note posting: on “send” click, call POST `/api/jobs/[jobId]/notes`. Clear input, refetch notes or append locally.  
  - **Task ********* Handle reschedule: click date → open a date‐picker (shadcn component). On select, call PUT with `scheduled_at`.  
  - **Task ********* For “Assign to Team Member”: clicking opens a nested drawer or modal (`TeamMemberAssign`). It will render a list of team members (via `/api/teams/[team_id]/members`). Each with radio button; on select and confirm, call PUT `/api/jobs/[jobId]` with `team_id=newMemberTeamId`.  
- **Sub-Phase 4.2.3** Finishing Touches & UX  
  - **Task ********* Ensure the drawer animates smoothly (enter/exit) and catches background scroll (no page behind is scrollable).  
  - **Task ********* Accessibility: all buttons have `aria-label`, keyboard focus trap in drawer.  
  - **Task ********* Loading skeleton for job details: grey boxes for each field.  
  - **Task 4.2.3.4** Edge cases: job has no client (rare)—`Client: N/A` displayed; hide call/chat/email buttons.  
  - **Task 4.2.3.5** If job is archived, hide “Mark as Completed” and “Assign to Team Member”. Change “Archive Job” label to “Unarchive Job” (sets status back to ‘new’) for admin only.  

**– End of Sub-Phase 4.2 Tests –**  
1. Open a job from the list; confirm all fields match API.  
2. Change status, add notes, reschedule → see DB update.  
3. Assign a team member → verify job.team_id updated.  
4. Delete/archive flows behave correctly.  

---

## 5. Frontend — Clients Dashboard & Components

> Lives under `/pages/dashboard/clients`.

### 5.1 Clients List Page (`/dashboard/clients`)

- **Sub-Phase 5.1.1** Layout & Skeleton  
  - Similar to Jobs page: Tab bar, top action bar with “New Job” / “New Client” / “Ask Dex”, search bar.  
  - If no clients exist, show illustration + “No clients yet. Add one now.”  
- **Sub-Phase 5.1.2** Client Card Component (`components/ClientCard.tsx`)  
  - **Props**:  
    ```ts
    interface ClientCardProps {
      client: {
        id: string;
        name: string;
        address?: string | null;
        email?: string | null;
        phone?: string | null;
        rating: number; // 0 to 5
        totalJobs: number;
        activeJobs: number;
        created_at: string;
      };
    }
    ```  
  - **Rendering**:  
    - Card container: `bg-white rounded-lg shadow-sm p-4 flex flex-col justify-between`.  
    - Top row:  
      - Left: Client name (text-xl font-semibold).  
      - Right: Star rating (0–5, filled yellow stars for rating, outline for remainder). Also show numerical “3/5” next to stars if rating > 0.  
    - Middle section (mt-2): icon list:  
      - Calendar icon + “Client since DD/MM/YYYY”.  
      - Location icon + address if exists (otherwise skip).  
      - Envelope icon + email if exists (otherwise skip).  
      - Phone icon + phone if exists (otherwise skip).  
    - Bottom row (mt-2 flex items-center):  
      - Jobs summary: “X Total Job • Y Active” (document icon + click → “View Client Jobs”?). Clicking “X Total Job” navigates to `/dashboard/clients/[id]/jobs` or filters jobs dashboard.  
      - Action icons (faded until hover):  
        - Call (phone icon, `tel:` link). Disabled if no phone.  
        - Message (chat icon, opens “Send Message” UI inside drawer).  
        - Edit (pencil icon, opens “Edit Client” drawer/form).  
        - Notes (notebook icon, opens client’s notes chat).  
        - Delete (trash icon, bottom‐right). Red‐colored on hover.  
      - Below that: Buttons:  
        - **Create Job** (blue button). Calls `onCreateJob(client.id)`.  
        - **Request Review** (outline button). On click: send email to client requesting Google review. (Triggers an API `/api/clients/[id]/request-review` which uses an email service).  
- **Sub-Phase 5.1.3** Integrate Data Fetch  
  - **Task 5.1.3.1** Hook `useClients({ search, limit, offset })` returns client list.  
  - **Task 5.1.3.2** Pagination or “Load More” similar to jobs.  
  - **Task 5.1.3.3** Handle error/no‐data states.  

**– End of Sub-Phase 5.1 Tests –**  
1. Clients appear with correct info.  
2. Clicking icons behave (opens appropriate drawer).  

---

### 5.2 Client Details Drawer (`components/ClientDrawer.tsx`)

> Slides in from the right. Similar structure to Job Drawer.

- **Sub-Phase 5.2.1** Drawer Layout  
  - **Task ********* Top Bar:  
    - Client name (text‐large) + Close icon.  
    - Under name: `Client since DD/MM/YYYY`.  
  - **Task ********* Client Info section (grid):  
    - **Address** (icon + text; if none, show “No address provided”).  
    - **Email** (icon + mailto link; if none, show “N/A”).  
    - **Phone** (icon + `tel:` link; if none, “N/A”).  
  - **Task ********* Actions section:  
    - **Create Job** button (blue).  
    - **Request Review** button (clipboard icon).  
    - **Edit Client** button (pencil icon). Opens an edit form in a nested drawer or inline modal (see 5.3).  
    - **View Client Jobs** button (job icon). Switches to Jobs Dashboard with filter `client_id=this`.  
    - **Send Message** button (chat icon). Opens the “Client Notes” chat textarea.  
    - **Delete Client** (red trash icon at bottom). Shows confirm before calling DELETE.  
  - **Task ********* Client Activity Log (ClientNotes):  
    - Render chat bubbles similar to JobNotes.  
    - Input field (“Message client…”) + send icon. On send, POST `/api/clients/[clientId]/notes`.  
- **Sub-Phase 5.2.2** Data Fetch & State  
  - **Task ********* Hook `useClientDetails(clientId)` fetches `/api/clients/[id]`.  
  - **Task ********* Show skeleton while loading. Show error if failure.  
  - **Task ********* Edit client: open inline fields for name, address, email, phone, rating (stars dropdown). On save, PUT `/api/clients/[id]`.  
  - **Task ********* Posting notes: similar to jobs.  
- **Sub-Phase 5.2.3** UX & Edge Cases  
  - **Task ********* If client is archived (deleted), hide all actions except “Delete Permanently” (if implemented) and show banner: “This client is archived.”  
  - **Task ********* If user does not have permission to edit (RLS fails), disable “Edit” and “Delete” buttons. Show tooltip “Permission denied.”  

**– End of Sub-Phase 5.2 Tests –**  
1. Open client → data matches API.  
2. Edit details → reflect in list.  
3. Send/read notes.  

---

### 5.3 New/Edit Client Form Flow

> This flow is AI-assisted (Ask Dex can parse input) but also manual multi‐step.

- **Sub-Phase 5.3.1** “Add New Client” Button  
  - **Task ********* On Clients page, clicking “New Client” opens full‐screen overlay (on mobile) or centred modal (on desktop). Title: “New Client – Step-by-Step Setup”.  
  - **Task ********* Intro text (in chat bubble style): “Let’s add your new client. I’ll walk you through each detail step by step. Tip: You can use the AI assistant below to quickly parse client info from text!”  
  - **Task ********* Show AI assistant input (textarea) with placeholder “Paste client info here for AI to parse…”. A “Parse” button sends text to `/api/ai/parse-client` (stub; later AI). The response populates fields.  
- **Sub-Phase 5.3.2** Stepwise Form  
  - **Task ********* Step 1: **Name**  
    - Chat bubble: “Great! What’s the client’s name?”  
    - Input: single‐line text for “Name” (required).  
    - Button “Next →” disabled until nonempty.  
  - **Task ********* Step 2: **Address**  
    - Chat: “Now for the address.”  
    - Input: multi‐line “Address” (required).  
    - “Next →” until nonempty.  
  - **Task ********* Step 3: **Phone**  
    - Chat: “Phone number (optional).”  
    - Input: tel type (pattern to validate UK phone).  
    - “Next →” (skip allowed).  
  - **Task ********* Step 4: **Email**  
    - Chat: “Email address (optional).”  
    - Input: email type.  
    - “Next →” (skip).  
  - **Task ********* Step 5: **Rating**  
    - Chat: “Rate this client (0–5 stars).”  
    - Input: star selector.  
    - “Next →”.  
  - **Task ********* Step 6: **Notes**  
    - Chat: “Any notes about this client?”  
    - Input: text area.  
    - “Finish” button (creates client).  
- **Sub-Phase 5.3.3** Form Submission & API Integration  
  - **Task ********* On “Finish”, collect fields into `{ name, address, phone?, email?, rating?, notes? }`. Call POST `/api/clients`. Show loading spinner.  
  - **Task ********* On success: close modal/drawer, show toast “Client created!” and refetch clients list.  
  - **Task ********* On error: show error message “Failed to create client. Try again.”  
- **Sub-Phase 5.3.4** Edit Client (in Drawer)  
  - **Task ********* “Edit Client” in Client Drawer opens similar multi-step UI but with pre‐filled values. Change “Finish” to “Save Changes”.  
  - **Task ********* On save, call PUT `/api/clients/[id]`.  
  - **Task ********* On success: close, show toast “Client updated!”, refetch.  

**– End of Sub-Phase 5.3 Tests –**  
1. Walk through adding a client manually (without AI) → appears in list.  
2. Walk through AI‐parse (later) → fields pre‐fill correctly.  
3. Edit client → changes persist.  

---

## 6. Frontend — Document Creation (Quotes, Invoices, Contracts)

> These flows are accessible from Job Drawer “Create Quote / Invoice / Contract” and optionally from a direct “+” menu.

### 6.1 Create Quote Flow

- **Sub-Phase 6.1.1** Trigger & Drawer UI  
  - **Task 6.1.1.1** Clicking **Create Quote** for a job opens a nested drawer over the JobDrawer. Title: “Create Quote for ‘JOB_TITLE’ (Client: CLIENT_NAME)”.  
  - **Task 6.1.1.2** Intro bubble: “Let’s create a quote. Describe how you’re pricing this job. For example:  
    - ‘£150 labour + £30 materials’  
    - ‘£200 per day for 2 days’  
    - ‘£50/hr for 6 hours’”  
  - **Task 6.1.1.3** Under bubble: input textarea (styled like chat bubble) for natural‐language pricing. Placeholder: “e.g. £200 labour + £150 materials”.  
  - **Task 6.1.1.4** Below input: AI suggestions area (empty by default; once AI returns suggestions, show clickable options “Painting Work”, “Electrical Work”, etc.).  
  - **Task 6.1.1.5** “Submit” button (blue) disabled until input nonempty.  
- **Sub-Phase 6.1.2** AI Integration (Ask Dex)  
  - **Task 6.1.2.1** On input either user types manually or clicks an AI suggestion.  
  - **Task 6.1.2.2** If user typed natural text, on “Submit” call `/api/ai/parse-quote` (body: `{ job_id, text: userInput }`). The AI returns `{ amount: number, breakdown: string[] }`.  
    - Example: `{ amount: 350, breakdown: ["£200 labour", "£150 materials"] }`.  
  - **Task 6.1.2.3** If AI fails to parse (returns error or `{ amount: null }`), show AI bubble: “I couldn’t find a price. Please include an amount. For example: …” and keep the drawer open.  
  - **Task 6.1.2.4** If AI returns valid quote, show a confirmation bubble: “I parsed a quote of £AMOUNT. Breakdown: … Are you happy to save this?” with two buttons: “Yes, save” / “No, edit”.  
- **Sub-Phase 6.1.3** Manual Override & Confirmation  
  - **Task 6.1.3.1** If user clicks “Edit”, return to the textarea with AI‐parsed text inserted (e.g. “£200 labour + £150 materials”).  
  - **Task 6.1.3.2** If user clicks “Yes, save”, call POST `/api/quotes` with `{ job_id, amount, details: breakdown.join(", ") }`.  
  - **Task 6.1.3.3** On success, show toast “Quote created!” and close nested drawer. Add system note via `/api/jobs/[jobId]/notes` automatically on backend. Refetch Job details.  
- **Sub-Phase 6.1.4** PDF Generation & Download  
  - **Task 6.1.4.1** After creating a quote, navigate to view Quote or offer a “Download PDF” button within the confirmation toast or JobDrawer.  
  - **Task 6.1.4.2** “Download PDF” calls `/api/quotes/[quoteId]/generate-pdf`. On response, open the PDF URL in a new tab or trigger download.  

**– End of Sub-Phase 6.1 Tests –**  
1. Enter a valid price → quote created.  
2. Enter invalid (missing currency) → AI prompts to re‐enter.  
3. Confirm “Yes, save” and check that quote appears in DB & JobNotes.  
4. Download PDF works.  

---

### 6.2 Create Invoice Flow

- **Sub-Phase 6.2.1** Trigger & Drawer UI  
  - **Task 6.2.1.1** Clicking **Create Invoice** in JobDrawer opens nested drawer. Title: “Create Invoice for ‘JOB_TITLE’”.  
  - **Task 6.2.1.2** Intro bubble: “Let’s generate an invoice. You can base it off an existing quote or enter a custom amount.”  
  - **Task 6.2.1.3** Show a dropdown “Select Quote” with existing quotes for this job (fetched from `/api/quotes?job_id=JOBID`). If none, show “No quotes available”.  
  - **Task 6.2.1.4** If user selects a quote, auto‐populate `amount=quote.amount`, `details=quote.details`, `due_date` = one week from today (editable). If user chooses “Custom”, show fields manually.  
  - **Task 6.2.1.5** Fields:  
    - **Amount** (currency input, required).  
    - **Tax** (number input, default=0).  
    - **Details** (textarea).  
    - **Due Date** (date picker).  
  - **Task 6.2.1.6** Submit button disabled until `amount` defined.  
- **Sub-Phase 6.2.2** Invoice Creation Logic  
  - **Task 6.2.2.1** On submit, call POST `/api/invoices` with `{ job_id, quote_id?, amount, tax, details, due_date }`.  
  - **Task 6.2.2.2** On success: show toast “Invoice created!”, close drawer, refetch job details (to show invoice summary).  
  - **Task 6.2.2.3** Add system note automatically: “Invoice created: £AMOUNT, Due: DATE.”  
- **Sub-Phase 6.2.3** Manage Status & Payment  
  - **Task 6.2.3.1** If user views Invoice from JobDrawer (list or link), show current `status` and `amount_due = amount + tax`.  
  - **Task 6.2.3.2** If invoice `status='sent'`: show button “Mark as Paid”. On click: call PUT `/api/invoices/[invoiceId]` with `{ status: 'paid' }`. Add system note “Invoice marked paid.”  
  - **Task 6.2.3.3** If `status='overdue'`: show red badge. (UI logic: compare due_date with today; if past and status != 'paid', show overdue)  

**– End of Sub-Phase 6.2 Tests –**  
1. Create invoice from quote; verify fields.  
2. Mark as paid; check DB and notes.  

---

### 6.3 Create Contract Flow

- **Sub-Phase 6.3.1** Trigger & Drawer UI  
  - **Task ********* Clicking **Create Contract** opens nested drawer. Title: “Create Contract for ‘JOB_TITLE’”.  
  - **Task ********* Intro bubble: “Please enter the contract terms. You can type free‐form or ask the AI to draft standard trade contract terms.”  
  - **Task ********* Input textarea for terms (multi‐line). AI integration button “Generate Terms” sends a prompt to `/api/ai/generate-contract` with job description & client info.  
  - **Task ********* Buttons:  
    - “Generate Terms” (AI)  
    - “Clear” (empty the textarea)  
    - “Submit” (disabled until `terms` length ≥ 50 characters)  
- **Sub-Phase 6.3.2** AI Integration & Confirmation  
  - **Task ********* On “Generate Terms”, call `/api/ai/generate-contract` with `{ job_id }`. Receive simple legal text. Populate textarea.  
  - **Task ********* Show AI bubble with “Drafted contract terms. Review/edit below.”  
- **Sub-Phase 6.3.3** Contract Submission  
  - **Task ********* On “Submit”, call POST `/api/contracts` with `{ job_id, terms }`.  
  - **Task ********* On success: show toast “Contract created!”, close drawer, refetch job.  
  - **Task ********* Add system note “Contract created.”  
- **Sub-Phase 6.3.4** Signing & Download  
  - **Task ********* If contract exists and `signed_at=null`, show a “Sign Contract” button (blue). Clicking calls POST `/api/contracts/[contractId]/sign`. Add note “Contract signed.”  
  - **Task 6.3.4.2** Show “Download PDF” always (calls `/api/contracts/[contractId]/generate-pdf`).  

**– End of Sub-Phase 6.3 Tests –**  
1. Generate AI terms, edit, submit.  
2. Download PDF, sign contract, confirm `signed_at` updated.  

---

## 7. Frontend — Stats & Archived Dashboards

### 7.1 Stats Dashboard (`/dashboard/stats`)

- **Sub-Phase 7.1.1** Layout & Skeleton  
  - **Task ********* Similar top bar: “Dashboard”, tabs, “New Job” / “New Client” / “Ask Dex”.  
  - **Task ********* Below: wide “Statistics” heading with icon bar.  
  - **Task ********* Four summary cards in grid (two columns on desktop, single column on mobile):  
    1. **Total Clients** (green user icon, number, “Active client relationships”).  
    2. **Total Jobs** (blue wrench icon, number, “All-time job count”).  
    3. **This Month** (purple calendar icon, number, “+X% vs last month”).  
    4. **Completion Rate** (teal check icon, percentage, “X of Y jobs completed”).  
  - **Task ********* Below summaries: two charts side-by-side on desktop (stacked on mobile):  
    1. **Jobs Over Time**: Line chart (month vs number) for last 12 months.  
    2. **Clients Growth**: Bar chart (month vs new clients).  
  - **Task ********* “Recent Activity” list: last 10 system‐generated notes (Job created, Quote sent, Invoice paid). Each item: icon + text + timestamp.  
- **Sub-Phase 7.1.2** Data Fetch & Chart Logic  
  - **Task ********* Hook `useDashboardStats()` calls `/api/stats/overview` and `/api/stats/charts`.  
    - `/api/stats/overview` returns `{ totalClients, totalJobs, thisMonthCount, lastMonthCount, completedJobs, totalJobsForRate }`.  
    - `/api/stats/charts` returns `{ jobsOverTime: [ { month: '2025-01', count: 5 }, … ], clientsOverTime: [ { month: '2025-01', count: 2 }, … ] }`.  
    - `/api/stats/recent-activity` returns array of last 10 events.  
  - **Task 7.1.2.2** Use **React Chart.js** (or Recharts) to render charts. Each chart is its own component in `/components/StatsCharts`.  
  - **Task 7.1.2.3** Show skeleton placeholders while loading (grey boxes).  

**– End of Sub-Phase 7.1 Tests –**  
1. Stats numbers correct.  
2. Charts render with mock data.  
3. Recent activity shows correct entries.  

---

### 7.2 Archived Dashboard (`/dashboard/archived`)

- **Sub-Phase 7.2.1** Layout & Skeleton  
  - **Task 7.2.1.1** Same top bar as other tabs.  
  - **Task 7.2.1.2** Title: “Archived (N)” where N = number of archived jobs (fetched from API).  
  - **Task 7.2.1.3** Grid of archived job cards (use same `JobCard` component, but dimmed).  
  - **Task 7.2.1.4** Each archived card: Show job title, client, status “Completed” or “Archived”, scheduled/created date, “Archived DATE”.  
  - **Task 7.2.1.5** Each card has a “Restore” link/button bottom right.  
- **Sub-Phase 7.2.2** Data Fetch & Actions  
  - **Task 7.2.2.1** Hook `useArchivedJobs({ limit, offset })` calls `/api/jobs?status=archived`.  
  - **Task 7.2.2.2** Clicking “Restore” calls PUT `/api/jobs/[jobId]` with `{ status: 'new' }`. On success: remove card from view or refetch.  
  - **Task 7.2.2.3** If none archived: show “No archived jobs. Great!” placeholder.  

**– End of Sub-Phase 7.2 Tests –**  
1. Archived jobs list loads correctly.  
2. Restore moves job back to active list.  

---

## 8. Frontend — Team Module (User Side)

> Pages under `/pages/teams`.

### 8.1 Teams List Page (`/teams`)

- **Sub-Phase 8.1.1** Layout & Skeleton  
  - **Task 8.1.1.1** Page title: “Teams”.  
  - **Task 8.1.1.2** Large “Create Team” button (green) at top right.  
  - **Task 8.1.1.3** If user belongs to ≥1 team: render a list of `TeamCard` components. If none: show “You aren’t part of any teams. Create or join one!” placeholder with “Create Team” or “Join Team” (link to invite) buttons.  
- **Sub-Phase 8.1.2** `TeamCard` Component  
  - **Props**:  
    ```ts
    interface TeamCardProps {
      team: {
        id: string;
        name: string;
        role: 'owner' | 'manager' | 'member';
        membersCount: number;
        created_at: string;
      };
    }
    ```  
  - **Rendering**:  
    - Container: `bg-white rounded-lg shadow-sm p-4 flex justify-between items-center`.  
    - Left: Team name (text-lg) + “(You are ROLE)” (small text).  
    - Right: Member count (icon + number).  
    - Clicking anywhere on card navigates to `/team/[team.id]`.  
- **Sub-Phase 8.1.3** Create Team Flow  
  - **Task 8.1.3.1** On clicking “Create Team”, open modal/drawer: “Create New Team”.  
  - **Task 8.1.3.2** Input: “Team Name” (required). “Allow members to invite others?” (toggle). “Require approval for job changes?” (toggle). “Auto-assign new jobs?” (toggle). “Default Job Visibility” (dropdown: Owner only, Team only, Public).  
  - **Task 8.1.3.3** Button “Create Team” (enabled when name nonempty). On click: call POST `/api/teams` with `{ name, allow_invites, require_job_approval, auto_assign_jobs, default_job_visibility }`.  
  - **Task 8.1.3.4** On success: close modal, show toast “Team created!”, redirect to `/team/[newTeamId]`.  

**– End of Sub-Phase 8.1 Tests –**  
1. Teams list loads correctly.  
2. Create team sets properties in DB.  

---

### 8.2 Team Details Page (`/team/[teamId]`)

- **Sub-Phase 8.2.1** Layout & Header  
  - **Task 8.2.1.1** Page header: “Team: TEAM_NAME” (large). Under it: “You are a ROLE in this team.”  
  - **Task 8.2.1.2** Breadcrumb: “Dashboard > Teams > [TEAM_NAME]”.  
  - **Task 8.2.1.3** Buttons on top right: “Invite Member” (green) and “Team Settings” (gear icon).  
- **Sub-Phase 8.2.2** Members List  
  - **Task 8.2.2.1** Fetch members via `/api/teams/[teamId]`. Render a `TeamMemberRow` for each: avatar initial (circle), full name, role label (Owner/Manager/Member), “Remove” icon if current user is owner or manager and member is not owner.  
  - **Task 8.2.2.2** If only one member (owner), show “No other members”.  
- **Sub-Phase 8.2.3** Invite Member Flow  
  - **Task 8.2.3.1** Clicking “Invite Member” opens a small modal: “Invite to Team”. Input: “Email” (required), “Role” (dropdown: Member or Manager; only Owner can assign Manager).  
  - **Task 8.2.3.2** On “Send Invite”: call POST `/api/teams/[teamId]/invite` with `{ email, role }`.  
  - **Task 8.2.3.3** On success: show toast “Invitation sent!”. Invite appears in “Pending Invites” list under a separate header. Each invite: email, role, “Resend” button, “Cancel” button.  
  - **Task 8.2.3.4** “Resend” calls PUT `/api/teams/[teamId]/invite/[inviteId]/resend`. “Cancel” calls DELETE `/api/teams/[teamId]/invite/[inviteId]`.  
- **Sub-Phase 8.2.4** Team Settings Flow  
  - **Task 8.2.4.1** Clicking “Team Settings” opens a nested drawer:  
    - **Team Name** (input, required)  
    - **Allow Members to Invite Others?** (toggle)  
    - **Require Approval for Job Changes?** (toggle)  
    - **Auto-Assign New Jobs?** (toggle)  
    - **Default Job Visibility** (dropdown).  
    - **Buttons**: “Save Settings” (blue). “Delete Team” (red, at bottom; only visible if ROLE=Owner).  
  - **Task 8.2.4.2** On “Save”, call PUT `/api/teams/[teamId]` with updated fields. On success: close, show toast “Team settings updated.”  
  - **Task 8.2.4.3** On “Delete Team” (owner only), confirm “Are you sure? This will remove all members and jobs under this team.” If confirmed, call DELETE `/api/teams/[teamId]`. On success: navigate back to `/teams`, show toast “Team deleted.”  
- **Sub-Phase 8.2.5** Job Permissions & Visibility  
  - **Task 8.2.5.1** If `require_job_approval=true`, any team member who tries to edit a job’s details sees a banner: “Edits require owner approval.” “Request Approval” button. Clicking sends API `/api/team/[teamId]/job-approval-request` (body: `{ job_id }`).  
  - **Task 8.2.5.2** If `auto_assign_jobs=true`, any job created by a team member automatically has `team_id` set to this team. If not, job’s `team_id` remains null until manually assigned.  
  - **Task 8.2.5.3** **Default Job Visibility**:  
    - `owner_only`: Only team owner sees jobs created by team. Others see nothing.  
    - `team_only`: All team members see team’s jobs.  
    - `public`: Public means accessible by any user in the same Supabase instance? (Usually, keep default `team_only`; `public` rarely used by tradespeople.)  

**– End of Sub-Phase 8.2 Tests –**  
1. Invite flows produce records in DB; invited user receives email (stub).  
2. Team settings persist & RLS respects them.  
3. Role‐based remove/invite/delete behavior.  

---

## 9. Frontend — Profile & Settings

### 9.1 Profile Page (`/profile`)

- **Sub-Phase 9.1.1** Layout & Info Display  
  - **Task ********* Header: “Your Profile”.  
  - **Task ********* Show profile picture (if any; else avatar circle with initials). Beneath: “Full Name”, “Company Name” (if any).  
  - **Task ********* Show email (read-only), phone (editable), website (editable), address (editable), country (dropdown selector with UK default).  
  - **Task ********* “Edit Profile” button toggles inputs into form mode. “Save Changes” saves via PUT `/api/users/update-profile`. “Cancel” reverts.  
- **Sub-Phase 9.1.2** Password Change & OAuth  
  - **Task ********* Show “Change Password” section with “Current Password”, “New Password”, “Confirm Password” fields. “Change Password” button disabled until fields valid. On click: call `/api/auth/change-password` (serverless function using Supabase Admin SDK). On success: show toast.  
  - **Task ********* Show “Connected Accounts”: Google (if connected), Facebook (if connected). Buttons “Disconnect” appear if connected, else “Connect” if not. Clicking uses Supabase OAuth events.  

**– End of Sub-Phase 9.1 Tests –**  
1. View & edit profile info, save & reflect.  
2. Change password & confirm via Supabase.  
3. Connect/disconnect OAuth providers.  

---

### 9.2 Settings Page (`/settings`)

- **Sub-Phase 9.2.1** Notification Preferences  
  - **Task 9.2.1.1** Toggle “Email notifications for Quote accepted” (default: on). Calls PUT `/api/users/me/preferences` with `{ emailOnQuoteAccepted: boolean }`.  
  - **Task 9.2.1.2** Toggle “Mobile push notifications for new jobs assigned” (default: on). Push via Supabase Realtime + browser push (stub).  
  - **Task 9.2.1.3** Toggle “In-app banners for team invites” (default: on).  
- **Sub-Phase 9.2.2** Privacy & Security  
  - **Task 9.2.2.1** Toggle “2FA” (two-factor authentication via SMS or Authenticator). If enabling, send OTP to user phone. Confirm via Supabase Auth.  
  - **Task 9.2.2.2** List active sessions (device, location, last active). “Logout from all devices” button. Calls Supabase Admin API to revoke all sessions except current.  
  - **Task 9.2.2.3** Show “Delete Account” (red). Confirm before calling DELETE `/api/users/[id]`. This needs Super Admin approval? (Alternatively, mark user as “disabled” in DB). Show confirmation.  

**– End of Sub-Phase 9.2 Tests –**  
1. Toggling preferences persists in DB and is used by notification system.  
2. 2FA flow sends OTP & verifies.  
3. Sessions list loads; “Logout all” invalidates tokens.  

---

## 10. Frontend — Ask Dex (AI Chat Interface)

> This is a full‐page (mobile) or drawer (desktop) chat interface for the built‐in AI “Dex” that provides business advice, auto‐fills forms, suggests cost breakdowns, etc.

- **Sub-Phase 10.1** UI Layout & Components  
  - **Task 10.1.1** On desktop, clicking “Ask Dex” opens a right‐side drawer (width ~500px). On mobile, it navigates to `/ask-dex` as full page.  
  - **Task 10.1.2** Top bar: “Ask Dex” (title) + subtitle “Your business advisor” + Clear (X) icon.  
  - **Task 10.1.3** Scrollable chat window background with faint grid or dotted pattern.  
  - **Task 10.1.4** Chat bubbles:  
    - **User bubbles**: aligned right, light‐blue background (`bg-blue-100`) and rounded corners.  
    - **Dex bubbles**: aligned left, dark‐blue background (`bg-blue-800 text-white`) with small white border radius.  
    - Each bubble shows timestamp (small, grey) at bottom.  
  - **Task 10.1.5** At bottom: input area with:  
    - Textarea (`rows=1` auto‐expands up to 4 rows) with placeholder “Message…”.  
    - “Send” button (paper‐plane icon) on right, greyed out if input is empty.  
  - **Task 10.1.6** While Dex (AI) is typing, show a “Typing…” indicator bubble with three animated dots.  
- **Sub-Phase 10.2** AI Interaction & Endpoints  
  - **Task 10.2.1** On user “Send”: append user bubble locally; disable input; scroll to bottom; call POST `/api/ai/chat` with body `{ user_id: auth.uid(), message: text, context: lastNMessages }`.  
  - **Task 10.2.2** `/api/ai/chat` forwards message to an AI model (e.g. OpenAI GPT) with system prompt that “You are Dex, a business advisor for tradespeople…”. Include conversation history (last 10 messages). Return `{ reply: string, suggestions?: string[] }`.  
  - **Task 10.2.3** On success: append Dex bubble with reply; if `suggestions` provided, render clickable suggestion chips beneath Dex bubble (grey pill with suggestion text). Clicking chip auto‐fills input area.  
  - **Task 10.2.4** If API error / network failure: append Dex bubble: “Sorry, I’m having trouble right now. Try again later.” Enable input for retry.  
  - **Task 10.2.5** Each query and response should be saved in Supabase table `ChatMessages` (columns: `id, user_id, role ('user'|'dex'), message, timestamp, context_refs?`).  

**– End of Sub-Phase 10 Tests –**  
1. Chat UI renders correctly; messages align properly.  
2. AI replies show.  
3. Suggestion chips clickable.  

---

## 11. Frontend — Notification System

> Notifications are displayed in the left Nav Drawer (“Notifications” link) and optionally as in‐app banners.

### 11.1 In‐App Banners

- **Sub-Phase 11.1.1** Banners Component (`components/NotificationBanner.tsx`)  
  - **Props**: `{ message: string; type: 'info' | 'success' | 'warning' | 'error'; duration?: number }`.  
  - **Rendering**: Fixed banner at top of viewport (below top bar), full width; background color: blue for `info`, green for `success`, yellow for `warning`, red for `error`. Text white. “X” close icon on right.  
  - **Behavior**: Auto‐dismiss after `duration` ms (default 5000). Close icon dismisses immediately.  
- **Sub-Phase 11.1.2** Triggering Banners  
  - **Task ********** Whenever an API route returns a success (e.g. “Client created”, “Job assigned”), show an appropriate banner.  
  - **Task ********** If an API call fails (status ≥ 400), show `error` banner with message from API.  

### 11.2 Notification Drawer

- **Sub-Phase 11.2.1** Layout & List  
  - **Task 11.2.1.1** In left Nav Drawer, clicking “Notifications” expands a submenu: “View All Notifications” link.  
  - **Task 11.2.1.2** `/notifications` page: header “Notifications” + “Mark All as Read” button (grey).  
  - **Task 11.2.1.3** List of notifications from `/api/notifications` hook. Each row: icon (based on type: quote, invoice, contract, team), message text, timestamp (e.g. “2h ago”). If `is_read=false`, show a blue dot on left.  
  - **Task 11.2.1.4** Clicking a notification:  
    - Marks it as read via POST `/api/notifications/mark-read`.  
    - Navigate to relevant area: e.g. if a job assigned → open JobDrawer for that job; if team invite accepted → open Team page; etc.  
- **Sub-Phase 11.2.2** Fetch & Pagination  
  - **Task 11.2.2.1** Hook `useNotifications({ limit, offset })` calls `/api/notifications?limit=&offset=`. Stores unread count to display on Nav Drawer badge.  
  - **Task 11.2.2.2** Infinite scroll or “Load More” at bottom.  
- **Sub-Phase 11.2.3** “Mark All as Read”  
  - **Task 11.2.3.1** On clicking, call POST `/api/notifications/mark-all-read`. On success: refresh list, set unread=0.  

**– End of Sub-Phase 11 Tests –**  
1. Notifications appear on events (e.g. quote accepted).  
2. Marking read/unread works.  
3. Navigation on click is correct.  

---

## 12. Admin Portal (Super-Admin Interface)

> Lives under a separate Next.js application (or separate folder under same monorepo), e.g. `/apps/admin`. Uses similar design system (Tailwind + shadcn).

### 12.1 Admin Login (`/admin/login`)

- **Sub-Phase 12.1.1** Login UI  
  - **Task 1********* Centered card with title “Admin Portal” and subtitle “DeskBelt Administrative Access”.  
  - **Task 1********* Input fields: “Admin Email Address”, “Password”.  
  - **Task 1********* “Remember this device” checkbox.  
  - **Task 1********* “Sign In to Admin Panel” button (blue). Disabled until email & password valid.  
  - **Task 1********* “Forgot password?” link.  
- **Sub-Phase 12.1.2** Authentication & Session  
  - **Task 1********* POST `/admin/api/auth/login` verifies credentials (via Supabase Admin or custom table). On success: set a secure HTTP‐only JWT cookie.  
  - **Task 1********* All `/admin/*` pages (except login) require valid session; otherwise redirect to login.  

**– End of Sub-Phase 12.1 Tests –**  
1. Invalid login → error.  
2. Valid login → redirect to `/admin/dashboard`.  

---

### 12.2 Admin Dashboard (`/admin/dashboard`)

- **Sub-Phase 12.2.1** Layout & Summary Cards  
  - **Task 1********* Left nav: vertical menu with icons & labels: Dashboard, User Management, Team Management, System Settings, Analytics, Audit Logs. Current hover highlight.  
  - **Task 1********* Page header: “Welcome to Admin Portal”, subtitle “Monitor and manage your DeskBelt system. You have super_admin access.”  
  - **Task 12.2.1.3** Status bar under header: “System Status: Online” (green dot), “Security: Active” (shield icon), “Last Updated: HH:mm:ss”.  
  - **Task 12.2.1.4** Summary cards (grid 2×2 on desktop, stacked on mobile):  
    1. **Total Users**: number, “▲X% vs last month”.  
    2. **Active Users**: number, “▲X% vs last month”.  
    3. **Total Teams**: number, “▲X% vs last month”.  
    4. **Total Jobs**: number, “▲X% vs last month”.  
  - **Task 12.2.1.5** Below:  
    - **System Alerts** panel (blue/yellow/red blocks) showing recent warnings (fetched from `/admin/api/alerts`).  
    - **Quick Actions** panel (grid of four): “Manage Users”, “Manage Teams”, “System Settings”, “View Analytics” (each a button linking to respective sections).  
- **Sub-Phase 12.2.2** Data Fetch  
  - **Task 1********* Hook `useAdminDashboardStats()` calls `/admin/api/stats/overview` & `/admin/api/stats/alerts`.  
  - **Task 1********* Show skeletons if loading.  

**– End of Sub-Phase 12.2 Tests –**  
1. Cards display correct numbers.  
2. Alerts display.  

---

### 12.3 User Management (`/admin/user-management`)

- **Sub-Phase 12.3.1** List View  
  - **Task 12.3.1.1** Page header: “User Management – Manage user accounts, permissions, and activity”.  
  - **Task 12.3.1.2** Search bar: “Search users by email…” with “Refresh” button and “Export” (CSV) button.  
  - **Task ********** Table columns:  
    - Checkbox (multi‐select)  
    - Avatar (initial or gravatar) + primary email (bold) + user ID (small grey)  
    - Status (Active, Disabled, Banned) – green/grey/red badge  
    - Risk Level (Low, Medium, High) – green/yellow/red badge  
    - Created (date)  
    - Last Activity (date/time)  
    - Logins (number)  
    - Revenue (USD or £) – total paid for plan (if any)  
    - Actions (vertical ellipsis menu)  
  - **Task ********** Pagination controls at bottom: “Page 1 of X”.  
  - **Task ********** Bulk actions toolbar (appears when ≥1 selected): “Disable”, “Enable”, “Export Selected”.  
- **Sub-Phase 12.3.2** Actions Menu  
  - **Task ********** Clicking ellipsis on a row shows menu:  
    - “View Profile” → `/admin/user-management/[userId]` (details)  
    - “Reset Password” → POST `/admin/api/users/[userId]/reset-password` (sends email)  
    - “Disable Account” or “Enable Account” depending on current status → PATCH `/admin/api/users/[userId]` with `{ status: ... }`  
    - “Delete User” → DELETE `/admin/api/users/[userId]` (hard delete). Confirm modal.  
  - **Task ********** “Export” button triggers CSV download of current filters (calls `/admin/api/users/export?filters…`).  

- **Sub-Phase 12.3.3** User Detail Page (`/admin/user-management/[userId]`)  
  - **Task ********** Header: “User: EMAIL (ID: USERID)” + “Status: Active/Disabled”.  
  - **Task ********** Tabs: “Profile”, “Sessions”, “Permissions”, “Revenue”, “Audit Logs”.  
    - **Profile Tab**: show full user fields as read‐only (email, name, phone, company, address, country, created_at, last_login). “Edit” button toggles editable fields.  
    - **Sessions Tab**: list active sessions: device, IP, location, last active, “Terminate” button per session. “Terminate All” button.  
    - **Permissions Tab**: show user’s role (tradesperson, team_member, admin). If `role=team_member`, show which team. “Change Role” dropdown → PATCH user.  
    - **Revenue Tab**: show subscription history, payments, plan details.  
    - **Audit Logs Tab**: show all `AuditLogs` where `actor_id = userId` (via `/admin/api/audit-logs?user=userId`).  
  - **Task ********** “Back to Users” link at top.  

**– End of Sub-Phase 12.3 Tests –**  
1. Search/filter works.  
2. Actions (reset password, disable/enable, delete) behave.  
3. Detail tabs display correct data.  

---

### 12.4 Team Management (`/admin/team-management`)

- **Sub-Phase 12.4.1** List View (Mirrors User Management)  
  - **Task ********** Page header: “Team Management – Manage teams, memberships, and team settings”.  
  - **Task ********** Search bar: “Search teams by name…” + “Refresh” + “Export”.  
  - **Task ********** Table columns:  
    - Checkbox  
    - Icon & Team Name (bold) + Team ID (small)  
    - Owner (full_name or “Unknown”)  
    - Status (Active/Disabled) – badge  
    - Members (count)  
    - Created (date)  
    - Business (if any)  
    - Actions (ellipsis)  
  - **Task ********** Bulk actions: “Disable Teams”, “Enable Teams”, “Export Selected”.  
- **Sub-Phase 12.4.2** Actions Menu  
  - **Task ********** Ellipsis menu items:  
    - “View Team” → `/admin/team-management/[teamId]`  
    - “Disable Team” / “Enable Team” → PATCH `/admin/api/teams/[teamId]` (status).  
    - “Delete Team” → DELETE `/admin/api/teams/[teamId]` (confirmation).  
  - **Task ********** “Export” triggers CSV of teams.  

- **Sub-Phase 12.4.3** Team Detail Page (`/admin/team-management/[teamId]`)  
  - **Task ********** Header: “Team: NAME (ID: teamId)” + “Status: Active/Disabled”.  
  - **Task ********** Tabs:  
    - **Overview**: shows basic info (owner, created_at, preferences), summary card for #members, #jobs, #clients (calls internal `/admin/api/teams/[teamId]/stats`).  
    - **Members**: similar to user detail, but list all members of that team: avatar, name, email, role. “Remove” / “Change Role” actions.  
    - **Settings**: mirrors what a team owner sets on user side, but with more advanced toggles (e.g. global job limits, feature flags). “Save” button.  
    - **Jobs**: list of all jobs under that team (calls `/api/jobs?team_id=teamId`). Admin can delete any.  
    - **Audit Logs**: actions related to that team (`audit_logs` where `target_type='team' AND target_id=teamId`).  
  - **Task 12.4.3.3** “Back to Teams” link.  

**– End of Sub-Phase 12.4 Tests –**  
1. Team list & search works.  
2. View team details, modify members/roles.  
3. Settings persist.  

---

### 12.5 System Settings (`/admin/system-settings`)

- **Sub-Phase 12.5.1** Global Flags & Config  
  - **Task 12.5.1.1** Page header: “System Settings”.  
  - **Task 12.5.1.2** Sections:  
    1. **Auth & Security**:  
       - **Password Rules**: min length, complexity toggles (uppercase, number, symbol).  
       - **Account Lockout**: enable / disable. If enabled, set “Failed login attempts before lockout” (<5), “Lockout Duration” (minutes).  
       - **Session Timeout**: inactivity timeout (minutes).  
    2. **AI Settings**:  
       - **Enable Ask Dex**: toggle.  
       - **AI Model**: dropdown (e.g. “gpt-4”, “gpt-3.5-turbo”).  
       - **Max Tokens**: numeric.  
       - **Temperature**: slider (0.0–1.0).  
    3. **Features & Limits**:  
       - **Max Jobs per User**: numeric (0=unlimited).  
       - **Max Clients per User**: numeric.  
       - **Enable Team Module**: toggle on/off.  
       - **Enable Billing**: toggle on/off (if off, hide Quotes/Invoices/Contracts).  
    4. **Email Templates**:  
       - **Password Reset Template**: textarea editor (must contain `%LINK%`).  
       - **Team Invite Template**: must contain `%INVITE_LINK%`.  
       - **Review Request Template**: must contain `%REVIEW_LINK%`.  
  - **Task 12.5.1.3** “Save All Settings” button at bottom. On click, validate and PUT `/admin/api/system-settings` with JSON of all. Show success/error banners.  
- **Sub-Phase 12.5.2** Feature Flagging in Code  
  - **Task 12.5.2.1** Frontend reads `SystemSettings` from Supabase (cached in a client‐side context) to enable/disable features dynamically.  
  - **Task 12.5.2.2** If “Disable Team Module” is ON, hide all Team menu items and redirect `/teams`→404.  

**– End of Sub-Phase 12.5 Tests –**  
1. Settings read/write correctly.  
2. Disabling features hides UI.  

---

### 12.6 Analytics (`/admin/analytics`)

- **Sub-Phase 12.6.1** Layout & Filters  
  - **Task 12.6.1.1** Header: “Analytics – Deep dives into usage & performance”.  
  - **Task 12.6.1.2** Filter panel (collapsible) on left:  
    - Date range picker (`start_date`, `end_date`).  
    - Dropdown “User Role” (Any, tradesperson, team_member, admin).  
    - Dropdown “Team” (All, list of active teams).  
    - Checkbox “Include archived jobs”.  
    - Button “Apply Filters”.  
  - **Task 12.6.1.3** Main area: two tabs “User Analysis” and “Job Analysis”.  

- **Sub-Phase 12.6.2** User Analysis Tab  
  - **Task 12.6.2.1** Show a paginated table of users matching filters: columns: Email, Role, Registered Date, Last Login, Jobs Created, Clients Created, Revenue.  
  - **Task 12.6.2.2** Above table: Summary cards: “New Users (date range)”, “Returning Users (%)”, “Average Jobs per User”.  
  - **Task 12.6.2.3** Under table: chart “Monthly New Users” (line), “Job Creation by Role” (bar).  

- **Sub-Phase 12.6.3** Job Analysis Tab  
  - **Task 12.6.3.1** Summary cards: “Total Quotes Sent”, “Total Invoices Paid”, “Average Completion Time (days)”, “Jobs by Status (%)” (pie).  
  - **Task 12.6.3.2** Chart: “Job Creation Over Time” (line), “Invoice Revenue Over Time” (bar).  
  - **Task 12.6.3.3** Table of top 10 busiest users (descending by #jobs) with columns: Email, #Jobs, #Clients, Avg Revenue.  

- **Sub-Phase 12.6.4** Data Fetch  
  - **Task 12.6.4.1** Hook `useAnalytics({ filters })` calls `/admin/api/analytics` with filters. API responds with all summary, chart, table data in one payload.  
  - **Task 12.6.4.2** Show skeleton placeholders for charts & tables on load.  

**– End of Sub-Phase 12.6 Tests –**  
1. Charts & tables populate with dummy data.  
2. Filters update data.  

---

### 12.7 Audit Logs (`/admin/audit-logs`)

- **Sub-Phase 12.7.1** Layout & Filters  
  - **Task 12.7.1.1** Header: “Audit Logs – Track user & system activity”.  
  - **Task 12.7.1.2** Filters: date range, actor (dropdown of users), action type (text search), target type (dropdown).  
  - **Task 12.7.1.3** Search button.  

- **Sub-Phase 12.7.2** Logs Table  
  - **Task 12.7.2.1** Columns: Timestamp, Actor (email), Action (text), Target Type, Target ID, Details (JSON preview icon to expand).  
  - **Task 12.7.2.2** Pagination bottom.  

- **Sub-Phase 12.7.3** Data Fetch  
  - **Task 12.7.3.1** Hook `useAuditLogs(filters)` calls `/admin/api/audit-logs`.  
  - **Task 12.7.3.2** Show placeholder rows while loading.  

**– End of Sub-Phase 12.7 Tests –**  
1. Logs listing matches DB entries.  
2. Filtering works.  

---

## 13. Security, Testing & QA

### 13.1 Security Hardening

- **Sub-Phase 13.1.1** OWASP & RLS Validation  
  - **Task 1********* Verify all Next.js API routes validate the Supabase JWT (`requireSession()`).  
  - **Task 13.1.1.2** Ensure all inputs are validated (e.g. use Zod schemas) before writing to DB.  
  - **Task 13.1.1.3** Protect against SQL injection (Supabase uses parameterised queries).  
  - **Task 13.1.1.4** Enable CSRF protection on forms (Next.js CSRF token).  
  - **Task 13.1.1.5** Escape all user‐provided text when rendering in the UI to avoid XSS.  
- **Sub-Phase 13.1.2** Password & Session  
  - **Task 1********* Enforce strong password rules (min 8 chars, uppercase, number, symbol).  
  - **Task 13.1.2.2** Rate‐limit login attempts (max 5 attempts per 15 minutes) via Supabase “Sign In Logs” & custom middleware.  
  - **Task 13.1.2.3** Use secure cookies (`HttpOnly`, `Secure`, `SameSite=Strict`).  
  - **Task 13.1.2.4** Ensure 2FA if enabled is enforced on login.  
- **Sub-Phase 13.1.3** Data Privacy & GDPR  
  - **Task 1********* Provide “Right to be forgotten”: On `/profile`, “Delete Account” anonymises PII (emails, phone) and/or deletes data. Soft‐delete jobs/clients or assign to a “Deleted User” placeholder.  
  - **Task 13.1.3.2** Ensure all user data at rest is encrypted via Supabase defaults.  
  - **Task 13.1.3.3** Audit logs do not store sensitive PII (only IDs).  
- **Sub-Phase 13.1.4** Admin Portal Security  
  - **Task 1********* Admin API routes use a separate Supabase role with elevated permissions, using service‐role key server‐side.  
  - **Task ********** Admin sessions are limited in time (e.g. 1 hour) and require re‐auth if idle.  
  - **Task ********** CSRF for admin actions (Next.js CSRF).  
  - **Task ********** Ensure RBAC: only super_admin access admin routes.  

### 13.2 End-to-End Testing (Cypress)

- **Sub-Phase 13.2.1** Set Up Cypress  
  - **Task ********** Install Cypress in repo; configure baseUrl for local dev (`http://localhost:3000`).  
  - **Task ********** Write helper commands for login via API (bypass UI).  
- **Sub-Phase 13.2.2** Auth Flows  
  - **Task 1********* Test registration, login, logout, forgot password end-to-end.  
  - **Task 1********* Test OAuth Google/Facebook flows (stub redirect).  
- **Sub-Phase 13.2.3** Jobs Module  
  - **Task 1********* Test creating a job, editing status, adding notes, assigning team, archiving, restoring.  
  - **Task 1********* Test search & filter on jobs.  
- **Sub-Phase 13.2.4** Clients Module  
  - **Task 1********* Test adding a client (manual + AI parse if implemented), editing, deleting, notes.  
  - **Task 1********* Test request review triggers email (stub).  
- **Sub-Phase 13.2.5** Document Flows  
  - **Task ********** Test create quote (AI‐driven + manual), download PDF.  
  - **Task ********** Test invoice & contract flows.  
- **Sub-Phase 13.2.6** Team Module  
  - **Task ********** Test creating team, inviting member, accepting invite (simulate second user), role changes, job permission toggles.  
- **Sub-Phase 13.2.7** Ask Dex  
  - **Task ********** Mock AI endpoint, test sending/receiving messages, suggestion chips.  
- **Sub-Phase 13.2.8** Admin Portal  
  - **Task ********** Test login, user management (disable, enable, delete), team management, system settings toggles, analytics filters, audit log search.  
  - **Task ********** Test RBAC: non-admin cannot access admin pages (redirect).  
- **Sub-Phase 13.2.9** Notifications  
  - **Task ********** Test that notifications fire on events (e.g. quote accepted).  

**– End of Sub-Phase 13.2 Tests –**  
- All critical E2E user flows pass without flakiness.  

---

## 14. Docker & Deployment

### 14.1 Docker Setup

- **Sub-Phase 14.1.1** Dockerfiles  
  - **Task ********** Create `/docker/web/Dockerfile` for the user frontend:  
    ```dockerfile
    FROM node:18-alpine AS base
    WORKDIR /app
    COPY package*.json tsconfig.json ./
    RUN npm install --production
    COPY . .
    RUN npm run build
    EXPOSE 3000
    CMD ["npm","run","start"]
    ```  
  - **Task ********** Create `/docker/admin/Dockerfile` similarly.  
  - **Task 14.1.1.3** Create `/docker/nginx/Dockerfile`:  
    ```dockerfile
    FROM nginx:alpine
    COPY nginx.conf /etc/nginx/nginx.conf
    ```  
    - `nginx.conf` proxies `/api` to Next.js, serves static assets, sets caching.  
- **Sub-Phase 14.1.2** Docker Compose  
  - **Task 14.1.2.1** Create `docker-compose.yml` at project root:  
    ```yaml
    version: "3.8"
    services:
      db:
        image: supabase/postgres:latest
        environment:
          POSTGRES_PASSWORD: ${DB_PASSWORD}
          POSTGRES_USER: ${DB_USER}
          POSTGRES_DB: ${DB_NAME}
        ports:
          - "5432:5432"
        volumes:
          - db_data:/var/lib/postgresql/data
      web:
        build: ./apps/web
        depends_on:
          - db
        environment:
          SUPABASE_URL: http://db:5432
          SUPABASE_KEY: ${SUPABASE_KEY}
        ports:
          - "3000:3000"
      admin:
        build: ./apps/admin
        depends_on:
          - db
        environment:
          SUPABASE_URL: http://db:5432
          SUPABASE_KEY: ${SUPABASE_KEY}
        ports:
          - "4000:3000"
      nginx:
        build: ./docker/nginx
        depends_on:
          - web
          - admin
        ports:
          - "80:80"
    volumes:
      db_data:
    ```  
  - **Task ********** `.env.example` with placeholders for `DB_USER`, `DB_PASSWORD`, `DB_NAME`, `SUPABASE_KEY`, `NEXT_PUBLIC_SUPABASE_URL`, etc.  

### 14.2 CI/CD & Deployment

- **Sub-Phase 14.2.1** GitHub Actions (or equivalent)  
  - **Task ********** On push to `main`, run:  
    - Lint (ESLint, Prettier).  
    - Type check (TypeScript).  
    - Run Cypress E2E tests (headless).  
    - Build Docker images (`docker compose build`).  
    - Push images to Container Registry (e.g. GitHub Container Registry).  
  - **Task ********** On merge to `main`, auto‐deploy to staging via `docker-compose` on a staging server (SSH).  
- **Sub-Phase 14.2.2** Production Deployment  
  - **Task ********** Manual trigger “Deploy to Production” workflow:  
    - Pull latest `main`, rebuild images, push to prod registry.  
    - On prod server, run `docker-compose pull && docker-compose up -d`.  
    - Run DB migrations via `supabase db push`.  
  - **Task ********** Health check endpoint `/healthz` returns `200 OK`. Monitor via uptime tool.  

**– End of Sub-Phase 14.2 Tests –**  
1. CI pipeline green.  
2. Staging deploy works.  
3. Production deploy with zero‐downtime (if possible).  

---

## 15. Post-Release & Monitoring

- **Task 15.1** Implement Sentry (or equivalent) for frontend error tracking.  
- **Task 15.2** Set up Supabase logs and Postgres slow query monitoring.  
- **Task 15.3** Weekly backup schedule for Supabase DB (automations tool or cron).  
- **Task 15.4** Regular security audits (OWASP Zap) and dependencies scanning (Dependabot).  

---

## 16. Summary of File Structure

/apps
/web
/public
/pages
/api
/auth
/clients
/jobs
/quotes
/invoices
/contracts
/teams
/notifications
/ai
/dashboard
/jobs
/clients
/stats
/archived
/profile
/settings
/teams
/ask-dex
/_app.tsx
/_document.tsx
/components
JobCard.tsx
JobDrawer.tsx
ClientCard.tsx
ClientDrawer.tsx
TeamCard.tsx
NotificationBanner.tsx
StatsCharts
… (other shared components)
/context
AuthContext.tsx
NotificationContext.tsx
SettingsContext.tsx
/hooks
useJobs.ts
useJobDetails.ts
useClients.ts
useClientDetails.ts
useTeams.ts
useTeamDetails.ts
useDashboardStats.ts
useNotifications.ts
useProfile.ts
useSettings.ts
useAskDex.ts
…
/types
Job.ts
Client.ts
User.ts
Team.ts
Quote.ts
Invoice.ts
Contract.ts
Notification.ts
ChatMessage.ts
/utils
api.ts // axios or fetch wrappers
date.ts // date formatting
validations.ts // Zod schemas
…
/styles
globals.css
tailwind.config.js
shadcn config
/docker
Dockerfile
package.json
tsconfig.json
…

/admin
/public
/pages
/api
/auth
/users
/teams
/system-settings
/analytics
/audit-logs
/dashboard
/user-management
/team-management
/system-settings
/analytics
/audit-logs
/login.tsx
/_app.tsx
/_document.tsx
/components
AdminLayout.tsx
UserTable.tsx
TeamTable.tsx
SettingsForm.tsx
AnalyticsCharts
AuditLogTable.tsx
…
/types
AdminUser.ts
AdminTeam.ts
SystemSetting.ts
AnalyticsData.ts
AuditLog.ts
…
/utils
adminApi.ts
date.ts
…
/styles
globals.css
tailwind.config.js
/docker
Dockerfile
package.json
tsconfig.json
…

/libs
/ui
// Shared shadcn/Tailwind components for web & admin (Button, Modal, Drawer, Input, etc.)
/db
// Supabase migration SQL files (001_.sql, 002_.sql, …)
/migrations
001_create_users.sql
002_rls_users.sql
003_create_teams.sql
004_rls_teams.sql
005_create_clients.sql
006_rls_clients.sql
007_create_jobs.sql
008_rls_jobs.sql
009_create_jobnotes.sql
010_rls_jobnotes.sql
011_create_quotes_invoices_contracts.sql
012_rls_documents.sql
013_create_auditlogs.sql
014_rls_auditlogs.sql
015_create_systemsettings.sql
016_rls_systemsettings.sql
/seeders
seed_initial_data.sql
/validation
// Zod schemas for server & client reuse

/docker
/nginx
nginx.conf
Dockerfile

/docker-compose.yml
/.env.example
/.github/workflows/ci.yml
/README.md