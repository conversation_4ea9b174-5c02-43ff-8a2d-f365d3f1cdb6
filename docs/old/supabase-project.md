# DeskBelt3 Supabase Project Documentation

> **⚠️ IMPORTANT**: Always use this specific project for DeskBelt development. Do not modify other projects in the Supabase account.

## Project Details

- **Project Name**: `deskbelt3`
- **Project URL**: `https://nwwynkkigyahrjumqmrj.supabase.co`
- **Project Reference**: `nwwynkkigyahrjumqmrj`
- **Region**: Auto-selected (likely US East)

## API Keys

### Public (Anon) Key
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.UK9jQmEjI23pyvOT0nWNrIBlNsrenfidw0NQi_AEHlg
```

### Service Role Key (Full Access)
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.4C7L13hLUC2rvvJNTCuP8Vr_ALWOsJkfno63Kv9ViPs
```

## Database Schema Overview

Based on the DeskBelt feature specification, the database will include:

### Core Tables
- **users** - User profiles and authentication
- **teams** - Team organization and settings
- **team_members** - Many-to-many team membership
- **clients** - Client information and contact details
- **jobs** - Job/project management
- **job_notes** - WhatsApp-style chat for jobs
- **client_notes** - WhatsApp-style chat for clients

### Document Storage
- **quotes** - Quote generation and tracking
- **invoices** - Invoice management and payment tracking
- **contracts** - Contract terms and signing
- **documents** - File storage references (PDFs, images)

### System Tables
- **audit_logs** - System activity tracking for admin
- **system_settings** - Global configuration flags
- **notifications** - User notifications and alerts

## Security Configuration

### Row-Level Security (RLS)
All tables will have RLS enabled with policies ensuring:
- Users can only access their own data
- Team members can access shared team data
- Super admins can access all data for management
- Proper isolation between different user accounts

### Authentication
- Email/password authentication via Supabase Auth
- OAuth integration (Google, Facebook)
- 2FA support for enhanced security
- Session management and token refresh

## Environment Variables

### Frontend (.env.local)
```env
NEXT_PUBLIC_SUPABASE_URL=https://nwwynkkigyahrjumqmrj.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.UK9jQmEjI23pyvOT0nWNrIBlNsrenfidw0NQi_AEHlg
```

### Backend/Server (.env)
```env
SUPABASE_URL=https://nwwynkkigyahrjumqmrj.supabase.co
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.4C7L13hLUC2rvvJNTCuP8Vr_ALWOsJkfno63Kv9ViPs
```

## Migration Strategy

All database changes will be:
1. **Scripted** - SQL migration files in `/libs/db/migrations/`
2. **Versioned** - Sequential numbering (001_, 002_, etc.)
3. **Tested** - Each migration tested on development before production
4. **Documented** - Each migration includes comments explaining changes

## Backup and Recovery

- **Automatic backups** enabled via Supabase
- **Point-in-time recovery** available
- **Manual backup exports** before major schema changes

## Development Guidelines

1. **Always verify project** - Check URL matches `nwwynkkigyahrjumqmrj.supabase.co`
2. **Test migrations locally** - Use Supabase CLI for local development
3. **Follow RLS patterns** - Ensure data isolation between users/teams
4. **Document schema changes** - Update this file when making structural changes
5. **Use service role carefully** - Only for admin operations and migrations

## Current Status

- ✅ Project created and configured
- 🔄 **IN PROGRESS**: Phase 4.1 - Database schema setup
- ⏳ **PENDING**: Phase 4.2 - RLS policies implementation
- ⏳ **PENDING**: Phase 4.3 - Authentication integration

---

**Last Updated**: January 2025
**Project Phase**: Phase 4 - Authentication & Supabase Integration 