# codebaseStructure.md  
> **Purpose** – A living map of *your* code, not vendor junk.  
> Loaded in every session alongside `memory.md`.

---

## 📐 STRUCTURE RULES  
1. **Auto-maintain** – Update this file whenever you add, move, or delete project files.  
2. **One-liner per entry** – `folder/file – short, high-context description`.  
3. **No code** – Only structure.  
4. **Sort** – Keep real folder order (root→leaf).  
5. **Trim deep trees** – If a folder has 20+ files, summarise instead of listing each one.  
6. **Exclusion Policy** – **NEVER** list:  
   - `node_modules`, `.git`, `.turbo`, `.cache`, `.npm`, `dist`, `build`, `.next`, `.expo`, `coverage`, or any other dependency/artefact dirs.  
   - Third-party package files anywhere inside `vendor/` or similar.  
   - IDE config or lock files (e.g. `.vscode`, `yarn.lock`).  
   - Generated files (e.g. Prisma client, Swagger docs) unless edited by devs.

---

## 🗂️ CODEBASE STRUCTURE MAP

**DeskBelt3 Monorepo** (Complete Frontend + Database Ready)

**Root/**
├── **api/** – Express.js backend (running on localhost:3001)
│   └── src/
│       ├── config/ – Database and environment configuration
│       ├── controllers/ – API route handlers
│       ├── middleware/ – Auth, validation, error handling
│       │   └── auth.ts – JWT authentication middleware with Supabase integration and dev fallback
│       ├── models/ – Database models and types
│       ├── routes/
│       │   └── ai.ts – AI chat endpoints with LLaMA 3.3 8B primary, Mistral 7B fallback
│       │   └── chat.ts – Chat messages CRUD operations for Ask Dex chatbot
│       │   └── clients.ts – Client CRUD operations with business_name support and notes API
│       │   └── jobs.ts – Job management endpoints with complete quotes, invoices, and contracts API
│       │   └── notifications.ts – Complete notifications API with CRUD operations, stats, preferences (TypeScript fixes applied)
│       │   └── teams.ts – Complete teams API with CRUD operations, member management, and invitations
│       ├── utils/ – Shared utilities and helpers
│       └── index.ts – Express server with all API routes registered
├── **apps/**
│   ├── **admin/** – Admin portal (Next.js, not yet implemented)
│   └── **web/** – Main user frontend (Next.js 15 + React 19)
│       └── src/
│           ├── **app/** – Next.js App Router pages
│           │   ├── ask-dex/ – AI chat interface page (mobile full-page version)
│           │   ├── dashboard/
│           │   │   ├── archived/ – Archived jobs/clients page
│           │   │   ├── clients/ – Complete clients dashboard with search/pagination
│           │   │   ├── jobs/ – Complete jobs dashboard with search/filters
│           │   │   └── stats/ – Analytics dashboard with charts and comprehensive metrics
│           │   ├── notifications/ – Comprehensive notifications center with filtering and real-time updates
│           │   ├── design-system/ – Interactive component showcase
│           │   ├── forgot-password/ – Password reset page (functional)
│           │   ├── login/ – Authentication page (functional)
│           │   ├── profile/ – User profile page (skeleton)
│           │   ├── register/ – User registration page (functional)
│           │   ├── settings/ – User settings page (skeleton)
│           │   └── teams/ – Team management pages (fully functional)
│           ├── **components/** – React components (comprehensive)
│           │   ├── AskDexDrawer.tsx – AI chatbot drawer with WhatsApp-style interface and message persistence
│           │   ├── ClientCard.tsx – Client summary card with actions
│           │   ├── ClientDrawer.tsx – Client details drawer with notes
│           │   ├── ClientNotesDrawer.tsx – Chat-like client notes interface with API integration
│           │   ├── ContractPreviewModal.tsx – Professional contract preview with business details and signatures
│           │   ├── CreateContractDrawer.tsx – AI-powered contract creation matching quote/invoice workflow
│           │   ├── CreateInvoiceDrawer.tsx – Invoice creation drawer with line items and VAT calculation
│           │   ├── CreateQuoteDrawer.tsx – Complete quote creation with AI integration, terms & conditions
│           │   ├── InvoicePreviewModal.tsx – Professional invoice preview with business details from profile
│           │   ├── JobStatusChart.tsx – Doughnut chart component for job status distribution
│           │   ├── ProfileDrawer.tsx – User profile management with business details editing
│           │   ├── RevenueChart.tsx – Line chart component for monthly revenue display
│           │   ├── SettingsDrawer.tsx – Application settings with business-focused preferences
│           │   ├── StatsCard.tsx – Reusable metric cards with value and change indicators
│           │   ├── CreateTeamDrawer.tsx – Team creation with settings
│           │   ├── EditClientDrawer.tsx – Client editing interface
│           │   ├── InviteTeamMemberDrawer.tsx – Team member invitation flow
│           │   ├── JobCard.tsx – Job summary card with status dropdown, date picker, visual indicators
│           │   ├── JobDrawer.tsx – Job details drawer with draft quotes display and management
│           │   ├── Layout.tsx – Main app layout with navigation and notifications integration
│           │   ├── NewClientDrawer.tsx – Simple 5-field client creation form (no AI)
│           │   ├── NewJobDrawer.tsx – Multi-step job creation with AI
│           │   ├── NotificationBanner.tsx – In-app notification banners with stacking and auto-dismiss
│           │   ├── QuotePreviewModal.tsx – Full-screen PDF-like quote preview with zoom controls
│           │   ├── RequestReviewDrawer.tsx – Review request with pre-built message templates
│           │   ├── TeamCard.tsx – Team summary card with role and settings indicators
│           │   ├── TeamMembersDrawer.tsx – Team member management and pending invites
│           │   ├── TeamSettingsDrawer.tsx – Team configuration and delete functionality
│           │   └── [25+ other UI components] – Buttons, modals, forms, etc.
│           ├── **contexts/** – React contexts
│           │   ├── AuthContext.tsx – Supabase authentication
│           │   ├── DrawerContext.tsx – Global drawer state management with Ask Dex integration
│           │   └── ThemeContext.tsx – Dark/light mode toggle
│           ├── **hooks/** – Custom React hooks
│           │   ├── useAuth.ts – Authentication state management
│           │   ├── useClientDetails.ts – Client details with notes
│           │   ├── useDexChat.ts – Ask Dex chatbot state management with real-time messaging
│           │   ├── useClients.ts – Client list with search/pagination
│           │   ├── useNotifications.ts – Notifications state management with real-time Supabase subscriptions
│           │   ├── useJobContracts.ts – Job contracts management with status tracking
│           │   ├── useJobDetails.ts – Job details with notes
│           │   ├── useDashboardStats.ts – Dashboard statistics with charts and metrics data
│           │   ├── useJobInvoices.ts – Job invoices management with line items
│           │   ├── useJobQuotes.ts – Job quotes management with draft filtering
│           │   ├── useJobs.ts – Job list with search/filters
│           │   ├── useProfile.ts – User profile management with business details
│           │   ├── useSettings.ts – Application settings with business-focused configuration
│           │   ├── useRedirectIfAuthenticated.ts – Auth page guard
│           │   ├── useRequireAuth.ts – Protected route guard
│           │   ├── useTeamDetails.ts – Team details with members and invites (real API integration)
│           │   └── useTeams.ts – Team list with role information (real API integration)
│           ├── **lib/** – Utilities and configurations
│           │   ├── supabase.ts – Supabase client configuration
│           │   └── utils.ts – Shared utility functions
│           ├── **styles/** – Global styles and Tailwind config
│           │   └── globals.css – Global CSS with Tailwind imports
│           └── **types/** – TypeScript type definitions
│               ├── Chat.ts – Chat message interfaces and types for Ask Dex
│               ├── Client.ts – Client interfaces and types
│               ├── Job.ts – Job interfaces and types
│               ├── Notification.ts – Notification interfaces and types with comprehensive notification categories
│               ├── Team.ts – Team interfaces and types
│               └── index.ts – Exported type definitions
├── **libs/**
│   ├── **db/** – Database migrations and schema
│   │   └── migrations/ – 13 SQL migration files (all applied)
│   │       ├── 001_create_users.sql – User profiles
│   │       ├── 002_create_teams.sql – Team organization
│   │       ├── 003_create_clients.sql – Client management
│   │       ├── 004_create_jobs.sql – Job/project tracking
│   │       ├── 005_create_notes.sql – WhatsApp-style chat
│   │       ├── 006_create_documents.sql – Quotes/invoices/contracts
│   │       ├── 007_create_system_tables.sql – Audit logs/notifications
│   │       ├── 008_create_rls_policies.sql – RLS setup
│   │       ├── 009_apply_rls_policies.sql – Security policies
│   │       ├── 010_seed_development_data.sql – Development data
│   │       ├── 011_add_terms_to_quotes.sql – Added terms column to quotes table
│   │       ├── 012_create_chat_messages.sql – Chat messages table for Ask Dex with RLS policies
│   │       ├── 013_create_notifications.sql – Notifications and notification_preferences tables with RLS policies
│   │       └── 007_add_contract_status.sql – Added status column to contracts table
│   └── **ui/** – Shared UI component library
│       └── src/
│           ├── components/ – Reusable UI components
│           └── utils/ – Component utilities
├── **docker/** – Containerization files (ready for deployment)
├── **docs/** – Project documentation
│   ├── AI_Integration.md – AI system configuration and model management
│   ├── AIWorkflow.md – Comprehensive AI-powered document creation strategy
│   ├── ai-drawer-implementation-guide.md – Complete guide for implementing AI-powered drawers
│   ├── codebaseStructure.md – This file
│   ├── database-schema-summary.md – Database structure overview
│   ├── design-system.md – Complete design system specification
│   ├── features.md – Comprehensive feature specification (1630 lines)
│   ├── memory.md – Critical project context and decisions
│   ├── primaryDoc.md – High-level project overview
│   ├── supabase-project.md – Database setup and configuration
│   ├── tasks.md – Development task tracking and status
│   └── workflow_guidelines.md – Development workflow rules
├── package.json – Monorepo configuration with workspace scripts
└── README.md – Project setup and development instructions

**Key Status:**
- ✅ **Frontend Fully Functional** – All major modules complete (Jobs, Clients, Teams, Stats, Profile, Settings)
- ✅ **Database Ready** – Complete schema with RLS policies applied, business_name column added to clients
- ✅ **Authentication Working** – Login/register/forgot-password functional
- ✅ **AI Strategy Defined** – Hierarchical workflow for sub-60-second document creation (Client→Job→Quote/Invoice/Contract) with comprehensive AI parsing and intelligence features documented in AIWorkflow.md
- ✅ **AI Infrastructure Complete** – OpenRouter integration, prompt management, rate limiting implemented
- ✅ **AI Job Creation Implemented** – NewJobDrawer with AI parsing and smart auto-population functional
- ✅ **AI Model Optimization Complete** – LLaMA 3.3 8B primary, Mistral 7B fallback, automatic failover system
- ✅ **API Server Operational** – Express backend running with all major API routes (jobs, clients, teams) and authentication middleware
- 🚀 **Development Server Running** – localhost:3001 (API) and localhost:3000 (web) with hot reload
- ✅ **AI Drawer Pattern Complete** – Comprehensive implementation guide created for quotes, invoices, contracts
- ✅ **AI Assistant "Dex" Implemented** – Conversational AI in NewJobDrawer with proper fallback handling, rate limiting awareness (50 requests/day), and manual override mode
- ✅ **NewClientDrawer Simplified** – Converted from AI-powered multi-step to simple 5-field form with direct database integration
- ✅ **Database Schema Updated** – Added business_name support to clients table with full API integration
- ✅ **Client Notes System Complete** – Full client notes functionality with API endpoints, chat-like UI, and proper drawer layering
- ✅ **Quotes System Complete** – Full quote creation with AI integration, terms & conditions, draft management, and PDF-like preview
- ✅ **Invoices System Complete** – Full invoice creation with line items, VAT calculation, business details from profile, and professional preview
- ✅ **Contracts System Complete** – AI-powered contract generation with professional preview, status tracking, and full CRUD operations
- ✅ **Document Management Complete** – All three document types (quotes, invoices, contracts) with consistent workflow and preview functionality
- ✅ **Phase 8 Stats Dashboard Complete** – Comprehensive analytics dashboard with Chart.js integration, revenue/metrics cards, responsive charts, and enhanced business settings
- ✅ **Phase 9 Teams Module Complete** – Full teams functionality with API routes, authentication middleware, member management, invitations, and real database integration
- ✅ **Backend API Development Complete** – All major modules (jobs, clients, teams) have comprehensive CRUD operations, authentication, and proper database integration
- ✅ **Phases 10 & 11 Complete** – Profile & Settings already complete (617-line Profile page, 623-line Settings page), all AI API endpoints implemented, enhanced client creation with AI parsing, contextual intelligence system with pure AI analysis, and client drawer functionality fixes
- ✅ **Phase 12 Ask Dex AI Chatbot Complete** – Trade-focused AI assistant with WhatsApp-style interface, message persistence, desktop drawer (~500px), mobile full-page, OpenRouter LLaMA 3.3 70B integration, UK trade-specific prompts, and proper authentication/error handling
- ✅ **Phase 13.1 & 13.2 Notifications System Complete** – Comprehensive notifications system with database migration (notifications & notification_preferences tables), full API routes with CRUD operations, NotificationBanner component with stacking, notifications center page with filtering/real-time updates, useNotifications hook with Supabase subscriptions, and navigation integration with unread count badges. TypeScript compilation errors fixed for API server stability.
- 📋 **Next Priority** – Phase 13.3 (Push Notifications) or Phase 11.5-11.7 (Enhanced Invoice/Contract Creation, AI Performance Monitoring)

---

> Keep this file light, accurate, and free of vendor noise. It's your codebase compass.
