-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.ai_usage_logs (
                                      id uuid NOT NULL DEFAULT gen_random_uuid(),
                                      user_id uuid NOT NULL,
                                      endpoint text NOT NULL,
                                      request_timestamp timestamp with time zone NOT NULL DEFAULT now(),
                                      tokens_used integer DEFAULT 0,
                                      success boolean DEFAULT true,
                                      error_message text,
                                      CONSTRAINT ai_usage_logs_pkey PRIMARY KEY (id),
                                      CONSTRAINT ai_usage_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.archived_chat_messages (
                                               id uuid NOT NULL DEFAULT gen_random_uuid(),
                                               archived_chat_id uuid NOT NULL,
                                               message text NOT NULL,
                                               is_from_user boolean NOT NULL DEFAULT false,
                                               created_at timestamp with time zone DEFAULT now(),
                                               CONSTRAINT archived_chat_messages_pkey PRIMARY KEY (id),
                                               CONSTRAINT archived_chat_messages_archived_chat_id_fkey FOREIGN KEY (archived_chat_id) REFERENCES public.archived_chats(id)
);
CREATE TABLE public.archived_chats (
                                       id uuid NOT NULL DEFAULT gen_random_uuid(),
                                       user_id uuid NOT NULL,
                                       title text NOT NULL,
                                       preview text,
                                       message_count integer DEFAULT 0,
                                       archived_at timestamp with time zone DEFAULT now(),
                                       created_at timestamp with time zone DEFAULT now(),
                                       updated_at timestamp with time zone DEFAULT now(),
                                       CONSTRAINT archived_chats_pkey PRIMARY KEY (id),
                                       CONSTRAINT archived_chats_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.audit_logs (
                                   id uuid NOT NULL DEFAULT gen_random_uuid(),
                                   timestamp timestamp with time zone DEFAULT now(),
                                   actor_id uuid,
                                   action text NOT NULL,
                                   target_type text NOT NULL,
                                   target_id uuid,
                                   details jsonb,
                                   ip_address inet,
                                   user_agent text,
                                   CONSTRAINT audit_logs_pkey PRIMARY KEY (id),
                                   CONSTRAINT audit_logs_actor_id_fkey FOREIGN KEY (actor_id) REFERENCES public.users(id)
);
CREATE TABLE public.chat_messages (
                                      id uuid NOT NULL DEFAULT gen_random_uuid(),
                                      user_id uuid NOT NULL,
                                      message text NOT NULL,
                                      is_from_user boolean NOT NULL DEFAULT true,
                                      context jsonb DEFAULT '{}'::jsonb,
                                      created_at timestamp with time zone DEFAULT now(),
                                      updated_at timestamp with time zone DEFAULT now(),
                                      CONSTRAINT chat_messages_pkey PRIMARY KEY (id),
                                      CONSTRAINT chat_messages_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.client_notes (
                                     id uuid NOT NULL DEFAULT gen_random_uuid(),
                                     client_id uuid NOT NULL,
                                     author_id uuid NOT NULL,
                                     message text NOT NULL,
                                     created_at timestamp with time zone DEFAULT now(),
                                     CONSTRAINT client_notes_pkey PRIMARY KEY (id),
                                     CONSTRAINT client_notes_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
                                     CONSTRAINT client_notes_author_id_fkey FOREIGN KEY (author_id) REFERENCES public.users(id)
);
CREATE TABLE public.clients (
                                id uuid NOT NULL DEFAULT gen_random_uuid(),
                                name text NOT NULL,
                                address text,
                                email text,
                                phone text,
                                rating integer DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
                                notes text,
                                created_at timestamp with time zone DEFAULT now(),
                                updated_at timestamp with time zone DEFAULT now(),
                                created_by uuid NOT NULL,
                                business_name text,
                                CONSTRAINT clients_pkey PRIMARY KEY (id),
                                CONSTRAINT clients_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.contracts (
                                  id uuid NOT NULL DEFAULT gen_random_uuid(),
                                  job_id uuid NOT NULL,
                                  terms text NOT NULL,
                                  created_at timestamp with time zone DEFAULT now(),
                                  updated_at timestamp with time zone DEFAULT now(),
                                  created_by uuid NOT NULL,
                                  signed_at timestamp with time zone,
                                  status text DEFAULT 'draft'::text CHECK (status = ANY (ARRAY['draft'::text, 'sent'::text, 'signed'::text, 'completed'::text])),
                                  CONSTRAINT contracts_pkey PRIMARY KEY (id),
                                  CONSTRAINT contracts_job_id_fkey FOREIGN KEY (job_id) REFERENCES public.jobs(id),
                                  CONSTRAINT contracts_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.db3_test (
                                 id bigint NOT NULL DEFAULT nextval('db3_test_id_seq'::regclass),
                                 test_name text NOT NULL,
                                 test_desc text,
                                 created_at timestamp with time zone DEFAULT now(),
                                 updated_at timestamp with time zone DEFAULT now(),
                                 CONSTRAINT db3_test_pkey PRIMARY KEY (id)
);
CREATE TABLE public.documents (
                                  id uuid NOT NULL DEFAULT gen_random_uuid(),
                                  name text NOT NULL,
                                  file_path text NOT NULL,
                                  file_size integer,
                                  mime_type text,
                                  document_type text CHECK (document_type = ANY (ARRAY['quote_pdf'::text, 'invoice_pdf'::text, 'contract_pdf'::text, 'image'::text, 'other'::text])),
                                  related_id uuid,
                                  related_type text,
                                  created_at timestamp with time zone DEFAULT now(),
                                  created_by uuid NOT NULL,
                                  CONSTRAINT documents_pkey PRIMARY KEY (id),
                                  CONSTRAINT documents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.invoices (
                                 id uuid NOT NULL DEFAULT gen_random_uuid(),
                                 job_id uuid NOT NULL,
                                 quote_id uuid,
                                 amount numeric NOT NULL,
                                 tax numeric DEFAULT 0,
                                 details text,
                                 due_date date,
                                 status text DEFAULT 'draft'::text CHECK (status = ANY (ARRAY['draft'::text, 'sent'::text, 'paid'::text, 'overdue'::text])),
                                 created_at timestamp with time zone DEFAULT now(),
                                 updated_at timestamp with time zone DEFAULT now(),
                                 created_by uuid NOT NULL,
                                 line_items jsonb,
                                 CONSTRAINT invoices_pkey PRIMARY KEY (id),
                                 CONSTRAINT invoices_job_id_fkey FOREIGN KEY (job_id) REFERENCES public.jobs(id),
                                 CONSTRAINT invoices_quote_id_fkey FOREIGN KEY (quote_id) REFERENCES public.quotes(id),
                                 CONSTRAINT invoices_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.job_notes (
                                  id uuid NOT NULL DEFAULT gen_random_uuid(),
                                  job_id uuid NOT NULL,
                                  author_id uuid NOT NULL,
                                  message text NOT NULL,
                                  message_type text DEFAULT 'text'::text CHECK (message_type = ANY (ARRAY['text'::text, 'system'::text, 'AI'::text])),
                                  created_at timestamp with time zone DEFAULT now(),
                                  CONSTRAINT job_notes_pkey PRIMARY KEY (id),
                                  CONSTRAINT job_notes_job_id_fkey FOREIGN KEY (job_id) REFERENCES public.jobs(id),
                                  CONSTRAINT job_notes_author_id_fkey FOREIGN KEY (author_id) REFERENCES public.users(id)
);
CREATE TABLE public.jobs (
                             id uuid NOT NULL DEFAULT gen_random_uuid(),
                             title text NOT NULL,
                             description text,
                             client_id uuid NOT NULL,
                             created_by uuid NOT NULL,
                             workforce_id uuid,
                             scheduled_at timestamp with time zone,
                             status text DEFAULT 'new'::text CHECK (status = ANY (ARRAY['new'::text, 'quoted'::text, 'in_progress'::text, 'on_hold'::text, 'completed'::text, 'archived'::text])),
                             created_at timestamp with time zone DEFAULT now(),
                             updated_at timestamp with time zone DEFAULT now(),
                             scheduled_start_time time without time zone,
                             scheduled_end_time time without time zone,
                             estimated_duration interval,
                             scheduling_notes text,
                             CONSTRAINT jobs_pkey PRIMARY KEY (id),
                             CONSTRAINT jobs_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
                             CONSTRAINT jobs_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id),
                             CONSTRAINT jobs_team_id_fkey FOREIGN KEY (workforce_id) REFERENCES public.workforce(id)
);
CREATE TABLE public.notification_preferences (
                                                 id uuid NOT NULL DEFAULT gen_random_uuid(),
                                                 user_id uuid NOT NULL UNIQUE,
                                                 email_notifications boolean DEFAULT true,
                                                 push_notifications boolean DEFAULT true,
                                                 in_app_notifications boolean DEFAULT true,
                                                 job_updates boolean DEFAULT true,
                                                 team_updates boolean DEFAULT true,
                                                 payment_updates boolean DEFAULT true,
                                                 marketing_emails boolean DEFAULT false,
                                                 quiet_hours_start time without time zone DEFAULT '22:00:00'::time without time zone,
                                                 quiet_hours_end time without time zone DEFAULT '08:00:00'::time without time zone,
                                                 timezone character varying DEFAULT 'Europe/London'::character varying,
                                                 created_at timestamp with time zone DEFAULT now(),
                                                 updated_at timestamp with time zone DEFAULT now(),
                                                 CONSTRAINT notification_preferences_pkey PRIMARY KEY (id),
                                                 CONSTRAINT notification_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.notifications (
                                      id uuid NOT NULL DEFAULT gen_random_uuid(),
                                      user_id uuid NOT NULL,
                                      type text NOT NULL CHECK (type = ANY (ARRAY['info'::text, 'success'::text, 'warning'::text, 'error'::text, 'job_assigned'::text, 'team_invite'::text, 'quote_accepted'::text, 'contract_signed'::text, 'payment_received'::text])),
                                      title text NOT NULL,
                                      message text NOT NULL,
                                      link text,
                                      is_read boolean DEFAULT false,
                                      created_at timestamp with time zone DEFAULT now(),
                                      action_url character varying,
                                      icon character varying,
                                      updated_at timestamp with time zone DEFAULT now(),
                                      expires_at timestamp with time zone,
                                      CONSTRAINT notifications_pkey PRIMARY KEY (id),
                                      CONSTRAINT notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.quotes (
                               id uuid NOT NULL DEFAULT gen_random_uuid(),
                               job_id uuid NOT NULL,
                               amount numeric NOT NULL,
                               details text,
                               status text DEFAULT 'draft'::text CHECK (status = ANY (ARRAY['draft'::text, 'sent'::text, 'accepted'::text, 'rejected'::text])),
                               created_at timestamp with time zone DEFAULT now(),
                               updated_at timestamp with time zone DEFAULT now(),
                               created_by uuid NOT NULL,
                               terms text,
                               market_rate_work_type text,
                               market_rate_suggestion text,
                               market_rate_estimated_range text,
                               CONSTRAINT quotes_pkey PRIMARY KEY (id),
                               CONSTRAINT quotes_job_id_fkey FOREIGN KEY (job_id) REFERENCES public.jobs(id),
                               CONSTRAINT quotes_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.system_defaults (
                                        key text NOT NULL,
                                        value jsonb NOT NULL,
                                        updated_at timestamp with time zone DEFAULT now(),
                                        CONSTRAINT system_defaults_pkey PRIMARY KEY (key)
);
CREATE TABLE public.system_settings (
                                        key text NOT NULL,
                                        value jsonb NOT NULL,
                                        description text,
                                        updated_at timestamp with time zone DEFAULT now(),
                                        updated_by uuid,
                                        CONSTRAINT system_settings_pkey PRIMARY KEY (key),
                                        CONSTRAINT system_settings_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(id)
);
CREATE TABLE public.team_invites (
                                     id uuid NOT NULL DEFAULT gen_random_uuid(),
                                     team_id uuid NOT NULL,
                                     email text NOT NULL,
                                     role text DEFAULT 'member'::text CHECK (role = ANY (ARRAY['member'::text, 'manager'::text])),
                                     token text NOT NULL UNIQUE,
                                     expires_at timestamp with time zone NOT NULL,
                                     created_at timestamp with time zone DEFAULT now(),
                                     created_by uuid NOT NULL,
                                     CONSTRAINT team_invites_pkey PRIMARY KEY (id),
                                     CONSTRAINT team_invites_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.workforce(id),
                                     CONSTRAINT team_invites_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.user_limits (
                                    user_id uuid NOT NULL,
                                    jobs_per_week integer CHECK (jobs_per_week > 0),
                                    jobs_per_month integer CHECK (jobs_per_month > 0),
                                    created_at timestamp with time zone DEFAULT now(),
                                    updated_at timestamp with time zone DEFAULT now(),
                                    ai_limits jsonb DEFAULT '{}'::jsonb,
                                    CONSTRAINT user_limits_pkey PRIMARY KEY (user_id),
                                    CONSTRAINT user_limits_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.users (
                              id uuid NOT NULL,
                              email text NOT NULL UNIQUE,
                              full_name text,
                              phone text,
                              company_name text,
                              address text,
                              website text,
                              country text DEFAULT 'UK'::text,
                              role text DEFAULT 'tradesperson'::text CHECK (role = ANY (ARRAY['tradesperson'::text, 'team_member'::text, 'admin'::text, 'super_admin'::text])),
                              created_at timestamp with time zone DEFAULT now(),
                              updated_at timestamp with time zone DEFAULT now(),
                              last_login timestamp with time zone,
                              status text NOT NULL DEFAULT 'active'::text CHECK (status = ANY (ARRAY['active'::text, 'paused'::text, 'suspended'::text, 'disabled'::text])),
                              CONSTRAINT users_pkey PRIMARY KEY (id),
                              CONSTRAINT users_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id)
);
CREATE TABLE public.workforce (
                                  id uuid NOT NULL DEFAULT gen_random_uuid(),
                                  name text NOT NULL,
                                  owner_id uuid NOT NULL,
                                  allow_invites boolean DEFAULT false,
                                  require_job_approval boolean DEFAULT false,
                                  auto_assign_jobs boolean DEFAULT false,
                                  default_job_visibility text DEFAULT 'team_only'::text CHECK (default_job_visibility = ANY (ARRAY['owner_only'::text, 'team_only'::text, 'public'::text])),
                                  created_at timestamp with time zone DEFAULT now(),
                                  updated_at timestamp with time zone DEFAULT now(),
                                  CONSTRAINT workforce_pkey PRIMARY KEY (id),
                                  CONSTRAINT teams_owner_id_fkey FOREIGN KEY (owner_id) REFERENCES public.users(id)
);
CREATE TABLE public.workforce_invitation_logs (
                                                  id uuid NOT NULL DEFAULT gen_random_uuid(),
                                                  invitation_id uuid NOT NULL,
                                                  action character varying NOT NULL CHECK (action::text = ANY (ARRAY['created'::character varying, 'sent'::character varying, 'resent'::character varying, 'viewed'::character varying, 'accepted'::character varying, 'declined'::character varying, 'expired'::character varying, 'cancelled'::character varying, 'reminded'::character varying]::text[])),
  details jsonb,
  user_id uuid,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT workforce_invitation_logs_pkey PRIMARY KEY (id),
  CONSTRAINT workforce_invitation_logs_invitation_id_fkey FOREIGN KEY (invitation_id) REFERENCES public.workforce_invitations(id),
  CONSTRAINT workforce_invitation_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.workforce_invitations (
                                              id uuid NOT NULL DEFAULT gen_random_uuid(),
                                              workforce_id uuid NOT NULL,
                                              invited_by uuid NOT NULL,
                                              email character varying NOT NULL,
                                              role character varying NOT NULL DEFAULT 'member'::character varying CHECK (role::text = ANY (ARRAY['owner'::character varying, 'manager'::character varying, 'member'::character varying]::text[])),
                                              status character varying NOT NULL DEFAULT 'pending'::character varying CHECK (status::text = ANY (ARRAY['pending'::character varying, 'accepted'::character varying, 'declined'::character varying, 'expired'::character varying, 'cancelled'::character varying]::text[])),
                                              token character varying NOT NULL UNIQUE,
                                              expires_at timestamp with time zone NOT NULL,
                                              can_manage_jobs boolean NOT NULL DEFAULT false,
                                              can_manage_clients boolean NOT NULL DEFAULT false,
                                              can_manage_invoices boolean NOT NULL DEFAULT false,
                                              can_manage_quotes boolean NOT NULL DEFAULT false,
                                              can_view_reports boolean NOT NULL DEFAULT false,
                                              can_manage_team boolean NOT NULL DEFAULT false,
                                              sent_at timestamp with time zone DEFAULT now(),
                                              accepted_at timestamp with time zone,
                                              declined_at timestamp with time zone,
                                              accepted_by uuid,
                                              personal_message text,
                                              invitation_method character varying NOT NULL DEFAULT 'email'::character varying CHECK (invitation_method::text = ANY (ARRAY['email'::character varying, 'whatsapp'::character varying, 'sms'::character varying]::text[])),
                                              created_at timestamp with time zone DEFAULT now(),
                                              updated_at timestamp with time zone DEFAULT now(),
                                              CONSTRAINT workforce_invitations_pkey PRIMARY KEY (id),
                                              CONSTRAINT workforce_invitations_workforce_id_fkey FOREIGN KEY (workforce_id) REFERENCES public.workforce(id),
                                              CONSTRAINT workforce_invitations_invited_by_fkey FOREIGN KEY (invited_by) REFERENCES auth.users(id),
                                              CONSTRAINT workforce_invitations_accepted_by_fkey FOREIGN KEY (accepted_by) REFERENCES auth.users(id)
);
CREATE TABLE public.workforce_members (
                                          user_id uuid NOT NULL,
                                          workforce_id uuid NOT NULL,
                                          role text DEFAULT 'member'::text CHECK (role = ANY (ARRAY['member'::text, 'manager'::text, 'owner'::text])),
                                          joined_at timestamp with time zone DEFAULT now(),
                                          can_manage_jobs boolean DEFAULT false,
                                          can_manage_clients boolean DEFAULT false,
                                          can_manage_invoices boolean DEFAULT false,
                                          can_manage_quotes boolean DEFAULT false,
                                          can_view_reports boolean DEFAULT false,
                                          can_manage_team boolean DEFAULT false,
                                          updated_at timestamp with time zone DEFAULT now(),
                                          CONSTRAINT workforce_members_pkey PRIMARY KEY (user_id, workforce_id),
                                          CONSTRAINT team_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
                                          CONSTRAINT team_members_team_id_fkey FOREIGN KEY (workforce_id) REFERENCES public.workforce(id)
);