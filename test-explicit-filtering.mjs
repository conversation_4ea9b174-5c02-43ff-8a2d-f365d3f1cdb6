#!/usr/bin/env node

/**
 * Simple User Data Isolation Test
 * 
 * Tests that explicit user filtering works correctly:
 * 1. Create 2 test users with unique emails
 * 2. Each user creates a client and job 
 * 3. Verify each user only sees their own data
 */

import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';

// Supabase configuration
const SUPABASE_URL = 'https://nwwynkkigyahrjumqmrj.supabase.co';
const SUPABASE_SERVICE_KEY = 'sb_secret_ma7kFAFVArMN0GO4RPdFeQ_AtmlHIy4';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
const API_BASE = 'http://localhost:4000/api';

const timestamp = Date.now();

// Test state
const testState = {
  users: [],
  cleanup: []
};

/**
 * Create a test user using the proper signup flow
 */
async function createTestUser(email, name) {
  console.log(`📝 Creating test user: ${email}`);
  
  try {
    // Create a frontend client for signup (using anon key)
    const frontendClient = createClient(SUPABASE_URL, 'sb_publishable_yM7FhOlmtFzF_OmntM6vww_7L2kSKZD');
    
    // Use the normal signup flow
    const { data: signUpData, error: signUpError } = await frontendClient.auth.signUp({
      email,
      password: 'TestPass123!'
    });
    
    if (signUpError) throw signUpError;
    if (!signUpData.user) throw new Error('No user returned from signup');
    
    // Create user profile (this should work because the user is now authenticated)
    const { error: profileError } = await frontendClient
      .from('users')
      .insert({
        id: signUpData.user.id,
        email,
        full_name: name,
        role: 'tradesperson',
        country: 'UK'
      });
    
    if (profileError) throw profileError;
    
    // Now sign in to get a fresh token
    const { data: signInData, error: signInError } = await frontendClient.auth.signInWithPassword({
      email,
      password: 'TestPass123!'
    });
    
    if (signInError) throw signInError;
    if (!signInData.session) throw new Error('No session returned from signin');
    
    const user = {
      id: signUpData.user.id,
      email,
      name,
      token: signInData.session.access_token
    };
    
    testState.users.push(user);
    testState.cleanup.push(user.id);
    
    console.log(`✅ Created user: ${name} (${user.id})`);
    return user;
    
  } catch (error) {
    console.error(`❌ Failed to create user ${email}:`, error.message);
    throw error;
  }
}

/**
 * Make authenticated API request
 */
async function apiRequest(endpoint, user, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${user.token}`,
    ...options.headers
  };
  
  const response = await fetch(url, {
    ...options,
    headers
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API request failed: ${response.status} - ${errorText}`);
  }
  
  return response.json();
}

/**
 * Test user data isolation
 */
async function testUserIsolation() {
  console.log('🔍 Testing user data isolation...\n');
  
  try {
    // Create two test users with simple email format
    const randomId = Math.floor(Math.random() * 1000000);
    const user1 = await createTestUser(`testuser${randomId}@example.com`, 'Test User 1');
    const user2 = await createTestUser(`testuser${randomId + 1}@example.com`, 'Test User 2');
    
    console.log('');
    
    // User 1: Create client and job
    console.log('📝 User 1: Creating client and job...');
    const user1Client = await apiRequest('/clients', user1, {
      method: 'POST',
      body: JSON.stringify({
        name: 'User 1 Client',
        email: '<EMAIL>'
      })
    });
    
    const user1Job = await apiRequest('/jobs', user1, {
      method: 'POST',
      body: JSON.stringify({
        title: 'User 1 Job',
        client_id: user1Client.client.id,
        description: 'Job created by user 1'
      })
    });
    
    console.log(`✅ User 1 created: Client ${user1Client.client.id}, Job ${user1Job.id}`);
    
    // User 2: Create client and job
    console.log('📝 User 2: Creating client and job...');
    const user2Client = await apiRequest('/clients', user2, {
      method: 'POST',
      body: JSON.stringify({
        name: 'User 2 Client',
        email: '<EMAIL>'
      })
    });
    
    const user2Job = await apiRequest('/jobs', user2, {
      method: 'POST',
      body: JSON.stringify({
        title: 'User 2 Job',
        client_id: user2Client.client.id,
        description: 'Job created by user 2'
      })
    });
    
    console.log(`✅ User 2 created: Client ${user2Client.client.id}, Job ${user2Job.id}`);
    console.log('');
    
    // Test isolation: User 1 should only see their data
    console.log('🔍 Testing User 1 data isolation...');
    const user1Clients = await apiRequest('/clients', user1);
    const user1Jobs = await apiRequest('/jobs', user1);
    
    console.log(`📊 User 1 sees ${user1Clients.clients.length} client(s), ${user1Jobs.jobs.length} job(s)`);
    
    if (user1Clients.clients.length !== 1) {
      throw new Error(`❌ User 1 should see 1 client, but sees ${user1Clients.clients.length}`);
    }
    
    if (user1Jobs.jobs.length !== 1) {
      throw new Error(`❌ User 1 should see 1 job, but sees ${user1Jobs.jobs.length}`);
    }
    
    if (user1Clients.clients[0].name !== 'User 1 Client') {
      throw new Error(`❌ User 1 sees wrong client: ${user1Clients.clients[0].name}`);
    }
    
    if (user1Jobs.jobs[0].title !== 'User 1 Job') {
      throw new Error(`❌ User 1 sees wrong job: ${user1Jobs.jobs[0].title}`);
    }
    
    console.log('✅ User 1 isolation test passed');
    
    // Test isolation: User 2 should only see their data
    console.log('🔍 Testing User 2 data isolation...');
    const user2Clients = await apiRequest('/clients', user2);
    const user2Jobs = await apiRequest('/jobs', user2);
    
    console.log(`📊 User 2 sees ${user2Clients.clients.length} client(s), ${user2Jobs.jobs.length} job(s)`);
    
    if (user2Clients.clients.length !== 1) {
      throw new Error(`❌ User 2 should see 1 client, but sees ${user2Clients.clients.length}`);
    }
    
    if (user2Jobs.jobs.length !== 1) {
      throw new Error(`❌ User 2 should see 1 job, but sees ${user2Jobs.jobs.length}`);
    }
    
    if (user2Clients.clients[0].name !== 'User 2 Client') {
      throw new Error(`❌ User 2 sees wrong client: ${user2Clients.clients[0].name}`);
    }
    
    if (user2Jobs.jobs[0].title !== 'User 2 Job') {
      throw new Error(`❌ User 2 sees wrong job: ${user2Jobs.jobs[0].title}`);
    }
    
    console.log('✅ User 2 isolation test passed');
    console.log('');
    
    // Test cross-user access (should fail)
    console.log('🔍 Testing cross-user access...');
    
    try {
      await apiRequest(`/clients/${user2Client.client.id}`, user1);
      throw new Error('❌ User 1 should not be able to access User 2 client');
    } catch (error) {
      if (error.message.includes('404')) {
        console.log('✅ Cross-user client access properly blocked');
      } else {
        throw error;
      }
    }
    
    try {
      await apiRequest(`/jobs/${user2Job.id}`, user1);
      throw new Error('❌ User 1 should not be able to access User 2 job');
    } catch (error) {
      if (error.message.includes('404')) {
        console.log('✅ Cross-user job access properly blocked');
      } else {
        throw error;
      }
    }
    
    console.log('');
    console.log('🎉 ALL USER ISOLATION TESTS PASSED! 🎉');
    console.log('✅ Explicit user filtering is working correctly');
    console.log('✅ Users can only see their own data');
    console.log('✅ Cross-user access is properly blocked');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  }
}

/**
 * Cleanup test data
 */
async function cleanup() {
  console.log('\n🧹 Cleaning up test data...');
  
  for (const userId of testState.cleanup) {
    try {
      await supabase.auth.admin.deleteUser(userId);
      console.log(`✅ Deleted user: ${userId}`);
    } catch (error) {
      console.error(`❌ Failed to delete user ${userId}:`, error.message);
    }
  }
  
  console.log('✅ Cleanup completed');
}

/**
 * Main test execution
 */
async function main() {
  console.log('🚀 Starting User Data Isolation Test (Explicit Filtering)\n');
  
  try {
    await testUserIsolation();
  } catch (error) {
    console.error('\n❌ Test execution failed:', error.message);
    process.exitCode = 1;
  } finally {
    await cleanup();
  }
}

// Execute the test
main();