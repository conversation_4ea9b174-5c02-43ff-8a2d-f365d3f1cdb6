-- Migration: Add terms column to quotes table
-- Date: 2025-01-29
-- Description: Add terms and conditions field to quotes table for storing quote-specific terms

-- Add terms column to quotes table
ALTER TABLE quotes 
ADD COLUMN terms TEXT;

-- Add index for better performance when searching quotes with terms
CREATE INDEX idx_quotes_terms ON quotes USING gin(to_tsvector('english', terms));

-- Update existing quotes to have default terms if needed
UPDATE quotes 
SET terms = '• Quote valid for 30 days from date of issue
• 10% deposit required before work commences
• Payment in stages: 10% deposit, 40% at halfway point, 50% upon completion
• All materials included unless otherwise specified
• Additional work outside agreed scope will be quoted separately
• Client must provide clear access to work areas and utilities
• Client responsible for obtaining all necessary permits and permissions
• Work may be suspended if payments are not received as scheduled
• Tradesman reserves right to charge for additional visits due to access issues
• All work guaranteed for 12 months from completion date
• Disputes to be resolved through local trade association mediation'
WHERE terms IS NULL; 