-- =============================================
-- Migration: 001_create_users.sql
-- Description: Create users table for DeskBelt authentication and profiles
-- Created: 2025-01-17
-- =============================================

-- Create users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  phone TEXT,
  company_name TEXT,
  address TEXT,
  website TEXT,
  country TEXT DEFAULT 'UK',
  role TEXT DEFAULT 'tradesperson' CHECK (role IN ('tradesperson', 'team_member', 'admin', 'super_admin')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE
);

-- <PERSON>reate updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add trigger to auto-update updated_at
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON public.users(role);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON public.users(created_at);

-- Add comments for documentation
COMMENT ON TABLE public.users IS 'User profiles and authentication data for DeskBelt tradespeople';
COMMENT ON COLUMN public.users.id IS 'UUID from auth.users, primary key';
COMMENT ON COLUMN public.users.role IS 'User role: tradesperson, team_member, admin, super_admin';
COMMENT ON COLUMN public.users.country IS 'Country code, defaults to UK for tradespeople'; 