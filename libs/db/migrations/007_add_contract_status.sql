-- =============================================
-- Migration: 007_add_contract_status.sql
-- Description: Add status column to contracts table
-- Created: 2025-01-17
-- =============================================

-- Add status column to contracts table
ALTER TABLE public.contracts 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'signed', 'completed'));

-- Create index for the new status column
CREATE INDEX IF NOT EXISTS idx_contracts_status ON public.contracts(status);

-- Add comment for documentation
COMMENT ON COLUMN public.contracts.status IS 'Contract status: draft, sent, signed, completed'; 