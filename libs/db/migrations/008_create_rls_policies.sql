-- =============================================
-- Migration: 008_create_rls_policies.sql
-- Description: Enable RLS and create security policies for all tables
-- Created: 2025-01-17
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_invites ENABLE ROW LEVEL SECURITY;

-- Create helper function to check if user is super admin
CREATE OR REPLACE FUNCTION is_super_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = user_id AND role = 'super_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to check team membership
CREATE OR REPLACE FUNCTION is_team_member(user_id UUID, team_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.team_members 
    WHERE user_id = is_team_member.user_id AND team_id = is_team_member.team_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to check if user can access job (owns it or is team member)
CREATE OR REPLACE FUNCTION can_access_job(user_id UUID, job_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.jobs j
    LEFT JOIN public.team_members tm ON j.team_id = tm.team_id
    WHERE j.id = can_access_job.job_id 
    AND (j.created_by = user_id OR tm.user_id = user_id OR is_super_admin(user_id))
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================
-- USERS TABLE POLICIES
-- =====================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Super admins can view all users
CREATE POLICY "Super admins can view all users" ON public.users
  FOR SELECT USING (is_super_admin(auth.uid()));

-- Super admins can update any user
CREATE POLICY "Super admins can update any user" ON public.users
  FOR UPDATE USING (is_super_admin(auth.uid()));

-- Allow new user creation (for registration)
CREATE POLICY "Allow user registration" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- =====================================
-- TEAMS TABLE POLICIES
-- =====================================

-- Team members can view teams they belong to
CREATE POLICY "Team members can view their teams" ON public.teams
  FOR SELECT USING (
    is_team_member(auth.uid(), id) OR is_super_admin(auth.uid())
  );

-- Team owners can update their teams
CREATE POLICY "Team owners can update teams" ON public.teams
  FOR UPDATE USING (
    owner_id = auth.uid() OR is_super_admin(auth.uid())
  );

-- Users can create teams (they become owner)
CREATE POLICY "Users can create teams" ON public.teams
  FOR INSERT WITH CHECK (owner_id = auth.uid());

-- Team owners can delete their teams
CREATE POLICY "Team owners can delete teams" ON public.teams
  FOR DELETE USING (
    owner_id = auth.uid() OR is_super_admin(auth.uid())
  );

-- =====================================
-- TEAM_MEMBERS TABLE POLICIES
-- =====================================

-- Users can view team memberships they're part of
CREATE POLICY "Users can view relevant team memberships" ON public.team_members
  FOR SELECT USING (
    user_id = auth.uid() OR 
    is_team_member(auth.uid(), team_id) OR 
    is_super_admin(auth.uid())
  );

-- Team owners/managers can manage memberships
CREATE POLICY "Team owners can manage memberships" ON public.team_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.teams t 
      WHERE t.id = team_id AND t.owner_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM public.team_members tm 
      WHERE tm.team_id = team_members.team_id 
      AND tm.user_id = auth.uid() 
      AND tm.role IN ('owner', 'manager')
    ) OR
    is_super_admin(auth.uid())
  );

-- =====================================
-- CLIENTS TABLE POLICIES
-- =====================================

-- Users can view clients they created or from their teams
CREATE POLICY "Users can view accessible clients" ON public.clients
  FOR SELECT USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.jobs j
      JOIN public.team_members tm ON j.team_id = tm.team_id
      WHERE j.client_id = clients.id AND tm.user_id = auth.uid()
    ) OR
    is_super_admin(auth.uid())
  );

-- Users can create clients
CREATE POLICY "Users can create clients" ON public.clients
  FOR INSERT WITH CHECK (created_by = auth.uid());

-- Users can update clients they created or team members with permission
CREATE POLICY "Users can update accessible clients" ON public.clients
  FOR UPDATE USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.jobs j
      JOIN public.team_members tm ON j.team_id = tm.team_id
      WHERE j.client_id = clients.id AND tm.user_id = auth.uid() AND tm.role IN ('owner', 'manager')
    ) OR
    is_super_admin(auth.uid())
  );

-- Users can delete clients they created
CREATE POLICY "Users can delete own clients" ON public.clients
  FOR DELETE USING (
    created_by = auth.uid() OR is_super_admin(auth.uid())
  );

-- =====================================
-- JOBS TABLE POLICIES
-- =====================================

-- Users can view jobs they created or from their teams
CREATE POLICY "Users can view accessible jobs" ON public.jobs
  FOR SELECT USING (
    created_by = auth.uid() OR
    is_team_member(auth.uid(), team_id) OR
    is_super_admin(auth.uid())
  );

-- Users can create jobs
CREATE POLICY "Users can create jobs" ON public.jobs
  FOR INSERT WITH CHECK (created_by = auth.uid());

-- Users can update jobs they created or team members with permission
CREATE POLICY "Users can update accessible jobs" ON public.jobs
  FOR UPDATE USING (
    created_by = auth.uid() OR
    (is_team_member(auth.uid(), team_id) AND NOT EXISTS (
      SELECT 1 FROM public.teams t 
      WHERE t.id = team_id AND t.require_job_approval = true
    )) OR
    EXISTS (
      SELECT 1 FROM public.team_members tm 
      WHERE tm.team_id = jobs.team_id 
      AND tm.user_id = auth.uid() 
      AND tm.role IN ('owner', 'manager')
    ) OR
    is_super_admin(auth.uid())
  );

-- Users can delete jobs they created
CREATE POLICY "Users can delete own jobs" ON public.jobs
  FOR DELETE USING (
    created_by = auth.uid() OR is_super_admin(auth.uid())
  );

-- =====================================
-- NOTES TABLE POLICIES (JOB & CLIENT)
-- =====================================

-- Users can view notes for jobs/clients they can access
CREATE POLICY "Users can view accessible job notes" ON public.job_notes
  FOR SELECT USING (can_access_job(auth.uid(), job_id) OR is_super_admin(auth.uid()));

CREATE POLICY "Users can view accessible client notes" ON public.client_notes
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.clients c 
      WHERE c.id = client_id AND (
        c.created_by = auth.uid() OR
        EXISTS (
          SELECT 1 FROM public.jobs j
          JOIN public.team_members tm ON j.team_id = tm.team_id
          WHERE j.client_id = c.id AND tm.user_id = auth.uid()
        )
      )
    ) OR is_super_admin(auth.uid())
  );

-- Users can create notes for accessible jobs/clients
CREATE POLICY "Users can create job notes" ON public.job_notes
  FOR INSERT WITH CHECK (
    author_id = auth.uid() AND can_access_job(auth.uid(), job_id)
  );

CREATE POLICY "Users can create client notes" ON public.client_notes
  FOR INSERT WITH CHECK (
    author_id = auth.uid() AND EXISTS (
      SELECT 1 FROM public.clients c 
      WHERE c.id = client_id AND (
        c.created_by = auth.uid() OR
        EXISTS (
          SELECT 1 FROM public.jobs j
          JOIN public.team_members tm ON j.team_id = tm.team_id
          WHERE j.client_id = c.id AND tm.user_id = auth.uid()
        )
      )
    )
  );

-- =====================================
-- DOCUMENT TABLE POLICIES (QUOTES, INVOICES, CONTRACTS, FILES)
-- =====================================

-- Users can view documents for jobs they can access
CREATE POLICY "Users can view accessible quotes" ON public.quotes
  FOR SELECT USING (can_access_job(auth.uid(), job_id) OR is_super_admin(auth.uid()));

CREATE POLICY "Users can view accessible invoices" ON public.invoices
  FOR SELECT USING (can_access_job(auth.uid(), job_id) OR is_super_admin(auth.uid()));

CREATE POLICY "Users can view accessible contracts" ON public.contracts
  FOR SELECT USING (can_access_job(auth.uid(), job_id) OR is_super_admin(auth.uid()));

CREATE POLICY "Users can view accessible documents" ON public.documents
  FOR SELECT USING (created_by = auth.uid() OR is_super_admin(auth.uid()));

-- Users can create documents for accessible jobs
CREATE POLICY "Users can create quotes" ON public.quotes
  FOR INSERT WITH CHECK (
    created_by = auth.uid() AND can_access_job(auth.uid(), job_id)
  );

CREATE POLICY "Users can create invoices" ON public.invoices
  FOR INSERT WITH CHECK (
    created_by = auth.uid() AND can_access_job(auth.uid(), job_id)
  );

CREATE POLICY "Users can create contracts" ON public.contracts
  FOR INSERT WITH CHECK (
    created_by = auth.uid() AND can_access_job(auth.uid(), job_id)
  );

CREATE POLICY "Users can create documents" ON public.documents
  FOR INSERT WITH CHECK (created_by = auth.uid());

-- Users can update documents they created
CREATE POLICY "Users can update own quotes" ON public.quotes
  FOR UPDATE USING (
    created_by = auth.uid() OR is_super_admin(auth.uid())
  );

CREATE POLICY "Users can update own invoices" ON public.invoices
  FOR UPDATE USING (
    created_by = auth.uid() OR is_super_admin(auth.uid())
  );

CREATE POLICY "Users can update own contracts" ON public.contracts
  FOR UPDATE USING (
    created_by = auth.uid() OR is_super_admin(auth.uid())
  );

-- =====================================
-- NOTIFICATION TABLE POLICIES
-- =====================================

-- Users can view their own notifications
CREATE POLICY "Users can view own notifications" ON public.notifications
  FOR SELECT USING (
    user_id = auth.uid() OR is_super_admin(auth.uid())
  );

-- System can create notifications for users
CREATE POLICY "System can create notifications" ON public.notifications
  FOR INSERT WITH CHECK (true); -- Will be restricted by service role

-- Users can update their own notifications (mark as read)
CREATE POLICY "Users can update own notifications" ON public.notifications
  FOR UPDATE USING (
    user_id = auth.uid() OR is_super_admin(auth.uid())
  );

-- =====================================
-- AUDIT LOGS & SYSTEM SETTINGS (ADMIN ONLY)
-- =====================================

-- Only super admins can view audit logs
CREATE POLICY "Super admins can view audit logs" ON public.audit_logs
  FOR SELECT USING (is_super_admin(auth.uid()));

-- System can create audit logs
CREATE POLICY "System can create audit logs" ON public.audit_logs
  FOR INSERT WITH CHECK (true); -- Will be restricted by service role

-- Only super admins can view/modify system settings
CREATE POLICY "Super admins can manage system settings" ON public.system_settings
  FOR ALL USING (is_super_admin(auth.uid()));

-- =====================================
-- TEAM INVITES POLICIES
-- =====================================

-- Team owners/managers can view invites for their teams
CREATE POLICY "Team owners can view team invites" ON public.team_invites
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.team_members tm 
      WHERE tm.team_id = team_invites.team_id 
      AND tm.user_id = auth.uid() 
      AND tm.role IN ('owner', 'manager')
    ) OR
    is_super_admin(auth.uid())
  );

-- Team owners/managers can create invites
CREATE POLICY "Team owners can create invites" ON public.team_invites
  FOR INSERT WITH CHECK (
    created_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.team_members tm 
      WHERE tm.team_id = team_invites.team_id 
      AND tm.user_id = auth.uid() 
      AND tm.role IN ('owner', 'manager')
    )
  );

-- Team owners/managers can delete invites
CREATE POLICY "Team owners can delete invites" ON public.team_invites
  FOR DELETE USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.team_members tm 
      WHERE tm.team_id = team_invites.team_id 
      AND tm.user_id = auth.uid() 
      AND tm.role IN ('owner', 'manager')
    ) OR
    is_super_admin(auth.uid())
  );

-- Add comments
COMMENT ON FUNCTION is_super_admin IS 'Helper function to check if user has super_admin role';
COMMENT ON FUNCTION is_team_member IS 'Helper function to check if user is member of a team';
COMMENT ON FUNCTION can_access_job IS 'Helper function to check if user can access a job (creator or team member)'; 