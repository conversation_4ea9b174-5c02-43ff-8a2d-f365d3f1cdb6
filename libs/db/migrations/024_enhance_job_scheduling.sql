-- =============================================
-- Migration: 024_enhance_job_scheduling.sql
-- Description: Add enhanced scheduling fields to jobs table for time-based scheduling
-- Created: 2025-08-05
-- =============================================

-- Add enhanced scheduling columns to jobs table
ALTER TABLE public.jobs ADD COLUMN IF NOT EXISTS scheduled_start_time TIME;
ALTER TABLE public.jobs ADD COLUMN IF NOT EXISTS scheduled_end_time TIME;
ALTER TABLE public.jobs ADD COLUMN IF NOT EXISTS estimated_duration INTERVAL;
ALTER TABLE public.jobs ADD COLUMN IF NOT EXISTS scheduling_notes TEXT;

-- Add indexes for enhanced scheduling performance
CREATE INDEX IF NOT EXISTS idx_jobs_scheduled_times ON public.jobs(scheduled_at, scheduled_start_time) WHERE scheduled_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_jobs_time_range ON public.jobs(scheduled_start_time, scheduled_end_time) WHERE scheduled_start_time IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.jobs.scheduled_start_time IS 'Start time for the scheduled job (time only, date in scheduled_at)';
COMMENT ON COLUMN public.jobs.scheduled_end_time IS 'End time for the scheduled job (time only, date in scheduled_at)';
COMMENT ON COLUMN public.jobs.estimated_duration IS 'Estimated duration of the job (e.g., 2 hours, 30 minutes)';
COMMENT ON COLUMN public.jobs.scheduling_notes IS 'Additional notes about the scheduling (access instructions, parking, etc.)';

-- Add constraint to ensure end time is after start time
ALTER TABLE public.jobs ADD CONSTRAINT check_job_time_order 
    CHECK (
        scheduled_end_time IS NULL 
        OR scheduled_start_time IS NULL 
        OR scheduled_end_time > scheduled_start_time
    );

-- Add constraint to ensure time fields are only set when date is set
ALTER TABLE public.jobs ADD CONSTRAINT check_job_time_requires_date
    CHECK (
        (scheduled_start_time IS NULL AND scheduled_end_time IS NULL)
        OR scheduled_at IS NOT NULL
    );