-- =============================================
-- Migration: 015_ensure_owners_are_team_members.sql
-- Description: Ensures that every team owner is also a member of their team in the team_members table.
-- Created: 2025-01-30
-- =============================================

INSERT INTO public.team_members (user_id, team_id, role)
SELECT
  t.owner_id,
  t.id,
  'owner'
FROM
  public.teams t
WHERE
  NOT EXISTS (
    SELECT 1
    FROM public.team_members tm
    WHERE tm.user_id = t.owner_id AND tm.team_id = t.id
  );

-- Add comments for clarity
COMMENT ON MIGRATION IS 'This one-off script backfills team owners into the team_members table to ensure data integrity, as owners were not previously added automatically.'; 