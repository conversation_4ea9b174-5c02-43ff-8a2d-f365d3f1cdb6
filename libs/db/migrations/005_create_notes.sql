-- =============================================
-- Migration: 005_create_notes.sql
-- Description: Create job_notes and client_notes tables for chat functionality
-- Created: 2025-01-17
-- =============================================

-- Create job_notes table for WhatsApp-style chat
CREATE TABLE IF NOT EXISTS public.job_notes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE NOT NULL,
  author_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  message TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'system', 'AI')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create client_notes table for WhatsApp-style chat
CREATE TABLE IF NOT EXISTS public.client_notes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE NOT NULL,
  author_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_job_notes_job_id ON public.job_notes(job_id);
CREATE INDEX IF NOT EXISTS idx_job_notes_author_id ON public.job_notes(author_id);
CREATE INDEX IF NOT EXISTS idx_job_notes_created_at ON public.job_notes(created_at);
CREATE INDEX IF NOT EXISTS idx_job_notes_message_type ON public.job_notes(message_type);

-- Compound index for efficient chat loading
CREATE INDEX IF NOT EXISTS idx_job_notes_job_created ON public.job_notes(job_id, created_at);

CREATE INDEX IF NOT EXISTS idx_client_notes_client_id ON public.client_notes(client_id);
CREATE INDEX IF NOT EXISTS idx_client_notes_author_id ON public.client_notes(author_id);
CREATE INDEX IF NOT EXISTS idx_client_notes_created_at ON public.client_notes(created_at);

-- Compound index for efficient chat loading
CREATE INDEX IF NOT EXISTS idx_client_notes_client_created ON public.client_notes(client_id, created_at);

-- Add comments for documentation
COMMENT ON TABLE public.job_notes IS 'WhatsApp-style chat messages for jobs';
COMMENT ON COLUMN public.job_notes.job_id IS 'Job this note belongs to';
COMMENT ON COLUMN public.job_notes.author_id IS 'User who wrote this note';
COMMENT ON COLUMN public.job_notes.message IS 'The actual message content';
COMMENT ON COLUMN public.job_notes.message_type IS 'Type: text (user), system (auto), AI (chatbot)';

COMMENT ON TABLE public.client_notes IS 'WhatsApp-style chat messages for clients';
COMMENT ON COLUMN public.client_notes.client_id IS 'Client this note belongs to';
COMMENT ON COLUMN public.client_notes.author_id IS 'User who wrote this note';
COMMENT ON COLUMN public.client_notes.message IS 'The actual message content'; 