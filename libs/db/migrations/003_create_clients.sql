-- =============================================
-- Migration: 003_create_clients.sql
-- Description: Create clients table for customer management
-- Created: 2025-01-17
-- =============================================

-- Create clients table
CREATE TABLE IF NOT EXISTS public.clients (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  address TEXT,
  email TEXT,
  phone TEXT,
  rating INTEGER DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL
);

-- Add updated_at trigger to clients
CREATE TRIGGER update_clients_updated_at
    BEFORE UPDATE ON public.clients
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_clients_created_by ON public.clients(created_by);
CREATE INDEX IF NOT EXISTS idx_clients_name ON public.clients(name);
CREATE INDEX IF NOT EXISTS idx_clients_email ON public.clients(email);
CREATE INDEX IF NOT EXISTS idx_clients_phone ON public.clients(phone);
CREATE INDEX IF NOT EXISTS idx_clients_rating ON public.clients(rating);
CREATE INDEX IF NOT EXISTS idx_clients_created_at ON public.clients(created_at);

-- Add comments for documentation
COMMENT ON TABLE public.clients IS 'Client/customer information for tradespeople';
COMMENT ON COLUMN public.clients.name IS 'Client full name or company name';
COMMENT ON COLUMN public.clients.rating IS 'Client rating 0-5 stars';
COMMENT ON COLUMN public.clients.created_by IS 'User who added this client';
COMMENT ON COLUMN public.clients.notes IS 'General notes about the client'; 