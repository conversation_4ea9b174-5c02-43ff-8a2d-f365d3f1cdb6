-- Migration: Create archived chats tables
-- Created: 2025-01-07
-- Purpose: Add chat archiving functionality to Ask Dex

-- Create archived_chats table
CREATE TABLE IF NOT EXISTS archived_chats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  preview TEXT,
  message_count INTEGER DEFAULT 0,
  archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> archived_chat_messages table
CREATE TABLE IF NOT EXISTS archived_chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  archived_chat_id UUID NOT NULL REFERENCES archived_chats(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  is_from_user BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_archived_chats_user_id ON archived_chats(user_id);
CREATE INDEX IF NOT EXISTS idx_archived_chats_archived_at ON archived_chats(archived_at DESC);
CREATE INDEX IF NOT EXISTS idx_archived_chat_messages_chat_id ON archived_chat_messages(archived_chat_id);
CREATE INDEX IF NOT EXISTS idx_archived_chat_messages_created_at ON archived_chat_messages(created_at);

-- Enable RLS
ALTER TABLE archived_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE archived_chat_messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies for archived_chats
CREATE POLICY "Users can view their own archived chats"
  ON archived_chats FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own archived chats"
  ON archived_chats FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own archived chats"
  ON archived_chats FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own archived chats"
  ON archived_chats FOR DELETE
  USING (auth.uid() = user_id);

-- RLS Policies for archived_chat_messages
CREATE POLICY "Users can view messages from their archived chats"
  ON archived_chat_messages FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM archived_chats 
      WHERE archived_chats.id = archived_chat_messages.archived_chat_id 
      AND archived_chats.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert messages to their archived chats"
  ON archived_chat_messages FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM archived_chats 
      WHERE archived_chats.id = archived_chat_messages.archived_chat_id 
      AND archived_chats.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete messages from their archived chats"
  ON archived_chat_messages FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM archived_chats 
      WHERE archived_chats.id = archived_chat_messages.archived_chat_id 
      AND archived_chats.user_id = auth.uid()
    )
  ); 