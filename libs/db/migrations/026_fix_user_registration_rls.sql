-- =============================================
-- Migration: 026_fix_user_registration_rls.sql
-- Description: Fix RLS policy for user registration during invitation flow
-- Created: 2025-08-25
-- Issue: New user registration fails due to RLS policy during invitation acceptance
-- =============================================

-- Drop the existing restrictive user registration policy
DROP POLICY IF EXISTS "Allow user registration" ON public.users;

-- Create a more flexible user registration policy that handles various registration scenarios
CREATE POLICY "Allow user registration and profile creation" ON public.users
  FOR INSERT WITH CHECK (
    -- Case 1: Standard authenticated registration (auth.uid() matches the user being created)
    auth.uid() = id
    OR 
    -- Case 2: Service role operations (for system-level user profile creation)
    current_setting('role') = 'service_role'
    OR
    -- Case 3: During signup process when session is being established
    -- Allow if the email being inserted matches a pending invitation
    (
      auth.uid() IS NULL 
      AND EXISTS (
        SELECT 1 FROM public.workforce_invitations wi 
        WHERE wi.email = users.email 
        AND wi.status = 'pending' 
        AND wi.expires_at > now()
      )
    )
    OR
    -- Case 4: Allow if there's a valid auth.users record with the same id but no profile yet
    (
      EXISTS (
        SELECT 1 FROM auth.users au 
        WHERE au.id = users.id 
        AND au.email = users.email
      )
      AND NOT EXISTS (
        SELECT 1 FROM public.users existing 
        WHERE existing.id = users.id
      )
    )
  );

-- Add a comment explaining the policy
COMMENT ON POLICY "Allow user registration and profile creation" ON public.users IS 
'Allows user profile creation for: 1) Authenticated users creating their own profile, 2) Service role operations, 3) Invitation-based registration, 4) Auth users without existing profiles';

-- Create a function to help with secure user profile creation during registration
CREATE OR REPLACE FUNCTION create_user_profile_securely(
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT DEFAULT NULL,
  user_role TEXT DEFAULT 'tradesperson'
)
RETURNS public.users AS $$
DECLARE
  new_profile public.users;
BEGIN
  -- Validate that either we're authenticated as this user, or it's a valid invitation scenario
  IF (
    auth.uid() = user_id 
    OR 
    current_setting('role') = 'service_role'
    OR
    (
      EXISTS (
        SELECT 1 FROM public.workforce_invitations wi 
        WHERE wi.email = user_email 
        AND wi.status = 'pending' 
        AND wi.expires_at > now()
      )
      AND EXISTS (
        SELECT 1 FROM auth.users au 
        WHERE au.id = user_id 
        AND au.email = user_email
      )
    )
  ) THEN
    -- Create the user profile
    INSERT INTO public.users (
      id,
      email,
      full_name,
      role,
      country,
      status,
      created_at,
      updated_at
    ) VALUES (
      user_id,
      user_email,
      user_full_name,
      user_role,
      'UK',
      'active',
      now(),
      now()
    ) RETURNING * INTO new_profile;
    
    RETURN new_profile;
  ELSE
    RAISE EXCEPTION 'Unauthorized user profile creation attempt';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users and service role
GRANT EXECUTE ON FUNCTION create_user_profile_securely TO authenticated;
GRANT EXECUTE ON FUNCTION create_user_profile_securely TO service_role;

-- Add comment for documentation
COMMENT ON FUNCTION create_user_profile_securely IS 'Securely creates user profiles with proper authorization checks for registration and invitation flows';