-- =============================================
-- Migration: 004_create_jobs.sql
-- Description: Create jobs table for job/project management
-- Created: 2025-01-17
-- =============================================

-- Create jobs table
CREATE TABLE IF NOT EXISTS public.jobs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE NOT NULL,
  created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  team_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
  scheduled_at TIMESTAMP WITH TIME ZONE,
  status TEXT DEFAULT 'new' CHECK (status IN ('new', 'quoted', 'in_progress', 'on_hold', 'completed', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add updated_at trigger to jobs
CREATE TRIGGER update_jobs_updated_at
    BEFORE UPDATE ON public.jobs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_jobs_client_id ON public.jobs(client_id);
CREATE INDEX IF NOT EXISTS idx_jobs_created_by ON public.jobs(created_by);
CREATE INDEX IF NOT EXISTS idx_jobs_team_id ON public.jobs(team_id);
CREATE INDEX IF NOT EXISTS idx_jobs_status ON public.jobs(status);
CREATE INDEX IF NOT EXISTS idx_jobs_scheduled_at ON public.jobs(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON public.jobs(created_at);

-- Create compound indexes for common queries
CREATE INDEX IF NOT EXISTS idx_jobs_status_created_at ON public.jobs(status, created_at);
CREATE INDEX IF NOT EXISTS idx_jobs_created_by_status ON public.jobs(created_by, status);

-- Add comments for documentation
COMMENT ON TABLE public.jobs IS 'Jobs/projects for tradespeople to manage work';
COMMENT ON COLUMN public.jobs.title IS 'Job title or brief description';
COMMENT ON COLUMN public.jobs.client_id IS 'Client this job is for';
COMMENT ON COLUMN public.jobs.created_by IS 'User who created this job';
COMMENT ON COLUMN public.jobs.team_id IS 'Team assigned to this job (if any)';
COMMENT ON COLUMN public.jobs.status IS 'Current job status: new, quoted, in_progress, on_hold, completed, archived';
COMMENT ON COLUMN public.jobs.scheduled_at IS 'When the job is scheduled to be done'; 