-- =============================================
-- Migration: 020_add_ai_limits_and_usage_logs.sql
-- Description: Add AI limits JSONB column, create ai_usage_logs, and RLS policies
-- Created: 2025-07-05
-- =============================================

-- 1. Add ai_limits JSONB column to user_limits table (NULL object default)
ALTER TABLE public.user_limits
  ADD COLUMN IF NOT EXISTS ai_limits JSONB DEFAULT '{}'::jsonb
  CHECK (jsonb_typeof(ai_limits) = 'object');

COMMENT ON COLUMN public.user_limits.ai_limits IS 'JSON object storing AI chat limits, e.g. {"hour":20,"day":80,"week":150,"month":500}. NULL or missing keys = unlimited';

-- 2. Create ai_usage_logs table for tracking AI usage (monthly partitions)
CREATE TABLE IF NOT EXISTS public.ai_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  endpoint TEXT NOT NULL, -- e.g. dex-chat, quote-intelligence
  request_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  tokens_used INTEGER DEFAULT 0,
  success BOOLEAN DEFAULT true,
  error_message TEXT
) PARTITION BY RANGE (request_timestamp);

-- Create current month partition (YYYY_MM)
DO $$
DECLARE
  partition_name text := format('ai_usage_logs_%s', to_char(now(), 'YYYY_MM'));
  partition_start date := date_trunc('month', now());
  partition_end date := (date_trunc('month', now()) + INTERVAL '1 month');
BEGIN
  EXECUTE format('CREATE TABLE IF NOT EXISTS public.%I PARTITION OF public.ai_usage_logs
                  FOR VALUES FROM (%L) TO (%L);',
                  partition_name,
                  partition_start,
                  partition_end);
END$$;

-- Indexes for efficient look-ups
CREATE INDEX IF NOT EXISTS idx_ai_usage_user_timestamp ON public.ai_usage_logs(user_id, request_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_ai_usage_endpoint ON public.ai_usage_logs(endpoint);

-- 3. Row-level security policies (RLS)
ALTER TABLE public.ai_usage_logs ENABLE ROW LEVEL SECURITY;

-- Policy: service-role & owner can read
CREATE POLICY "Allow owner & service role read" ON public.ai_usage_logs
  FOR SELECT USING (auth.role() = 'service_role' OR auth.uid() = user_id);

-- Policy: service-role can insert
CREATE POLICY "Allow service role insert" ON public.ai_usage_logs
  FOR INSERT TO public.ai_usage_logs
  WITH CHECK (auth.role() = 'service_role');

-- 4. Audit comment
COMMENT ON TABLE public.ai_usage_logs IS 'Per-request AI usage log for enforcing rate limits and analytics'; 