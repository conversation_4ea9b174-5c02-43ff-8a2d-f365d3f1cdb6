-- =============================================
-- Migration: 010_seed_development_data.sql
-- Description: Seed initial development data for testing
-- Created: 2025-01-17
-- =============================================

-- Insert seed users (these would normally come from Supabase Auth)
-- Note: In real app, these UUIDs would be auto-generated by Supabase Auth
-- For development, we'll create placeholder UUIDs

-- Test super admin user
INSERT INTO public.users (
    id, 
    email, 
    full_name, 
    phone, 
    company_name, 
    address, 
    country, 
    role
) VALUES (
    '00000000-0000-0000-0000-000000000001',
    '<EMAIL>',
    'System Administrator',
    '+44 20 7946 0958',
    'DeskBelt Ltd',
    '123 Admin Street, London, UK',
    'UK',
    'super_admin'
) ON CONFLICT (id) DO NOTHING;

-- Test tradesperson user
INSERT INTO public.users (
    id, 
    email, 
    full_name, 
    phone, 
    company_name, 
    address, 
    website, 
    country, 
    role
) VALUES (
    '00000000-0000-0000-0000-000000000002',
    '<EMAIL>',
    '<PERSON>',
    '+44 77 1234 5678',
    'Sparky Electrics',
    '456 Workshop Lane, Birmingham, UK',
    'https://sparkyelectrics.com',
    'UK',
    'tradesperson'
) ON CONFLICT (id) DO NOTHING;

-- Test team member user
INSERT INTO public.users (
    id, 
    email, 
    full_name, 
    phone, 
    company_name, 
    address, 
    country, 
    role
) VALUES (
    '00000000-0000-0000-0000-000000000003',
    '<EMAIL>',
    'Mike Johnson',
    '+44 77 9876 5432',
    'Sparky Electrics',
    '789 Helper Street, Birmingham, UK',
    'UK',
    'team_member'
) ON CONFLICT (id) DO NOTHING;

-- Insert test team
INSERT INTO public.teams (
    id,
    name,
    owner_id,
    allow_invites,
    require_job_approval,
    auto_assign_jobs,
    default_job_visibility
) VALUES (
    '10000000-0000-0000-0000-000000000001',
    'Sparky Electrics Team',
    '00000000-0000-0000-0000-000000000002',
    true,
    false,
    true,
    'team_only'
) ON CONFLICT (id) DO NOTHING;

-- Add team members
INSERT INTO public.team_members (user_id, team_id, role) VALUES 
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', 'owner'),
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', 'member')
ON CONFLICT (user_id, team_id) DO NOTHING;

-- Insert test clients
INSERT INTO public.clients (
    id,
    name,
    address,
    email,
    phone,
    rating,
    notes,
    created_by
) VALUES 
    (
        '20000000-0000-0000-0000-000000000001',
        'Sarah Thompson',
        '123 Rose Street, Manchester, UK',
        '<EMAIL>',
        '+44 16 1234 5678',
        5,
        'Excellent client, always pays on time. Has recommended us to neighbors.',
        '00000000-0000-0000-0000-000000000002'
    ),
    (
        '20000000-0000-0000-0000-000000000002',
        'The Red Lion Pub',
        '456 High Street, Birmingham, UK',
        '<EMAIL>',
        '+44 12 1987 6543',
        4,
        'Commercial client - pub needs regular electrical maintenance.',
        '00000000-0000-0000-0000-000000000002'
    ),
    (
        '20000000-0000-0000-0000-000000000003',
        'David & Emma Wilson',
        '789 Oak Avenue, Coventry, UK',
        '<EMAIL>',
        '+44 24 7654 3210',
        3,
        'New build house - full electrical installation required.',
        '00000000-0000-0000-0000-000000000002'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert test jobs
INSERT INTO public.jobs (
    id,
    title,
    description,
    client_id,
    created_by,
    team_id,
    status,
    scheduled_at
) VALUES 
    (
        '30000000-0000-0000-0000-000000000001',
        'Kitchen Socket Installation',
        'Install 4 new double sockets in kitchen for appliances. Customer wants USB charging points included.',
        '20000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000002',
        '10000000-0000-0000-0000-000000000001',
        'quoted',
        NOW() + INTERVAL '3 days'
    ),
    (
        '30000000-0000-0000-0000-000000000002',
        'Pub Electrical Maintenance',
        'Annual electrical inspection and testing. Replace faulty circuit breakers in main panel.',
        '20000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000002',
        '10000000-0000-0000-0000-000000000001',
        'in_progress',
        NOW() + INTERVAL '1 day'
    ),
    (
        '30000000-0000-0000-0000-000000000003',
        'New Build House Wiring',
        'Complete electrical installation for 3-bedroom new build. Include outdoor lighting and electric car charging point.',
        '20000000-0000-0000-0000-000000000003',
        '00000000-0000-0000-0000-000000000002',
        '10000000-0000-0000-0000-000000000001',
        'new',
        NOW() + INTERVAL '2 weeks'
    ),
    (
        '30000000-0000-0000-0000-000000000004',
        'Garden Lighting Setup',
        'Install outdoor garden lighting with smart controls. 8 LED spotlights and pathway lighting.',
        '20000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000002',
        NULL,
        'completed',
        NOW() - INTERVAL '1 week'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert test job notes
INSERT INTO public.job_notes (
    job_id,
    author_id,
    message,
    message_type
) VALUES 
    (
        '30000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000002',
        'Met with Sarah to discuss socket placement. She wants them 1.2m from floor level.',
        'text'
    ),
    (
        '30000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000002',
        'Quote sent to client for £320 including materials and labor.',
        'system'
    ),
    (
        '30000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000003',
        'Arrived at pub 9am. Found 3 faulty circuit breakers in main panel.',
        'text'
    ),
    (
        '30000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000002',
        'Status changed from new → in_progress',
        'system'
    ),
    (
        '30000000-0000-0000-0000-000000000004',
        '00000000-0000-0000-0000-000000000002',
        'Job completed successfully. All lights tested and working. Customer very happy!',
        'text'
    )
ON CONFLICT DO NOTHING;

-- Insert test client notes
INSERT INTO public.client_notes (
    client_id,
    author_id,
    message
) VALUES 
    (
        '20000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000002',
        'Sarah mentioned she might need outdoor lighting in spring. Follow up in March.'
    ),
    (
        '20000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000002',
        'Manager prefers early morning appointments before pub opens at 11am.'
    )
ON CONFLICT DO NOTHING;

-- Insert test quotes
INSERT INTO public.quotes (
    id,
    job_id,
    amount,
    details,
    status,
    created_by
) VALUES 
    (
        '40000000-0000-0000-0000-000000000001',
        '30000000-0000-0000-0000-000000000001',
        320.00,
        '4x double sockets with USB points £240, Labour £80',
        'sent',
        '00000000-0000-0000-0000-000000000002'
    ),
    (
        '40000000-0000-0000-0000-000000000002',
        '30000000-0000-0000-0000-000000000003',
        4500.00,
        'Full house wiring £3000, Outdoor lighting £800, EV charging point £700',
        'draft',
        '00000000-0000-0000-0000-000000000002'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert test invoices
INSERT INTO public.invoices (
    id,
    job_id,
    quote_id,
    amount,
    tax,
    details,
    due_date,
    status,
    created_by
) VALUES 
    (
        '50000000-0000-0000-0000-000000000001',
        '30000000-0000-0000-0000-000000000004',
        NULL,
        480.00,
        80.00,
        'Garden lighting installation - 8 LED spots, pathway lights, smart controls',
        NOW() + INTERVAL '30 days',
        'paid',
        '00000000-0000-0000-0000-000000000002'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert test contract
INSERT INTO public.contracts (
    id,
    job_id,
    terms,
    created_by,
    signed_at
) VALUES 
    (
        '60000000-0000-0000-0000-000000000001',
        '30000000-0000-0000-0000-000000000003',
        'Electrical Installation Contract

This contract is between Sparky Electrics and David & Emma Wilson for the complete electrical installation of a 3-bedroom new build property.

Scope of Work:
- Complete house wiring to BS 7671 standards
- Installation of consumer unit with RCD protection
- Socket outlets, lighting circuits, and switches throughout
- Outdoor garden lighting system with smart controls
- Electric vehicle charging point installation

Total Contract Value: £4,500.00
Payment Terms: 50% deposit, 50% on completion
Completion Date: Within 4 weeks of start date

All work guaranteed for 12 months and compliant with UK electrical regulations.',
        '00000000-0000-0000-0000-000000000002',
        NULL
    )
ON CONFLICT (id) DO NOTHING;

-- Insert test notifications
INSERT INTO public.notifications (
    user_id,
    type,
    title,
    message,
    link,
    is_read
) VALUES 
    (
        '00000000-0000-0000-0000-000000000002',
        'quote_update',
        'Quote Accepted',
        'Sarah Thompson has accepted your quote for Kitchen Socket Installation (£320.00)',
        '/dashboard/jobs/30000000-0000-0000-0000-000000000001',
        false
    ),
    (
        '00000000-0000-0000-0000-000000000003',
        'job_assigned',
        'New Job Assigned',
        'You have been assigned to work on Pub Electrical Maintenance',
        '/dashboard/jobs/30000000-0000-0000-0000-000000000002',
        true
    )
ON CONFLICT DO NOTHING;

-- Insert test system settings
INSERT INTO public.system_settings (key, value, description) VALUES 
    ('max_jobs_per_user', '100', 'Maximum number of active jobs per user'),
    ('max_clients_per_user', '200', 'Maximum number of clients per user'),
    ('enable_ai_features', 'true', 'Enable Ask Dex AI features'),
    ('enable_team_module', 'true', 'Enable team collaboration features'),
    ('enable_billing_module', 'true', 'Enable quotes, invoices, and contracts'),
    ('default_currency', '"GBP"', 'Default currency for quotes and invoices'),
    ('max_file_upload_size', '10485760', 'Maximum file upload size in bytes (10MB)')
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Add some audit log entries
INSERT INTO public.audit_logs (
    actor_id,
    action,
    target_type,
    target_id,
    details
) VALUES 
    (
        '00000000-0000-0000-0000-000000000002',
        'job_created',
        'job',
        '30000000-0000-0000-0000-000000000001',
        '{"job_title": "Kitchen Socket Installation", "client_name": "Sarah Thompson"}'
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        'quote_sent',
        'quote',
        '40000000-0000-0000-0000-000000000001',
        '{"amount": 320.00, "job_title": "Kitchen Socket Installation"}'
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        'invoice_paid',
        'invoice',
        '50000000-0000-0000-0000-000000000001',
        '{"amount": 480.00, "job_title": "Garden Lighting Setup"}'
    )
ON CONFLICT DO NOTHING; 