-- =============================================
-- Migration: 006_create_documents.sql
-- Description: Create quotes, invoices, and contracts tables
-- Created: 2025-01-17
-- =============================================

-- Create quotes table
CREATE TABLE IF NOT EXISTS public.quotes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  details TEXT,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'accepted', 'rejected')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL
);

-- Create invoices table
CREATE TABLE IF NOT EXISTS public.invoices (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE NOT NULL,
  quote_id UUID REFERENCES public.quotes(id) ON DELETE SET NULL,
  amount DECIMAL(10, 2) NOT NULL,
  tax DECIMAL(10, 2) DEFAULT 0,
  details TEXT,
  due_date DATE,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid', 'overdue')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL
);

-- Create contracts table
CREATE TABLE IF NOT EXISTS public.contracts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE NOT NULL,
  terms TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  signed_at TIMESTAMP WITH TIME ZONE
);

-- Create documents table for file references (PDFs, images)
CREATE TABLE IF NOT EXISTS public.documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  document_type TEXT CHECK (document_type IN ('quote_pdf', 'invoice_pdf', 'contract_pdf', 'image', 'other')),
  related_id UUID, -- Can reference quotes.id, invoices.id, contracts.id, jobs.id, etc.
  related_type TEXT, -- 'quote', 'invoice', 'contract', 'job', 'client'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL
);

-- Add updated_at triggers
CREATE TRIGGER update_quotes_updated_at
    BEFORE UPDATE ON public.quotes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at
    BEFORE UPDATE ON public.invoices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contracts_updated_at
    BEFORE UPDATE ON public.contracts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_quotes_job_id ON public.quotes(job_id);
CREATE INDEX IF NOT EXISTS idx_quotes_created_by ON public.quotes(created_by);
CREATE INDEX IF NOT EXISTS idx_quotes_status ON public.quotes(status);
CREATE INDEX IF NOT EXISTS idx_quotes_created_at ON public.quotes(created_at);

CREATE INDEX IF NOT EXISTS idx_invoices_job_id ON public.invoices(job_id);
CREATE INDEX IF NOT EXISTS idx_invoices_quote_id ON public.invoices(quote_id);
CREATE INDEX IF NOT EXISTS idx_invoices_created_by ON public.invoices(created_by);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON public.invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON public.invoices(due_date);

CREATE INDEX IF NOT EXISTS idx_contracts_job_id ON public.contracts(job_id);
CREATE INDEX IF NOT EXISTS idx_contracts_created_by ON public.contracts(created_by);
CREATE INDEX IF NOT EXISTS idx_contracts_signed_at ON public.contracts(signed_at);

CREATE INDEX IF NOT EXISTS idx_documents_related_id ON public.documents(related_id);
CREATE INDEX IF NOT EXISTS idx_documents_related_type ON public.documents(related_type);
CREATE INDEX IF NOT EXISTS idx_documents_document_type ON public.documents(document_type);
CREATE INDEX IF NOT EXISTS idx_documents_created_by ON public.documents(created_by);

-- Add comments for documentation
COMMENT ON TABLE public.quotes IS 'Quotes generated for jobs';
COMMENT ON COLUMN public.quotes.amount IS 'Quote amount in GBP';
COMMENT ON COLUMN public.quotes.status IS 'Quote status: draft, sent, accepted, rejected';

COMMENT ON TABLE public.invoices IS 'Invoices generated for jobs';
COMMENT ON COLUMN public.invoices.amount IS 'Invoice amount before tax in GBP';
COMMENT ON COLUMN public.invoices.tax IS 'Tax amount (usually 20% VAT) in GBP';
COMMENT ON COLUMN public.invoices.quote_id IS 'Reference to quote if invoice is based on quote';

COMMENT ON TABLE public.contracts IS 'Contracts for jobs';
COMMENT ON COLUMN public.contracts.terms IS 'Contract terms and conditions';
COMMENT ON COLUMN public.contracts.signed_at IS 'When contract was signed (null if unsigned)';

COMMENT ON TABLE public.documents IS 'File storage references for PDFs and other documents';
COMMENT ON COLUMN public.documents.related_id IS 'ID of related entity (quote, invoice, contract, job)';
COMMENT ON COLUMN public.documents.related_type IS 'Type of related entity'; 