-- =============================================
-- Migration: 014_update_team_settings.sql
-- Description: Adds default permissions to the teams table and simplifies roles.
-- Created: 2025-01-30
-- =============================================

-- Add default permission columns to the teams table
ALTER TABLE public.teams
  ADD COLUMN IF NOT EXISTS "permission_manage_jobs" BOOLEAN NOT NULL DEFAULT true,
  ADD COLUMN IF NOT EXISTS "permission_manage_clients" BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN IF NOT EXISTS "permission_view_financials" BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN IF NOT EXISTS "permission_generate_documents" BOOLEAN NOT NULL DEFAULT false;

-- Drop the old check constraint for default_job_visibility
ALTER TABLE public.teams
  DROP CONSTRAINT IF EXISTS teams_default_job_visibility_check;

-- Update existing values to conform to the new options, then add the new constraint
UPDATE public.teams
SET default_job_visibility = 'entire_team'
WHERE default_job_visibility = 'team_only' OR default_job_visibility = 'public';

ALTER TABLE public.teams
  ADD CONSTRAINT teams_default_job_visibility_check
  CHECK (default_job_visibility IN ('owner_only', 'entire_team', 'assigned_only'));

-- Drop the old check constraint for team member roles
ALTER TABLE public.team_members
  DROP CONSTRAINT IF EXISTS team_members_role_check;

-- Update existing 'manager' roles to 'member' to simplify
UPDATE public.team_members
SET role = 'member'
WHERE role = 'manager';

-- Add the new simplified role constraint
ALTER TABLE public.team_members
  ADD CONSTRAINT team_members_role_check
  CHECK (role IN ('owner', 'member'));

-- Add comments for new columns
COMMENT ON COLUMN public.teams.permission_manage_jobs IS 'Default permission for new members to manage jobs.';
COMMENT ON COLUMN public.teams.permission_manage_clients IS 'Default permission for new members to manage clients.';
COMMENT ON COLUMN public.teams.permission_view_financials IS 'Default permission for new members to view financial data.';
COMMENT ON COLUMN public.teams.permission_generate_documents IS 'Default permission for new members to generate documents.'; 