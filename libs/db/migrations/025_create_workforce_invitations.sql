-- =============================================
-- Migration: 025_create_workforce_invitations.sql
-- Description: Create comprehensive workforce invitation system
-- Created: 2025-08-08
-- =============================================

-- Create workforce_invitations table
CREATE TABLE public.workforce_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  workforce_id UUID NOT NULL REFERENCES public.workforce(id) ON DELETE CASCADE,
  invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  role VARCHAR(20) NOT NULL DEFAULT 'member',
  
  -- Invitation status and lifecycle
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  
  -- Permissions to be granted when accepted
  can_manage_jobs BOOLEAN NOT NULL DEFAULT false,
  can_manage_clients BOOLEAN NOT NULL DEFAULT false,
  can_manage_invoices BOOLEAN NOT NULL DEFAULT false,
  can_manage_quotes BOOLEAN NOT NULL DEFAULT false,
  can_view_reports BOOLEAN NOT NULL DEFAULT false,
  can_manage_team BOOLEAN NOT NULL DEFAULT false,
  
  -- Invitation tracking
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  accepted_at TIMESTAMP WITH TIME ZONE,
  declined_at TIMESTAMP WITH TIME ZONE,
  accepted_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Personalization
  personal_message TEXT,
  invitation_method VARCHAR(20) NOT NULL DEFAULT 'email',
  
  -- Audit trail
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Constraints
  CONSTRAINT workforce_invitations_role_check 
    CHECK (role IN ('owner', 'manager', 'member')),
  CONSTRAINT workforce_invitations_status_check 
    CHECK (status IN ('pending', 'accepted', 'declined', 'expired', 'cancelled')),
  CONSTRAINT workforce_invitations_method_check 
    CHECK (invitation_method IN ('email', 'whatsapp', 'sms')),
  CONSTRAINT workforce_invitations_unique_pending 
    UNIQUE (workforce_id, email, status) DEFERRABLE INITIALLY DEFERRED
);

-- Create indexes for performance
CREATE INDEX idx_workforce_invitations_workforce_id ON public.workforce_invitations(workforce_id);
CREATE INDEX idx_workforce_invitations_email ON public.workforce_invitations(email);
CREATE INDEX idx_workforce_invitations_token ON public.workforce_invitations(token);
CREATE INDEX idx_workforce_invitations_status ON public.workforce_invitations(status);
CREATE INDEX idx_workforce_invitations_expires_at ON public.workforce_invitations(expires_at);
CREATE INDEX idx_workforce_invitations_invited_by ON public.workforce_invitations(invited_by);

-- Create invitation audit log table
CREATE TABLE public.workforce_invitation_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  invitation_id UUID NOT NULL REFERENCES public.workforce_invitations(id) ON DELETE CASCADE,
  action VARCHAR(50) NOT NULL,
  details JSONB,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  CONSTRAINT workforce_invitation_logs_action_check 
    CHECK (action IN ('created', 'sent', 'resent', 'viewed', 'accepted', 'declined', 'expired', 'cancelled', 'reminded'))
);

CREATE INDEX idx_workforce_invitation_logs_invitation_id ON public.workforce_invitation_logs(invitation_id);
CREATE INDEX idx_workforce_invitation_logs_action ON public.workforce_invitation_logs(action);
CREATE INDEX idx_workforce_invitation_logs_created_at ON public.workforce_invitation_logs(created_at);

-- Add comments for documentation
COMMENT ON TABLE public.workforce_invitations IS 'Team invitations with advanced tracking and personalization';
COMMENT ON COLUMN public.workforce_invitations.token IS 'Unique secure token for invitation acceptance';
COMMENT ON COLUMN public.workforce_invitations.personal_message IS 'Custom message from inviter';
COMMENT ON COLUMN public.workforce_invitations.invitation_method IS 'Method used to send invitation (email, whatsapp, sms)';

COMMENT ON TABLE public.workforce_invitation_logs IS 'Audit trail for invitation lifecycle events';
COMMENT ON COLUMN public.workforce_invitation_logs.details IS 'Additional context and metadata for the action';

-- Function to automatically expire invitations
CREATE OR REPLACE FUNCTION expire_old_invitations()
RETURNS void AS $$
BEGIN
  -- Update pending invitations that have expired
  UPDATE public.workforce_invitations 
  SET status = 'expired', updated_at = now()
  WHERE status = 'pending' AND expires_at < now();
  
  -- Log expiration events
  INSERT INTO public.workforce_invitation_logs (invitation_id, action, details)
  SELECT id, 'expired', jsonb_build_object('expired_at', now())
  FROM public.workforce_invitations 
  WHERE status = 'expired' AND updated_at = now();
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_workforce_invitations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_workforce_invitations_updated_at
  BEFORE UPDATE ON public.workforce_invitations
  FOR EACH ROW
  EXECUTE FUNCTION update_workforce_invitations_updated_at();

-- Enable RLS
ALTER TABLE public.workforce_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workforce_invitation_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for workforce_invitations
CREATE POLICY "Users can view invitations for their teams" ON public.workforce_invitations
  FOR SELECT USING (
    workforce_id IN (
      SELECT w.id FROM public.workforce w 
      WHERE w.owner_id = auth.uid()
      UNION
      SELECT wm.workforce_id FROM public.workforce_members wm 
      WHERE wm.user_id = auth.uid() AND wm.can_manage_team = true
    )
  );

CREATE POLICY "Users can view their own invitations by email" ON public.workforce_invitations
  FOR SELECT USING (
    email = (SELECT email FROM auth.users WHERE id = auth.uid())
  );

CREATE POLICY "Team managers can create invitations" ON public.workforce_invitations
  FOR INSERT WITH CHECK (
    workforce_id IN (
      SELECT w.id FROM public.workforce w 
      WHERE w.owner_id = auth.uid()
      UNION
      SELECT wm.workforce_id FROM public.workforce_members wm 
      WHERE wm.user_id = auth.uid() AND wm.can_manage_team = true
    )
    AND invited_by = auth.uid()
  );

CREATE POLICY "Team managers can update invitations" ON public.workforce_invitations
  FOR UPDATE USING (
    workforce_id IN (
      SELECT w.id FROM public.workforce w 
      WHERE w.owner_id = auth.uid()
      UNION
      SELECT wm.workforce_id FROM public.workforce_members wm 
      WHERE wm.user_id = auth.uid() AND wm.can_manage_team = true
    )
  );

-- RLS Policies for workforce_invitation_logs
CREATE POLICY "Users can view logs for their team invitations" ON public.workforce_invitation_logs
  FOR SELECT USING (
    invitation_id IN (
      SELECT wi.id FROM public.workforce_invitations wi
      JOIN public.workforce w ON wi.workforce_id = w.id
      WHERE w.owner_id = auth.uid()
      UNION
      SELECT wi.id FROM public.workforce_invitations wi
      JOIN public.workforce_members wm ON wi.workforce_id = wm.workforce_id
      WHERE wm.user_id = auth.uid() AND wm.can_manage_team = true
    )
  );

CREATE POLICY "System can insert invitation logs" ON public.workforce_invitation_logs
  FOR INSERT WITH CHECK (true);