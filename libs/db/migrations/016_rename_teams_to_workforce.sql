-- =============================================
-- Migration: 016_rename_teams_to_workforce.sql
-- Description: Rename teams tables to workforce to better reflect the purpose
-- Created: 2025-01-30
-- =============================================

-- Rename the teams table to workforce
ALTER TABLE public.teams RENAME TO workforce;

-- Rename the team_members table to workforce_members
ALTER TABLE public.team_members RENAME TO workforce_members;

-- Update the foreign key column names to match
ALTER TABLE public.workforce_members RENAME COLUMN team_id TO workforce_id;

-- Update the foreign key references in other tables
ALTER TABLE public.jobs RENAME COLUMN team_id TO workforce_id;

-- Update the foreign key constraints
ALTER TABLE public.workforce_members 
  DROP CONSTRAINT IF EXISTS team_members_team_id_fkey,
  ADD CONSTRAINT workforce_members_workforce_id_fkey 
    FOREIGN KEY (workforce_id) REFERENCES public.workforce(id) ON DELETE CASCADE;

ALTER TABLE public.jobs 
  DROP CONSTRAINT IF EXISTS jobs_team_id_fkey,
  ADD CONSTRAINT jobs_workforce_id_fkey 
    FOREIGN KEY (workforce_id) REFERENCES public.workforce(id) ON DELETE SET NULL;

-- Update check constraints with new names
ALTER TABLE public.workforce 
  DROP CONSTRAINT IF EXISTS teams_default_job_visibility_check,
  ADD CONSTRAINT workforce_default_job_visibility_check
    CHECK (default_job_visibility IN ('owner_only', 'entire_team', 'assigned_only'));

ALTER TABLE public.workforce_members 
  DROP CONSTRAINT IF EXISTS team_members_role_check,
  ADD CONSTRAINT workforce_members_role_check
    CHECK (role IN ('owner', 'member'));

-- Update index names
DROP INDEX IF EXISTS idx_teams_owner_id;
DROP INDEX IF EXISTS idx_teams_created_at;
DROP INDEX IF EXISTS idx_team_members_user_id;
DROP INDEX IF EXISTS idx_team_members_team_id;
DROP INDEX IF EXISTS idx_team_members_role;

CREATE INDEX IF NOT EXISTS idx_workforce_owner_id ON public.workforce(owner_id);
CREATE INDEX IF NOT EXISTS idx_workforce_created_at ON public.workforce(created_at);
CREATE INDEX IF NOT EXISTS idx_workforce_members_user_id ON public.workforce_members(user_id);
CREATE INDEX IF NOT EXISTS idx_workforce_members_workforce_id ON public.workforce_members(workforce_id);
CREATE INDEX IF NOT EXISTS idx_workforce_members_role ON public.workforce_members(role);

-- Update comments
COMMENT ON TABLE public.workforce IS 'Workforce for organizing tradespeople and managing their staff/subcontractors';
COMMENT ON COLUMN public.workforce.owner_id IS 'User who owns the business and manages the workforce';

COMMENT ON TABLE public.workforce_members IS 'Many-to-many relationship between users and workforce';
COMMENT ON COLUMN public.workforce_members.workforce_id IS 'Reference to the workforce'; 