-- =============================================
-- Migration: 021_create_system_defaults.sql
-- Description: Create system_defaults table and seed AI default limits
-- Created: 2025-07-05
-- =============================================

-- Create table for storing system-wide key/value JSON settings
CREATE TABLE IF NOT EXISTS public.system_defaults (
  key TEXT PRIMARY KEY,
  value JSONB NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE public.system_defaults IS 'Key/value store for system-wide default settings (JSON format)';

-- Seed default AI limits if not present
INSERT INTO public.system_defaults (key, value)
VALUES (
  'ai_limits',
  '{"hour":20,"day":150,"week":150,"month":500}'::jsonb
) ON CONFLICT (key) DO NOTHING; 