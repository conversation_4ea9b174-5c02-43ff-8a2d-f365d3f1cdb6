-- =============================================
-- Migration: 009_apply_rls_policies.sql
-- Description: Apply comprehensive RLS policies for all tables
-- Created: 2025-01-17
-- =============================================

-- USERS TABLE POLICIES
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id OR is_super_admin(auth.uid()));

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id OR is_super_admin(auth.uid()));

CREATE POLICY "Super admins can insert users" ON public.users
    FOR INSERT WITH CHECK (is_super_admin(auth.uid()));

CREATE POLICY "Super admins can delete users" ON public.users
    FOR DELETE USING (is_super_admin(auth.uid()));

-- TEAMS TABLE POLICIES  
CREATE POLICY "Team members can view their teams" ON public.teams
    FOR SELECT USING (
        is_super_admin(auth.uid()) OR 
        is_team_member(auth.uid(), id)
    );

CREATE POLICY "Team owners can update their teams" ON public.teams
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        owner_id = auth.uid()
    );

CREATE POLICY "Authenticated users can create teams" ON public.teams
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Team owners and super admins can delete teams" ON public.teams
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        owner_id = auth.uid()
    );

-- TEAM_MEMBERS TABLE POLICIES
CREATE POLICY "Team members can view team membership" ON public.team_members
    FOR SELECT USING (
        is_super_admin(auth.uid()) OR 
        user_id = auth.uid() OR
        is_team_member(auth.uid(), team_id)
    );

CREATE POLICY "Team owners can manage membership" ON public.team_members
    FOR ALL USING (
        is_super_admin(auth.uid()) OR
        EXISTS (SELECT 1 FROM public.teams WHERE id = team_id AND owner_id = auth.uid())
    );

-- CLIENTS TABLE POLICIES
CREATE POLICY "Users can view their clients and team clients" ON public.clients
    FOR SELECT USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.jobs j 
            JOIN public.teams t ON j.team_id = t.id 
            WHERE j.client_id = clients.id AND is_team_member(auth.uid(), t.id)
        )
    );

CREATE POLICY "Users can create clients" ON public.clients
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their clients" ON public.clients
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

CREATE POLICY "Users can delete their clients" ON public.clients
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

-- JOBS TABLE POLICIES
CREATE POLICY "Users can view accessible jobs" ON public.jobs
    FOR SELECT USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid() OR
        (team_id IS NOT NULL AND is_team_member(auth.uid(), team_id))
    );

CREATE POLICY "Users can create jobs" ON public.jobs
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update accessible jobs" ON public.jobs
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid() OR
        (team_id IS NOT NULL AND is_team_member(auth.uid(), team_id))
    );

CREATE POLICY "Users can delete their jobs" ON public.jobs
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

-- JOB_NOTES TABLE POLICIES
CREATE POLICY "Users can view notes for accessible jobs" ON public.job_notes
    FOR SELECT USING (can_access_job(auth.uid(), job_id));

CREATE POLICY "Users can create notes for accessible jobs" ON public.job_notes
    FOR INSERT WITH CHECK (
        auth.uid() = author_id AND 
        can_access_job(auth.uid(), job_id)
    );

CREATE POLICY "Users can update their own notes" ON public.job_notes
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        author_id = auth.uid()
    );

CREATE POLICY "Users can delete their own notes" ON public.job_notes
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        author_id = auth.uid()
    );

-- CLIENT_NOTES TABLE POLICIES
CREATE POLICY "Users can view client notes for accessible clients" ON public.client_notes
    FOR SELECT USING (
        is_super_admin(auth.uid()) OR
        EXISTS (
            SELECT 1 FROM public.clients c 
            WHERE c.id = client_id AND (
                c.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM public.jobs j 
                    JOIN public.teams t ON j.team_id = t.id 
                    WHERE j.client_id = c.id AND is_team_member(auth.uid(), t.id)
                )
            )
        )
    );

CREATE POLICY "Users can create client notes for accessible clients" ON public.client_notes
    FOR INSERT WITH CHECK (
        auth.uid() = author_id AND
        EXISTS (
            SELECT 1 FROM public.clients c 
            WHERE c.id = client_id AND (
                c.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM public.jobs j 
                    JOIN public.teams t ON j.team_id = t.id 
                    WHERE j.client_id = c.id AND is_team_member(auth.uid(), t.id)
                )
            )
        )
    );

CREATE POLICY "Users can update their own client notes" ON public.client_notes
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        author_id = auth.uid()
    );

CREATE POLICY "Users can delete their own client notes" ON public.client_notes
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        author_id = auth.uid()
    );

-- QUOTES TABLE POLICIES
CREATE POLICY "Users can view quotes for accessible jobs" ON public.quotes
    FOR SELECT USING (can_access_job(auth.uid(), job_id));

CREATE POLICY "Users can create quotes for accessible jobs" ON public.quotes
    FOR INSERT WITH CHECK (
        auth.uid() = created_by AND 
        can_access_job(auth.uid(), job_id)
    );

CREATE POLICY "Users can update their quotes" ON public.quotes
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

CREATE POLICY "Users can delete their quotes" ON public.quotes
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

-- INVOICES TABLE POLICIES
CREATE POLICY "Users can view invoices for accessible jobs" ON public.invoices
    FOR SELECT USING (can_access_job(auth.uid(), job_id));

CREATE POLICY "Users can create invoices for accessible jobs" ON public.invoices
    FOR INSERT WITH CHECK (
        auth.uid() = created_by AND 
        can_access_job(auth.uid(), job_id)
    );

CREATE POLICY "Users can update their invoices" ON public.invoices
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

CREATE POLICY "Users can delete their invoices" ON public.invoices
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

-- CONTRACTS TABLE POLICIES
CREATE POLICY "Users can view contracts for accessible jobs" ON public.contracts
    FOR SELECT USING (can_access_job(auth.uid(), job_id));

CREATE POLICY "Users can create contracts for accessible jobs" ON public.contracts
    FOR INSERT WITH CHECK (
        auth.uid() = created_by AND 
        can_access_job(auth.uid(), job_id)
    );

CREATE POLICY "Users can update their contracts" ON public.contracts
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

CREATE POLICY "Users can delete their contracts" ON public.contracts
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

-- DOCUMENTS TABLE POLICIES
CREATE POLICY "Users can view documents for accessible items" ON public.documents
    FOR SELECT USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid() OR
        (related_type = 'job' AND can_access_job(auth.uid(), related_id))
    );

CREATE POLICY "Users can create documents" ON public.documents
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their documents" ON public.documents
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

CREATE POLICY "Users can delete their documents" ON public.documents
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        created_by = auth.uid()
    );

-- NOTIFICATIONS TABLE POLICIES
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (
        is_super_admin(auth.uid()) OR 
        user_id = auth.uid()
    );

CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true); -- Notifications created by system/triggers

CREATE POLICY "Users can update their notifications" ON public.notifications
    FOR UPDATE USING (
        is_super_admin(auth.uid()) OR 
        user_id = auth.uid()
    );

CREATE POLICY "Users can delete their notifications" ON public.notifications
    FOR DELETE USING (
        is_super_admin(auth.uid()) OR 
        user_id = auth.uid()
    );

-- AUDIT_LOGS TABLE POLICIES (Super admin only)
CREATE POLICY "Super admins can view all audit logs" ON public.audit_logs
    FOR SELECT USING (is_super_admin(auth.uid()));

CREATE POLICY "System can create audit logs" ON public.audit_logs
    FOR INSERT WITH CHECK (true); -- Created by system/triggers

-- SYSTEM_SETTINGS TABLE POLICIES (Super admin only)
CREATE POLICY "Super admins can manage system settings" ON public.system_settings
    FOR ALL USING (is_super_admin(auth.uid()));

-- TEAM_INVITES TABLE POLICIES
CREATE POLICY "Team owners can manage invites" ON public.team_invites
    FOR ALL USING (
        is_super_admin(auth.uid()) OR
        EXISTS (SELECT 1 FROM public.teams WHERE id = team_id AND owner_id = auth.uid())
    );

CREATE POLICY "Invited users can view their invites" ON public.team_invites
    FOR SELECT USING (
        is_super_admin(auth.uid()) OR
        email = (SELECT email FROM auth.users WHERE id = auth.uid()) OR
        EXISTS (SELECT 1 FROM public.teams WHERE id = team_id AND owner_id = auth.uid())
    ); 