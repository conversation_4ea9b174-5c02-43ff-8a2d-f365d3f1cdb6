-- Add market rate intelligence fields to quotes table
-- Migration: 023_add_market_rate_to_quotes.sql
-- Purpose: Store AI-generated market rate information with quotes

-- Add market rate fields to quotes table
ALTER TABLE quotes ADD COLUMN IF NOT EXISTS market_rate_work_type TEXT;
<PERSON>TER TABLE quotes ADD COLUMN IF NOT EXISTS market_rate_suggestion TEXT;
ALTER TABLE quotes ADD COLUMN IF NOT EXISTS market_rate_estimated_range TEXT;

-- Remove pricing fields that are no longer needed
ALTER TABLE quotes DROP COLUMN IF EXISTS pricing_type;
ALTER TABLE quotes DROP COLUMN IF EXISTS subtotal;
ALTER TABLE quotes DROP COLUMN IF EXISTS vat_percentage;
ALTER TABLE quotes DROP COLUMN IF EXISTS vat_amount;
ALTER TABLE quotes DROP COLUMN IF EXISTS pricing_description;

-- Update RLS policies if needed (quotes policies should already exist from previous migrations)
-- No additional RLS policies needed as market rate data follows same access patterns as other quote data