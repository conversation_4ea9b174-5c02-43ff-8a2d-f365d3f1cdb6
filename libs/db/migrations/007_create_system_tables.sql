-- =============================================
-- Migration: 007_create_system_tables.sql
-- Description: Create audit_logs, notifications, and system_settings tables
-- Created: 2025-01-17
-- =============================================

-- Create audit_logs table for system activity tracking
CREATE TABLE IF NOT EXISTS public.audit_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  actor_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
  action TEXT NOT NULL,
  target_type TEXT NOT NULL,
  target_id UUID,
  details JSONB,
  ip_address INET,
  user_agent TEXT
);

-- Create notifications table for user notifications
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  link TEXT,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create system_settings table for global configuration
CREATE TABLE IF NOT EXISTS public.system_settings (
  key TEXT PRIMARY KEY,
  value JSONB NOT NULL,
  description TEXT,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES public.users(id) ON DELETE SET NULL
);

-- Create team_invites table for pending team invitations
CREATE TABLE IF NOT EXISTS public.team_invites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
  email TEXT NOT NULL,
  role TEXT DEFAULT 'member' CHECK (role IN ('member', 'manager')),
  token TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL
);

-- Add updated_at trigger to system_settings
CREATE TRIGGER update_system_settings_updated_at
    BEFORE UPDATE ON public.system_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON public.audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_actor_id ON public.audit_logs(actor_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_target_type ON public.audit_logs(target_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_target_id ON public.audit_logs(target_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON public.notifications(type);

-- Compound index for efficient notification queries
CREATE INDEX IF NOT EXISTS idx_notifications_user_read_created ON public.notifications(user_id, is_read, created_at);

CREATE INDEX IF NOT EXISTS idx_team_invites_team_id ON public.team_invites(team_id);
CREATE INDEX IF NOT EXISTS idx_team_invites_email ON public.team_invites(email);
CREATE INDEX IF NOT EXISTS idx_team_invites_token ON public.team_invites(token);
CREATE INDEX IF NOT EXISTS idx_team_invites_expires_at ON public.team_invites(expires_at);

-- Add comments for documentation
COMMENT ON TABLE public.audit_logs IS 'System activity tracking for admin monitoring';
COMMENT ON COLUMN public.audit_logs.actor_id IS 'User who performed the action (null for system actions)';
COMMENT ON COLUMN public.audit_logs.action IS 'Action performed (e.g., "created_job", "deleted_client")';
COMMENT ON COLUMN public.audit_logs.target_type IS 'Type of entity affected (e.g., "job", "client", "user")';
COMMENT ON COLUMN public.audit_logs.target_id IS 'ID of the entity affected';
COMMENT ON COLUMN public.audit_logs.details IS 'Additional details in JSON format';

COMMENT ON TABLE public.notifications IS 'User notifications and alerts';
COMMENT ON COLUMN public.notifications.type IS 'Notification type (e.g., "job_assigned", "quote_accepted")';
COMMENT ON COLUMN public.notifications.link IS 'Optional link to relevant page/resource';

COMMENT ON TABLE public.system_settings IS 'Global system configuration settings';
COMMENT ON COLUMN public.system_settings.key IS 'Setting key (e.g., "max_jobs_per_user")';
COMMENT ON COLUMN public.system_settings.value IS 'Setting value in JSON format';

COMMENT ON TABLE public.team_invites IS 'Pending team invitation tokens';
COMMENT ON COLUMN public.team_invites.token IS 'Unique invitation token for email links';
COMMENT ON COLUMN public.team_invites.expires_at IS 'When the invitation expires'; 