-- Migration: Add manual pricing fields to quotes table
-- Date: 2025-08-05
-- Description: Add support for manual price override with VAT breakdown

-- Add manual pricing columns to quotes table
ALTER TABLE quotes ADD COLUMN IF NOT EXISTS pricing_type TEXT DEFAULT 'manual' 
  CHECK (pricing_type IN ('manual', 'ai_generated'));
ALTER TABLE quotes ADD COLUMN IF NOT EXISTS subtotal DECIMAL(10,2);
ALTER TABLE quotes ADD COLUMN IF NOT EXISTS vat_percentage DECIMAL(5,2);
ALTER TABLE quotes ADD COLUMN IF NOT EXISTS vat_amount DECIMAL(10,2);
ALTER TABLE quotes ADD COLUMN IF NOT EXISTS pricing_description TEXT;

-- Update existing quotes to have pricing_type = 'ai_generated' 
-- since they were created before manual pricing feature
UPDATE quotes SET pricing_type = 'ai_generated' WHERE pricing_type IS NULL;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_quotes_pricing_type ON quotes(pricing_type);

-- Add comments for documentation
COMMENT ON COLUMN quotes.pricing_type IS 'Type of pricing: manual (user entered) or ai_generated (AI suggested)';
COMMENT ON COLUMN quotes.subtotal IS 'Amount before VAT (for manual pricing)';
COMMENT ON COLUMN quotes.vat_percentage IS 'VAT percentage rate (e.g., 20.00 for 20%)';
COMMENT ON COLUMN quotes.vat_amount IS 'Calculated VAT amount in GBP';
COMMENT ON COLUMN quotes.pricing_description IS 'Custom description for manual pricing breakdown';