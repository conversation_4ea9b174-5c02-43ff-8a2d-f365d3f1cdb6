-- =============================================
-- Migration: 019_create_user_limits.sql
-- Description: Per-user job creation limits (weekly / monthly)
-- Created: 2025-07-02
-- =============================================

-- Create user_limits table to control resource quotas
CREATE TABLE IF NOT EXISTS public.user_limits (
  user_id UUID PRIMARY KEY REFERENCES public.users(id) ON DELETE CASCADE,
  jobs_per_week INTEGER CHECK (jobs_per_week > 0), -- NULL means unlimited
  jobs_per_month INTEGER CHECK (jobs_per_month > 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add updated_at trigger
CREATE TRIGGER update_user_limits_updated_at
    BEFORE UPDATE ON public.user_limits
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Index for fast look-ups
CREATE INDEX IF NOT EXISTS idx_user_limits_user_id ON public.user_limits(user_id);

-- Comments for documentation
COMMENT ON TABLE public.user_limits IS 'Per-user quotas such as job creation limits';
COMMENT ON COLUMN public.user_limits.jobs_per_week IS 'Maximum jobs a user can create per rolling 7-day window (NULL = unlimited)';
COMMENT ON COLUMN public.user_limits.jobs_per_month IS 'Maximum jobs a user can create per calendar month (NULL = unlimited)'; 