-- Migration: Add individual permission columns to team_members table
-- This migration adds granular permission control for team members
-- Applied via Supabase MCP tools on [current date]

-- Add individual permission columns to team_members table
ALTER TABLE team_members ADD COLUMN IF NOT EXISTS can_manage_jobs boolean DEFAULT false;
ALTER TABLE team_members ADD COLUMN IF NOT EXISTS can_manage_clients boolean DEFAULT false;
ALTER TABLE team_members ADD COLUMN IF NOT EXISTS can_manage_invoices boolean DEFAULT false;
ALTER TABLE team_members ADD COLUMN IF NOT EXISTS can_manage_quotes boolean DEFAULT false;
ALTER TABLE team_members ADD COLUMN IF NOT EXISTS can_view_reports boolean DEFAULT false;
ALTER TABLE team_members ADD COLUMN IF NOT EXISTS can_manage_team boolean DEFAULT false;

-- Update existing owners to have all permissions
UPDATE team_members 
SET 
  can_manage_jobs = true,
  can_manage_clients = true,
  can_manage_invoices = true,
  can_manage_quotes = true,
  can_view_reports = true,
  can_manage_team = true
WHERE user_id IN (
  SELECT owner_id FROM teams WHERE teams.id = team_members.team_id
);

-- Add updated_at column to team_members for tracking permission changes
ALTER TABLE team_members ADD COLUMN IF NOT EXISTS updated_at timestamp with time zone DEFAULT now();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for team_members table
DROP TRIGGER IF EXISTS update_team_members_updated_at ON team_members;
CREATE TRIGGER update_team_members_updated_at
    BEFORE UPDATE ON team_members
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON COLUMN team_members.can_manage_jobs IS 'Permission to create, edit, and delete jobs';
COMMENT ON COLUMN team_members.can_manage_clients IS 'Permission to add, edit, and delete client information';
COMMENT ON COLUMN team_members.can_manage_invoices IS 'Permission to create and manage invoices';
COMMENT ON COLUMN team_members.can_manage_quotes IS 'Permission to create and manage quotes';
COMMENT ON COLUMN team_members.can_view_reports IS 'Permission to access financial reports and analytics';
COMMENT ON COLUMN team_members.can_manage_team IS 'Permission to add/remove members and change permissions'; 