-- =============================================
-- Migration: 002_create_teams.sql
-- Description: Create teams table for team organization and collaboration
-- Created: 2025-01-17
-- =============================================

-- Create teams table
CREATE TABLE IF NOT EXISTS public.teams (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  owner_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  allow_invites B<PERSON><PERSON>EAN DEFAULT false,
  require_job_approval BOOLEAN DEFAULT false,
  auto_assign_jobs BOOLEAN DEFAULT false,
  default_job_visibility TEXT DEFAULT 'team_only' CHECK (default_job_visibility IN ('owner_only', 'team_only', 'public')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create team_members junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS public.team_members (
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'member' CHECK (role IN ('member', 'manager', 'owner')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id, team_id)
);

-- Add updated_at trigger to teams
CREATE TRIGGER update_teams_updated_at
    BEFORE UPDATE ON public.teams
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_teams_owner_id ON public.teams(owner_id);
CREATE INDEX IF NOT EXISTS idx_teams_created_at ON public.teams(created_at);
CREATE INDEX IF NOT EXISTS idx_team_members_user_id ON public.team_members(user_id);
CREATE INDEX IF NOT EXISTS idx_team_members_team_id ON public.team_members(team_id);
CREATE INDEX IF NOT EXISTS idx_team_members_role ON public.team_members(role);

-- Add comments for documentation
COMMENT ON TABLE public.teams IS 'Teams for organizing tradespeople and managing collaborative work';
COMMENT ON COLUMN public.teams.owner_id IS 'User who created and owns the team';
COMMENT ON COLUMN public.teams.allow_invites IS 'Whether team members can invite others';
COMMENT ON COLUMN public.teams.require_job_approval IS 'Whether job changes need owner approval';
COMMENT ON COLUMN public.teams.auto_assign_jobs IS 'Whether new jobs auto-assign to team';
COMMENT ON COLUMN public.teams.default_job_visibility IS 'Default visibility for team jobs';

COMMENT ON TABLE public.team_members IS 'Many-to-many relationship between users and teams';
COMMENT ON COLUMN public.team_members.role IS 'Role within the team: member, manager, owner'; 