{"name": "@deskbelt/ui", "version": "0.1.0", "description": "DeskBelt UI Component Library", "main": "src/index.ts", "module": "src/index.ts", "types": "src/index.ts", "files": ["src/**/*"], "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "typescript": "^5.3.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "keywords": ["react", "components", "ui", "design-system"], "author": "DeskBelt Team", "license": "MIT"}