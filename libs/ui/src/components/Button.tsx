import React from 'react';
import { cn } from '../utils/cn';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'link' | 'gradient';
type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
type ButtonTheme = 'jobs' | 'clients' | 'ai' | 'default';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  context?: 'default' | 'jobs' | 'clients' | 'ai' | 'schedule' | 'invoicing' | 'analytics' | 'team' | 'ai';
  theme?: ButtonTheme;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  icon?: React.ReactNode;  // Alias for leftIcon
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  children: React.ReactNode;
  /** Accessible label for screen readers when content is not descriptive enough */
  'aria-label'?: string;
  /** Description of the button's current state for screen readers */
  'aria-describedby'?: string;
  /** Whether the button controls an expanded/collapsed element */
  'aria-expanded'?: boolean;
  /** ID of element(s) controlled by this button */
  'aria-controls'?: string;
  /** Whether the button press has a popup/modal result */
  'aria-haspopup'?: boolean | 'menu' | 'listbox' | 'tree' | 'grid' | 'dialog';
}

const buttonVariants = {
  primary: {
    jobs: 'bg-jobs-700 hover:bg-jobs-800 focus:ring-jobs-500 text-white border-transparent',
    clients: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white border-transparent',
    ai: 'bg-ai-600 hover:bg-ai-700 focus:ring-ai-500 text-white border-transparent',
    default: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white border-transparent',
  },
  secondary: {
    jobs: 'bg-transparent hover:bg-jobs-50 focus:ring-jobs-500 text-jobs-700 border-jobs-700',
    clients: 'bg-transparent hover:bg-clients-50 focus:ring-clients-500 text-clients-700 border-clients-700',
    ai: 'bg-transparent hover:bg-ai-50 focus:ring-ai-500 text-ai-700 border-ai-600',
    default: 'bg-transparent hover:bg-primary-50 focus:ring-primary-500 text-primary-700 border-primary-700',
  },
  outline: {
    jobs: 'bg-transparent hover:bg-gray-50 focus:ring-jobs-500 text-gray-700 border-gray-300',
    clients: 'bg-transparent hover:bg-gray-50 focus:ring-clients-500 text-gray-700 border-gray-300',
    ai: 'bg-transparent hover:bg-gray-50 focus:ring-ai-500 text-gray-700 border-gray-300',
    default: 'bg-transparent hover:bg-gray-50 focus:ring-primary-500 text-gray-700 border-gray-300',
  },
  ghost: {
    jobs: 'bg-transparent hover:bg-jobs-50 focus:ring-jobs-500 text-jobs-700 border-transparent',
    clients: 'bg-transparent hover:bg-clients-50 focus:ring-clients-500 text-clients-700 border-transparent',
    ai: 'bg-transparent hover:bg-ai-50 focus:ring-ai-500 text-ai-700 border-transparent',
    default: 'bg-transparent hover:bg-gray-50 focus:ring-primary-500 text-gray-700 border-transparent',
  },
  link: {
    jobs: 'bg-transparent hover:underline focus:ring-jobs-500 text-jobs-700 border-transparent p-0 h-auto',
    clients: 'bg-transparent hover:underline focus:ring-clients-500 text-clients-700 border-transparent p-0 h-auto',
    ai: 'bg-transparent hover:underline focus:ring-ai-500 text-ai-700 border-transparent p-0 h-auto',
    default: 'bg-transparent hover:underline focus:ring-primary-500 text-primary-700 border-transparent p-0 h-auto',
  },
  gradient: {
    jobs: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:ring-blue-500 text-white border-transparent shadow-sm hover:shadow-md hover:animate-hover-lift',
    clients: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:ring-green-500 text-white border-transparent shadow-sm hover:shadow-md hover:animate-hover-lift',
    ai: 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 focus:ring-orange-500 text-white border-transparent shadow-sm hover:shadow-md hover:animate-hover-lift',
    default: 'bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600 focus:ring-blue-500 text-white border-transparent shadow-sm hover:shadow-md hover:animate-hover-lift',
  },
};

const buttonSizes = {
  xs: 'px-2 py-1 text-xs h-7', // 28px - compact buttons for tight spaces
  sm: 'px-3 py-2 text-sm h-9', // 36px - small buttons
  md: 'px-6 py-3 text-base h-11', // 44px minimum for mobile accessibility
  lg: 'px-8 py-4 text-lg h-12', // 48px - large buttons
  xl: 'px-10 py-5 text-xl h-14', // 56px - extra large buttons for heroes/CTAs
};

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  theme = 'default',
  isLoading = false,
  leftIcon,
  icon,
  rightIcon,
  fullWidth = false,
  children,
  className,
  disabled,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center gap-2 font-semibold rounded-lg border-2 transition-all duration-fast ease-out disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-current hover:duration-fast active:animate-button-press focus-visible-enhanced';
  
  const variantClasses = buttonVariants[variant][theme];
  const sizeClasses = variant === 'link' ? '' : buttonSizes[size];
  const fullWidthClass = fullWidth ? 'w-full' : '';

  const iconLeft = leftIcon || icon;

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses,
        sizeClasses,
        fullWidthClass,
        className
      )}
      disabled={disabled || isLoading}
      aria-disabled={disabled || isLoading}
      type={props.type || 'button'}
      {...props}
    >
      {isLoading ? (
        <>
          <svg 
            className="animate-spin h-4 w-4" 
            fill="none" 
            viewBox="0 0 24 24"
            aria-hidden="true"
            role="img"
            aria-label="Loading"
          >
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className="sr-only">Loading...</span>
          {children}
        </>
      ) : (
        <>
          {iconLeft &&  <span className="flex-shrink-0" aria-hidden="true">{iconLeft}</span>}
          {children}
          {rightIcon && <span className="flex-shrink-0" aria-hidden="true">{rightIcon}</span>}
        </>
      )}
    </button>
  );
};

// Export legacy preset variants for backward compatibility
export const PrimaryButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button variant="primary" {...props} />
);

export const SecondaryButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button variant="secondary" {...props} />
);

export const OutlineButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button variant="outline" {...props} />
);

export const GhostButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button variant="ghost" {...props} />
);

export const SuccessButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button variant="primary" theme="clients" {...props} />
);

export const DangerButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button variant="primary" className="bg-red-600 hover:bg-red-700 focus:ring-red-500 border-red-600" {...props} />
);

export default Button;