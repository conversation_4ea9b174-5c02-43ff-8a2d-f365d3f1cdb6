import React from 'react';
import { cn } from '../utils/cn';

interface VisuallyHiddenProps {
  children: React.ReactNode;
  className?: string;
  /** Whether the element should be focusable */
  focusable?: boolean;
}

/**
 * VisuallyHidden component hides content visually while keeping it accessible to screen readers
 */
export const VisuallyHidden: React.FC<VisuallyHiddenProps> = ({ 
  children, 
  className,
  focusable = false 
}) => {
  return (
    <span 
      className={cn(
        focusable ? 'sr-only-focusable' : 'sr-only',
        className
      )}
    >
      {children}
    </span>
  );
};

interface ScreenReaderOnlyProps {
  children: React.ReactNode;
  /** Whether to announce immediately when rendered */
  announce?: boolean;
  /** Priority of the announcement */
  priority?: 'polite' | 'assertive';
}

/**
 * ScreenReaderOnly component for content only meant for screen readers
 */
export const ScreenReaderOnly: React.FC<ScreenReaderOnlyProps> = ({ 
  children, 
  announce = false,
  priority = 'polite'
}) => {
  return (
    <div 
      className="sr-only"
      aria-live={announce ? priority : undefined}
      aria-atomic={announce ? 'true' : undefined}
    >
      {children}
    </div>
  );
};

interface AccessibleDescriptionProps {
  id: string;
  children: React.ReactNode;
  /** Whether the description is critical for understanding */
  critical?: boolean;
}

/**
 * AccessibleDescription provides descriptive text that can be referenced by aria-describedby
 */
export const AccessibleDescription: React.FC<AccessibleDescriptionProps> = ({ 
  id, 
  children,
  critical = false 
}) => {
  return (
    <div 
      id={id}
      className={cn(
        'text-sm text-secondary-600 dark:text-secondary-400',
        critical && 'font-medium'
      )}
      role={critical ? 'alert' : undefined}
    >
      {children}
    </div>
  );
};

interface StatusMessageProps {
  children: React.ReactNode;
  variant: 'success' | 'error' | 'warning' | 'info';
  /** Whether to announce immediately */
  announce?: boolean;
  /** Include an icon with the message */
  showIcon?: boolean;
}

/**
 * StatusMessage component for accessible status announcements
 */
export const StatusMessage: React.FC<StatusMessageProps> = ({ 
  children, 
  variant,
  announce = true,
  showIcon = true
}) => {
  const variants = {
    success: {
      className: 'success-announcement',
      icon: '✓',
      role: 'status' as const
    },
    error: {
      className: 'error-announcement',
      icon: '✕',
      role: 'alert' as const
    },
    warning: {
      className: 'warning-announcement',
      icon: '⚠',
      role: 'alert' as const
    },
    info: {
      className: 'text-primary-600 dark:text-primary-400 text-sm',
      icon: 'ℹ',
      role: 'status' as const
    }
  };

  const config = variants[variant];

  return (
    <div 
      className={config.className}
      role={config.role}
      aria-live={announce ? (variant === 'error' ? 'assertive' : 'polite') : undefined}
      aria-atomic="true"
    >
      {showIcon && (
        <span className="flex-shrink-0" aria-hidden="true">
          {config.icon}
        </span>
      )}
      <span>{children}</span>
    </div>
  );
};

interface ProgressAnnouncementProps {
  /** Current progress value (0-100) */
  value: number;
  /** Maximum value (default: 100) */
  max?: number;
  /** Text description of what's progressing */
  label: string;
  /** Whether to announce progress updates */
  announceUpdates?: boolean;
}

/**
 * ProgressAnnouncement component for accessible progress indicators
 */
export const ProgressAnnouncement: React.FC<ProgressAnnouncementProps> = ({ 
  value, 
  max = 100, 
  label,
  announceUpdates = true 
}) => {
  const percentage = Math.round((value / max) * 100);
  
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label className="text-sm font-medium text-secondary-700 dark:text-secondary-300">
          {label}
        </label>
        <span className="text-sm text-secondary-600 dark:text-secondary-400">
          {percentage}%
        </span>
      </div>
      
      <div 
        className="progress-accessible"
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={`${label}: ${percentage}% complete`}
        aria-live={announceUpdates ? 'polite' : undefined}
      >
        <div 
          className="progress-bar-accessible"
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      <ScreenReaderOnly announce={announceUpdates}>
        {label} progress: {percentage}% complete
      </ScreenReaderOnly>
    </div>
  );
};

interface LoadingAnnouncementProps {
  /** What is being loaded */
  label: string;
  /** Whether loading is complete */
  complete?: boolean;
  /** Additional context for screen readers */
  description?: string;
}

/**
 * LoadingAnnouncement component for accessible loading states
 */
export const LoadingAnnouncement: React.FC<LoadingAnnouncementProps> = ({ 
  label, 
  complete = false,
  description 
}) => {
  return (
    <div 
      role="status"
      aria-live="polite"
      aria-atomic="true"
      className="flex items-center gap-3"
    >
      {!complete && (
        <div 
          className="animate-spinner w-4 h-4 border-2 border-primary-200 border-t-primary-600 rounded-full"
          aria-hidden="true"
        />
      )}
      
      <div>
        <span className="text-sm font-medium">
          {complete ? `${label} complete` : `Loading ${label}...`}
        </span>
        
        {description && (
          <div className="text-xs text-secondary-600 dark:text-secondary-400 mt-1">
            {description}
          </div>
        )}
      </div>
      
      <ScreenReaderOnly announce>
        {complete 
          ? `${label} has finished loading` 
          : `${label} is currently loading, please wait`
        }
      </ScreenReaderOnly>
    </div>
  );
};

interface ErrorBoundaryAnnouncementProps {
  /** Error message */
  error: string;
  /** Recovery action description */
  action?: string;
  /** Callback for retry action */
  onRetry?: () => void;
}

/**
 * ErrorBoundaryAnnouncement component for accessible error handling
 */
export const ErrorBoundaryAnnouncement: React.FC<ErrorBoundaryAnnouncementProps> = ({ 
  error, 
  action,
  onRetry 
}) => {
  return (
    <div 
      role="alert"
      aria-live="assertive"
      className="error-announcement p-4 rounded-lg border border-error-200 dark:border-error-800 bg-error-50 dark:bg-error-900/20"
    >
      <div className="flex items-start gap-3">
        <span className="text-error-600 dark:text-error-400 text-lg" aria-hidden="true">
          ⚠
        </span>
        
        <div className="flex-1">
          <h3 className="font-medium text-error-800 dark:text-error-200 mb-1">
            Error occurred
          </h3>
          
          <p className="text-error-600 dark:text-error-400 text-sm mb-3">
            {error}
          </p>
          
          {action && onRetry && (
            <button
              onClick={onRetry}
              className="text-sm bg-error-600 hover:bg-error-700 text-white px-3 py-1.5 rounded-md focus-ring-error transition-colors duration-fast"
            >
              {action}
            </button>
          )}
        </div>
      </div>
      
      <ScreenReaderOnly announce priority="assertive">
        Error: {error}. {action && 'Retry option available.'}
      </ScreenReaderOnly>
    </div>
  );
};

interface NavigationSkipProps {
  /** Array of skip navigation targets */
  targets: Array<{
    href: string;
    label: string;
  }>;
  /** Additional CSS classes */
  className?: string;
}

/**
 * NavigationSkip component for keyboard navigation shortcuts
 */
export const NavigationSkip: React.FC<NavigationSkipProps> = ({ 
  targets, 
  className 
}) => {
  return (
    <nav 
      className={cn('sr-only-focusable', className)}
      aria-label="Skip navigation links"
    >
      {targets.map((target, index) => (
        <a
          key={index}
          href={target.href}
          className="skip-link"
          onFocus={(e) => {
            // Ensure the target element is focusable
            const targetElement = document.querySelector(target.href);
            if (targetElement && !targetElement.hasAttribute('tabindex')) {
              targetElement.setAttribute('tabindex', '-1');
            }
          }}
        >
          {target.label}
        </a>
      ))}
    </nav>
  );
};

export default {
  VisuallyHidden,
  ScreenReaderOnly,
  AccessibleDescription,
  StatusMessage,
  ProgressAnnouncement,
  LoadingAnnouncement,
  ErrorBoundaryAnnouncement,
  NavigationSkip,
};