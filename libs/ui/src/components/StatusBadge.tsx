import React from 'react';
import { cn } from '../utils/cn';

type JobStatus = 'new' | 'quoted' | 'in_progress' | 'on_hold' | 'completed' | 'archived';
type Size = 'sm' | 'md' | 'lg';

interface StatusBadgeProps {
  status: JobStatus;
  size?: Size;
  className?: string;
}

const statusConfig = {
  new: {
    label: 'New',
    bgColor: 'bg-jobs-100',
    textColor: 'text-jobs-700',
    dotColor: 'bg-jobs-500',
  },
  quoted: {
    label: 'Quoted',
    bgColor: 'bg-info-100',
    textColor: 'text-info-700',
    dotColor: 'bg-info-500',
  },
  in_progress: {
    label: 'In Progress',
    bgColor: 'bg-success-100',
    textColor: 'text-success-700',
    dotColor: 'bg-success-500',
  },
  on_hold: {
    label: 'On Hold',
    bgColor: 'bg-warning-100',
    textColor: 'text-warning-700',
    dotColor: 'bg-warning-500',
  },
  completed: {
    label: 'Completed',
    bgColor: 'bg-success-100',
    textColor: 'text-success-700',
    dotColor: 'bg-success-500',
  },
  archived: {
    label: 'Archived',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-700',
    dotColor: 'bg-gray-500',
  },
};

const sizeConfig = {
  sm: {
    container: 'px-2 py-1 text-xs',
    dot: 'w-1.5 h-1.5',
  },
  md: {
    container: 'px-3 py-1 text-sm',
    dot: 'w-2 h-2',
  },
  lg: {
    container: 'px-4 py-2 text-base',
    dot: 'w-2.5 h-2.5',
  },
};

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  size = 'sm',
  className,
}) => {
  const config = statusConfig[status];
  const sizeStyles = sizeConfig[size];

  return (
    <span
      className={cn(
        'inline-flex items-center gap-1.5 rounded-full font-medium',
        config.bgColor,
        config.textColor,
        sizeStyles.container,
        className
      )}
    >
      <span
        className={cn(
          'rounded-full',
          config.dotColor,
          sizeStyles.dot
        )}
      />
      {config.label}
    </span>
  );
};

export default StatusBadge; 