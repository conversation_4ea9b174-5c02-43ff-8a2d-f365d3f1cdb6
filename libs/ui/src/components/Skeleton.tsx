import { HTMLAttributes } from 'react';

export interface SkeletonProps extends HTMLAttributes<HTMLDivElement> {
  width?: string | number;
  height?: string | number;
}

export const Skeleton = ({ className, width, height, ...props }: SkeletonProps) => {
  return (
    <div
      className={`
        relative overflow-hidden
        bg-gray-200 dark:bg-gray-700
        rounded-md
        before:absolute before:inset-0
        before:-translate-x-full
        before:animate-[shimmer_2s_infinite]
        before:bg-gradient-to-r
        before:from-transparent before:via-white/10 before:to-transparent
        ${className}
      `}
      style={{
        width,
        height,
      }}
      {...props}
    />
  );
};