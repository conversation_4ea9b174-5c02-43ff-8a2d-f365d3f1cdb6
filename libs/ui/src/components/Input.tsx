import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../utils/cn';

// Enhanced Input variants with comprehensive styling options
const inputVariants = cva(
  // Base styles aligned with design system
  [
    'flex w-full transition-all duration-fast ease-out',
    'bg-white dark:bg-secondary-800',
    'text-secondary-900 dark:text-secondary-100',
    'placeholder-secondary-400 dark:placeholder-secondary-500',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-secondary-50 dark:disabled:bg-secondary-900',
    'focus:outline-none focus:ring-1 focus:ring-offset-0 focus:animate-focus-ring',
  ],
  {
    variants: {
      variant: {
        // Default - standard input styling (matches reference design)
        default: [
          'border border-secondary-200 dark:border-secondary-600',
          'rounded-xl',
          'focus:border-primary-400 focus:ring-primary-400/30',
        ],
        
        // Floating - for floating label inputs
        floating: [
          'border border-secondary-300 dark:border-secondary-600',
          'rounded-xl pt-6 pb-3',
          'focus:border-primary-500 focus:ring-primary-500/20',
        ],
        
        // Search - optimized for search functionality
        search: [
          'border border-secondary-300 dark:border-secondary-600',
          'rounded-full pl-10',
          'focus:border-primary-500 focus:ring-primary-500/20',
        ],
        
        // Minimal - subtle styling for inline editing
        minimal: [
          'border-0 border-b-2 border-secondary-200 dark:border-secondary-700',
          'rounded-none bg-transparent px-0',
          'focus:border-primary-500 focus:ring-0',
        ],
        
        // Ghost - no visible borders until focus
        ghost: [
          'border border-transparent bg-secondary-50 dark:bg-secondary-800',
          'rounded-xl',
          'hover:border-secondary-200 dark:hover:border-secondary-600',
          'focus:border-primary-500 focus:ring-primary-500/20',
        ],
      },
      
      size: {
        sm: 'h-9 px-3 py-2 text-sm',
        md: 'h-11 px-4 py-3 text-base',
        lg: 'h-13 px-4 py-4 text-lg',
      },
      
      state: {
        default: '',
        error: 'border-error-500 dark:border-error-500 focus:border-error-500 focus:ring-error-500/20',
        success: 'border-success-500 dark:border-success-500 focus:border-success-500 focus:ring-success-500/20',
        warning: 'border-warning-500 dark:border-warning-500 focus:border-warning-500 focus:ring-warning-500/20',
      },
      
      // Module context for consistent theming
      context: {
        default: '',
        jobs: 'focus:border-jobs-500 focus:ring-jobs-500/20',
        clients: 'focus:border-clients-500 focus:ring-clients-500/20',
        schedule: 'focus:border-accent-500 focus:ring-accent-500/20',
        invoicing: 'focus:border-warning-500 focus:ring-warning-500/20',
        analytics: 'focus:border-cyan-500 focus:ring-cyan-500/20',
        team: 'focus:border-pink-500 focus:ring-pink-500/20',
        ai: 'focus:border-ai-500 focus:ring-ai-500/20',
      },
    },
    
    defaultVariants: {
      variant: 'default',
      size: 'md',
      state: 'default',
      context: 'default',
    },
  }
);

interface InputProps
    extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
        VariantProps<typeof inputVariants> {
  
  /** Input label */
  label?: string;

  /** Size of the input */
  size?: 'sm' | 'md' | 'lg';
  
  /** Error message */
  error?: string;
  
  /** Helper text */
  helperText?: string;
  
  /** Icon element to display on the left */
  leftIcon?: React.ReactNode;
  
  /** Icon element to display on the right */
  rightIcon?: React.ReactNode;
  
  /** Whether the field is required */
  required?: boolean;
  
  /** Whether to show loading state */
  isLoading?: boolean;
  
  /** Floating label text (for floating variant) */
  floatingLabel?: string;
  
  /** Accessible description for screen readers */
  'aria-describedby'?: string;
  
  /** Whether the input is invalid */
  'aria-invalid'?: boolean;
  
  /** Label for the input when label prop is not sufficient */
  'aria-label'?: string;
  
  /** ID of element that labels this input */
  'aria-labelledby'?: string;
  
  /** Whether input controls autocomplete suggestions */
  'aria-expanded'?: boolean;
  
  /** Whether input has popup/dropdown */
  'aria-haspopup'?: boolean | 'menu' | 'listbox' | 'tree' | 'grid' | 'dialog';
}

// Loading Spinner Component
const LoadingSpinner = () => (
  <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
  </svg>
);

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  required,
  isLoading,
  floatingLabel,
  variant,
  size,
  state,
  context,
  className,
  id,
  disabled,
  ...props
}, ref) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  
  // Determine the actual state based on error prop
  const actualState = error ? 'error' : state;
  
  // Calculate padding adjustments for icons
  const leftPadding = leftIcon ? (size === 'sm' ? 'pl-9' : size === 'lg' ? 'pl-12' : 'pl-10') : '';
  const rightPadding = (rightIcon || isLoading) ? (size === 'sm' ? 'pr-9' : size === 'lg' ? 'pr-12' : 'pr-10') : '';

  // Generate accessible IDs
  const errorId = error ? `${inputId}-error` : undefined;
  const helpId = helperText ? `${inputId}-help` : undefined;
  const describedBy = [errorId, helpId, props['aria-describedby']].filter(Boolean).join(' ') || undefined;

  return (
    <div className="form-field-accessible">
      {/* Standard Label */}
      {label && variant !== 'floating' && (
        <label 
          htmlFor={inputId}
          className="block text-sm font-medium text-secondary-700 dark:text-secondary-300"
        >
          {label}
          {required && (
            <>
              <span className="text-error-500 ml-1" aria-label="required">*</span>
              <span className="sr-only">This field is required</span>
            </>
          )}
        </label>
      )}
      
      {/* Input Container */}
      <div className="relative">
        {/* Left Icon */}
        {leftIcon && (
          <div className={cn(
            "absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400 dark:text-secondary-500",
            size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'
          )}>
            {leftIcon}
          </div>
        )}
        
        {/* Input Field */}
        <input
          ref={ref}
          id={inputId}
          disabled={disabled || isLoading}
          required={required}
          className={cn(
            inputVariants({ variant, size, state: actualState, context }),
            leftPadding,
            rightPadding,
            actualState === 'error' ? 'field-error' : '',
            actualState === 'success' ? 'field-success' : '',
            actualState === 'warning' ? 'field-warning' : '',
            className
          )}
          aria-invalid={error ? 'true' : props['aria-invalid'] || 'false'}
          aria-describedby={describedBy}
          aria-required={required}
          {...props}
        />
        
        {/* Floating Label */}
        {variant === 'floating' && floatingLabel && (
          <label
            htmlFor={inputId}
            className={cn(
              "absolute left-4 transition-all duration-200 ease-out pointer-events-none",
              "text-secondary-500 dark:text-secondary-400",
              // Position based on input state
              props.value || props.defaultValue
                ? 'top-2 text-xs font-medium'
                : 'top-1/2 -translate-y-1/2 text-base'
            )}
          >
            {floatingLabel}
            {required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}
        
        {/* Right Icon / Loading */}
        {(rightIcon || isLoading) && (
          <div className={cn(
            "absolute right-3 top-1/2 transform -translate-y-1/2",
            size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'
          )}>
            {isLoading ? (
              <LoadingSpinner />
            ) : (
              <div className="text-secondary-400 dark:text-secondary-500">
                {rightIcon}
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Error Message */}
      {error && (
        <div 
          id={errorId}
          className="error-announcement"
          role="alert"
          aria-live="polite"
        >
          <svg 
            className="w-4 h-4 flex-shrink-0" 
            fill="currentColor" 
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span>{error}</span>
        </div>
      )}
      
      {/* Helper Text */}
      {!error && helperText && (
        <p 
          id={helpId}
          className="text-sm text-secondary-500 dark:text-secondary-400"
        >
          {helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

// === PRESET INPUT VARIANTS FOR CONVENIENCE ===

/**
 * Search Input - Optimized for search functionality
 */
export const SearchInput = (props: Omit<InputProps, 'variant'>) => (
  <Input variant="search" {...props} />
);

/**
 * Floating Label Input - Modern floating label pattern
 */
export const FloatingInput = (props: Omit<InputProps, 'variant'>) => (
  <Input variant="floating" {...props} />
);

/**
 * Minimal Input - Subtle styling for inline editing
 */
export const MinimalInput = (props: Omit<InputProps, 'variant'>) => (
  <Input variant="minimal" {...props} />
);

/**
 * Ghost Input - Subtle appearance until focused
 */
export const GhostInput = (props: Omit<InputProps, 'variant'>) => (
  <Input variant="ghost" {...props} />
);

export default Input; 