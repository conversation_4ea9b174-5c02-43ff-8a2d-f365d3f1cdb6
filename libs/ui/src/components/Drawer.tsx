import React, { useEffect, useRef } from 'react';
import { cn } from '../utils/cn';

interface DrawerProps {
  isOpen: boolean;
  onClose: () => void;
  side?: 'left' | 'right' | 'bottom';
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  children: React.ReactNode;
  className?: string;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  overlay?: boolean;
  zIndex?: number;
}

const drawerSizes = {
  sm: 'w-80',      // 320px
  md: 'w-96',      // 384px  
  lg: 'w-[28rem]', // 448px
  xl: 'w-[32rem]', // 512px
  full: 'w-full',
};

const drawerAnimations = {
  left: {
    enter: 'animate-in slide-in-from-left duration-300',
    exit: 'animate-out slide-out-to-left duration-200',
  },
  right: {
    enter: 'animate-in slide-in-from-right duration-300',
    exit: 'animate-out slide-out-to-right duration-200',
  },
  bottom: {
    enter: 'animate-in slide-in-from-bottom duration-300',
    exit: 'animate-out slide-out-to-bottom duration-200',
  },
};

export const Drawer: React.FC<DrawerProps> = ({
  isOpen,
  onClose,
  side = 'right',
  title,
  size = 'md',
  children,
  className,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  overlay = true,
  zIndex = 40,
}) => {
  const drawerRef = useRef<HTMLDivElement>(null);

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // Focus management
  useEffect(() => {
    if (!isOpen) return;

    const drawer = drawerRef.current;
    if (!drawer) return;

    const focusableElements = drawer.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;

    // Focus first element after animation completes
    const timer = setTimeout(() => {
      firstElement?.focus();
    }, 350);

    return () => clearTimeout(timer);
  }, [isOpen]);

  // Prevent body scroll when drawer is open (on mobile)
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0" style={{ zIndex }}>
      {/* Overlay */}
      {overlay && (
        <div
          className={cn(
            'absolute inset-0 bg-black/50 backdrop-blur-sm',
            'animate-in fade-in duration-200'
          )}
          onClick={handleOverlayClick}
        />
      )}

      {/* Drawer */}
      <div
        ref={drawerRef}
        className={cn(
          'absolute bg-white dark:bg-gray-900',
          'border-gray-300 dark:border-gray-600 shadow-2xl',
          'overflow-hidden flex flex-col',
          drawerAnimations[side].enter,
          // Position and size based on side
          side === 'left' ? 'inset-y-0 left-0 border-r-2' : 
          side === 'right' ? 'inset-y-0 right-0 border-l-2' :
          // Bottom drawer: full mobile, centered desktop
          side === 'bottom' ? 'inset-x-0 bottom-0 top-0 border-t-2 md:left-1/2 md:transform md:-translate-x-1/2 md:max-w-2xl md:mx-0' : '',
          // Size classes - only apply width for side drawers
          side !== 'bottom' ? drawerSizes[size] : 'w-full md:w-auto',
          // Mobile responsiveness
          'sm:max-w-full',
          // Enhanced contrast with subtle background difference
          'bg-gray-50/95 dark:bg-gray-800/95 backdrop-blur-sm',
          className
        )}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-6 border-b-2 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 relative z-10">
            {title && (
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {title}
              </h2>
            )}
            {showCloseButton && (
              <button
                onClick={onClose}
                className={cn(
                  'p-2 rounded-lg transition-colors',
                  'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200',
                  'hover:bg-gray-100 dark:hover:bg-gray-800',
                  'focus:outline-none focus:ring-2 focus:ring-primary-500'
                )}
                aria-label="Close drawer"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
}; 