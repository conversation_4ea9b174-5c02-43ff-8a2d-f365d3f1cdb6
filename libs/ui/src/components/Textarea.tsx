import React, { forwardRef, useState, useEffect } from 'react';
import { cn } from '../utils/cn';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'jobs' | 'clients' | 'ai';
  showCharCount?: boolean;
  maxLength?: number;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(({
  label,
  error,
  helperText,
  variant = 'default',
  showCharCount = true,
  maxLength = 2000,
  className,
  id,
  value,
  defaultValue,
  onChange,
  ...props
}, ref) => {
  const [charCount, setCharCount] = useState(0);
  const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
  
  // Track character count
  useEffect(() => {
    const currentValue = value || defaultValue || '';
    setCharCount(String(currentValue).length);
  }, [value, defaultValue]);

  const variantStyles = {
    default: 'focus:ring-primary-500 focus:border-primary-500',
    jobs: 'focus:ring-jobs-500 focus:border-jobs-500',
    clients: 'focus:ring-clients-500 focus:border-clients-500',
    ai: 'focus:ring-ai-500 focus:border-ai-500',
  };

  const baseTextareaStyles = 'w-full min-h-[88px] px-4 py-3 border rounded-lg transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 disabled:opacity-50 disabled:cursor-not-allowed resize-y';
  
  const borderStyles = error 
    ? 'border-error-500 focus:border-error-500 focus:ring-error-500' 
    : 'border-gray-300 dark:border-gray-600';

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCharCount(e.target.value.length);
    if (onChange) {
      onChange(e);
    }
  };

  const isNearLimit = maxLength && charCount > maxLength * 0.8;
  const isOverLimit = maxLength && charCount > maxLength;

  return (
    <div className="space-y-1">
      {label && (
        <label 
          htmlFor={textareaId}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          {label}
        </label>
      )}
      
      <textarea
        ref={ref}
        id={textareaId}
        maxLength={maxLength}
        className={cn(
          baseTextareaStyles,
          borderStyles,
          !error && variantStyles[variant],
          'focus:outline-none focus:ring-2 focus:ring-offset-0',
          className
        )}
        value={value}
        defaultValue={defaultValue}
        onChange={handleChange}
        {...props}
      />
      
      <div className="flex items-center justify-between">
        <div>
          {error && (
            <p className="text-sm text-error-600 dark:text-error-400">
              {error}
            </p>
          )}
          
          {!error && helperText && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {helperText}
            </p>
          )}
        </div>
        
        {showCharCount && maxLength && (
          <p className={cn(
            'text-xs',
            isOverLimit ? 'text-error-600 dark:text-error-400' :
            isNearLimit ? 'text-warning-600 dark:text-warning-400' :
            'text-gray-500 dark:text-gray-400'
          )}>
            {charCount}/{maxLength}
          </p>
        )}
      </div>
    </div>
  );
});

Textarea.displayName = 'Textarea';

export default Textarea; 