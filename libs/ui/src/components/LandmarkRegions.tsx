import React from 'react';
import { cn } from '../utils/cn';

interface LandmarkProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  'aria-label'?: string;
  'aria-labelledby'?: string;
}

/**
 * Header landmark region for site header and navigation
 */
export const HeaderLandmark: React.FC<LandmarkProps> = ({
  children,
  className,
  id = 'main-header',
  ...props
}) => {
  return (
    <header
      id={id}
      className={cn('landmark-header', className)}
      role="banner"
      {...props}
    >
      {children}
    </header>
  );
};

/**
 * Navigation landmark region
 */
export const NavigationLandmark: React.FC<LandmarkProps & {
  /** Type of navigation (primary, secondary, breadcrumb, etc.) */
  navType?: 'primary' | 'secondary' | 'breadcrumb' | 'pagination' | 'footer';
}> = ({
  children,
  className,
  id = 'main-navigation',
  navType = 'primary',
  ...props
}) => {
  const navIds = {
    primary: 'main-navigation',
    secondary: 'secondary-navigation',
    breadcrumb: 'breadcrumb-navigation',
    pagination: 'pagination-navigation',
    footer: 'footer-navigation',
  };

  return (
    <nav
      id={id || navIds[navType]}
      className={cn('landmark-nav', className)}
      role="navigation"
      {...props}
    >
      {children}
    </nav>
  );
};

/**
 * Main content landmark region
 */
export const MainLandmark: React.FC<LandmarkProps> = ({
  children,
  className,
  id = 'main-content',
  ...props
}) => {
  return (
    <main
      id={id}
      className={cn('landmark-main', className)}
      role="main"
      {...props}
    >
      {children}
    </main>
  );
};

/**
 * Aside/Complementary landmark region for sidebars
 */
export const AsideLandmark: React.FC<LandmarkProps> = ({
  children,
  className,
  id,
  ...props
}) => {
  return (
    <aside
      id={id}
      className={cn('landmark-aside', className)}
      role="complementary"
      {...props}
    >
      {children}
    </aside>
  );
};

/**
 * Footer landmark region
 */
export const FooterLandmark: React.FC<LandmarkProps> = ({
  children,
  className,
  id = 'main-footer',
  ...props
}) => {
  return (
    <footer
      id={id}
      className={cn('landmark-footer', className)}
      role="contentinfo"
      {...props}
    >
      {children}
    </footer>
  );
};

/**
 * Search landmark region
 */
export const SearchLandmark: React.FC<LandmarkProps> = ({
  children,
  className,
  id = 'search',
  ...props
}) => {
  return (
    <div
      id={id}
      className={cn('landmark-search', className)}
      role="search"
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Form landmark region for major forms
 */
export const FormLandmark: React.FC<LandmarkProps & {
  /** Form name for screen readers */
  formName?: string;
}> = ({
  children,
  className,
  id,
  formName,
  ...props
}) => {
  return (
    <form
      id={id}
      className={cn('landmark-form', className)}
      role="form"
      aria-label={formName}
      {...props}
    >
      {children}
    </form>
  );
};

/**
 * Region landmark for significant sections
 */
export const RegionLandmark: React.FC<LandmarkProps & {
  /** Required heading that labels this region */
  labelledBy?: string;
  /** Direct label for the region */
  label?: string;
}> = ({
  children,
  className,
  id,
  labelledBy,
  label,
  ...props
}) => {
  return (
    <section
      id={id}
      className={cn('landmark-region', className)}
      role="region"
      aria-labelledby={labelledBy}
      aria-label={label}
      {...props}
    >
      {children}
    </section>
  );
};

/**
 * Application landmark for interactive web applications
 */
export const ApplicationLandmark: React.FC<LandmarkProps & {
  /** Application name */
  appName: string;
}> = ({
  children,
  className,
  id,
  appName,
  ...props
}) => {
  return (
    <div
      id={id}
      className={cn('landmark-application', className)}
      role="application"
      aria-label={appName}
      {...props}
    >
      {children}
    </div>
  );
};

interface PageStructureProps {
  children: React.ReactNode;
  /** Skip navigation targets */
  skipTargets?: Array<{
    href: string;
    label: string;
  }>;
}

/**
 * PageStructure provides the complete accessible page layout
 */
export const PageStructure: React.FC<PageStructureProps> = ({
  children,
  skipTargets = [
    { href: '#main-content', label: 'Skip to main content' },
    { href: '#main-navigation', label: 'Skip to navigation' },
    { href: '#search', label: 'Skip to search' },
  ]
}) => {
  return (
    <>
      {/* Skip Navigation */}
      <nav 
        className="sr-only-focusable"
        aria-label="Skip navigation links"
      >
        {skipTargets.map((target, index) => (
          <a
            key={index}
            href={target.href}
            className="skip-link"
            onFocus={(e) => {
              // Ensure the target element is focusable
              const targetElement = document.querySelector(target.href);
              if (targetElement && !targetElement.hasAttribute('tabindex')) {
                targetElement.setAttribute('tabindex', '-1');
              }
            }}
          >
            {target.label}
          </a>
        ))}
      </nav>

      {/* Page Content */}
      {children}
    </>
  );
};

export default {
  HeaderLandmark,
  NavigationLandmark,
  MainLandmark,
  AsideLandmark,
  FooterLandmark,
  SearchLandmark,
  FormLandmark,
  RegionLandmark,
  ApplicationLandmark,
  PageStructure,
};