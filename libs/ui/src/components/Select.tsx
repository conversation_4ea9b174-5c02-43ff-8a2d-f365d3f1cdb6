import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../utils/cn';

interface Option {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectProps {
  options: Option[];
  value?: string;
  placeholder?: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  error?: boolean;
  errorMessage?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  multiple?: boolean;
}

const selectSizes = {
  sm: 'h-9 px-3 text-sm',
  md: 'h-11 px-4 text-base', // 44px minimum for mobile accessibility
  lg: 'h-12 px-4 text-lg',
};

export const Select: React.FC<SelectProps> = ({
  options,
  value,
  placeholder = 'Select an option...',
  onChange,
  disabled = false,
  error = false,
  errorMessage,
  size = 'md',
  className,
  multiple = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValues, setSelectedValues] = useState<string[]>(
    multiple ? (Array.isArray(value) ? value : value ? [value] : []) : []
  );
  const selectRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLUListElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          setIsOpen(false);
          break;
        case 'ArrowDown':
        case 'ArrowUp':
          e.preventDefault();
          // TODO: Implement arrow key navigation
          break;
        case 'Enter':
          e.preventDefault();
          // TODO: Select focused option
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleOptionClick = (optionValue: string) => {
    if (multiple) {
      const newSelectedValues = selectedValues.indexOf(optionValue) !== -1
        ? selectedValues.filter(v => v !== optionValue)
        : [...selectedValues, optionValue];
      setSelectedValues(newSelectedValues);
      onChange(newSelectedValues.join(','));
    } else {
      onChange(optionValue);
      setIsOpen(false);
    }
  };

  const getDisplayValue = () => {
    if (multiple) {
      if (selectedValues.length === 0) return placeholder;
      if (selectedValues.length === 1) {
        const option = options.find(opt => opt.value === selectedValues[0]);
        return option?.label || selectedValues[0];
      }
      return `${selectedValues.length} selected`;
    }

    const selectedOption = options.find(opt => opt.value === value);
    return selectedOption?.label || placeholder;
  };

  const isSelected = (optionValue: string) => {
    return multiple ? selectedValues.indexOf(optionValue) !== -1 : value === optionValue;
  };

  return (
    <div className="relative" ref={selectRef}>
      {/* Select trigger */}
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className={cn(
          'w-full flex items-center justify-between',
          'bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600',
          'rounded-lg transition-all duration-200',
          'text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400',
          'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent',
          // Error state
          error && 'border-error-500 focus:ring-error-500',
          // Disabled state
          disabled && 'opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800',
          // Sizes
          selectSizes[size],
          className
        )}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <span className={cn(
          'block text-left truncate whitespace-normal break-words',
          (!value || (multiple && selectedValues.length === 0)) && 'text-gray-500 dark:text-gray-400'
        )}>
          {getDisplayValue()}
        </span>
        
        <svg
          className={cn(
            'w-5 h-5 text-gray-400 transition-transform duration-200',
            isOpen && 'rotate-180'
          )}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg shadow-medium max-h-60 overflow-auto">
          <ul
            ref={listRef}
            role="listbox"
            aria-multiselectable={multiple}
            className="py-1"
          >
            {options.map((option) => (
              <li
                key={option.value}
                onClick={() => !option.disabled && handleOptionClick(option.value)}
                className={cn(
                  'px-4 py-2 cursor-pointer select-none transition-colors duration-150',
                  'text-gray-900 dark:text-white',
                  // Hover state
                  !option.disabled && 'hover:bg-gray-100 dark:hover:bg-gray-800',
                  // Selected state
                  isSelected(option.value) && 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300',
                  // Disabled state
                  option.disabled && 'opacity-50 cursor-not-allowed'
                )}
                role="option"
                aria-selected={isSelected(option.value)}
              >
                <div className="flex items-center justify-between">
                  <span>{option.label}</span>
                  {multiple && isSelected(option.value) && (
                    <svg className="w-4 h-4 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Error message */}
      {error && errorMessage && (
        <p className="mt-1 text-sm text-error-600 dark:text-error-400">
          {errorMessage}
        </p>
      )}
    </div>
  );
}; 