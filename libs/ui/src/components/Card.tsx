import React from 'react';
import { cn } from '../utils/cn';

type CardVariant = 'default' | 'job' | 'client' | 'elevated' | 'interactive' | 'outlined' | 'filled';
type CardSize = 'sm' | 'md' | 'lg';
type CardPadding = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: CardVariant;
  size?: CardSize;
  padding?: CardPadding;
  hoverable?: boolean;
  clickable?: boolean;
  children: React.ReactNode;
  /** Accessible label for screen readers when card content isn't self-descriptive */
  'aria-label'?: string;
  /** Description for screen readers */
  'aria-describedby'?: string;
  /** Whether card is currently expanded/collapsed */
  'aria-expanded'?: boolean;
  /** Whether card is currently selected */
  'aria-selected'?: boolean;
  /** Role override for semantic meaning */
  role?: string;
}

const cardVariants = {
  default: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700',
  job: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 border-l-4 border-l-jobs-700',
  client: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 border-l-4 border-l-clients-700',
  elevated: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 shadow-lg border-0',
  interactive: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:shadow-md hover:scale-[1.01] cursor-pointer active:scale-[0.99] transform',
  outlined: 'bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 shadow-none',
  filled: 'bg-gray-50 dark:bg-gray-900 border-gray-100 dark:border-gray-800 shadow-sm',
};

const cardSizes = {
  sm: 'p-3',
  md: 'p-4',
  lg: 'p-6',
};

const cardPaddings = {
  none: '',
  xs: 'p-2',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
  xl: 'p-10',
};

export const Card: React.FC<CardProps> = ({
  variant = 'default',
  size = 'md',
  padding,
  hoverable = true,
  clickable = false,
  children,
  className,
  ...props
}) => {
  // Use padding prop if provided, otherwise use size
  const paddingClass = padding ? cardPaddings[padding] : cardSizes[size];
  const hoverableClass = hoverable && !clickable ? 'hover:shadow-medium hover:scale-[1.02] cursor-pointer transition-all duration-fast ease-out' : '';
  const clickableClass = clickable ? 'hover:shadow-md hover:scale-[1.01] cursor-pointer active:scale-[0.99] transform transition-all duration-fast ease-out active:animate-button-press' : '';

  return (
    <div
      className={cn(
        'border rounded-xl transition-all duration-fast ease-out shadow-sm',
        cardVariants[variant],
        paddingClass,
        hoverableClass,
        clickableClass,
        clickable ? 'focus-card' : '',
        className
      )}
      role={props.role || (clickable ? 'button' : undefined)}
      tabIndex={clickable ? 0 : undefined}
      {...props}
    >
      {children}
    </div>
  );
};

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn('flex items-center justify-between mb-3', className)}
      {...props}
    >
      {children}
    </div>
  );
};

interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
}

export const CardTitle: React.FC<CardTitleProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <h3
      className={cn('text-lg font-semibold text-gray-900 dark:text-white', className)}
      {...props}
    >
      {children}
    </h3>
  );
};

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const CardContent: React.FC<CardContentProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn('text-gray-600 dark:text-gray-300', className)}
      {...props}
    >
      {children}
    </div>
  );
};

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn('flex items-center justify-between mt-4 pt-3 border-t border-gray-100 dark:border-gray-700', className)}
      {...props}
    >
      {children}
    </div>
  );
};

// Export preset variants for convenience
export const InteractiveCard = (props: Omit<CardProps, 'variant'>) => (
  <Card variant="interactive" clickable {...props} />
);

export const ElevatedCard = (props: Omit<CardProps, 'variant'>) => (
  <Card variant="elevated" {...props} />
);

export const GlassCard = (props: Omit<CardProps, 'variant'>) => (
  <Card variant="default" className="backdrop-blur-sm bg-white/90 dark:bg-gray-800/90" {...props} />
);

export const FlatCard = (props: Omit<CardProps, 'variant'>) => (
  <Card variant="default" hoverable={false} {...props} />
);

export const OutlinedCard = (props: Omit<CardProps, 'variant'>) => (
  <Card variant="outlined" {...props} />
);

export const FilledCard = (props: Omit<CardProps, 'variant'>) => (
  <Card variant="filled" {...props} />
);

export default Card;