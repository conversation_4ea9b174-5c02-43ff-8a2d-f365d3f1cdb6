import React, { useEffect, useRef } from 'react';
import { cn } from '../utils/cn';

interface LiveRegionProps {
  /** The message to announce to screen readers */
  message?: string;
  /** How urgent the announcement is */
  priority?: 'polite' | 'assertive';
  /** Whether to clear the message after announcing */
  clearAfterAnnouncement?: boolean;
  /** Delay before clearing the message (in ms) */
  clearDelay?: number;
  /** Additional CSS classes */
  className?: string;
  /** Children to render (alternative to message) */
  children?: React.ReactNode;
}

export const LiveRegion: React.FC<LiveRegionProps> = ({
  message,
  priority = 'polite',
  clearAfterAnnouncement = true,
  clearDelay = 1000,
  className,
  children
}) => {
  const regionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (message && clearAfterAnnouncement) {
      const timer = setTimeout(() => {
        if (regionRef.current) {
          regionRef.current.textContent = '';
        }
      }, clearDelay);

      return () => clearTimeout(timer);
    }
  }, [message, clearAfterAnnouncement, clearDelay]);

  return (
    <div
      ref={regionRef}
      className={cn('live-region', className)}
      aria-live={priority}
      aria-atomic="true"
      role={priority === 'assertive' ? 'alert' : 'status'}
    >
      {message || children}
    </div>
  );
};

interface AnnouncementManagerProps {
  className?: string;
}

export const AnnouncementManager: React.FC<AnnouncementManagerProps> = ({ className }) => {
  return (
    <>
      {/* Polite announcements */}
      <LiveRegion 
        priority="polite" 
        className={cn('polite-announcements', className)}
      />
      
      {/* Assertive/urgent announcements */}
      <LiveRegion 
        priority="assertive" 
        className={cn('assertive-announcements', className)}
      />
    </>
  );
};

// Hook for making announcements
export const useAnnouncement = () => {
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const regionClass = priority === 'assertive' ? '.assertive-announcements' : '.polite-announcements';
    const region = document.querySelector(regionClass);
    
    if (region) {
      region.textContent = message;
      
      // Clear after a delay
      setTimeout(() => {
        region.textContent = '';
      }, 1000);
    }
  };

  return { announce };
};

export default LiveRegion;