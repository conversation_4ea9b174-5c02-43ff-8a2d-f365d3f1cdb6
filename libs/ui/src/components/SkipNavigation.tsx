import React from 'react';
import { cn } from '../utils/cn';

interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

export const SkipLink: React.FC<SkipLinkProps> = ({ 
  href, 
  children, 
  className 
}) => {
  return (
    <a
      href={href}
      className={cn('skip-link', className)}
      onFocus={(e) => {
        // Ensure the target element is focusable
        const target = document.querySelector(href);
        if (target && !target.hasAttribute('tabindex')) {
          target.setAttribute('tabindex', '-1');
        }
      }}
    >
      {children}
    </a>
  );
};

interface SkipNavigationProps {
  className?: string;
  links?: Array<{
    href: string;
    label: string;
  }>;
}

export const SkipNavigation: React.FC<SkipNavigationProps> = ({ 
  className,
  links = [
    { href: '#main-content', label: 'Skip to main content' },
    { href: '#main-navigation', label: 'Skip to navigation' },
    { href: '#search', label: 'Skip to search' }
  ]
}) => {
  return (
    <nav 
      className={cn('sr-only-focusable', className)}
      aria-label="Skip navigation links"
    >
      {links.map((link, index) => (
        <SkipLink 
          key={index}
          href={link.href}
        >
          {link.label}
        </SkipLink>
      ))}
    </nav>
  );
};

export default SkipNavigation;