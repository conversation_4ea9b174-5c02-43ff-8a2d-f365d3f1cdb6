/**
 * Accessibility Testing Utilities
 * Provides runtime checks for common accessibility issues
 */

// Color contrast calculation utilities
const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

const getLuminance = (r: number, g: number, b: number): number => {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};

export const getContrastRatio = (color1: string, color2: string): number => {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 0;
  
  const lum1 = getLuminance(rgb1.r, rgb1.g, rgb1.b);
  const lum2 = getLuminance(rgb2.r, rgb2.g, rgb2.b);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

export const checkColorContrast = (element: HTMLElement): {
  ratio: number;
  level: 'AAA' | 'AA' | 'fail';
  passes: boolean;
} => {
  const style = window.getComputedStyle(element);
  const color = style.color;
  const backgroundColor = style.backgroundColor;
  
  // Extract RGB values (simplified - would need more robust parsing in production)
  const textColor = color.match(/\d+/g);
  const bgColor = backgroundColor.match(/\d+/g);
  
  if (!textColor || !bgColor) {
    return { ratio: 0, level: 'fail', passes: false };
  }
  
  const textHex = `#${parseInt(textColor[0]).toString(16).padStart(2, '0')}${parseInt(textColor[1]).toString(16).padStart(2, '0')}${parseInt(textColor[2]).toString(16).padStart(2, '0')}`;
  const bgHex = `#${parseInt(bgColor[0]).toString(16).padStart(2, '0')}${parseInt(bgColor[1]).toString(16).padStart(2, '0')}${parseInt(bgColor[2]).toString(16).padStart(2, '0')}`;
  
  const ratio = getContrastRatio(textHex, bgHex);
  
  let level: 'AAA' | 'AA' | 'fail' = 'fail';
  let passes = false;
  
  if (ratio >= 7) {
    level = 'AAA';
    passes = true;
  } else if (ratio >= 4.5) {
    level = 'AA';
    passes = true;
  } else if (ratio >= 3) {
    // Check if it's large text (18pt+ or 14pt+ bold)
    const fontSize = parseFloat(style.fontSize);
    const fontWeight = style.fontWeight;
    const isLargeText = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));
    
    if (isLargeText) {
      level = 'AA';
      passes = true;
    }
  }
  
  return { ratio, level, passes };
};

// Focus management testing
export const testFocusManagement = (container: HTMLElement): {
  focusableElements: number;
  hasTabIndex: number;
  hasFocusVisual: number;
  issues: string[];
} => {
  const issues: string[] = [];
  const focusableElements = container.querySelectorAll(
    'a, button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
  );
  
  let hasTabIndex = 0;
  let hasFocusVisual = 0;
  
  focusableElements.forEach((element) => {
    const el = element as HTMLElement;
    
    // Check for tabindex
    if (el.hasAttribute('tabindex')) {
      hasTabIndex++;
    }
    
    // Check for focus styles (simplified check)
    const style = window.getComputedStyle(el, ':focus');
    if (style.outline !== 'none' || style.boxShadow !== 'none') {
      hasFocusVisual++;
    }
    
    // Check for disabled elements without proper ARIA
    if (el.hasAttribute('disabled') && !el.hasAttribute('aria-disabled')) {
      issues.push(`Element ${el.tagName} is disabled but missing aria-disabled attribute`);
    }
  });
  
  return {
    focusableElements: focusableElements.length,
    hasTabIndex,
    hasFocusVisual,
    issues
  };
};

// ARIA testing
export const testAriaLabels = (container: HTMLElement): {
  missingLabels: number;
  missingRoles: number;
  issues: string[];
} => {
  const issues: string[] = [];
  let missingLabels = 0;
  let missingRoles = 0;
  
  // Check buttons without labels
  const buttons = container.querySelectorAll('button');
  buttons.forEach((button) => {
    const hasLabel = button.hasAttribute('aria-label') || 
                    button.hasAttribute('aria-labelledby') || 
                    button.textContent?.trim();
    
    if (!hasLabel) {
      missingLabels++;
      issues.push('Button without accessible label found');
    }
  });
  
  // Check inputs without labels
  const inputs = container.querySelectorAll('input:not([type="hidden"])');
  inputs.forEach((input) => {
    const hasLabel = input.hasAttribute('aria-label') || 
                    input.hasAttribute('aria-labelledby') || 
                    container.querySelector(`label[for="${input.id}"]`);
    
    if (!hasLabel) {
      missingLabels++;
      issues.push(`Input (type: ${input.getAttribute('type')}) without label found`);
    }
  });
  
  // Check for custom interactive elements without roles
  const customElements = container.querySelectorAll('[onclick]:not(button, a, input, select, textarea)');
  customElements.forEach((element) => {
    if (!element.hasAttribute('role')) {
      missingRoles++;
      issues.push('Interactive element without role attribute found');
    }
  });
  
  return {
    missingLabels,
    missingRoles,
    issues
  };
};

// Heading structure testing
export const testHeadingStructure = (container: HTMLElement = document.body): {
  headings: Array<{ level: number; text: string; hasSkipped: boolean }>;
  issues: string[];
} => {
  const headings = Array.from(container.querySelectorAll('h1, h2, h3, h4, h5, h6'));
  const issues: string[] = [];
  
  const headingData = headings.map((heading, index) => {
    const level = parseInt(heading.tagName[1]);
    const text = heading.textContent?.trim() || '';
    const prevLevel = index > 0 ? parseInt(headings[index - 1].tagName[1]) : 0;
    const hasSkipped = level > prevLevel + 1;
    
    if (hasSkipped) {
      issues.push(`Heading level ${level} follows level ${prevLevel}, skipping levels`);
    }
    
    return { level, text, hasSkipped };
  });
  
  // Check for multiple h1s
  const h1Count = headings.filter(h => h.tagName === 'H1').length;
  if (h1Count > 1) {
    issues.push(`Multiple h1 elements found (${h1Count}). Consider using only one h1 per page.`);
  }
  
  if (h1Count === 0) {
    issues.push('No h1 element found. Consider adding a main heading.');
  }
  
  return {
    headings: headingData,
    issues
  };
};

// Image accessibility testing
export const testImageAccessibility = (container: HTMLElement): {
  images: number;
  withAlt: number;
  decorativeImages: number;
  issues: string[];
} => {
  const images = container.querySelectorAll('img');
  const issues: string[] = [];
  let withAlt = 0;
  let decorativeImages = 0;
  
  images.forEach((img) => {
    if (img.hasAttribute('alt')) {
      withAlt++;
      if (img.getAttribute('alt') === '') {
        decorativeImages++;
      }
    } else {
      issues.push(`Image without alt attribute: ${img.src}`);
    }
    
    // Check for meaningful alt text
    const altText = img.getAttribute('alt');
    if (altText && (altText.toLowerCase().includes('image') || altText.toLowerCase().includes('picture'))) {
      issues.push(`Alt text should not contain "image" or "picture": ${altText}`);
    }
  });
  
  return {
    images: images.length,
    withAlt,
    decorativeImages,
    issues
  };
};

// Comprehensive accessibility audit
export const auditAccessibility = (container: HTMLElement = document.body): {
  score: number;
  tests: {
    colorContrast: ReturnType<typeof checkColorContrast>;
    focusManagement: ReturnType<typeof testFocusManagement>;
    ariaLabels: ReturnType<typeof testAriaLabels>;
    headingStructure: ReturnType<typeof testHeadingStructure>;
    imageAccessibility: ReturnType<typeof testImageAccessibility>;
  };
  summary: {
    totalIssues: number;
    criticalIssues: number;
    recommendations: string[];
  };
} => {
  // Run all tests
  const colorContrast = checkColorContrast(container);
  const focusManagement = testFocusManagement(container);
  const ariaLabels = testAriaLabels(container);
  const headingStructure = testHeadingStructure(container);
  const imageAccessibility = testImageAccessibility(container);
  
  // Calculate score
  let score = 100;
  const allIssues = [
    ...focusManagement.issues,
    ...ariaLabels.issues,
    ...headingStructure.issues,
    ...imageAccessibility.issues
  ];
  
  // Deduct points for issues
  score -= allIssues.length * 5;
  if (!colorContrast.passes) score -= 10;
  if (focusManagement.hasFocusVisual < focusManagement.focusableElements * 0.8) score -= 15;
  if (ariaLabels.missingLabels > 0) score -= 20;
  
  score = Math.max(0, score);
  
  const criticalIssues = ariaLabels.missingLabels + (colorContrast.passes ? 0 : 1);
  
  const recommendations: string[] = [];
  if (!colorContrast.passes) {
    recommendations.push('Improve color contrast to meet WCAG standards');
  }
  if (ariaLabels.missingLabels > 0) {
    recommendations.push('Add proper labels to all interactive elements');
  }
  if (focusManagement.hasFocusVisual < focusManagement.focusableElements) {
    recommendations.push('Ensure all focusable elements have visible focus indicators');
  }
  if (headingStructure.issues.length > 0) {
    recommendations.push('Fix heading structure to follow proper hierarchy');
  }
  
  return {
    score,
    tests: {
      colorContrast,
      focusManagement,
      ariaLabels,
      headingStructure,
      imageAccessibility
    },
    summary: {
      totalIssues: allIssues.length,
      criticalIssues,
      recommendations
    }
  };
};

// Development helper to log accessibility issues
export const logAccessibilityReport = (container?: HTMLElement): void => {
  if (process.env.NODE_ENV !== 'development') return;
  
  const audit = auditAccessibility(container);
  
  console.group('🔍 Accessibility Audit Report');
  console.log(`Overall Score: ${audit.score}/100`);
  console.log(`Total Issues: ${audit.summary.totalIssues}`);
  console.log(`Critical Issues: ${audit.summary.criticalIssues}`);
  
  if (audit.summary.recommendations.length > 0) {
    console.group('📋 Recommendations');
    audit.summary.recommendations.forEach(rec => console.log(`• ${rec}`));
    console.groupEnd();
  }
  
  // Log detailed issues
  const allIssues = [
    ...audit.tests.focusManagement.issues,
    ...audit.tests.ariaLabels.issues,
    ...audit.tests.headingStructure.issues,
    ...audit.tests.imageAccessibility.issues
  ];
  
  if (allIssues.length > 0) {
    console.group('⚠️ Detailed Issues');
    allIssues.forEach(issue => console.warn(issue));
    console.groupEnd();
  }
  
  console.groupEnd();
};

export default {
  getContrastRatio,
  checkColorContrast,
  testFocusManagement,
  testAriaLabels,
  testHeadingStructure,
  testImageAccessibility,
  auditAccessibility,
  logAccessibilityReport,
};