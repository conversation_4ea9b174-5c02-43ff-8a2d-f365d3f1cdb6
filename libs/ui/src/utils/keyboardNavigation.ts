/**
 * Keyboard Navigation Utilities for Accessibility
 * Provides helpers for managing keyboard focus and navigation patterns
 */

// Common keyboard codes
export const KEYBOARD_CODES = {
  ENTER: 'Enter',
  SPACE: ' ',
  ESCAPE: 'Escape',
  TAB: 'Tab',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  HOME: 'Home',
  END: 'End',
  PAGE_UP: 'PageUp',
  PAGE_DOWN: 'PageDown',
} as const;

/**
 * Get all focusable elements within a container
 */
export const getFocusableElements = (container: HTMLElement): HTMLElement[] => {
  const focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    'details',
    'summary',
    'iframe',
    'object',
    'embed',
    'area[href]',
    'audio[controls]',
    'video[controls]',
    '[contenteditable]:not([contenteditable="false"])',
  ].join(', ');

  return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[];
};

/**
 * Focus trap for modals and dialogs
 */
export class FocusTrap {
  private container: HTMLElement;
  private firstFocusable: HTMLElement | null = null;
  private lastFocusable: HTMLElement | null = null;
  private previousActiveElement: HTMLElement | null = null;

  constructor(container: HTMLElement) {
    this.container = container;
    this.updateFocusableElements();
  }

  private updateFocusableElements() {
    const focusableElements = getFocusableElements(this.container);
    this.firstFocusable = focusableElements[0] || null;
    this.lastFocusable = focusableElements[focusableElements.length - 1] || null;
  }

  activate(): void {
    this.previousActiveElement = document.activeElement as HTMLElement;
    this.updateFocusableElements();
    
    // Focus the first focusable element
    if (this.firstFocusable) {
      this.firstFocusable.focus();
    }

    // Add event listener for tab trapping
    this.container.addEventListener('keydown', this.handleKeyDown);
  }

  deactivate(): void {
    this.container.removeEventListener('keydown', this.handleKeyDown);
    
    // Return focus to the previous element
    if (this.previousActiveElement) {
      this.previousActiveElement.focus();
    }
  }

  private handleKeyDown = (event: KeyboardEvent): void => {
    if (event.key !== KEYBOARD_CODES.TAB) return;

    this.updateFocusableElements();

    if (!this.firstFocusable || !this.lastFocusable) return;

    if (event.shiftKey) {
      // Shift + Tab (backward)
      if (document.activeElement === this.firstFocusable) {
        event.preventDefault();
        this.lastFocusable.focus();
      }
    } else {
      // Tab (forward)
      if (document.activeElement === this.lastFocusable) {
        event.preventDefault();
        this.firstFocusable.focus();
      }
    }
  };
}

/**
 * Arrow key navigation for lists and grids
 */
export class ArrowKeyNavigation {
  private container: HTMLElement;
  private items: HTMLElement[] = [];
  private currentIndex = 0;
  private orientation: 'horizontal' | 'vertical' | 'grid' = 'vertical';

  constructor(
    container: HTMLElement, 
    options: {
      orientation?: 'horizontal' | 'vertical' | 'grid';
      itemSelector?: string;
      wrap?: boolean;
    } = {}
  ) {
    this.container = container;
    this.orientation = options.orientation || 'vertical';
    this.updateItems(options.itemSelector);
    this.container.addEventListener('keydown', this.handleKeyDown);
  }

  private updateItems(selector?: string): void {
    if (selector) {
      this.items = Array.from(this.container.querySelectorAll(selector)) as HTMLElement[];
    } else {
      this.items = getFocusableElements(this.container);
    }
  }

  private handleKeyDown = (event: KeyboardEvent): void => {
    const { key } = event;
    let newIndex = this.currentIndex;

    switch (this.orientation) {
      case 'horizontal':
        if (key === KEYBOARD_CODES.ARROW_LEFT) {
          newIndex = Math.max(0, this.currentIndex - 1);
          event.preventDefault();
        } else if (key === KEYBOARD_CODES.ARROW_RIGHT) {
          newIndex = Math.min(this.items.length - 1, this.currentIndex + 1);
          event.preventDefault();
        }
        break;

      case 'vertical':
        if (key === KEYBOARD_CODES.ARROW_UP) {
          newIndex = Math.max(0, this.currentIndex - 1);
          event.preventDefault();
        } else if (key === KEYBOARD_CODES.ARROW_DOWN) {
          newIndex = Math.min(this.items.length - 1, this.currentIndex + 1);
          event.preventDefault();
        }
        break;

      case 'grid':
        // TODO: Implement grid navigation with row/column awareness
        break;
    }

    // Handle Home and End keys
    if (key === KEYBOARD_CODES.HOME) {
      newIndex = 0;
      event.preventDefault();
    } else if (key === KEYBOARD_CODES.END) {
      newIndex = this.items.length - 1;
      event.preventDefault();
    }

    if (newIndex !== this.currentIndex) {
      this.currentIndex = newIndex;
      this.items[this.currentIndex]?.focus();
    }
  };

  destroy(): void {
    this.container.removeEventListener('keydown', this.handleKeyDown);
  }
}

/**
 * Roving tabindex pattern for managing focus in lists
 */
export const useRovingTabIndex = (
  containerRef: React.RefObject<HTMLElement>,
  itemSelector: string = '[role="option"], button, a, input, select, textarea'
) => {
  const setFocusableItem = (index: number) => {
    if (!containerRef.current) return;

    const items = containerRef.current.querySelectorAll(itemSelector);
    
    items.forEach((item, i) => {
      const element = item as HTMLElement;
      if (i === index) {
        element.setAttribute('tabindex', '0');
        element.focus();
      } else {
        element.setAttribute('tabindex', '-1');
      }
    });
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (!containerRef.current) return;

    const items = Array.from(containerRef.current.querySelectorAll(itemSelector));
    const currentItem = document.activeElement;
    const currentIndex = items.indexOf(currentItem as Element);

    if (currentIndex === -1) return;

    let newIndex = currentIndex;

    switch (event.key) {
      case KEYBOARD_CODES.ARROW_UP:
        newIndex = Math.max(0, currentIndex - 1);
        event.preventDefault();
        break;
      case KEYBOARD_CODES.ARROW_DOWN:
        newIndex = Math.min(items.length - 1, currentIndex + 1);
        event.preventDefault();
        break;
      case KEYBOARD_CODES.HOME:
        newIndex = 0;
        event.preventDefault();
        break;
      case KEYBOARD_CODES.END:
        newIndex = items.length - 1;
        event.preventDefault();
        break;
    }

    if (newIndex !== currentIndex) {
      setFocusableItem(newIndex);
    }
  };

  return {
    handleKeyDown,
    setFocusableItem,
  };
};

/**
 * Dropdown/combobox keyboard navigation
 */
export const useDropdownNavigation = (
  triggerRef: React.RefObject<HTMLElement>,
  menuRef: React.RefObject<HTMLElement>,
  options: {
    onOpen?: () => void;
    onClose?: () => void;
    onSelect?: (index: number) => void;
  } = {}
) => {
  const handleTriggerKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case KEYBOARD_CODES.ENTER:
      case KEYBOARD_CODES.SPACE:
      case KEYBOARD_CODES.ARROW_DOWN:
        event.preventDefault();
        options.onOpen?.();
        break;
      case KEYBOARD_CODES.ARROW_UP:
        event.preventDefault();
        options.onOpen?.();
        break;
    }
  };

  const handleMenuKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case KEYBOARD_CODES.ESCAPE:
        event.preventDefault();
        options.onClose?.();
        triggerRef.current?.focus();
        break;
      case KEYBOARD_CODES.ENTER:
        event.preventDefault();
        const activeItem = document.activeElement;
        if (menuRef.current && activeItem) {
          const items = Array.from(menuRef.current.querySelectorAll('[role="option"], button, a'));
          const index = items.indexOf(activeItem);
          if (index >= 0) {
            options.onSelect?.(index);
          }
        }
        break;
    }
  };

  return {
    handleTriggerKeyDown,
    handleMenuKeyDown,
  };
};

/**
 * Utility to check if an element is visible and focusable
 */
export const isElementFocusable = (element: HTMLElement): boolean => {
  if (!element || element.hidden) return false;
  
  const style = window.getComputedStyle(element);
  if (style.display === 'none' || style.visibility === 'hidden') return false;
  
  if (element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true') {
    return false;
  }
  
  return true;
};

/**
 * Announce changes to screen readers
 */
export const announceToScreenReader = (
  message: string, 
  priority: 'polite' | 'assertive' = 'polite'
): void => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

export default {
  KEYBOARD_CODES,
  getFocusableElements,
  FocusTrap,
  ArrowKeyNavigation,
  useRovingTabIndex,
  useDropdownNavigation,
  isElementFocusable,
  announceToScreenReader,
};