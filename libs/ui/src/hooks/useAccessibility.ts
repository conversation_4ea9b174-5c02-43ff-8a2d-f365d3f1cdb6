import { useEffect, useRef, useState, useCallback } from 'react';

/**
 * Hook for managing screen reader announcements
 */
export const useScreenReaderAnnouncement = () => {
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 1000);
  }, []);

  return { announce };
};

/**
 * Hook for managing unique IDs for accessibility
 */
export const useAccessibleId = (prefix: string = 'accessible') => {
  const idRef = useRef<string>();
  
  if (!idRef.current) {
    idRef.current = `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  }
  
  return idRef.current;
};

/**
 * Hook for keyboard navigation support
 */
export const useKeyboardNavigation = (
  containerRef: React.RefObject<HTMLElement>,
  options: {
    orientation?: 'horizontal' | 'vertical' | 'grid';
    wrap?: boolean;
    skipDisabled?: boolean;
  } = {}
) => {
  const { orientation = 'vertical', wrap = true, skipDisabled = true } = options;

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const focusableElements = Array.from(
        container.querySelectorAll(
          'button:not([disabled]), input:not([disabled]), a[href], textarea:not([disabled]), select:not([disabled]), [tabindex]:not([tabindex="-1"])'
        )
      ) as HTMLElement[];

      if (focusableElements.length === 0) return;

      const currentIndex = focusableElements.findIndex(el => el === document.activeElement);
      let nextIndex = currentIndex;

      const getNextIndex = (direction: 'next' | 'prev') => {
        let index = currentIndex;
        do {
          if (direction === 'next') {
            index = wrap ? (index + 1) % focusableElements.length : Math.min(index + 1, focusableElements.length - 1);
          } else {
            index = wrap ? (index - 1 + focusableElements.length) % focusableElements.length : Math.max(index - 1, 0);
          }
        } while (
          skipDisabled && 
          focusableElements[index]?.hasAttribute('disabled') && 
          index !== currentIndex
        );
        return index;
      };

      switch (event.key) {
        case 'ArrowDown':
          if (orientation === 'vertical' || orientation === 'grid') {
            event.preventDefault();
            nextIndex = getNextIndex('next');
          }
          break;
        case 'ArrowUp':
          if (orientation === 'vertical' || orientation === 'grid') {
            event.preventDefault();
            nextIndex = getNextIndex('prev');
          }
          break;
        case 'ArrowRight':
          if (orientation === 'horizontal' || orientation === 'grid') {
            event.preventDefault();
            nextIndex = getNextIndex('next');
          }
          break;
        case 'ArrowLeft':
          if (orientation === 'horizontal' || orientation === 'grid') {
            event.preventDefault();
            nextIndex = getNextIndex('prev');
          }
          break;
        case 'Home':
          event.preventDefault();
          nextIndex = 0;
          break;
        case 'End':
          event.preventDefault();
          nextIndex = focusableElements.length - 1;
          break;
        default:
          return;
      }

      if (nextIndex !== currentIndex && focusableElements[nextIndex]) {
        focusableElements[nextIndex].focus();
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    return () => container.removeEventListener('keydown', handleKeyDown);
  }, [containerRef, orientation, wrap, skipDisabled]);
};

/**
 * Hook for focus management and restoration
 */
export const useFocusManagement = () => {
  const previousActiveElementRef = useRef<HTMLElement | null>(null);

  const saveFocus = useCallback(() => {
    previousActiveElementRef.current = document.activeElement as HTMLElement;
  }, []);

  const restoreFocus = useCallback(() => {
    if (previousActiveElementRef.current) {
      previousActiveElementRef.current.focus();
      previousActiveElementRef.current = null;
    }
  }, []);

  const trapFocus = useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button:not([disabled]), input:not([disabled]), a[href], textarea:not([disabled]), select:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        if (event.shiftKey) {
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement?.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement?.focus();
          }
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    
    // Focus first element
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return {
    saveFocus,
    restoreFocus,
    trapFocus,
  };
};

/**
 * Hook for detecting user preferences
 */
export const useAccessibilityPreferences = () => {
  const [preferences, setPreferences] = useState({
    prefersReducedMotion: false,
    prefersHighContrast: false,
    prefersColorScheme: 'no-preference' as 'light' | 'dark' | 'no-preference',
  });

  useEffect(() => {
    const updatePreferences = () => {
      setPreferences({
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        prefersHighContrast: window.matchMedia('(prefers-contrast: high)').matches,
        prefersColorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches 
          ? 'dark' 
          : window.matchMedia('(prefers-color-scheme: light)').matches 
            ? 'light' 
            : 'no-preference',
      });
    };

    // Initial check
    updatePreferences();

    // Listen for changes
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    const darkSchemeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const lightSchemeQuery = window.matchMedia('(prefers-color-scheme: light)');

    const queries = [reducedMotionQuery, highContrastQuery, darkSchemeQuery, lightSchemeQuery];
    
    queries.forEach(query => {
      query.addEventListener('change', updatePreferences);
    });

    return () => {
      queries.forEach(query => {
        query.removeEventListener('change', updatePreferences);
      });
    };
  }, []);

  return preferences;
};

/**
 * Hook for managing ARIA expanded state
 */
export const useExpandable = (initialExpanded: boolean = false) => {
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  const { announce } = useScreenReaderAnnouncement();

  const toggle = useCallback(() => {
    setIsExpanded(prev => {
      const newState = !prev;
      announce(newState ? 'Expanded' : 'Collapsed', 'polite');
      return newState;
    });
  }, [announce]);

  const expand = useCallback(() => {
    setIsExpanded(true);
    announce('Expanded', 'polite');
  }, [announce]);

  const collapse = useCallback(() => {
    setIsExpanded(false);
    announce('Collapsed', 'polite');
  }, [announce]);

  return {
    isExpanded,
    toggle,
    expand,
    collapse,
    ariaExpanded: isExpanded,
  };
};

/**
 * Hook for live region announcements with debouncing
 */
export const useLiveRegion = (debounceMs: number = 300) => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const { announce } = useScreenReaderAnnouncement();

  const announceWithDebounce = useCallback((
    message: string, 
    priority: 'polite' | 'assertive' = 'polite'
  ) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      announce(message, priority);
    }, debounceMs);
  }, [announce, debounceMs]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return { announceWithDebounce };
};

/**
 * Hook for managing roving tabindex pattern
 */
export const useRovingTabIndex = (
  containerRef: React.RefObject<HTMLElement>,
  itemSelector: string = '[role="option"], button, a, input, select, textarea'
) => {
  const [activeIndex, setActiveIndex] = useState(0);

  const setFocusableItem = useCallback((index: number) => {
    if (!containerRef.current) return;

    const items = containerRef.current.querySelectorAll(itemSelector);
    
    items.forEach((item, i) => {
      const element = item as HTMLElement;
      if (i === index) {
        element.setAttribute('tabindex', '0');
        element.focus();
        setActiveIndex(index);
      } else {
        element.setAttribute('tabindex', '-1');
      }
    });
  }, [containerRef, itemSelector]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Initialize first item as focusable
    const items = container.querySelectorAll(itemSelector);
    if (items.length > 0) {
      items.forEach((item, i) => {
        (item as HTMLElement).setAttribute('tabindex', i === 0 ? '0' : '-1');
      });
    }
  }, [containerRef, itemSelector]);

  useKeyboardNavigation(containerRef, {
    orientation: 'vertical',
    wrap: true,
    skipDisabled: true,
  });

  return {
    activeIndex,
    setFocusableItem,
  };
};

export default {
  useScreenReaderAnnouncement,
  useAccessibleId,
  useKeyboardNavigation,
  useFocusManagement,
  useAccessibilityPreferences,
  useExpandable,
  useLiveRegion,
  useRovingTabIndex,
};