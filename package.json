{"name": "deskbelt", "version": "1.0.0", "description": "DeskBelt - Comprehensive Freelance Management Platform", "private": true, "workspaces": ["apps/*", "libs/*", "api"], "scripts": {"dev": "concurrently --kill-others-on-fail \"npm run dev:api\" \"npm run dev:web\"", "dev:turbo": "concurrently --kill-others-on-fail \"npm run dev:api\" \"npm run dev:web:turbo\"", "dev:web": "cd apps/web && npm run dev", "dev:web:turbo": "cd apps/web && npm run dev", "dev:admin": "cd apps/admin && npm run dev", "dev:api": "cd api && npm run dev", "build": "npm run build:api && npm run build:web && npm run build:admin", "build:web": "cd apps/web && npm run build", "build:admin": "cd apps/admin && npm run build", "build:api": "cd api && npm run build", "start": "concurrently \"npm run start:api\" \"npm run start:web\"", "start:web": "cd apps/web && npm run start", "start:admin": "cd apps/admin && npm run start", "start:api": "cd api && npm run start", "lint": "npm run lint:web && npm run lint:admin", "lint:web": "cd apps/web && npm run lint", "lint:admin": "cd apps/admin && npm run lint", "test": "npm run test:web && npm run test:admin", "test:web": "cd apps/web && npm run test", "test:admin": "cd apps/admin && npm run test", "clean": "rimraf apps/*/dist apps/*/.next api/dist", "install:all": "npm install && npm run install:web && npm run install:admin && npm run install:api", "install:web": "cd apps/web && npm install", "install:admin": "cd apps/admin && npm install", "install:api": "cd api && npm install"}, "devDependencies": {"concurrently": "^8.2.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "rimraf": "^5.0.5"}, "keywords": ["freelance", "management", "invoicing", "project-management", "client-management"], "author": "", "license": "ISC", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@supabase/supabase-js": "^2.53.0", "node-fetch": "^3.3.2"}}