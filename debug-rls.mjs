#!/usr/bin/env node

/**
 * Debug RLS Implementation
 * Simple test to verify user-impersonated client is working
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://nwwynkkigyahrjumqmrj.supabase.co';
const SUPABASE_SERVICE_KEY = 'sb_secret_ma7kFAFVArMN0GO4RPdFeQ_AtmlHIy4';
const SUPABASE_ANON_KEY = 'sb_publishable_yM7FhOlmtFzF_OmntM6vww_7L2kSKZD';

// Service role client (bypasses RLS)
const serviceClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Function to create user-impersonated client
const createUserClient = (accessToken) => {
  return createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
    global: {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'apikey': SUPABASE_ANON_KEY
      }
    },
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false
    }
  });
};

async function debugRLS() {
  console.log('🔍 Debugging RLS Implementation...\n');
  
  try {
    // 1. Create test user
    console.log('📝 Creating test user...');
    const userEmail = `debug-user-${Date.now()}@example.com`;
    
    const { data: authUser, error: authError } = await serviceClient.auth.admin.createUser({
      email: userEmail,
      password: 'TestPass123!',
      email_confirm: true
    });
    
    if (authError) throw authError;
    console.log(`✅ Created auth user: ${authUser.user.id}`);
    
    // 2. Create user profile
    const { error: profileError } = await serviceClient
      .from('users')
      .insert({
        id: authUser.user.id,
        email: userEmail,
        full_name: 'Debug User',
        role: 'tradesperson'
      });
    
    if (profileError) throw profileError;
    console.log('✅ Created user profile');
    
    // 3. Sign in to get access token
    const { data: signInData, error: signInError } = await serviceClient.auth.signInWithPassword({
      email: userEmail,
      password: 'TestPass123!'
    });
    
    if (signInError) throw signInError;
    console.log('✅ Got access token');
    
    // 4. Create user-impersonated client
    const userClient = createUserClient(signInData.session.access_token);
    console.log('✅ Created user-impersonated client');
    
    // 5. Test RLS with service role vs user client
    console.log('\n🔍 Testing RLS...');
    
    // Count total jobs with service role (should show all)
    const { count: serviceCount } = await serviceClient
      .from('jobs')
      .select('*', { count: 'exact', head: true });
    
    console.log(`📊 Service role sees ${serviceCount} total jobs`);
    
    // Count jobs with user client (should show only user's jobs - likely 0)
    const { count: userCount, error: userError } = await userClient
      .from('jobs')
      .select('*', { count: 'exact', head: true });
    
    if (userError) {
      console.error('❌ User client error:', userError);
    } else {
      console.log(`📊 User client sees ${userCount} jobs`);
      
      if (userCount < serviceCount) {
        console.log('✅ RLS is working! User sees fewer jobs than service role');
      } else {
        console.log('❌ RLS might not be working - user sees same as service role');
      }
    }
    
    // 6. Test creating a job with user client
    console.log('\n📝 Testing job creation with user client...');
    
    // First create a client
    const { data: newClient, error: clientError } = await userClient
      .from('clients')
      .insert({
        name: 'Debug Client',
        created_by: authUser.user.id
      })
      .select()
      .single();
    
    if (clientError) {
      console.error('❌ Client creation error:', clientError);
    } else {
      console.log('✅ Created test client with user client');
      
      // Create a job
      const { data: newJob, error: jobError } = await userClient
        .from('jobs')
        .insert({
          title: 'Debug Job',
          client_id: newClient.id,
          created_by: authUser.user.id,
          status: 'new'
        })
        .select()
        .single();
      
      if (jobError) {
        console.error('❌ Job creation error:', jobError);
      } else {
        console.log('✅ Created test job with user client');
        
        // Now test if user can see their own job
        const { count: userJobCount } = await userClient
          .from('jobs')
          .select('*', { count: 'exact', head: true });
        
        console.log(`📊 After creating job, user client sees ${userJobCount} jobs`);
      }
    }
    
    // Cleanup
    console.log('\n🧹 Cleaning up...');
    await serviceClient.auth.admin.deleteUser(authUser.user.id);
    console.log('✅ Cleaned up test user');
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugRLS();