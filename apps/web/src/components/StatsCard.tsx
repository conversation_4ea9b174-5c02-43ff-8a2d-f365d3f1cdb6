import { ArrowUpIcon, ArrowDownIcon, ArrowTrendingUpIcon } from '@heroicons/react/24/outline';

interface StatsCardProps {
  title: string;
  value: number | string;
  change?: number;
  icon: React.ReactNode;
  format?: 'currency' | 'number' | 'percentage';
  variant?: 'default' | 'gradient' | 'minimal' | 'elevated';
  color?: 'primary' | 'success' | 'warning' | 'accent' | 'clients' | 'jobs';
  className?: string;
}

export default function StatsCard({ 
  title, 
  value, 
  change, 
  icon, 
  format = 'number',
  variant = 'default',
  color = 'primary',
  className = '' 
}: StatsCardProps) {
  const formatValue = (val: number | string) => {
    if (typeof val === 'string') return val;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-GB', { 
          style: 'currency', 
          currency: 'GBP',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(val);
      case 'percentage':
        return `${val}%`;
      default:
        return new Intl.NumberFormat('en-GB').format(val);
    }
  };

  const formatChange = (changeValue: number) => {
    const sign = changeValue >= 0 ? '+' : '';
    return `${sign}${changeValue.toFixed(1)}%`;
  };

  const isPositiveChange = change !== undefined && change >= 0;

  const getColorClasses = (colorScheme: string) => {
    const colors = {
      primary: {
        bg: 'bg-primary-50 dark:bg-primary-950',
        icon: 'bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-colored',
        accent: 'text-primary-600 dark:text-primary-400'
      },
      success: {
        bg: 'bg-success-50 dark:bg-success-950',
        icon: 'bg-gradient-to-br from-success-500 to-success-600 text-white',
        accent: 'text-success-600 dark:text-success-400'
      },
      warning: {
        bg: 'bg-warning-50 dark:bg-warning-950',
        icon: 'bg-gradient-to-br from-warning-500 to-warning-600 text-white',
        accent: 'text-warning-600 dark:text-warning-400'
      },
      accent: {
        bg: 'bg-accent-50 dark:bg-accent-950',
        icon: 'bg-gradient-to-br from-accent-500 to-accent-600 text-white',
        accent: 'text-accent-600 dark:text-accent-400'
      },
      clients: {
        bg: 'bg-clients-50 dark:bg-clients-950',
        icon: 'bg-gradient-to-br from-clients-500 to-clients-600 text-white',
        accent: 'text-clients-600 dark:text-clients-400'
      },
      jobs: {
        bg: 'bg-jobs-50 dark:bg-jobs-950',
        icon: 'bg-gradient-to-br from-jobs-500 to-jobs-600 text-white shadow-colored',
        accent: 'text-jobs-600 dark:text-jobs-400'
      }
    };
    return colors[colorScheme as keyof typeof colors] || colors.primary;
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'gradient':
        return 'bg-gradient-to-br from-white to-secondary-50 dark:from-secondary-800 dark:to-secondary-900 border-gradient';
      case 'minimal':
        return 'bg-secondary-25 dark:bg-secondary-900 border-secondary-100 dark:border-secondary-800';
      case 'elevated':
        return 'card-elevated';
      default:
        return 'card-interactive';
    }
  };

  const colorScheme = getColorClasses(color);
  const variantClasses = getVariantClasses();

  return (
    <div className={`${variantClasses} group/stats animate-fade-in-up hover:shadow-lg transition-all duration-300 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          <p className="text-sm font-medium text-secondary-600 dark:text-secondary-400 uppercase tracking-wide mb-2 group-hover/stats:text-secondary-700 dark:group-hover/stats:text-secondary-300 transition-colors duration-200">
            {title}
          </p>
          <div className="flex items-baseline space-x-2">
            <p className="text-3xl font-bold text-secondary-900 dark:text-secondary-50 transition-colors duration-200">
              {formatValue(value)}
            </p>
            {change !== undefined && (
              <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold transition-all duration-200 ${
                isPositiveChange 
                  ? 'bg-success-100 dark:bg-success-950 text-success-700 dark:text-success-300 hover:bg-success-200 dark:hover:bg-success-900' 
                  : 'bg-error-100 dark:bg-error-950 text-error-700 dark:text-error-300 hover:bg-error-200 dark:hover:bg-error-900'
              }`}>
                <div className="w-3 h-3 mr-1 transition-colors duration-300">
                  {isPositiveChange ? (
                    <ArrowTrendingUpIcon className="w-3 h-3" />
                  ) : (
                    <ArrowDownIcon className="w-3 h-3" />
                  )}
                </div>
                {formatChange(change)}
              </div>
            )}
          </div>
        </div>
        
        {/* Enhanced Icon with Hover Effects */}
        <div className="flex-shrink-0 ml-4">
          <div className={`w-14 h-14 ${colorScheme.icon} rounded-2xl flex items-center justify-center transition-all duration-300 hover:shadow-lg cursor-pointer`}>
            <div className="w-7 h-7 transition-colors duration-200">
              {icon}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Additional Info with Micro-animations */}
      {change !== undefined && (
        <div className="flex items-center justify-between pt-4 border-t border-secondary-200 dark:border-secondary-700 group-hover/stats:border-secondary-300 dark:group-hover/stats:border-secondary-600 transition-colors duration-200">
          <span className="text-xs text-secondary-500 dark:text-secondary-400 group-hover/stats:text-secondary-600 dark:group-hover/stats:text-secondary-300 transition-colors duration-200">
            vs previous period
          </span>
          <div className={`flex items-center text-xs font-medium ${colorScheme.accent} transition-colors duration-200`}>
            <div className={`w-2 h-2 rounded-full ${isPositiveChange ? 'bg-success-400' : 'bg-error-400'} mr-2 animate-pulse-soft transition-colors duration-200`}></div>
            <span className="transition-colors duration-200">
              {isPositiveChange ? 'Growing' : 'Declining'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
} 