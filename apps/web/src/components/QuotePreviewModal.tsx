'use client';

import React, { useState } from 'react';
import { JobDetails } from '@/types/Job';
import { useProfile } from '@/hooks/useProfile';
import { 
  XMarkIcon,
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  PrinterIcon
} from '@heroicons/react/24/outline';

interface QuotePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: JobDetails | null;
  quoteDescription: string;
  termsAndConditions: string;
  amount?: number;
  
  // Market rate intelligence props
  marketRateWorkType?: string;
  marketRateSuggestion?: string;
  marketRateEstimatedRange?: string;
}

export const QuotePreviewModal: React.FC<QuotePreviewModalProps> = ({
  isOpen,
  onClose,
  job,
  quoteDescription,
  termsAndConditions,
  amount,
  marketRateWorkType,
  marketRateSuggestion,
  marketRateEstimatedRange
}) => {
  const [zoom, setZoom] = useState(100);
  const { data: profile } = useProfile();

  if (!isOpen || !job) return null;

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 50));
  };

  const handleDownload = () => {
    // TODO: Implement PDF download
    console.log('Downloading quote as PDF...');
  };

  const handleShare = () => {
    // TODO: Implement sharing functionality
    console.log('Sharing quote...');
  };

  const handlePrint = () => {
    window.print();
  };

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const quoteNumber = `Q-${job.id.slice(-8).toUpperCase()}`;
  const currentDate = new Date();
  
  // Get business details from user profile
  const businessDetails = {
    name: profile?.company_name || profile?.full_name || "Your Business Name",
    tagline: "Professional Trade Services",
    address: profile?.address || "Your Business Address\nCity, Postcode\nUnited Kingdom",
    phone: profile?.phone || "Your Phone Number",
    email: profile?.email || "<EMAIL>"
  };

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-2 sm:p-4">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full h-full max-w-4xl max-h-full flex flex-col">
        {/* Header with controls */}
        <div className="flex items-center justify-between p-2 sm:p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
            <h2 className="text-sm sm:text-lg font-medium text-gray-900 dark:text-white truncate">
              Quote Preview
            </h2>
            <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 hidden sm:inline">
              {quoteNumber}
            </span>
          </div>
          
          {/* Zoom and action controls */}
          <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
            {/* Mobile: Show only essential controls */}
            <div className="flex items-center space-x-1 sm:hidden">
              <button
                onClick={handleZoomOut}
                className="p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                disabled={zoom <= 50}
              >
                <MagnifyingGlassMinusIcon className="w-4 h-4" />
              </button>
              
              <span className="text-xs text-gray-600 dark:text-gray-400 min-w-[2rem] text-center">
                {zoom}%
              </span>
              
              <button
                onClick={handleZoomIn}
                className="p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                disabled={zoom >= 200}
              >
                <MagnifyingGlassPlusIcon className="w-4 h-4" />
              </button>
              
              <button
                onClick={onClose}
                className="p-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ml-2"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>

            {/* Desktop: Show all controls */}
            <div className="hidden sm:flex items-center space-x-2">
              <button
                onClick={handleZoomOut}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                disabled={zoom <= 50}
              >
                <MagnifyingGlassMinusIcon className="w-5 h-5" />
              </button>
              
              <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[3rem] text-center">
                {zoom}%
              </span>
              
              <button
                onClick={handleZoomIn}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                disabled={zoom >= 200}
              >
                <MagnifyingGlassPlusIcon className="w-5 h-5" />
              </button>
              
              <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />
              
              <button
                onClick={handlePrint}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Print"
              >
                <PrinterIcon className="w-5 h-5" />
              </button>
              
              <button
                onClick={handleDownload}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Download PDF"
              >
                <ArrowDownTrayIcon className="w-5 h-5" />
              </button>
              
              <button
                onClick={handleShare}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Share"
              >
                <ShareIcon className="w-5 h-5" />
              </button>
              
              <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />
              
              <button
                onClick={onClose}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Quote document */}
        <div className="flex-1 overflow-auto bg-gray-100 dark:bg-gray-800 p-2 sm:p-8">
          <div 
            className="bg-white shadow-lg mx-auto max-w-2xl min-h-full"
            style={{ 
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'top center',
              marginBottom: zoom < 100 ? '2rem' : '0'
            }}
          >
            {/* Quote Header */}
            <div className="p-4 sm:p-8 border-b-2 border-blue-600">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-4 sm:mb-6 space-y-4 sm:space-y-0">
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">QUOTE</h1>
                  <p className="text-base sm:text-lg text-blue-600 font-medium">{quoteNumber}</p>
                </div>
                <div className="text-left sm:text-right">
                  <div className="text-xl sm:text-2xl font-bold text-blue-600 mb-2">{businessDetails.name}</div>
                  <div className="text-xs sm:text-sm text-gray-600 whitespace-pre-line">
                    {businessDetails.tagline}<br />
                    {businessDetails.address}
                  </div>
                  {businessDetails.phone && (
                    <div className="text-xs sm:text-sm text-gray-600 mt-1">{businessDetails.phone}</div>
                  )}
                  {businessDetails.email && (
                    <div className="text-xs sm:text-sm text-gray-600">{businessDetails.email}</div>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-8">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">Quote For:</h3>
                  <div className="text-gray-700">
                    <p className="font-medium text-sm sm:text-base">{job.client.name}</p>
                    <p className="text-xs sm:text-sm whitespace-pre-line">{job.client.address}</p>
                    {job.client.email && <p className="text-xs sm:text-sm">{job.client.email}</p>}
                    {job.client.phone && <p className="text-xs sm:text-sm">{job.client.phone}</p>}
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">Quote Details:</h3>
                  <div className="text-gray-700 text-xs sm:text-sm">
                    <p><span className="font-medium">Date:</span> {formatDate(currentDate)}</p>
                    <p><span className="font-medium">Valid Until:</span> {formatDate(new Date(currentDate.getTime() + 30 * 24 * 60 * 60 * 1000))}</p>
                    <p><span className="font-medium">Job:</span> {job.title}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quote Description */}
            <div className="p-4 sm:p-8 border-b border-gray-200">
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Work Description</h3>
              <div className="text-gray-700 whitespace-pre-line leading-relaxed text-sm sm:text-base">
                {quoteDescription}
              </div>
            </div>

            {/* Market Rate Intelligence Section */}
            {(marketRateWorkType || marketRateSuggestion || marketRateEstimatedRange) && (
              <div className="p-4 sm:p-8 border-b border-gray-200">
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Market Rate Information</h3>
                
                <div className="bg-blue-50 p-3 sm:p-4 rounded-lg border border-blue-200">
                  <div className="space-y-3">
                    {marketRateWorkType && (
                      <div>
                        <span className="text-sm font-medium text-blue-900">Work Type:</span>
                        <p className="text-blue-800 text-sm mt-1">{marketRateWorkType}</p>
                      </div>
                    )}
                    
                    {marketRateSuggestion && (
                      <div>
                        <span className="text-sm font-medium text-blue-900">Market Analysis:</span>
                        <p className="text-blue-800 text-sm mt-1">{marketRateSuggestion}</p>
                      </div>
                    )}
                    
                    {marketRateEstimatedRange && (
                      <div>
                        <span className="text-sm font-medium text-blue-900">Estimated Range:</span>
                        <p className="text-blue-800 text-sm mt-1 font-medium">{marketRateEstimatedRange}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Terms and Conditions */}
            <div className="p-4 sm:p-8">
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Terms & Conditions</h3>
              <div className="text-gray-700 text-xs sm:text-sm whitespace-pre-line leading-relaxed">
                {termsAndConditions}
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 sm:p-8 bg-gray-50 border-t border-gray-200">
              <div className="text-center text-xs sm:text-sm text-gray-600">
                <p className="mb-2">Thank you for considering our services.</p>
                <p>This quote is valid for 30 days from the date of issue.</p>
                <p className="mt-4 font-medium">{businessDetails.name}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 