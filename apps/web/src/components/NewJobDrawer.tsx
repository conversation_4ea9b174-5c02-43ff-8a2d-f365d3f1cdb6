'use client';

import React, { useState, useEffect, useRef } from 'react';
import { CreateJobData } from '@/types/Job';
import { useClients } from '@/hooks/useClients';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { Input } from '@deskbelt/ui';
import { Select } from '@deskbelt/ui';
import { 
  XMarkIcon, 
  PaperAirplaneIcon,
  CheckIcon,
  UserIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import { useDrawer } from '@/contexts/DrawerContext';

interface ClientOption {
  id: string;
  name: string;
  address: string;
}

interface NewJobDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onJobCreated?: (jobData: CreateJobData) => void;
  preSelectedClientId?: string | null;
  onRequestNewClient?: () => void;
}

interface ChatMessage {
  id: string;
  type: 'dex' | 'user';
  content: string;
  timestamp: Date;
}

type ConversationState = 'collecting-job-info' | 'selecting-client' | 'ready-to-create';

export const NewJobDrawer: React.FC<NewJobDrawerProps> = ({ 
  isOpen, 
  onClose, 
  onJobCreated,
  preSelectedClientId,
  onRequestNewClient
}) => {
  const { onJobCreated: globalOnJobCreated } = useDrawer();
  const { authenticatedPost, authenticatedFetch } = useAuthenticatedFetch();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [conversationState, setConversationState] = useState<ConversationState>('collecting-job-info');
  const [showClientDropdown, setShowClientDropdown] = useState(false);
  const [showJobReview, setShowJobReview] = useState(false);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [jobData, setJobData] = useState<CreateJobData>({
    title: '',
    client_id: preSelectedClientId || '',
    client_name: '',
    description: '',
    scheduled_at: '',
    team_id: '',
    status: 'new'
  });
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Fetch real clients
  const { data: clientOptions, isLoading: clientsLoading } = useClients({ limit: 100 });

  // Handle pre-selected client
  useEffect(() => {
    if (isOpen && preSelectedClientId && clientOptions && clientOptions.length > 0) {
      const preSelectedClient = clientOptions.find(client => client.id === preSelectedClientId);
      if (preSelectedClient) {
        // Set the job data with pre-selected client
        setJobData(prev => ({
          ...prev,
          client_id: preSelectedClientId,
          client_name: preSelectedClient.name
        }));
        
        // Add welcome message mentioning the pre-selected client
        if (messages.length <= 1) { // Only if we haven't started the conversation yet
          const clientMessage = `I see you want to create a job for ${preSelectedClient.name}. What work needs doing?`;
          setMessages(prev => [
            ...prev,
            {
              id: crypto.randomUUID(),
              type: 'dex',
              content: clientMessage,
              timestamp: new Date()
            }
          ]);
        }
      }
    }
  }, [isOpen, preSelectedClientId, clientOptions, messages.length]);

  // Auto-scroll to bottom whenever messages change or new elements appear
  useEffect(() => {
    const scrollToBottom = () => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };
    
    // Use requestAnimationFrame to ensure DOM has been updated
    requestAnimationFrame(() => {
      setTimeout(scrollToBottom, 100);
    });
  }, [messages, showClientDropdown, showJobReview, isTyping]);

  // Initialize with Dex greeting
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const hasUsedBefore = localStorage.getItem('dex-drawer-used') === 'true';
      
      const greeting = hasUsedBefore 
        ? "What job needs logging today? Give me the rough outline of the work."
        : "Right, I'm Dex! I help log jobs. Give me a rough outline of the work that needs doing.";
      
      setMessages([
        {
          id: '1',
          type: 'dex',
          content: greeting,
          timestamp: new Date()
        }
      ]);
      setTimeout(() => inputRef.current?.focus(), 100);
      
      if (!hasUsedBefore) {
        localStorage.setItem('dex-drawer-used', 'true');
      }
    }
  }, [isOpen, messages.length]);

  const addMessage = (type: 'dex' | 'user', content: string, delay: number = 0) => {
    setTimeout(() => {
      const newMessage: ChatMessage = {
        id: crypto.randomUUID(),
        type,
        content,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, newMessage]);
    }, delay);
  };

  const showTyping = (duration: number = 1500) => {
    setIsTyping(true);
    setTimeout(() => setIsTyping(false), duration);
  };

  const generateIntelligentResponse = async (userInput: string, description: string) => {
    try {
      // Use AI to analyze what information we have and what we need
      const analysisPrompt = `You are Dex, a sharp, no-fuss assistant that helps UK tradespeople (electricians, plumbers, joiners, roofers, decorators, gardeners, cleaners, etc.) log jobs quickly and clearly for their clients. Your replies are turned directly into job records, so clarity matters — but perfection doesn't.

OBJECTIVE:
Your goal is to help the user get to a half-decent job description, fast. "Half-decent" means the task includes at least:
- One clear action (e.g. rewire, fit doorframes, install sockets), and  
- One qualifier or detail (e.g. number, property type, size, type of item).

Examples:
- "Fit 6 doorframes" → Good enough
- "Rewire terraced house" → Good enough  
- "Rewire" → Not enough
- "Doorframes" → Not enough

If the message is good enough: log the job. If not: guide the user, briefly.

JOB CREATION FORMAT:
When input is valid and enough, provide this exact format:

Job Title: [Short, clear job name – max 6 words]
Description: [What needs doing. Plain UK English. Keep it punchy.]

That signals the system to move to client selection.
- Never ask about address or location.
- Don't include tools, pricing, or customer names unless the user gives them.

IF IT'S VAGUE:
If the task is too vague (e.g. "boiler" or "garden"), ask one quick follow-up to nudge the user toward a clearer description.
- Only ask one or two, and don't repeat yourself.
- If they ignore you or waffle, log the best job you can and move on.

IF IT'S GIBBERISH OR NOT A TRADE TASK:
If the user types something off-topic (e.g. "marry me", jokes, nonsense):
- Stay cool. Don't get awkward. Give a quick, witty or dry nudge like:
- "That's sweet, but I only log actual jobs."
- "Unless you're wiring a ring main, I can't help with that."
- Then wait for a real job outline. Don't log anything unless it's real work.

FLOW CONTROL:
- Keep things moving toward job creation. Don't over-question.
- Never echo the same phrases. Respond like a tradesman would.
- If they say something rough but usable, just tidy it up and log the job.

STYLE & TONE:
- Sound like a friendly UK tradesman — to-the-point, bit of dry humour, zero waffle.
- Plain UK English. No corporate tone.
- One emoji max per job (optional, not required).
- No "next step" filler unless genuinely helpful.

Current job description: "${description}"
User's latest input: "${userInput}"`;
      
      const response = await authenticatedPost('/api/ai/chat-response', { 
        prompt: analysisPrompt,
        input: `Description: ${description}\nUser said: ${userInput}`
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.message) {
          return result.message;
        }
      }
    } catch (error) {
      console.error('AI response error:', error);
    }
    
    // If AI is completely down, return null to trigger manual mode
    return null;
  };

  const processUserInput = async (input: string) => {
    addMessage('user', input);
    setCurrentInput('');
    showTyping(); // Start typing indicator immediately
    
    try {
      if (conversationState === 'collecting-job-info') {
        const updatedDescription = jobData.description ? `${jobData.description} ${input}` : input;
        setJobData(prev => ({ ...prev, description: updatedDescription }));
        
        // Always get AI response first - NO FALLBACKS
        const aiResponse = await generateIntelligentResponse(input, updatedDescription);
        
        // If AI fails, show error message and stop
        if (aiResponse === null) {
          addMessage('dex', "I'm having connection issues with the AI. Please try again later.", 500);
          return;
        }
        
        // NO AUTOMATIC JOB PARSING - Let the AI chat decide when to proceed
        
        // Check if AI response contains a complete job specification
        const hasJobTitle = aiResponse.toLowerCase().includes('job title:') || aiResponse.toLowerCase().includes('job:');
        const hasDescription = aiResponse.toLowerCase().includes('description:');
        
        if (hasJobTitle && hasDescription) {
          // Extract job title and description from AI response
          const lines = aiResponse.split('\n');
          let extractedTitle = '';
          let extractedDescription = '';
          
          for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine.toLowerCase().startsWith('job title:') || trimmedLine.toLowerCase().startsWith('job:')) {
              extractedTitle = trimmedLine.replace(/^job title:\s*/i, '').replace(/^job:\s*/i, '').trim();
            }
            if (trimmedLine.toLowerCase().startsWith('description:')) {
              extractedDescription = trimmedLine.replace(/^description:\s*/i, '').trim();
            }
          }
          
          // Update job data with extracted information
          if (extractedTitle && extractedDescription) {
            setJobData(prev => ({ 
              ...prev, 
              title: extractedTitle,
              description: extractedDescription 
            }));
            
            // Show AI response first with minimal delay
            addMessage('dex', aiResponse, 500);
            
            // Then proceed to client selection
            addMessage('dex', `Perfect! Which client is this for?`, 500);
            setTimeout(() => {
              setShowClientDropdown(true);
              setConversationState('selecting-client');
            }, 1000);
            return;
          }
        }
        
        // Show AI response to continue the conversation with minimal delay
        addMessage('dex', aiResponse, 500);
        
      } else if (conversationState === 'selecting-client') {
        const matchedClient = clientOptions.find(client => 
          input.toLowerCase().includes(client.name.toLowerCase()) || 
          client.name.toLowerCase().includes(input.toLowerCase())
        );
        
        if (matchedClient) {
          handleClientSelection(matchedClient.id);
        } else if (input.toLowerCase().includes('new')) {
          addMessage('dex', "Great! What's the new client's name and address?", 500);
        } else {
          addMessage('dex', "I couldn't find that client. Use the dropdown above or say 'new client'!", 500);
        }
        
      } else if (conversationState === 'ready-to-create') {
        if (input.toLowerCase().includes('yes') || input.toLowerCase().includes('create')) {
          handleCreateJob();
        } else {
          addMessage('dex', 'No problem! What would you like to change? 🤔', 500);
          setConversationState('collecting-job-info');
        }
      }
    } catch (error) {
      console.error('Error processing user input:', error);
      addMessage('dex', "Something went wrong. Please try again.", 500);
    }
  };

  const handleClientSelection = (clientId: string) => {
    if (clientId === '__new__') {
      onRequestNewClient?.();
      addMessage('dex', 'Sure! Let\'s add a new client. Once you\'ve saved them, come back and we\'ll finish logging the job.', 500);
      setConversationState('collecting-job-info');
      setShowClientDropdown(false);
      return;
    }
    const selectedClient = clientOptions.find(c => c.id === clientId);
    if (!selectedClient) return;
    
    // Store both the id and name so we can send name if id is invalid for backend lookup/creation
    setJobData(prev => ({ ...prev, client_id: clientId, client_name: selectedClient.name }));
    setShowClientDropdown(false);
    
    addMessage('dex', `Perfect! Creating job for ${selectedClient.name}... ✨`, 1000);
    
    setTimeout(() => {
      handleCreateJob(clientId); // Pass clientId directly
    }, 2000);
  };

  const handleCreateJob = async (overrideClientId?: string) => {
    showTyping(500);
    
    const clientId = overrideClientId || jobData.client_id;
    
    try {
      // Create job via API
      const response = await authenticatedPost('/api/jobs', {
        title: jobData.title,
        client_id: clientId,
        client_name: jobData.client_name, // send name as fallback for backend to create/search
        description: jobData.description,
        scheduled_at: jobData.scheduled_at || null,
        team_id: jobData.team_id || null,
      });

      if (!response.ok) {
        const errorText = await response.text();
        // Inform user about missing client if bad request
        if (response.status === 400) {
          addMessage('dex', "I couldn't create that job because the client doesn't exist yet. Head over to the Clients page and add them first, then we'll log the job! 🗂️", 500);
          return;
        }
        throw new Error(`Failed to create job: ${response.status} ${errorText}`);
      }

      const createdJob = await response.json();
      
      // Update local jobData with the returned ID and ensure client_id is set
      const completeJobData = {
        ...jobData,
        id: createdJob.id,
        client_id: clientId, // Use the clientId parameter, not jobData.client_id
        status: 'new' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      setJobData(completeJobData);
      
      addMessage('dex', 'Job created successfully! 🎉', 500);
      
      setTimeout(() => {
        setShowJobReview(true);
        setConversationState('ready-to-create');
      }, 1000);
      
      // Notify global listeners to refresh jobs list
      onJobCreated?.(completeJobData);
      globalOnJobCreated?.();
      
    } catch (error) {
      console.error('Error creating job:', error);
      console.error('Job data being sent:', jobData);
      
      // More detailed error logging
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (error instanceof TypeError && errorMessage.includes('Failed to fetch')) {
        console.error('Network error - API server may be down or unreachable');
        addMessage('dex', 'Cannot reach the server. Is the API running on port 3001? 🔌', 500);
      } else {
        addMessage('dex', `Error: ${errorMessage || 'Something went wrong creating the job'}. Want to try again? 😅`, 500);
      }
    }
  };

  const handleClose = () => {
    setMessages([]);
    setConversationState('collecting-job-info');
    setShowClientDropdown(false);
    setShowJobReview(false);
    setEditingField(null);
    setEditValue('');
    setJobData({
      title: '',
      client_id: preSelectedClientId || '',
      client_name: '',
      description: '',
      scheduled_at: '',
      team_id: '',
      status: 'new'
    });
    setCurrentInput('');
    onClose();
  };

  const handleEditField = (field: string, currentValue: string) => {
    setEditingField(field);
    setEditValue(currentValue);
  };

  const handleSaveField = async () => {
    if (editingField && editValue.trim()) {
      const updatedData = editingField === 'client_id' 
        ? { ...jobData, client_id: editValue }
        : { ...jobData, [editingField]: editValue.trim() };
      
      // Update local state first
      setJobData(updatedData);
      
      // If job has been created (has ID), update via API
      if (jobData.id) {
        try {
          const response = await authenticatedFetch(`/api/jobs/${jobData.id}`, {
            method: 'PUT',
            body: JSON.stringify({
              title: updatedData.title,
              client_id: updatedData.client_id,
              description: updatedData.description,
              scheduled_at: updatedData.scheduled_at,
              team_id: updatedData.team_id,
            }),
          });

          if (!response.ok) {
            throw new Error(`Failed to update job: ${response.status}`);
          }

          // Job updated successfully
          
          // Notify global listeners to refresh jobs list
          globalOnJobCreated?.();
          
        } catch (error) {
          console.error('❌ Error updating job:', error);
          // Could show a toast notification here
        }
      }
    }
    setEditingField(null);
    setEditValue('');
  };

  // Handle blur with slight delay to allow button clicks to process first
  const handleFieldBlur = () => {
    setTimeout(() => {
      if (editingField) { // Only save if still in edit mode
        handleSaveField();
      }
    }, 100);
  };

  const handleCancelEdit = () => {
    setEditingField(null);
    setEditValue('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (currentInput.trim()) {
        processUserInput(currentInput.trim());
      }
    }
  };

  return (
    <Drawer
      isOpen={isOpen}
      onClose={handleClose}
      side="right"
      size="xl"
      showCloseButton={false}
      className="top-16 h-[calc(100%-4rem)] md:left-1/2 md:-translate-x-1/2 md:right-auto"
    >
      <div className="h-full flex flex-col bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 overflow-x-hidden">
        <div className="h-full flex flex-col max-w-4xl mx-auto w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white/80 backdrop-blur-sm">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">🤖</span>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Dex</h2>
              <p className="text-sm text-gray-500">Your AI job assistant</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={handleClose}>
            <XMarkIcon className="w-5 h-5" />
          </Button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] px-4 py-3 rounded-2xl ${
                  message.type === 'user'
                    ? 'bg-blue-500 text-white rounded-br-md'
                    : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm border rounded-bl-md'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <span className="text-xs opacity-70 mt-1 block">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
              </div>
            </div>
          ))}
          
          {/* Typing indicator */}
          {isTyping && (
            <div className="flex justify-start">
              <div className="bg-white dark:bg-gray-700 shadow-sm border px-4 py-3 rounded-2xl rounded-bl-md">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}

          {/* Client Selection Dropdown */}
          {showClientDropdown && (
            <div className="bg-blue-50 dark:bg-gray-800 p-4 rounded-lg border border-blue-200 dark:border-gray-600 space-y-3">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Select a client:</p>
              <Select
                value={jobData.client_id}
                onChange={(value: string) => {
                  handleClientSelection(value);
                }}
                placeholder="Choose a client..."
                className="w-full"
                options={[...clientOptions.map(client => ({
                  value: client.id,
                  label: `${client.name} - ${client.address}`
                })), { value: '__new__', label: '➕ Add New Client' }]}
              />
            </div>
          )}

          {/* Job Review Section */}
          {showJobReview && (
            <div className="bg-white dark:bg-gray-800 border-2 border-green-200 dark:border-green-700 rounded-lg p-4 space-y-4">
              <div className="flex items-center space-x-2 mb-3">
                <CheckIcon className="w-5 h-5 text-green-600" />
                <h3 className="font-semibold text-gray-900 dark:text-white">Job Created Successfully!</h3>
              </div>
              
              {/* Editable Job Title */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Job Title</label>
                {editingField === 'title' ? (
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <Input
                        value={editValue}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditValue(e.target.value)}
                        onBlur={handleFieldBlur}
                        onKeyDown={(e: React.KeyboardEvent) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleSaveField();
                          } else if (e.key === 'Escape') {
                            handleCancelEdit();
                          }
                        }}
                        className="flex-1 border-blue-500 ring-2 ring-blue-200"
                        autoFocus
                      />
                      <Button size="sm" onClick={handleSaveField}>Save</Button>
                      <Button size="sm" variant="outline" onClick={handleCancelEdit}>Cancel</Button>
                    </div>
                    <p className="text-xs text-blue-600">Press Enter or click out to save</p>
                  </div>
                ) : (
                  <div 
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                    onClick={() => handleEditField('title', jobData.title)}
                  >
                    <span className="text-gray-900 dark:text-white">📋 {jobData.title}</span>
                    <PencilIcon className="w-4 h-4 text-gray-400" />
                  </div>
                )}
              </div>

                            {/* Editable Client Info */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Client</label>
                {editingField === 'client_id' ? (
                  <div className="space-y-3">
                    <Select
                      value={editValue}
                      onChange={(value: string) => setEditValue(value)}
                      placeholder="Choose a client..."
                      className="w-full"
                      options={[...clientOptions.map(client => ({
                        value: client.id,
                        label: `${client.name} - ${client.address}`
                      })), { value: '__new__', label: '➕ Add New Client' }]}
                    />
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={handleSaveField}>Save</Button>
                      <Button size="sm" variant="outline" onClick={handleCancelEdit}>Cancel</Button>
                    </div>
                  </div>
                ) : (
                  <div 
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                    onClick={() => handleEditField('client_id', jobData.client_id)}
                  >
                    <span className="text-gray-900 dark:text-white whitespace-normal break-words max-w-full">
                      👤 {clientOptions.find(c => c.id === jobData.client_id)?.name || 'Unknown Client'}
                    </span>
                    <PencilIcon className="w-4 h-4 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Editable Description */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                {editingField === 'description' ? (
                  <div className="space-y-2">
                    <textarea
                      value={editValue}
                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setEditValue(e.target.value)}
                      onBlur={handleFieldBlur}
                      onKeyDown={(e: React.KeyboardEvent) => {
                        if (e.key === 'Enter' && e.ctrlKey) {
                          e.preventDefault();
                          handleSaveField();
                        } else if (e.key === 'Escape') {
                          handleCancelEdit();
                        }
                      }}
                      className="w-full p-3 border rounded-lg resize-none border-blue-500 ring-2 ring-blue-200"
                      rows={3}
                      autoFocus
                    />
                    <p className="text-xs text-blue-600">Ctrl+Enter or click out to save</p>
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={handleSaveField}>Save</Button>
                      <Button size="sm" variant="outline" onClick={handleCancelEdit}>Cancel</Button>
                    </div>
                  </div>
                ) : (
                  <div 
                    className="flex items-start justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                    onClick={() => handleEditField('description', jobData.description)}
                  >
                    <span className="text-gray-900 dark:text-white flex-1">📝 {jobData.description}</span>
                    <PencilIcon className="w-4 h-4 text-gray-400 ml-2 mt-1" />
                  </div>
                )}
              </div>

              <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                💡 Tap to edit • Click out or press Enter to save • Escape to cancel
              </p>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white/80 backdrop-blur-sm">
          <div className="flex items-end space-x-2">
            <textarea
              ref={inputRef}
              value={currentInput}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                setCurrentInput(e.target.value);
                // Auto-resize textarea
                e.target.style.height = 'auto';
                e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';
              }}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="flex-1 resize-none rounded-2xl px-4 py-3 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent max-h-32 min-h-[48px]"
              style={{
                height: 'auto',
                minHeight: '48px',
                maxHeight: '128px',
                overflowY: currentInput.split('\n').length > 4 ? 'auto' : 'hidden'
              }}
              rows={Math.min(Math.max(currentInput.split('\n').length, 1), 5)}
              disabled={isTyping}
            />
            <Button
              onClick={() => currentInput.trim() && processUserInput(currentInput.trim())}
              disabled={!currentInput.trim() || isTyping}
              className="rounded-full p-3 bg-blue-500 hover:bg-blue-600 text-white self-end mb-1"
            >
              <PaperAirplaneIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>
        </div>
      </div>
    </Drawer>
  );
}; 