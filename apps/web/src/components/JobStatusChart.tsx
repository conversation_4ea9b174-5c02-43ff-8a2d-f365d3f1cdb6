'use client';

import { Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
} from 'chart.js';
import { InteractionData } from '@/hooks/useDashboardStats';

// Register Chart.js components
ChartJS.register(
  ArcE<PERSON>,
  Tooltip,
  Legend
);

interface InteractionChartProps {
  data: InteractionData[];
  isDarkMode?: boolean;
}

function InteractionChart({ data, isDarkMode = false }: InteractionChartProps) {
  const chartData = {
    labels: data.map(item => item.type),
    datasets: [
      {
        data: data.map(item => item.count),
        backgroundColor: data.map(item => item.color),
        borderColor: isDarkMode ? '#1f2937' : '#ffffff',
        borderWidth: 2,
        hoverBackgroundColor: data.map(item => item.color),
        hoverBorderWidth: 3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '60%',
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          color: isDarkMode ? '#d1d5db' : '#374151',
          padding: 20,
          font: {
            size: 12,
          },
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      tooltip: {
        backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
        titleColor: isDarkMode ? '#f9fafb' : '#111827',
        bodyColor: isDarkMode ? '#d1d5db' : '#374151',
        borderColor: isDarkMode ? '#374151' : '#d1d5db',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((context.parsed / total) * 100).toFixed(1);
            return `${context.label}: ${context.parsed} (${percentage}%)`;
          }
        }
      },
    },
    elements: {
      arc: {
        borderRadius: 4,
      },
    },
    interaction: {
      intersect: false,
    },
  };

  const totalInteractions = data.reduce((total, item) => total + item.count, 0);

  return (
    <div className="card-interactive group/chart animate-fade-in-up hover:border-accent-200 dark:hover:border-accent-700">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50 group-hover/chart:text-accent-600 dark:group-hover/chart:text-accent-400 transition-colors duration-200">
          Client Interactions
        </h3>
        <p className="text-sm text-secondary-600 dark:text-secondary-400 group-hover/chart:text-secondary-700 dark:group-hover/chart:text-secondary-300 transition-colors duration-200">
          Total interactions sent: {totalInteractions.toLocaleString()}
        </p>
      </div>
      <div className="h-80 relative transform group-hover/chart:scale-[1.01] transition-transform duration-300">
        <Doughnut data={chartData} options={options} />
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center transform group-hover/chart:scale-105 transition-transform duration-200">
            <div className="text-2xl font-bold text-secondary-900 dark:text-secondary-50 group-hover/chart:text-accent-600 dark:group-hover/chart:text-accent-400 transition-colors duration-200">
              {totalInteractions}
            </div>
            <div className="text-sm text-secondary-600 dark:text-secondary-400 group-hover/chart:text-secondary-700 dark:group-hover/chart:text-secondary-300 transition-colors duration-200">
              Total Sent
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default InteractionChart; 