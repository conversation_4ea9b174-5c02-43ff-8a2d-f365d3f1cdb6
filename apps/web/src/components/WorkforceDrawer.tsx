import React, { useState, useEffect } from 'react';
import { XMarkIcon, UserGroupIcon, CogIcon, ShieldCheckIcon, PlusIcon, EnvelopeIcon, EyeIcon } from '@heroicons/react/24/outline';
import { useWorkforceSettings, WorkforceSettings, TeamMember } from '../hooks/useWorkforceSettings';
import { useWorkforce } from '../hooks/useWorkforce';
import { useAuth } from '@/contexts/AuthContext';
import TeamMembersDrawer from './TeamMembersDrawer';
import InviteTeamMemberDrawer from './InviteTeamMemberDrawer';
import InvitationManagementDrawer from './InvitationManagementDrawer';
import MemberPermissionsDrawer from './MemberPermissionsDrawer';

interface WorkforceDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

type TabType = 'members' | 'permissions' | 'invitations' | 'settings';

interface DrawerState {
  teamMembers: boolean;
  inviteTeamMember: boolean;
  invitationManagement: boolean;
  memberPermissions: boolean;
}

interface SelectedMember {
  id: string;
  name: string;
  email: string;
  permissions: {
    can_manage_jobs: boolean;
    can_manage_clients: boolean;
    can_manage_invoices: boolean;
    can_manage_quotes: boolean;
    can_view_reports: boolean;
    can_manage_team: boolean;
  };
}

// Helper for toast notifications
const showToast = (message: string, type: 'success' | 'error' = 'success') => {
  // In a real app, this would trigger a proper toast component
  console.log(`[${type.toUpperCase()}] ${message}`);
};

export default function WorkforceDrawer({ isOpen, onClose }: WorkforceDrawerProps) {
  const [activeTab, setActiveTab] = useState<TabType>('members');
  const [drawers, setDrawers] = useState<DrawerState>({
    teamMembers: false,
    inviteTeamMember: false,
    invitationManagement: false,
    memberPermissions: false
  });
  const [selectedMember, setSelectedMember] = useState<SelectedMember | null>(null);
  const { workforce, isLoading, error } = useWorkforce();

  const openDrawer = (drawer: keyof DrawerState) => {
    setDrawers(prev => ({ ...prev, [drawer]: true }));
  };

  const closeDrawer = (drawer: keyof DrawerState) => {
    setDrawers(prev => ({ ...prev, [drawer]: false }));
  };

  const handleInvitationSent = () => {
    // Refresh any invitation-related data if needed
    showToast('Invitation sent successfully!', 'success');
  };

  const openMemberPermissions = (member: TeamMember) => {
    setSelectedMember({
      id: member.user_id,
      name: member.users.full_name || member.users.email,
      email: member.users.email,
      permissions: {
        can_manage_jobs: member.can_manage_jobs,
        can_manage_clients: member.can_manage_clients,
        can_manage_invoices: member.can_manage_invoices,
        can_manage_quotes: member.can_manage_quotes,
        can_view_reports: member.can_view_reports,
        can_manage_team: member.can_manage_team
      }
    });
    openDrawer('memberPermissions');
  };

  const handlePermissionsUpdated = () => {
    // Refresh member data
    showToast('Member permissions updated successfully!', 'success');
  };

  if (!isOpen) return null;

  const renderContent = () => {
    if (isLoading) {
      return <div className="p-6 text-center">Loading your workforce...</div>;
    }

    if (error) {
      return <div className="p-6 text-center text-red-500">Error: {error}</div>;
    }

    if (!workforce) {
      return <div className="p-6 text-center">Unable to load workforce.</div>;
    }

    switch (activeTab) {
      case 'members':
        return <WorkforceMembersContent openDrawer={openDrawer} />;
      case 'permissions':
        return <PermissionsContent openMemberPermissions={openMemberPermissions} />;
      case 'invitations':
        return <InvitationsContent openDrawer={openDrawer} />;
      case 'settings':
        return <SettingsContent />;
      default:
        return null;
    }
  };

  const tabs = [
    { id: 'members' as const, label: 'Members', icon: UserGroupIcon },
    { id: 'permissions' as const, label: 'Permissions', icon: ShieldCheckIcon },
    { id: 'invitations' as const, label: 'Invitations', icon: EnvelopeIcon },
    { id: 'settings' as const, label: 'Settings', icon: CogIcon },
  ];

  return (
    <div className="fixed inset-0 z-40 flex justify-end bg-black bg-opacity-50">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="relative w-full max-w-md bg-white dark:bg-gray-900 shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-secondary-200 dark:border-secondary-700">
          <div>
            <h2 className="text-lg font-medium text-secondary-900 dark:text-secondary-50">Your Workforce</h2>
            <p className="text-sm text-secondary-600 dark:text-secondary-400">Manage workforce members and settings</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300 rounded-xl transition-all duration-200 hover:scale-105 focus-ring"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 flex items-center justify-center px-4 py-3 text-sm font-medium border-b-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto px-6 py-6">
          {renderContent()}
        </div>
      </div>

      {/* Enhanced Drawers */}
      <TeamMembersDrawer
        isOpen={drawers.teamMembers}
        onClose={() => closeDrawer('teamMembers')}
        teamData={workforce}
      />

      <InviteTeamMemberDrawer
        isOpen={drawers.inviteTeamMember}
        onClose={() => closeDrawer('inviteTeamMember')}
        teamId={workforce?.id || null}
        onInvitationSent={handleInvitationSent}
      />

      <InvitationManagementDrawer
        isOpen={drawers.invitationManagement}
        onClose={() => closeDrawer('invitationManagement')}
        teamId={workforce?.id || null}
      />

      <MemberPermissionsDrawer
        isOpen={drawers.memberPermissions}
        onClose={() => closeDrawer('memberPermissions')}
        teamId={workforce?.id || null}
        memberId={selectedMember?.id || null}
        memberName={selectedMember?.name || ''}
        memberEmail={selectedMember?.email || ''}
        currentPermissions={selectedMember?.permissions || {
          can_manage_jobs: false,
          can_manage_clients: false,
          can_manage_invoices: false,
          can_manage_quotes: false,
          can_view_reports: false,
          can_manage_team: false
        }}
        onPermissionsUpdated={handlePermissionsUpdated}
      />
    </div>
  );
}

// Workforce Members Tab Content
function WorkforceMembersContent({ openDrawer }: { openDrawer: (drawer: keyof DrawerState) => void }) {
  const { user } = useAuth();
  const { members, loading, error } = useWorkforceSettings();
  const { workforce } = useWorkforce();

  const getInitials = (member: TeamMember) => {
    if (member.users.full_name) {
      return member.users.full_name.split(' ').map(n => n[0]).join('').toUpperCase();
    }
    return member.users.email.substring(0, 2).toUpperCase();
  };

  const getDisplayName = (member: TeamMember) => {
    return member.users.full_name || member.users.email;
  };

  const isOwner = (member: TeamMember) => {
    return member.user_id === user?.id;
  };

  const getPermissionSummary = (member: TeamMember) => {
    const permissions = [
      member.can_manage_jobs && 'Jobs',
      member.can_manage_clients && 'Clients',
      member.can_manage_invoices && 'Invoices',
      member.can_manage_quotes && 'Quotes',
      member.can_view_reports && 'Reports',
      member.can_manage_team && 'Team'
    ].filter(Boolean);
    
    return permissions.length > 0 ? permissions.join(', ') : 'View only';
  };

  if (loading) return <div>Loading members...</div>;
  if (error) return <div className="text-red-500">Error: {error}</div>;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
          Team Members ({members.length})
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={() => openDrawer('teamMembers')}
            className="flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
          >
            <EyeIcon className="w-4 h-4 mr-1.5" />
            View All
          </button>
          <button
            onClick={() => openDrawer('inviteTeamMember')}
            className="flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="w-4 h-4 mr-1.5" />
            Invite Member
          </button>
        </div>
      </div>
      
      <div className="space-y-3">
        {members.slice(0, 3).map((member) => (
          <div key={member.user_id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                {getInitials(member)}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {getDisplayName(member)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {isOwner(member) ? 'Owner' : member.role} • {getPermissionSummary(member)}
                </p>
              </div>
            </div>
            <div className="text-xs text-gray-400 dark:text-gray-500">
              {new Date(member.joined_at).toLocaleDateString()}
            </div>
          </div>
        ))}
        
        {members.length === 0 && (
          <div className="text-center py-8">
            <UserGroupIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">No Team Members</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              Start building your workforce by inviting team members
            </p>
            <button
              onClick={() => openDrawer('inviteTeamMember')}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              Invite First Member
            </button>
          </div>
        )}

        {members.length > 3 && (
          <div className="text-center py-2">
            <button
              onClick={() => openDrawer('teamMembers')}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
            >
              View all {members.length} members →
            </button>
          </div>
        )}
      </div>

      <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">
          Quick Actions
        </h4>
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => openDrawer('inviteTeamMember')}
            className="flex items-center justify-center px-3 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 bg-white dark:bg-blue-800/30 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-800/50 transition-colors"
          >
            <PlusIcon className="w-4 h-4 mr-1.5" />
            Invite Member
          </button>
          <button
            onClick={() => openDrawer('invitationManagement')}
            className="flex items-center justify-center px-3 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 bg-white dark:bg-blue-800/30 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-800/50 transition-colors"
          >
            <EnvelopeIcon className="w-4 h-4 mr-1.5" />
            View Invitations
          </button>
        </div>
      </div>
    </div>
  );
}

// Permissions Tab Content
function PermissionsContent({ openMemberPermissions }: { openMemberPermissions: (member: TeamMember) => void }) {
  const { members, updateMemberPermissions, loading, error } = useWorkforceSettings();
  const { user } = useAuth();

  const handleTogglePermission = async (memberId: string, permission: keyof TeamMember, currentValue: boolean) => {
    const result = await updateMemberPermissions(memberId, {
      [permission]: !currentValue
    });
    
    if (!result) {
      showToast('Failed to update permission', 'error');
    } else {
      showToast('Permission updated successfully.', 'success');
    }
  };

  const getInitials = (member: TeamMember) => {
    if (member.users.full_name) {
      return member.users.full_name.split(' ').map(n => n[0]).join('').toUpperCase();
    }
    return member.users.email.substring(0, 2).toUpperCase();
  };

  const getDisplayName = (member: TeamMember) => {
    return member.users.full_name || member.users.email;
  };

  const isOwner = (member: TeamMember) => {
    return member.user_id === user?.id;
  };

  if (loading) return <div>Loading permissions...</div>;
  if (error) return <div className="text-red-500">Error: {error}</div>;

  const permissionItems = [
    { 
      key: 'can_manage_jobs' as keyof TeamMember,
      title: 'Manage Jobs', 
      description: 'Create, edit, and delete jobs'
    },
    { 
      key: 'can_manage_clients' as keyof TeamMember,
      title: 'Manage Clients', 
      description: 'Add, edit, and delete client information'
    },
    { 
      key: 'can_manage_invoices' as keyof TeamMember,
      title: 'Manage Invoices', 
      description: 'Create and manage invoices'
    },
    { 
      key: 'can_manage_quotes' as keyof TeamMember,
      title: 'Manage Quotes', 
      description: 'Create and manage quotes'
    },
    { 
      key: 'can_view_reports' as keyof TeamMember,
      title: 'View Reports', 
      description: 'Access financial reports and analytics'
    },
    { 
      key: 'can_manage_team' as keyof TeamMember,
      title: 'Manage Team', 
      description: 'Add/remove members and change permissions'
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
          Member Permissions
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Configure individual permissions for each team member.
        </p>
      </div>

      {members.filter(member => !isOwner(member)).map((member) => (
        <div key={member.user_id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                {getInitials(member)}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {getDisplayName(member)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Member</p>
              </div>
            </div>
            <button
              onClick={() => openMemberPermissions(member)}
              className="px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
            >
              Manage
            </button>
          </div>

          <div className="space-y-3">
            {permissionItems.map((item) => (
              <div key={item.key} className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">{item.title}</h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{item.description}</p>
                </div>
                                  <button
                    type="button"
                    className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                      member[item.key] ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                    role="switch"
                    aria-checked={Boolean(member[item.key])}
                    onClick={() => handleTogglePermission(member.user_id, item.key, member[item.key] as boolean)}
                  >
                  <span
                    aria-hidden="true"
                    className={`inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                      member[item.key] ? 'translate-x-5' : 'translate-x-0'
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>
        </div>
      ))}

      {members.filter(member => !isOwner(member)).length === 0 && (
        <div className="text-center py-8">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            No team members to configure permissions for. Invite members in the Members tab.
          </p>
        </div>
      )}
    </div>
  );
}

// Settings Tab Content
function SettingsContent() {
  const { settings, loading, error, updateSettings } = useWorkforceSettings();
  const [localSettings, setLocalSettings] = useState<WorkforceSettings | null>(null);

  useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
    }
  }, [settings]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setLocalSettings(prev => prev ? { ...prev, [name]: value } : null);
  };

  const handleToggleChange = (name: keyof Pick<WorkforceSettings, 'auto_assign_jobs' | 'require_job_approval'>) => {
    setLocalSettings(prev => prev ? { ...prev, [name]: !prev[name] } : null);
  };

  const handleSaveChanges = async () => {
    if (!localSettings) return;
    const result = await updateSettings(localSettings);
    if (!result) {
      showToast('Failed to save settings', 'error');
    } else {
      showToast('Settings saved successfully!', 'success');
    }
  };

  if (loading || !localSettings) return <div>Loading settings...</div>;
  if (error) return <div className="text-red-500">Error: {error}</div>;

  const settingItems = [
    { 
      id: 'auto_assign_jobs' as const, 
      title: 'Auto-Assign New Jobs',
      description: 'Automatically assign new jobs to available workforce members'
    },
    { 
      id: 'require_job_approval' as const, 
      title: 'Require Job Approval',
      description: 'Workforce members need approval before completing jobs'
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Business Name
        </label>
        <input
          type="text"
          name="name"
          id="businessName"
          value={localSettings.name}
          onChange={handleInputChange}
          className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="Your Business Name"
        />
      </div>

      {settingItems.map((item) => (
        <div key={item.id} className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex-grow">
              <span className="text-sm font-medium text-gray-900 dark:text-white">{item.title}</span>
              <p className="text-xs text-gray-500 dark:text-gray-400">{item.description}</p>
            </div>
            <button
              type="button"
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                localSettings[item.id] ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
              }`}
              role="switch"
              aria-checked={localSettings[item.id]}
              onClick={() => handleToggleChange(item.id)}
            >
              <span
                aria-hidden="true"
                className={`inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  localSettings[item.id] ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>
        </div>
      ))}

      <div>
        <label htmlFor="jobVisibility" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Default Job Visibility
        </label>
        <select
          name="default_job_visibility"
          id="jobVisibility"
          value={localSettings.default_job_visibility}
          onChange={handleInputChange}
          className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
        >
          <option value="owner_only">Owner Only</option>
          <option value="entire_team">Entire Workforce</option>
          <option value="assigned_only">Assigned Members Only</option>
        </select>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Controls who can see jobs by default. You as the owner always have full visibility.
        </p>
      </div>

      <div className="pt-4">
        <button
          onClick={handleSaveChanges}
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Save Changes
        </button>
      </div>
    </div>
  );
}

// Invitations Tab Content  
function InvitationsContent({ openDrawer }: { openDrawer: (drawer: keyof DrawerState) => void }) {
  const { workforce } = useWorkforce();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
          Invitation Management
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={() => openDrawer('inviteTeamMember')}
            className="flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="w-4 h-4 mr-1.5" />
            Send Invitation
          </button>
          <button
            onClick={() => openDrawer('invitationManagement')}
            className="flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
          >
            <EyeIcon className="w-4 h-4 mr-1.5" />
            Manage All
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <div className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
                <PlusIcon className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Send New Invitation</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Invite new members to join your workforce</p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              Professional email templates with personalization
            </div>
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              Granular permission control with preset options
            </div>
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              Multiple delivery methods (Email, WhatsApp, SMS)
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
            <button
              onClick={() => openDrawer('inviteTeamMember')}
              className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              Create Invitation
            </button>
          </div>
        </div>

        <div className="p-6 bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-xl border border-amber-200 dark:border-amber-800">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-amber-600 rounded-xl flex items-center justify-center">
                <EnvelopeIcon className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Manage Invitations</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Track and manage all sent invitations</p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              View invitation status and history
            </div>
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              Resend or cancel pending invitations
            </div>
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              Complete audit trail and tracking
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-amber-200 dark:border-amber-700">
            <button
              onClick={() => openDrawer('invitationManagement')}
              className="w-full flex items-center justify-center px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-lg transition-colors"
            >
              <EyeIcon className="w-4 h-4 mr-2" />
              View All Invitations
            </button>
          </div>
        </div>
      </div>

      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
          Invitation Features
        </h4>
        <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <p>• Secure token-based invitations with 7-day expiry</p>
          <p>• Professional templates with custom messaging</p>
          <p>• Granular permission control with role presets</p>
          <p>• Complete audit trail and status tracking</p>
          <p>• Multiple delivery methods for flexibility</p>
        </div>
      </div>
    </div>
  );
}