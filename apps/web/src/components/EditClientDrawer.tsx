'use client';

import React, { useState, useEffect } from 'react';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/contexts/ToastContext';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { Input } from '@deskbelt/ui';
import { Textarea } from '@deskbelt/ui';
import { XMarkIcon, PencilIcon } from '@heroicons/react/24/outline';

interface EditClientDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  clientId: string | null;
  clientData?: {
    id: string;
    name: string;
    business_name?: string;
    address: string;
    phone?: string;
    email?: string;
  };
  onClientUpdated?: (client: any) => void;
}

interface FormData {
  name: string;
  business_name: string;
  address: string;
  phone: string;
  email: string;
}

interface FormErrors {
  name?: string;
  business_name?: string;
  address?: string;
  phone?: string;
  email?: string;
}

export const EditClientDrawer: React.FC<EditClientDrawerProps> = ({ 
  isOpen, 
  onClose, 
  clientId,
  clientData,
  onClientUpdated 
}) => {
  const { authenticatedFetch } = useAuthenticatedFetch();
  const { showError, showApiError } = useToast();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    business_name: '',
    address: '',
    phone: '',
    email: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Pre-fill form when client data is available
  useEffect(() => {
    if (clientData && isOpen) {
      setFormData({
        name: clientData.name || '',
        business_name: clientData.business_name || '',
        address: clientData.address || '',
        phone: clientData.phone || '',
        email: clientData.email || ''
      });
    }
  }, [clientData, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Required fields
    if (!formData.name.trim()) {
      newErrors.name = 'Client name is required';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }

    // Email validation (if provided)
    if (formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email.trim())) {
        newErrors.email = 'Please enter a valid email address';
      }
    }

    // Phone validation (if provided) - UK format
    if (formData.phone.trim()) {
      const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,15}$/;
      if (!phoneRegex.test(formData.phone.trim())) {
        newErrors.phone = 'Please enter a valid phone number';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // Clear submit error when user makes changes
    if (submitError) {
      setSubmitError(null);
    }
  };

  const handleSubmit = async () => {
    if (!validateForm() || !clientId) {
      return;
    }

    setSubmitError(null);
    setIsSubmitting(true);

    try {
      const response = await authenticatedFetch(`/api/clients/${clientId}`, {
        method: 'PUT',
        body: JSON.stringify({
          name: formData.name.trim(),
          business_name: formData.business_name.trim() || null,
          address: formData.address.trim(),
          phone: formData.phone.trim() || null,
          email: formData.email.trim() || null,
        })
      });

      if (response.ok) {
        const result = await response.json();
        const updatedClient = result.client || result;
        
        // Notify parent component (which will show the success toast)
        onClientUpdated?.(updatedClient);
        
        // Close drawer
        onClose();
      } else {
        const errorData = await response.json();
        console.error('Failed to update client:', errorData);
        
        // Use showApiError to display comprehensive error information
        showApiError('Update Failed', errorData);
      }
    } catch (error) {
      console.error('Error updating client:', error);
      
      // Show error toast
      showError(
        'Connection Error',
        'Unable to connect to server. Please check your connection and try again.',
        7000
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset errors when closing
    setErrors({});
    setSubmitError(null);
    onClose();
  };

  const isFormValid = formData.name.trim() && formData.address.trim();

  return (
    <Drawer
      isOpen={isOpen}
      onClose={handleClose}
      side="right"
      size="xl"
      showCloseButton={false}
      className="top-16 h-[calc(100%-4rem)] md:left-1/2 md:-translate-x-1/2 md:right-auto"
    >
      <div className="h-full flex flex-col bg-gradient-to-b from-green-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="h-full flex flex-col max-w-5xl mx-auto w-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white/80 backdrop-blur-sm">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                <PencilIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">Edit Client</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">Update client information</p>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={handleClose}>
              <XMarkIcon className="w-5 h-5" />
            </Button>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="max-w-2xl mx-auto space-y-6">
              
              {/* Client Details Form */}
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Client Details</h3>
                
                <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Client Name - Required */}
                    <div className="md:col-span-2">
                      <Input
                        label="Client Name *"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        error={errors.name}
                        placeholder="John Smith"
                        className="w-full"
                      />
                    </div>

                    {/* Business Name - Optional */}
                    <div className="md:col-span-2">
                      <Input
                        label="Business Name"
                        value={formData.business_name}
                        onChange={(e) => handleInputChange('business_name', e.target.value)}
                        error={errors.business_name}
                        placeholder="Smith Construction Ltd"
                        className="w-full"
                      />
                    </div>

                    {/* Address - Required */}
                    <div className="md:col-span-2">
                      <Textarea
                        label="Address *"
                        value={formData.address}
                        onChange={(e) => handleInputChange('address', e.target.value)}
                        error={errors.address}
                        placeholder="123 Main Street, London, SW1A 1AA"
                        rows={3}
                        className="w-full resize-none"
                      />
                    </div>

                    {/* Phone - Optional */}
                    <div>
                      <Input
                        label="Phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        error={errors.phone}
                        placeholder="020 7946 0958"
                        className="w-full"
                      />
                    </div>

                    {/* Email - Optional */}
                    <div>
                      <Input
                        label="Email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        error={errors.email}
                        placeholder="<EMAIL>"
                        className="w-full"
                      />
                    </div>
                  </div>
                </form>
              </div>

              {/* Submit Error */}
              {submitError && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-red-600 dark:text-red-400 text-sm">{submitError}</p>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 bg-white/80 backdrop-blur-sm">
            <div className="flex items-center justify-between p-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Fields marked with * are required
              </p>
              <div className="flex space-x-3">
                <Button variant="ghost" onClick={handleClose}>
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  theme="clients"
                  onClick={handleSubmit}
                  disabled={!isFormValid || isSubmitting}
                  isLoading={isSubmitting}
                >
                  {isSubmitting ? 'Updating...' : 'Update Client'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  );
}; 