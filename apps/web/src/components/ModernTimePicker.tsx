import React, { useState, useMemo, useEffect } from 'react';
import {
  ClockIcon,
  XMarkIcon,
  SunIcon,
  MoonIcon,
} from '@heroicons/react/24/outline';

interface ModernTimePickerProps {
  selectedTime?: string | null; // HH:MM format
  onTimeSelect: (time: string) => void;
  onClose?: () => void;
  compact?: boolean;
  format24Hour?: boolean;
}

const ModernTimePicker: React.FC<ModernTimePickerProps> = ({
  selectedTime,
  onTimeSelect,
  onClose,
  compact = false,
  format24Hour = true
}) => {
  const [selectedHour, setSelectedHour] = useState(9);
  const [selectedMinute, setSelectedMinute] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState<'AM' | 'PM'>('AM');

  // Initialize from selected time
  useEffect(() => {
    if (selectedTime) {
      const [hourStr, minuteStr] = selectedTime.split(':');
      const hour = parseInt(hourStr);
      const minute = parseInt(minuteStr);
      
      if (format24Hour) {
        setSelectedHour(hour);
        setSelectedMinute(minute);
      } else {
        if (hour === 0) {
          setSelectedHour(12);
          setSelectedPeriod('AM');
        } else if (hour <= 12) {
          setSelectedHour(hour);
          setSelectedPeriod('AM');
        } else {
          setSelectedHour(hour - 12);
          setSelectedPeriod('PM');
        }
        setSelectedMinute(minute);
      }
    }
  }, [selectedTime, format24Hour]);

  // Quick time options
  const quickOptions = useMemo(() => {
    const options = [];
    const businessHours = [
      { time: '08:00', label: '8:00 AM', period: 'morning' },
      { time: '09:00', label: '9:00 AM', period: 'morning' },
      { time: '10:00', label: '10:00 AM', period: 'morning' },
      { time: '11:00', label: '11:00 AM', period: 'morning' },
      { time: '12:00', label: '12:00 PM', period: 'noon' },
      { time: '13:00', label: '1:00 PM', period: 'afternoon' },
      { time: '14:00', label: '2:00 PM', period: 'afternoon' },
      { time: '15:00', label: '3:00 PM', period: 'afternoon' },
      { time: '16:00', label: '4:00 PM', period: 'afternoon' },
      { time: '17:00', label: '5:00 PM', period: 'afternoon' },
    ];
    
    return businessHours;
  }, []);

  // Generate hours array
  const hours = useMemo(() => {
    if (format24Hour) {
      return Array.from({ length: 24 }, (_, i) => i);
    } else {
      return Array.from({ length: 12 }, (_, i) => i + 1);
    }
  }, [format24Hour]);

  // Generate minutes array
  const minutes = useMemo(() => {
    return Array.from({ length: 60 }, (_, i) => i);
  }, []);

  const formatTime = (hour: number, minute: number, period?: 'AM' | 'PM') => {
    if (format24Hour) {
      return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    } else {
      const hour24 = period === 'PM' && hour !== 12 ? hour + 12 : (period === 'AM' && hour === 12 ? 0 : hour);
      return `${hour24.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    }
  };

  const formatDisplayTime = (hour: number, minute: number, period?: 'AM' | 'PM') => {
    if (format24Hour) {
      return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    } else {
      return `${hour}:${minute.toString().padStart(2, '0')} ${period}`;
    }
  };

  const handleTimeSelect = () => {
    const timeString = formatTime(selectedHour, selectedMinute, selectedPeriod);
    onTimeSelect(timeString);
    onClose?.();
  };

  const handleQuickSelect = (time: string) => {
    onTimeSelect(time);
    onClose?.();
  };

  const getCurrentPeriodIcon = () => {
    const currentHour = format24Hour ? selectedHour : (selectedPeriod === 'PM' ? selectedHour + 12 : selectedHour);
    return currentHour >= 6 && currentHour < 18 ? (
      <SunIcon className="w-4 h-4 text-yellow-500" />
    ) : (
      <MoonIcon className="w-4 h-4 text-blue-400" />
    );
  };

  if (compact) {
    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg p-4 min-w-80">
        {/* Quick Options */}
        <div className="mb-4">
          <h4 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">Quick Select</h4>
          <div className="grid grid-cols-2 gap-2">
            {quickOptions.slice(0, 6).map((option, index) => (
              <button
                key={index}
                onClick={() => handleQuickSelect(option.time)}
                className={`px-3 py-2 text-xs font-medium rounded-lg transition-colors ${
                  selectedTime === option.time
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-50 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Time Selection */}
        <div className="flex items-center justify-center space-x-2 mb-4">
          {/* Hour */}
          <select
            value={selectedHour}
            onChange={(e) => setSelectedHour(parseInt(e.target.value))}
            className="px-3 py-2 text-lg font-semibold border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {hours.map(hour => (
              <option key={hour} value={hour}>
                {format24Hour ? hour.toString().padStart(2, '0') : hour}
              </option>
            ))}
          </select>

          <span className="text-xl font-bold text-gray-400">:</span>

          {/* Minute */}
          <select
            value={selectedMinute}
            onChange={(e) => setSelectedMinute(parseInt(e.target.value))}
            className="px-3 py-2 text-lg font-semibold border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {[0, 15, 30, 45].map(minute => (
              <option key={minute} value={minute}>
                {minute.toString().padStart(2, '0')}
              </option>
            ))}
          </select>

          {/* AM/PM */}
          {!format24Hour && (
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as 'AM' | 'PM')}
              className="px-3 py-2 text-lg font-semibold border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="AM">AM</option>
              <option value="PM">PM</option>
            </select>
          )}
        </div>

        {/* Apply Button */}
        <button
          onClick={handleTimeSelect}
          className="w-full px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
        >
          Set Time
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg p-6 min-w-96">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <ClockIcon className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Select Time
          </h3>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        )}
      </div>

      {/* Quick Options */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Business Hours</h4>
        <div className="grid grid-cols-2 gap-2">
          {quickOptions.map((option, index) => (
            <button
              key={index}
              onClick={() => handleQuickSelect(option.time)}
              className={`px-4 py-3 text-sm font-medium rounded-lg transition-colors flex items-center justify-between ${
                selectedTime === option.time
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-50 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'
              }`}
            >
              <span>{option.label}</span>
              {option.period === 'morning' && <SunIcon className="w-4 h-4 text-yellow-500" />}
              {option.period === 'noon' && <SunIcon className="w-4 h-4 text-orange-500" />}
              {option.period === 'afternoon' && <SunIcon className="w-4 h-4 text-orange-400" />}
            </button>
          ))}
        </div>
      </div>

      {/* Custom Time Selection */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
          Custom Time
          {getCurrentPeriodIcon()}
        </h4>
        
        {/* Time Display */}
        <div className="text-center mb-4">
          <div className="inline-flex items-center px-6 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
            <span className="text-3xl font-bold text-gray-900 dark:text-white">
              {formatDisplayTime(selectedHour, selectedMinute, selectedPeriod)}
            </span>
          </div>
        </div>

        {/* Time Controls */}
        <div className="flex justify-center space-x-4">
          {/* Hour Selector */}
          <div className="text-center">
            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
              {format24Hour ? 'Hour' : 'Hour'}
            </label>
            <div className="flex flex-col space-y-1 h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-1">
              {hours.map(hour => (
                <button
                  key={hour}
                  onClick={() => setSelectedHour(hour)}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    selectedHour === hour
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                  }`}
                >
                  {format24Hour ? hour.toString().padStart(2, '0') : hour}
                </button>
              ))}
            </div>
          </div>

          {/* Minute Selector */}
          <div className="text-center">
            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
              Minute
            </label>
            <div className="flex flex-col space-y-1 h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-1">
              {[0, 15, 30, 45].map(minute => (
                <button
                  key={minute}
                  onClick={() => setSelectedMinute(minute)}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    selectedMinute === minute
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                  }`}
                >
                  {minute.toString().padStart(2, '0')}
                </button>
              ))}
            </div>
          </div>

          {/* AM/PM Selector */}
          {!format24Hour && (
            <div className="text-center">
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                Period
              </label>
              <div className="flex flex-col space-y-1">
                {['AM', 'PM'].map(period => (
                  <button
                    key={period}
                    onClick={() => setSelectedPeriod(period as 'AM' | 'PM')}
                    className={`px-4 py-2 text-sm rounded transition-colors ${
                      selectedPeriod === period
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'
                    }`}
                  >
                    {period}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={() => onTimeSelect('')}
          className="text-sm text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
        >
          Clear Time
        </button>
        <button
          onClick={handleTimeSelect}
          className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
        >
          Set Time
        </button>
      </div>
    </div>
  );
};

export default ModernTimePicker;