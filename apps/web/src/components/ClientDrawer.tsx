'use client';

import React from 'react';
import { useClientDetails } from '@/hooks/useClientDetails';
import { useDrawer } from '@/contexts/DrawerContext';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { 
  XMarkIcon, 
  EnvelopeIcon,
  PhoneIcon,
  StarIcon,
  PencilIcon,
  TrashIcon,
  BriefcaseIcon,
  TrophyIcon,
  ChatBubbleLeftIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  CheckBadgeIcon
} from '@heroicons/react/24/outline';

interface ClientDrawerProps {
  clientId: string | null;
  onClose: () => void;
  onCreateJobClick?: (clientId: string) => void;
  onNotesClick?: (clientId: string) => void;
  onRatingChange?: (clientId: string, rating: number) => void;
  clientData?: any; // Pass updated client data directly
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', { 
    day: '2-digit', 
    month: 'short', 
    year: 'numeric'
  });
};



const renderStars = (rating: number, onRatingChange?: (rating: number) => void) => {
  const stars = [];
  for (let i = 1; i <= 5; i++) {
    stars.push(
      <button
        key={i}
        onClick={(e) => {
          e.stopPropagation();
          console.log('⭐ Star clicked:', i, 'onRatingChange available:', !!onRatingChange);
          onRatingChange?.(i);
        }}
        className={`w-4 h-4 transition-colors ${
          onRatingChange ? 'hover:text-yellow-500 cursor-pointer' : ''
        } ${
          i <= rating 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300 dark:text-gray-600'
        }`}
        disabled={!onRatingChange}
      >
        <StarIcon className="w-full h-full" />
      </button>
    );
  }
  return stars;
};

export const ClientDrawer: React.FC<ClientDrawerProps> = ({ 
  clientId, 
  onClose, 
  onCreateJobClick,
  onNotesClick,
  onRatingChange,
  clientData
}) => {
  const { data: client, isLoading, error, refetch } = useClientDetails(clientId);
  const { openNewJobDrawer, openRequestReviewDrawer, openEditClientDrawer } = useDrawer();

  // Use passed client data if available, otherwise use fetched data
  // For clients dashboard: clientData has full client with rating
  // For jobs dashboard: client (fetched) has full client with rating
  const effectiveClient = clientData || client;

  // Handle edit client
  const handleEditClient = () => {
    if (effectiveClient) {
      openEditClientDrawer({
        id: effectiveClient.id,
        name: effectiveClient.name,
        business_name: effectiveClient.business_name,
        address: effectiveClient.address,
        phone: effectiveClient.phone,
        email: effectiveClient.email
      });
    }
  };



  // Handle actions
      const handleCreateJob = () => {
      if (effectiveClient) {
        // First try to use the DrawerContext to open NewJobDrawer
        openNewJobDrawer();
        // Also call the prop callback if provided (for backward compatibility)
        onCreateJobClick?.(effectiveClient.id);
      }
    };

      const handleRequestReview = () => {
      if (effectiveClient) {
        openRequestReviewDrawer({
          id: effectiveClient.id,
          name: effectiveClient.name,
          email: effectiveClient.email,
          phone: effectiveClient.phone
        });
      }
    };



      const handleDeleteClient = () => {
      if (effectiveClient && confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
        // TODO: Implement delete client
        console.log('Deleting client:', effectiveClient.id);
        onClose();
      }
    };

      const handleNotesClick = () => {
      if (effectiveClient) {
        onNotesClick?.(effectiveClient.id);
      }
    };

      const handleRatingChange = (rating: number) => {
      // Use clientId directly if effectiveClient is not available
      const targetClientId = effectiveClient?.id || clientId;
      
      if (targetClientId && onRatingChange) {
        onRatingChange(targetClientId, rating);
      }
    };



  if (!clientId) return null;

  return (
    <Drawer
      isOpen={!!clientId}
      onClose={onClose}
      side="right"
      size="sm"
      showCloseButton={false}
      className="top-0 h-full w-3/4 sm:w-80"
    >
      <div className="h-full flex flex-col">
        {/* Compact Header */}
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                  {isLoading ? 'Loading...' : effectiveClient?.name || 'Client Details'}
              </h2>
                              {effectiveClient && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Client since: {formatDate(effectiveClient.created_at)}
                  </p>
                )}
            </div>
            <button
              onClick={onClose}
              className="ml-3 p-1.5 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex-shrink-0"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {isLoading && (
            <div className="p-4 space-y-4">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          )}

          {error && (
            <div className="p-4">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="w-5 h-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-400">
                      Failed to load client details
                    </h3>
                    <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
                    <div className="mt-3">
                      <Button variant="outline" size="sm" onClick={refetch}>
                        Try again
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

                        {effectiveClient && (
            <div className="p-4">
              {/* Client Info Section */}
              <div className="space-y-2 mb-6">
                {/* Notes About Client */}
                <button
                  onClick={handleNotesClick}
                  className="flex items-center space-x-3 py-2 w-full text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <DocumentTextIcon className="w-4 h-4 text-amber-600 dark:text-amber-500" />
                  <span className="text-sm font-normal text-gray-700 dark:text-gray-300">View Client Notes</span>
                </button>

                {/* Client Rating */}
                <div className="py-2">
                <div className="flex items-center space-x-3 mb-2">
                  <CheckBadgeIcon className="w-4 h-4 text-blue-500 dark:text-blue-400" />
                  <span className="text-sm font-normal text-gray-700 dark:text-gray-300">Client Rating</span>
                </div>
                <div className="flex items-center space-x-1 ml-7">
                                  {renderStars(effectiveClient.rating || 0, handleRatingChange)}
                {(effectiveClient.rating || 0) > 0 && (
                  <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                    {effectiveClient.rating}/5
                    </span>
                  )}
                </div>
              </div>
              </div>

              {/* Actions */}
              <div className="pt-4">
                <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
                  ACTIONS
                </h3>
                <div className="space-y-1">
                  <button
                    onClick={handleCreateJob}
                    className="flex items-center space-x-3 w-full text-left py-2 text-sm font-normal text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    <BriefcaseIcon className="w-4 h-4 text-gray-400" />
                    <span>Create Job</span>
                  </button>
                  
                  <button
                    onClick={handleRequestReview}
                    className="flex items-center space-x-3 w-full text-left py-2 text-sm font-normal text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    <TrophyIcon className="w-4 h-4 text-gray-400" />
                    <span>Request Review</span>
                  </button>
                  
                  <button
                    onClick={handleEditClient}
                    className="flex items-center space-x-3 w-full text-left py-2 text-sm font-normal text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    <PencilIcon className="w-4 h-4 text-gray-400" />
                    <span>Edit Client</span>
                  </button>
                  
                  <button
                    onClick={handleDeleteClient}
                    className="flex items-center space-x-3 w-full text-left py-2 text-sm font-normal text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                  >
                    <TrashIcon className="w-4 h-4 text-red-400" />
                    <span>Delete Client</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Quick Action Icons - Fixed at Bottom */}
        {effectiveClient && (
          <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4">
            <div className="flex items-center justify-around">
              <button
                onClick={() => effectiveClient?.phone && window.open(`tel:${effectiveClient.phone}`, '_self')}
                disabled={!effectiveClient?.phone}
                className={`p-2.5 rounded-full transition-all duration-200 ${effectiveClient?.phone ? 'text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:text-green-400 dark:hover:bg-green-900/30' : 'text-gray-300 cursor-not-allowed'}`}
              >
                <PhoneIcon className="w-5 h-5" />
              </button>
              <button
                onClick={() => effectiveClient?.phone && window.open(`https://wa.me/${effectiveClient.phone?.replace(/\D/g,'')}`, '_blank')}
                disabled={!effectiveClient?.phone}
                className={`p-2.5 rounded-full transition-all duration-200 ${effectiveClient?.phone ? 'text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:text-blue-400 dark:hover:bg-blue-900/30' : 'text-gray-300 cursor-not-allowed'}`}
              >
                <ChatBubbleLeftIcon className="w-5 h-5" />
              </button>
              <button
                onClick={() => effectiveClient?.email && window.open(`mailto:${effectiveClient.email}`, '_self')}
                disabled={!effectiveClient?.email}
                className={`p-2.5 rounded-full transition-all duration-200 ${effectiveClient?.email ? 'text-purple-600 hover:text-purple-700 hover:bg-purple-50 dark:hover:text-purple-400 dark:hover:bg-purple-900/30' : 'text-gray-300 cursor-not-allowed'}`}
              >
                <EnvelopeIcon className="w-5 h-5" />
              </button>
              <button
                onClick={handleDeleteClient}
                className="p-2.5 rounded-full text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:text-red-400 dark:hover:bg-red-900/30 transition-all duration-200"
              >
                <TrashIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </Drawer>
  );
}; 