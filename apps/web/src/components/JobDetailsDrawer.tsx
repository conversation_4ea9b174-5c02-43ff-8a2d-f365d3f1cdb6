// @ts-nocheck
import React from 'react';
import { Job } from '@/types/Job';
import { formatJobSchedule } from '@/types/Job';
import { formatDistanceToNow } from 'date-fns';

interface JobDetailsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  job: Job | null;
}

export const JobDetailsDrawer: React.FC<JobDetailsDrawerProps> = ({
  isOpen,
  onClose,
  job
}) => {
  if (!isOpen || !job) return null;


  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };


  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'new': return 'text-blue-600 bg-blue-50';
      case 'in_progress': return 'text-purple-600 bg-purple-50';
      case 'completed': return 'text-green-600 bg-green-50';
      case 'on_hold': return 'text-orange-600 bg-orange-50';
      case 'cancelled': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50">
      <div className="fixed right-0 top-0 h-full w-full max-w-4xl bg-white shadow-xl overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{job.title}</h1>
            <p className="text-gray-600">Detailed Job Information</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-8">
          {/* Overview Section */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Job Overview
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Job Number</label>
                  <p className="text-lg font-mono bg-white px-3 py-2 rounded border">#{job.id.slice(-6).toUpperCase()}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Client</label>
                  <p className="text-lg bg-white px-3 py-2 rounded border">{job.client?.name || 'Unknown Client'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(job.status)}`}>
                    {job.status?.replace('_', ' ').toUpperCase() || 'Unknown'}
                  </span>
                </div>

                {job.assigned_to && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Assigned To</label>
                    <p className="text-lg bg-white px-3 py-2 rounded border">{job.assigned_to.full_name}</p>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Created</label>
                  <p className="text-lg bg-white px-3 py-2 rounded border">
                    {formatDateTime(job.created_at)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                  <p className="text-lg bg-white px-3 py-2 rounded border">
                    {formatDistanceToNow(new Date(job.updated_at), { addSuffix: true })}
                  </p>
                </div>

                {job.hasNotes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Notes Available</label>
                    <p className="text-lg bg-white px-3 py-2 rounded border flex items-center">
                      <svg className="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Job has additional notes
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Schedule Section */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Schedule Information
            </h2>

            {job.scheduled_at ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Scheduled Date & Time</label>
                  <p className="text-lg bg-white px-3 py-2 rounded border">
                    {formatJobSchedule(job)}
                  </p>
                </div>

                {job.scheduled_start_time && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                    <p className="text-lg bg-white px-3 py-2 rounded border">
                      {job.scheduled_start_time}
                    </p>
                  </div>
                )}

                {job.scheduled_end_time && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                    <p className="text-lg bg-white px-3 py-2 rounded border">
                      {job.scheduled_end_time}
                    </p>
                  </div>
                )}

                {job.estimated_duration && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Estimated Duration</label>
                    <p className="text-lg bg-white px-3 py-2 rounded border">
                      {job.estimated_duration}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-gray-500 text-lg">This job is not yet scheduled</p>
                <p className="text-gray-400 text-sm">Use the schedule button to set a date and time</p>
              </div>
            )}

            {job.scheduling_notes && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Scheduling Notes</label>
                <p className="text-gray-700 bg-white px-3 py-2 rounded border">
                  {job.scheduling_notes}
                </p>
              </div>
            )}
          </div>

          {/* Description Section */}
          <div className="bg-green-50 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
              </svg>
              Job Description
            </h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <div className="bg-white p-4 rounded border">
                  <p className="text-gray-900 whitespace-pre-wrap">
                    {(job as any).description || 'No description provided'}
                  </p>
                </div>
              </div>

              {job.scheduling_notes && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Project Notes</label>
                  <div className="bg-white p-4 rounded border">
                    <p className="text-gray-900 whitespace-pre-wrap">{job.scheduling_notes}</p>
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Project Status</label>
                <div className="bg-white p-4 rounded border">
                  <p className="text-gray-900 capitalize">{job.status.replace('_', ' ')}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          {(job.client?.phone || job.client?.email) && (
            <div className="bg-purple-50 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                Contact Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {job.client?.phone && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                    <a 
                      href={`tel:${job.client.phone}`}
                      className="text-lg text-blue-600 hover:text-blue-800 bg-white px-3 py-2 rounded border block"
                    >
                      {job.client.phone}
                    </a>
                  </div>
                )}

                {job.client?.email && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <a 
                      href={`mailto:${job.client.email}`}
                      className="text-lg text-blue-600 hover:text-blue-800 bg-white px-3 py-2 rounded border block"
                    >
                      {job.client.email}
                    </a>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Team Information */}
          {job.assigned_to && (
            <div className="bg-indigo-50 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Team Assignment
              </h2>

              <div className="bg-white p-4 rounded border">
                <div className="flex items-center">
                  <div className="bg-blue-600 text-white w-10 h-10 rounded-full flex items-center justify-center font-bold mr-3">
                    {job.assigned_to.full_name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <p className="text-lg font-medium text-gray-900">{job.assigned_to.full_name}</p>
                    <p className="text-sm text-gray-600">Assigned Team Member</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Job Completion Requirements */}
          <div className="bg-orange-50 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
              Job Completion Details
            </h2>

            <div className="space-y-4">
              <div className="bg-white p-4 rounded border">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <label className="font-medium text-gray-700">Status</label>
                    <p className="capitalize">{job.status?.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <label className="font-medium text-gray-700">Created</label>
                    <p>{new Date(job.created_at).toLocaleDateString('en-GB')}</p>
                  </div>
                  <div>
                    <label className="font-medium text-gray-700">Last Updated</label>
                    <p>{formatDistanceToNow(new Date(job.updated_at), { addSuffix: true })}</p>
                  </div>
                </div>
              </div>

              {job.hasNotes && (
                <div className="bg-white p-4 rounded border">
                  <div className="flex items-center text-blue-600">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span className="font-medium">Additional job notes are available</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};