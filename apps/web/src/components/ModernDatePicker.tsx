import React, { useState, useMemo } from 'react';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CalendarIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import {
  CalendarDaysIcon,
} from '@heroicons/react/24/solid';

interface ModernDatePickerProps {
  selectedDate?: string | null;
  onDateSelect: (date: string) => void;
  onClose?: () => void;
  minDate?: string;
  maxDate?: string;
  compact?: boolean;
}

const ModernDatePicker: React.FC<ModernDatePickerProps> = ({
  selectedDate,
  onDateSelect,
  onClose,
  minDate,
  maxDate,
  compact = false
}) => {
  const [currentMonth, setCurrentMonth] = useState(() => {
    if (selectedDate) {
      return new Date(selectedDate);
    }
    return new Date();
  });

  const today = new Date();
  const selectedDateObj = selectedDate ? new Date(selectedDate) : null;

  // Helper function to format date for input (YYYY-MM-DD) in local timezone
  const formatDateForInput = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Quick date options
  const quickOptions = useMemo(() => {
    const options = [];
    const baseDate = new Date();
    
    // Today
    options.push({
      label: 'Today',
      date: new Date(),
      isToday: true
    });
    
    // Tomorrow
    const tomorrow = new Date(baseDate);
    tomorrow.setDate(tomorrow.getDate() + 1);
    options.push({
      label: 'Tomorrow',
      date: tomorrow,
      isToday: false
    });
    
    // Next 5 weekdays
    let workDate = new Date(tomorrow);
    workDate.setDate(workDate.getDate() + 1);
    
    let addedDays = 0;
    while (addedDays < 5) {
      if (workDate.getDay() !== 0 && workDate.getDay() !== 6) { // Skip weekends
        options.push({
          label: workDate.toLocaleDateString('en-GB', { weekday: 'long', day: 'numeric', month: 'short' }),
          date: new Date(workDate),
          isToday: false
        });
        addedDays++;
      }
      workDate.setDate(workDate.getDate() + 1);
    }
    
    return options;
  }, []);

  // Generate calendar days
  const calendarDays = useMemo(() => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const firstCalendarDay = new Date(firstDayOfMonth);
    
    // Start from Sunday
    firstCalendarDay.setDate(firstCalendarDay.getDate() - firstDayOfMonth.getDay());
    
    const days = [];
    const currentCalendarDay = new Date(firstCalendarDay);
    
    // Generate 42 days (6 weeks)
    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentCalendarDay));
      currentCalendarDay.setDate(currentCalendarDay.getDate() + 1);
    }
    
    return days;
  }, [currentMonth]);

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  const isToday = (date: Date) => {
    return date.toDateString() === today.toDateString();
  };

  const isSelected = (date: Date) => {
    return selectedDateObj && date.toDateString() === selectedDateObj.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentMonth.getMonth();
  };

  const isPastDate = (date: Date) => {
    return date < today && !isToday(date);
  };

  const isDisabled = (date: Date) => {
    if (minDate && date < new Date(minDate)) return true;
    if (maxDate && date > new Date(maxDate)) return true;
    return false;
  };

  const handleDateClick = (date: Date) => {
    if (isDisabled(date)) return;

    const dateString = formatDateForInput(date);
    onDateSelect(dateString);
    onClose?.();
  };

  const handleQuickOptionClick = (date: Date) => {
    const dateString = formatDateForInput(date);
    onDateSelect(dateString);
    onClose?.();
  };

  const goToToday = () => {
    setCurrentMonth(new Date());
  };

  if (compact) {
    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg p-4 min-w-80">
        {/* Quick Options */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {quickOptions.slice(0, 3).map((option, index) => (
              <button
                type="button"
                key={index}
                onClick={() => handleQuickOptionClick(option.date)}
                className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${
                  isSelected(option.date)
                    ? 'bg-blue-600 text-white'
                    : option.isToday
                    ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30'
                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Mini Calendar */}
        <div>
          {/* Header */}
          <div className="flex items-center justify-between mb-3">
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                navigateMonth('prev');
              }}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            <h3 className="text-sm font-semibold">
              {currentMonth.toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}
            </h3>
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                navigateMonth('next');
              }}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
          </div>

          {/* Days of week */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
              <div key={index} className="text-xs font-medium text-gray-500 dark:text-gray-400 text-center py-1">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {calendarDays.slice(0, 35).map((date, index) => (
              <button
                type="button"
                key={index}
                onClick={() => handleDateClick(date)}
                disabled={isDisabled(date)}
                className={`w-7 h-7 text-xs rounded-lg transition-colors ${
                  isSelected(date)
                    ? 'bg-blue-600 text-white font-semibold'
                    : isToday(date)
                    ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400 font-semibold'
                    : isPastDate(date)
                    ? 'text-gray-400 dark:text-gray-600'
                    : isCurrentMonth(date)
                    ? 'text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                    : 'text-gray-400 dark:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'
                } ${isDisabled(date) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              >
                {date.getDate()}
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg p-6 min-w-96">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <CalendarDaysIcon className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Select Date
          </h3>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        )}
      </div>

      {/* Quick Options */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Quick Select</h4>
        <div className="flex flex-wrap gap-2">
          {quickOptions.map((option, index) => (
            <button
              type="button"
              key={index}
              onClick={() => handleQuickOptionClick(option.date)}
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                isSelected(option.date)
                  ? 'bg-blue-600 text-white shadow-md'
                  : option.isToday
                  ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 border border-blue-200 dark:border-blue-800'
                  : 'bg-gray-50 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Calendar */}
      <div>
        {/* Month Navigation */}
        <div className="flex items-center justify-between mb-4">
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              navigateMonth('prev');
            }}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </button>
          <div className="flex items-center space-x-2">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              {currentMonth.toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}
            </h4>
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                goToToday();
              }}
              className="px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors"
            >
              Today
            </button>
          </div>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              navigateMonth('next');
            }}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <ChevronRightIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Days of week */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="text-sm font-medium text-gray-500 dark:text-gray-400 text-center py-2">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((date, index) => (
            <button
              type="button"
              key={index}
              onClick={() => handleDateClick(date)}
              disabled={isDisabled(date)}
              className={`w-10 h-10 text-sm rounded-lg transition-all duration-200 ${
                isSelected(date)
                  ? 'bg-blue-600 text-white font-semibold shadow-md transform scale-105'
                  : isToday(date)
                  ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400 font-semibold border-2 border-blue-300 dark:border-blue-700'
                  : isPastDate(date)
                  ? 'text-gray-400 dark:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'
                  : isCurrentMonth(date)
                  ? 'text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105'
                  : 'text-gray-400 dark:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'
              } ${isDisabled(date) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            >
              {date.getDate()}
            </button>
          ))}
        </div>
      </div>

      {/* Footer */}
      {selectedDateObj && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Selected: <span className="font-medium text-gray-900 dark:text-white">
                {selectedDateObj.toLocaleDateString('en-GB', { 
                  weekday: 'long', 
                  day: 'numeric', 
                  month: 'long', 
                  year: 'numeric' 
                })}
              </span>
            </div>
            <button
              onClick={() => onDateSelect('')}
              className="text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
            >
              Clear
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModernDatePicker;