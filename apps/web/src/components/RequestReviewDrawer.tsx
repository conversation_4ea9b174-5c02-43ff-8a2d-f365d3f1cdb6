'use client';

import React, { useState } from 'react';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { Textarea } from '@deskbelt/ui';
import { 
  XMarkIcon, 
  EnvelopeIcon,
  ChatBubbleLeftIcon,
  TrophyIcon,
  StarIcon,
  HeartIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

interface RequestReviewDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  clientId?: string;
}

interface ReviewTemplate {
  id: string;
  title: string;
  message: string;
  tone: 'friendly' | 'professional' | 'casual' | 'follow_up';
}

const reviewTemplates: ReviewTemplate[] = [
  {
    id: 'friendly',
    title: 'Friendly Request',
    message: "Hi {client_name}! 😊\n\nI hope you're happy with the work I completed for you. If you have a moment, I'd really appreciate it if you could leave me a quick review about your experience.\n\nIt would mean the world to me and help other customers find my services.\n\nThanks so much!\n\nBest regards",
    tone: 'friendly'
  },
  {
    id: 'professional',
    title: 'Professional Request',
    message: "Dear {client_name},\n\nThank you for choosing our services for your recent project. We hope you are completely satisfied with the quality of work delivered.\n\nWould you kindly consider leaving a review about your experience? Your feedback helps us improve our services and assists other customers in making informed decisions.\n\nWe value your opinion and appreciate your time.\n\nKind regards",
    tone: 'professional'
  },
  {
    id: 'casual',
    title: 'Casual Request',
    message: "Hey {client_name}!\n\nHope everything's working perfectly after the job I did for you! 👍\n\nIf you've got 2 minutes spare, would you mind leaving me a quick review? It really helps me out and lets other people know what to expect.\n\nCheers!",
    tone: 'casual'
  },
  {
    id: 'follow_up',
    title: 'Follow-up Request',
    message: "Hi {client_name},\n\nI hope you're still happy with the work I completed a while back.\n\nI know everyone's busy, but if you could spare a moment to leave a quick review, I'd be so grateful. It really makes a difference for small businesses like mine.\n\nNo worries if you can't - thanks for being a great customer either way!\n\nBest wishes",
    tone: 'follow_up'
  }
];

export const RequestReviewDrawer: React.FC<RequestReviewDrawerProps> = ({
  isOpen,
  onClose,
  clientName = 'there',
  clientEmail,
  clientPhone,
  clientId
}) => {
  // Extract first name only from full name
  const firstName = clientName.split(' ')[0];
  const [selectedTemplate, setSelectedTemplate] = useState<ReviewTemplate | null>(null);
  const [customMessage, setCustomMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const { authenticatedPost } = useAuthenticatedFetch();

  // Function to add system note to client
  const addSystemNote = async (method: 'email' | 'message') => {
    if (!clientId) return;
    
    try {
      const timestamp = new Date().toLocaleString('en-GB', {
        day: '2-digit',
        month: 'short', 
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
      
      const noteContent = `📬 Review request sent via ${method} on ${timestamp}`;
      
      // Add system note via API call
      const response = await authenticatedPost(getApiUrl(`/api/clients/${clientId}/notes`), {
        content: noteContent,
        type: 'system'
      });
      
      if (!response.ok) {
        throw new Error('Failed to add system note');
      }
    } catch (error) {
      console.error('Failed to add system note:', error);
    }
  };

  const handleTemplateSelect = (template: ReviewTemplate) => {
    setSelectedTemplate(template);
    // Replace placeholder with first name only
    const personalizedMessage = template.message.replace('{client_name}', firstName);
    setCustomMessage(personalizedMessage);
  };

  const handleSendEmail = async () => {
    if (!customMessage.trim() || !clientEmail || !clientId) return;
    
    setIsSending(true);
    try {
      console.log('📧 Sending review request via Gmail SMTP...');
      
      // Send review request via new API
      const response = await authenticatedPost('/api/review-requests/send', {
        clientId,
        clientEmail,
        clientName: clientName || 'there',
        message: customMessage,
        tone: selectedTemplate?.tone || 'friendly',
        businessName: 'DeskBelt', // You can get this from user profile later
        senderName: 'DeskBelt Team' // You can get this from user profile later
      });

      if (response.ok) {
        const data = await response.json();
        
        console.log('✅ Review request sent successfully!', data);
        
        // Show success feedback (you might want to add a toast notification here)
        alert('✅ Review request sent successfully! Your client will receive a professional email shortly.');
        
        // Reset form
        setSelectedTemplate(null);
        setCustomMessage('');
        
        // Note: The API endpoint now handles adding the system note automatically
        
      } else {
        const errorData = await response.json();
        console.error('❌ Review request failed:', errorData);
        
        if (response.status === 429) {
          alert('⏰ Rate limit reached. You can send up to 20 review requests per hour. Please try again later.');
        } else if (response.status === 403) {
          alert('🔒 Access denied. Please make sure you have permission to send emails for this client.');
        } else {
          alert(`❌ Failed to send review request: ${errorData.message || 'Unknown error'}`);
        }
      }
      
    } catch (error) {
      console.error('💥 Network error sending review request:', error);
      alert('🌐 Network error. Please check your connection and try again.');
    } finally {
      setIsSending(false);
    }
  };

  const handleSendMessage = async () => {
    if (!customMessage.trim() || !clientPhone) return;
    
    setIsSending(true);
    try {
      // Create SMS URL with message body
      const body = encodeURIComponent(customMessage);
      const smsUrl = `sms:${clientPhone}?body=${body}`;
      
      // Open default SMS client
      window.open(smsUrl, '_blank');
      
      // Add system note to client
      await addSystemNote('message');
      
      // Small delay for UX feedback
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Reset state but DON'T close drawer
      setSelectedTemplate(null);
      setCustomMessage('');
    } catch (error) {
      console.error('Failed to open SMS client:', error);
    } finally {
      setIsSending(false);
    }
  };

  const handleClose = () => {
    onClose();
    // Reset state when closing
    setSelectedTemplate(null);
    setCustomMessage('');
  };

  return (
    <Drawer
      isOpen={isOpen}
      onClose={handleClose}
      side="right"
      size="xl"
      showCloseButton={false}
      className="top-16 h-[calc(100%-4rem)] md:left-1/2 md:-translate-x-1/2 md:right-auto"
    >
      <div className="h-full flex flex-col bg-gradient-to-b from-amber-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="h-full flex flex-col max-w-4xl mx-auto w-full">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center">
                <TrophyIcon className="w-5 h-5 text-amber-600 dark:text-amber-500" />
              </div>
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  Request Review
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Ask {firstName} for feedback
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="p-2 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {!selectedTemplate ? (
            /* Template Selection */
            <div className="p-6">
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Choose a message style:
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Select a template to get started, then customize it as needed.
                </p>
              </div>

              <div className="space-y-3">
                {reviewTemplates.map((template) => (
                  <button
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className="w-full text-left p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-amber-300 dark:hover:border-amber-600 hover:bg-amber-50 dark:hover:bg-amber-900/10 transition-all duration-200 group"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white group-hover:text-amber-700 dark:group-hover:text-amber-300 transition-colors">
                          {template.title}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                          {template.message.replace('{client_name}', firstName).substring(0, 100)}...
                        </p>
                        <div className="flex items-center mt-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            template.tone === 'friendly' 
                              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                              : template.tone === 'professional'
                              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
                              : 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400'
                          }`}>
                            {template.tone}
                          </span>
                        </div>
                      </div>
                      <StarIcon className="w-5 h-5 text-gray-400 group-hover:text-amber-500 transition-colors flex-shrink-0 ml-3" />
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ) : (
            /* Message Customization */
            <div className="p-6">
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    Customize your message:
                  </h3>
                  <button
                    onClick={() => {
                      setSelectedTemplate(null);
                      setCustomMessage('');
                    }}
                    className="text-sm text-amber-600 dark:text-amber-500 hover:text-amber-700 dark:hover:text-amber-400 transition-colors"
                  >
                    ← Choose different template
                  </button>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Edit the message below to personalize it for {firstName}.
                </p>
              </div>

              <div className="mb-6">
                <Textarea
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="Write your review request message..."
                  rows={8}
                  className="w-full resize-none"
                />
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  {customMessage.length}/2000 characters
                </div>
              </div>

              {/* Send Options */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Send via:
                </h4>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <Button
                    onClick={handleSendEmail}
                    disabled={!customMessage.trim() || !clientEmail || isSending}
                    variant="outline"
                    size="md"
                    className="flex items-center justify-center space-x-2 py-3"
                  >
                    <EnvelopeIcon className="w-4 h-4" />
                    <span>Email</span>
                    {!clientEmail && (
                      <span className="text-xs text-gray-400">(No email)</span>
                    )}
                  </Button>
                  
                  <Button
                    onClick={handleSendMessage}
                    disabled={!customMessage.trim() || !clientPhone || isSending}
                    variant="outline"
                    size="md"
                    className="flex items-center justify-center space-x-2 py-3"
                  >
                    <ChatBubbleLeftIcon className="w-4 h-4" />
                    <span>Message</span>
                    {!clientPhone && (
                      <span className="text-xs text-gray-400">(No phone)</span>
                    )}
                  </Button>
                </div>

                {(!clientEmail && !clientPhone) && (
                  <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
                    <p className="text-sm text-amber-700 dark:text-amber-300">
                      <strong>No contact info available.</strong> Add email or phone number to this client to send review requests.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        </div>
      </div>
    </Drawer>
  );
}; 