'use client';

import React, { useState } from 'react';
import { CalendarDaysIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

export interface DateRange {
  startDate: string;
  endDate: string;
  label: string;
  preset: string;
}

interface DateRangeFilterProps {
  selectedRange: DateRange;
  onRangeChange: (range: DateRange) => void;
  className?: string;
}

export const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  selectedRange,
  onRangeChange,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showCustomPicker, setShowCustomPicker] = useState(false);
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');

  // Calculate date ranges
  const getDateRanges = (): DateRange[] => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    // This week (Monday to Sunday)
    const thisWeekStart = new Date(today);
    const dayOfWeek = today.getDay();
    const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // Sunday = 0, Monday = 1
    thisWeekStart.setDate(today.getDate() + mondayOffset);
    const thisWeekEnd = new Date(thisWeekStart);
    thisWeekEnd.setDate(thisWeekStart.getDate() + 6);

    // Last week
    const lastWeekStart = new Date(thisWeekStart);
    lastWeekStart.setDate(thisWeekStart.getDate() - 7);
    const lastWeekEnd = new Date(lastWeekStart);
    lastWeekEnd.setDate(lastWeekStart.getDate() + 6);

    // This month
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Last month
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // This quarter
    const currentQuarter = Math.floor(now.getMonth() / 3);
    const thisQuarterStart = new Date(now.getFullYear(), currentQuarter * 3, 1);
    const thisQuarterEnd = new Date(now.getFullYear(), (currentQuarter + 1) * 3, 0);

    // This year
    const thisYearStart = new Date(now.getFullYear(), 0, 1);
    const thisYearEnd = new Date(now.getFullYear(), 11, 31);

    // Last year
    const lastYearStart = new Date(now.getFullYear() - 1, 0, 1);
    const lastYearEnd = new Date(now.getFullYear() - 1, 11, 31);

    // Last 30 days
    const last30DaysStart = new Date(today);
    last30DaysStart.setDate(today.getDate() - 29);
    const last30DaysEnd = new Date(today);

    // Last 90 days
    const last90DaysStart = new Date(today);
    last90DaysStart.setDate(today.getDate() - 89);
    const last90DaysEnd = new Date(today);

    return [
      {
        startDate: formatDate(thisWeekStart),
        endDate: formatDate(thisWeekEnd),
        label: 'This Week',
        preset: 'this_week'
      },
      {
        startDate: formatDate(lastWeekStart),
        endDate: formatDate(lastWeekEnd),
        label: 'Last Week',
        preset: 'last_week'
      },
      {
        startDate: formatDate(thisMonthStart),
        endDate: formatDate(thisMonthEnd),
        label: 'This Month',
        preset: 'this_month'
      },
      {
        startDate: formatDate(lastMonthStart),
        endDate: formatDate(lastMonthEnd),
        label: 'Last Month',
        preset: 'last_month'
      },
      {
        startDate: formatDate(thisQuarterStart),
        endDate: formatDate(thisQuarterEnd),
        label: 'This Quarter',
        preset: 'this_quarter'
      },
      {
        startDate: formatDate(last30DaysStart),
        endDate: formatDate(last30DaysEnd),
        label: 'Last 30 Days',
        preset: 'last_30_days'
      },
      {
        startDate: formatDate(last90DaysStart),
        endDate: formatDate(last90DaysEnd),
        label: 'Last 90 Days',
        preset: 'last_90_days'
      },
      {
        startDate: formatDate(thisYearStart),
        endDate: formatDate(thisYearEnd),
        label: 'This Year',
        preset: 'this_year'
      },
      {
        startDate: formatDate(lastYearStart),
        endDate: formatDate(lastYearEnd),
        label: 'Last Year',
        preset: 'last_year'
      }
    ];
  };

  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  const formatDisplayDate = (dateStr: string): string => {
    const date = new Date(dateStr + 'T00:00:00');
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const handlePresetSelect = (range: DateRange) => {
    onRangeChange(range);
    setIsOpen(false);
    setShowCustomPicker(false);
  };

  const handleCustomRange = () => {
    setShowCustomPicker(true);
    setIsOpen(false);
  };

  const applyCustomRange = () => {
    if (customStartDate && customEndDate) {
      const customRange: DateRange = {
        startDate: customStartDate,
        endDate: customEndDate,
        label: `${formatDisplayDate(customStartDate)} - ${formatDisplayDate(customEndDate)}`,
        preset: 'custom'
      };
      onRangeChange(customRange);
      setShowCustomPicker(false);
      setCustomStartDate('');
      setCustomEndDate('');
    }
  };

  const cancelCustomRange = () => {
    setShowCustomPicker(false);
    setCustomStartDate('');
    setCustomEndDate('');
  };

  const dateRanges = getDateRanges();

  return (
    <div className={`relative ${className}`}>
      {/* Main Date Range Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
      >
        <CalendarDaysIcon className="w-4 h-4 mr-2" />
        <span className="mr-2">{selectedRange.label}</span>
        <ChevronDownIcon className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
          <div className="py-2">
            {dateRanges.map((range) => (
              <button
                key={range.preset}
                onClick={() => handlePresetSelect(range)}
                className={`w-full px-4 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                  selectedRange.preset === range.preset
                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                    : 'text-gray-700 dark:text-gray-300'
                }`}
              >
                <div className="font-medium">{range.label}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {formatDisplayDate(range.startDate)} - {formatDisplayDate(range.endDate)}
                </div>
              </button>
            ))}
            <div className="border-t border-gray-200 dark:border-gray-700 mt-2 pt-2">
              <button
                onClick={handleCustomRange}
                className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <div className="font-medium">Custom Range</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Choose your own dates
                </div>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Custom Date Picker Modal */}
      {showCustomPicker && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-96 mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Select Custom Date Range
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={customStartDate}
                  onChange={(e) => setCustomStartDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={customEndDate}
                  onChange={(e) => setCustomEndDate(e.target.value)}
                  min={customStartDate}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={cancelCustomRange}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={applyCustomRange}
                disabled={!customStartDate || !customEndDate}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
              >
                Apply Range
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

// Helper function to get default date range (This Month)
export const getDefaultDateRange = (): DateRange => {
  const now = new Date();
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  
  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  return {
    startDate: formatDate(thisMonthStart),
    endDate: formatDate(thisMonthEnd),
    label: 'This Month',
    preset: 'this_month'
  };
};