'use client';

import React, { useState, useEffect } from 'react';
import { JobDetails } from '@/types/Job';
import { CreateInvoiceData } from '@/types/Document';
import { useJobQuotes } from '@/hooks/useJobQuotes';
import { useProfile } from '@/hooks/useProfile';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { Input } from '@deskbelt/ui';
import { Textarea } from '@deskbelt/ui';
import { InvoicePreviewModal } from './InvoicePreviewModal';
import { 
  XMarkIcon,
  CurrencyPoundIcon,
  SparklesIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  EyeIcon,
  PencilIcon,
  CalendarIcon,
  PlusIcon,
  TrashIcon,
  LightBulbIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';

interface CreateInvoiceDrawerProps {
  job: JobDetails | null;
  isOpen: boolean;
  onClose: () => void;
  onInvoiceCreated?: (invoice: CreateInvoiceData) => void;
  onInvoiceSaved?: () => void;
  editingInvoice?: any;
}

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

type DrawerStep = 'input' | 'parsing' | 'success' | 'error';

export const CreateInvoiceDrawer: React.FC<CreateInvoiceDrawerProps> = ({
  job,
  isOpen,
  onClose,
  onInvoiceCreated,
  onInvoiceSaved,
  editingInvoice
}) => {
  const { data: profile } = useProfile();
  const { authenticatedPost, authenticatedFetch } = useAuthenticatedFetch();
  const [step, setStep] = useState<DrawerStep>('input');
  const [lineItems, setLineItems] = useState<LineItem[]>([]);
  const [includeVAT, setIncludeVAT] = useState(true);
  const [dueDate, setDueDate] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isProcessingAI, setIsProcessingAI] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showIntelligence, setShowIntelligence] = useState(false);
  const [intelligenceData, setIntelligenceData] = useState<any>(null);
  const [isLoadingIntelligence, setIsLoadingIntelligence] = useState(false);

  // Get real quotes data
  const { quotes } = useJobQuotes(job?.id || null);
  const acceptedQuotes = quotes.filter(quote => quote.status === 'accepted');

  // Auto-populate from job description and quotes when drawer opens
  useEffect(() => {
    if (isOpen && job && job.description && !editingInvoice && lineItems.length === 0) {
      generateInvoiceItems(job.description, acceptedQuotes);
    }
  }, [isOpen, job?.description, editingInvoice, acceptedQuotes]);

  // Populate form when editing existing invoice
  useEffect(() => {
    if (isOpen && editingInvoice) {
      // Use line_items if available, otherwise parse from details
      if (editingInvoice.line_items && editingInvoice.line_items.length > 0) {
        const items = editingInvoice.line_items.map((item: any, index: number) => ({
          id: `${Date.now()}-${index}`,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total
        }));
        setLineItems(items);
      } else {
        // Fallback to single item from details
        setLineItems([{
          id: '1',
          description: editingInvoice.details || '',
          quantity: 1,
          unitPrice: editingInvoice.amount || 0,
          total: editingInvoice.amount || 0
        }]);
      }
      setDueDate(editingInvoice.due_date || '');
    }
  }, [isOpen, editingInvoice]);

  // Reset state when drawer opens/closes
  useEffect(() => {
    if (!isOpen) {
      setStep('input');
      setLineItems([]);
      setIncludeVAT(true);
      setIsSubmitting(false);
      setIsProcessingAI(false);
      setError(null);
      setShowIntelligence(false);
      setIntelligenceData(null);
      setIsLoadingIntelligence(false);
    } else {
      // Set default due date to 30 days from now
      const defaultDueDate = new Date();
      defaultDueDate.setDate(defaultDueDate.getDate() + 30);
      setDueDate(defaultDueDate.toISOString().split('T')[0]);
    }
  }, [isOpen]);

  // Get contextual intelligence when line items change
  useEffect(() => {
    if (lineItems.length > 0 && lineItems.some(item => item.description.length > 10)) {
      // Add delay to avoid triggering on every keystroke
      const timeoutId = setTimeout(() => {
        getInvoiceIntelligence();
      }, 1000);

      return () => clearTimeout(timeoutId);
    } else {
      setIntelligenceData(null);
      setShowIntelligence(false);
    }
  }, [lineItems]);

  // Get invoice-specific intelligence using AI
  const getInvoiceIntelligence = async () => {
    setIsLoadingIntelligence(true);
    try {
      const invoiceDescription = lineItems.map(item => item.description).join(' ');
      
      // Use AI to analyze the invoice description and provide contextual insights
      const response = await authenticatedPost('/api/ai/quote-intelligence', { 
        description: `Invoice for: ${invoiceDescription}`,
        location: profile?.address?.includes('Manchester') ? 'Manchester' : 
                 profile?.address?.includes('Birmingham') ? 'Birmingham' : 
                 profile?.address?.includes('London') ? 'London' : 'UK',
        clientAddress: job?.client?.address || 'Unknown',
        isInvoice: true // Flag to indicate this is for invoice intelligence
      });

      if (response.ok) {
        const result = await response.json();
        
        // Transform the response for invoice-specific advice
        const invoiceIntelligence = {
          paymentAdvice: result.businessAdvice?.[0] || generatePaymentIntelligence(invoiceDescription),
          taxAdvice: generateTaxIntelligence(invoiceDescription, includeVAT),
          dueDateSuggestion: generateDueDateIntelligence(invoiceDescription),
          businessAdvice: result.businessAdvice?.slice(1) || [],
          riskFactors: result.riskFactors || []
        };
        
        setIntelligenceData(invoiceIntelligence);
        setShowIntelligence(true);
      } else {
        // Fallback to local analysis
        const localIntelligence = analyzeInvoiceLocally(invoiceDescription);
        setIntelligenceData(localIntelligence);
        setShowIntelligence(true);
      }
    } catch (error) {
      console.error('Invoice intelligence error:', error);
      // Fallback to local analysis
      const invoiceDescription = lineItems.map(item => item.description).join(' ');
      const localIntelligence = analyzeInvoiceLocally(invoiceDescription);
      setIntelligenceData(localIntelligence);
      setShowIntelligence(true);
    } finally {
      setIsLoadingIntelligence(false);
    }
  };

  // Local analysis of invoice content
  const analyzeInvoiceLocally = (description: string) => {
    const lowerDesc = description.toLowerCase();
    
    return {
      paymentTip: generatePaymentIntelligence(lowerDesc),
      taxAdvice: generateTaxIntelligence(lowerDesc, includeVAT),
      dueDateSuggestion: generateDueDateIntelligence(lowerDesc)
    };
  };

  // Generate payment intelligence
  const generatePaymentIntelligence = (description: string) => {
    const highValueWork = ['kitchen', 'bathroom', 'rewir', 'roof', 'extension'].some(keyword => 
      description.includes(keyword)
    );
    
    if (highValueWork) {
      return 'Consider requesting 50% upfront for high-value work';
    }
    
    return 'Standard 30-day payment terms recommended';
  };

  // Generate tax advice
  const generateTaxIntelligence = (description: string, vatIncluded: boolean) => {
    if (!vatIncluded) {
      return 'VAT registration required if annual turnover exceeds £85,000';
    }
    
    return 'VAT included - ensure you\'re registered for VAT';
  };

  // Generate due date suggestions
  const generateDueDateIntelligence = (description: string) => {
    const emergencyWork = ['emergency', 'urgent', 'leak', 'fault', 'breakdown'].some(keyword => 
      description.includes(keyword)
    );
    
    if (emergencyWork) {
      return 'Emergency work - consider "Due on receipt" terms';
    }
    
    return 'Standard 30-day terms maintain good cash flow';
  };

  // Generate invoice line items from job description and quotes using AI
  const generateInvoiceItems = async (jobDescription: string, quotes: any[]) => {
    if (!jobDescription.trim()) return;

    setIsProcessingAI(true);
    try {
      const quoteInfo = quotes.length > 0 
        ? `\n\nAccepted Quotes:\n${quotes.map(q => `- ${q.details} (£${q.amount})`).join('\n')}`
        : '';
      
      const response = await authenticatedPost('/api/ai/chat-response', { 
        prompt: `You are Dex, a professional UK tradesperson creating invoice line items based on a job description and any accepted quotes.

CRITICAL PRICING EXTRACTION: Look for ANY pricing information in the text (£5000, £500, etc.) and use those EXACT amounts. Do not make up prices.

Create itemised invoice line items that:
- Break down work into specific, billable items
- Use past tense (work completed)
- Extract and use actual pricing from quotes/descriptions (scan carefully for £ symbols and numbers like £5000)
- If you find £5000 mentioned anywhere, use that amount
- Include realistic quantities and unit prices based on extracted pricing
- Are professional and client-facing
- Focus on deliverables and materials used
- Use UK trade terminology

PRICING EXTRACTION RULES:
1. Scan ALL text for monetary amounts (£5000, £500, etc.)
2. Use those exact figures in your line items
3. If multiple amounts exist, use the main service amount
4. Do not invent prices if real ones are provided

Job Description: "${jobDescription}"${quoteInfo}

Return a JSON array of line items in this exact format:
[
  {
    "description": "Item description",
    "quantity": 1,
    "unitPrice": 5000.00
  }
]

Return ONLY the JSON array, no additional text or formatting.`,
        input: jobDescription
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.message) {
          try {
            const parsedItems = JSON.parse(result.message);
            if (Array.isArray(parsedItems)) {
              const newLineItems: LineItem[] = parsedItems.map((item, index) => ({
                id: `${Date.now()}-${index}`,
                description: item.description,
                quantity: item.quantity || 1,
                unitPrice: item.unitPrice || 0,
                total: (item.quantity || 1) * (item.unitPrice || 0)
              }));
              setLineItems(newLineItems);
            } else {
              // Fallback: create single item from job description
              createFallbackItem(jobDescription, quotes);
            }
          } catch (parseError) {
            // Fallback: create single item from job description
            createFallbackItem(jobDescription, quotes);
          }
        } else {
          createFallbackItem(jobDescription, quotes);
        }
      } else {
        createFallbackItem(jobDescription, quotes);
      }
    } catch (error) {
      console.error('AI invoice generation error:', error);
      createFallbackItem(jobDescription, quotes);
    } finally {
      setIsProcessingAI(false);
    }
  };

  const createFallbackItem = (jobDescription: string, quotes: any[]) => {
    const amount = quotes.length > 0 ? quotes[0].amount : 0;
    setLineItems([{
      id: `${Date.now()}`,
      description: jobDescription,
      quantity: 1,
      unitPrice: amount,
      total: amount
    }]);
  };

  // AI function to improve line items
  const improveLineItemsWithAI = async () => {
    if (lineItems.length === 0) return;

    setStep('parsing');
    setError(null);

    try {
      const itemsText = lineItems.map(item => 
        `${item.description} (Qty: ${item.quantity}, £${item.unitPrice})`
      ).join('\n');

      const response = await authenticatedPost('/api/ai/chat-response', { 
        prompt: `You are Dex, a professional UK tradesperson assistant. Improve these invoice line items to be more professional and detailed while keeping all pricing exactly the same.

Improve by:
- Making descriptions more professional and specific
- Using proper UK trade terminology
- Ensuring past tense for completed work
- Keeping all quantities and prices exactly the same
- Making items clear and client-friendly

Current items:
${itemsText}

Return a JSON array in this exact format:
[
  {
    "description": "Improved description",
    "quantity": ${lineItems[0]?.quantity || 1},
    "unitPrice": ${lineItems[0]?.unitPrice || 0}
  }
]

Return ONLY the JSON array, no additional text.`,
        input: itemsText
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.message) {
          try {
            const parsedItems = JSON.parse(result.message);
            if (Array.isArray(parsedItems)) {
              const improvedItems: LineItem[] = parsedItems.map((item, index) => ({
                id: lineItems[index]?.id || `${Date.now()}-${index}`,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                total: item.quantity * item.unitPrice
              }));
              setLineItems(improvedItems);
              setStep('input');
            } else {
              setStep('error');
              setError('Unable to improve line items.');
            }
          } catch (parseError) {
            setStep('error');
            setError('Unable to improve line items.');
          }
        } else {
          setStep('error');
          setError('Unable to improve line items.');
        }
      }
    } catch (error) {
      console.error('AI improvement error:', error);
      setStep('error');
      setError('Failed to process invoice. Please try again.');
    }
  };

  const addLineItem = () => {
    const newItem: LineItem = {
      id: `${Date.now()}`,
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0
    };
    setLineItems([...lineItems, newItem]);
  };

  const updateLineItem = (id: string, field: keyof LineItem, value: string | number) => {
    setLineItems(items => items.map(item => {
      if (item.id === id) {
        const updated = { ...item, [field]: value };
        if (field === 'quantity' || field === 'unitPrice') {
          updated.total = updated.quantity * updated.unitPrice;
        }
        return updated;
      }
      return item;
    }));
  };

  const removeLineItem = (id: string) => {
    setLineItems(items => items.filter(item => item.id !== id));
  };

  const subtotal = lineItems.reduce((sum, item) => sum + item.total, 0);
  const vatAmount = includeVAT ? subtotal * 0.2 : 0;
  const total = subtotal + vatAmount;

  const handleSaveInvoice = async () => {
    if (lineItems.length === 0 || !dueDate) return;

    console.log('💾 Starting invoice save for job:', job?.id, job?.title);
    setIsSubmitting(true);
    setError(null);

    try {
      const invoiceData = {
        amount: subtotal,
        tax: vatAmount,
        details: lineItems.map(item => 
          `${item.description} (Qty: ${item.quantity} @ £${item.unitPrice.toFixed(2)})`
        ).join('\n'),
        line_items: lineItems.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total
        })),
        due_date: dueDate
      };

      const url = editingInvoice 
        ? `/api/jobs/${job?.id}/invoices/${editingInvoice.id}`
        : `/api/jobs/${job?.id}/invoices`;
      
      const method = editingInvoice ? 'PUT' : 'POST';
      
      console.log('🌐 Making API call:', method, url);
      console.log('📦 Invoice data:', invoiceData);

      const response = await authenticatedFetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invoiceData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save invoice');
      }

      const result = await response.json();
      console.log('✅ Invoice saved successfully:', result);

      setStep('success');
      onInvoiceCreated?.({
        ...invoiceData,
        job_id: job?.id || ''
      });
      
      // Refresh invoices list immediately
      onInvoiceSaved?.();
      
      // Auto-close after success
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      console.error('Error saving invoice:', err);
      setStep('error');
      setError(err instanceof Error ? err.message : 'Failed to save invoice. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePreview = () => {
    setShowPreview(true);
  };

  const renderContent = () => {
    switch (step) {
      case 'input':
        return (
          <div className="space-y-6">
            {/* AI Processing Indicator */}
            {isProcessingAI && (
              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <div className="flex items-center space-x-3">
                  <SparklesIcon className="w-5 h-5 text-orange-600 dark:text-orange-400 animate-pulse" />
                  <div className="text-sm text-orange-800 dark:text-orange-200">
                    <p className="font-medium">Dex is preparing your invoice items...</p>
                  </div>
                </div>
              </div>
            )}

            {/* Loading Intelligence */}
            {isLoadingIntelligence && (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                  <div className="text-sm">
                    <span className="font-medium text-green-900 dark:text-green-100">Dex is analyzing your invoice...</span>
                    <p className="text-green-700 dark:text-green-300 text-xs">Getting payment and tax advice</p>
                  </div>
                </div>
              </div>
            )}

            {/* Invoice Intelligence */}
            {intelligenceData && (intelligenceData.paymentAdvice || intelligenceData.taxAdvice || intelligenceData.dueDateSuggestion || intelligenceData.businessAdvice || intelligenceData.riskFactors) && (
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-700 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">D</span>
                    </div>
                    <span className="text-sm font-medium text-green-900 dark:text-green-100">Dex's Invoice Advice</span>
                  </div>
                  <button
                    onClick={() => setShowIntelligence(!showIntelligence)}
                    className="p-1 text-green-400 hover:text-green-600 dark:hover:text-green-300"
                  >
                    {showIntelligence ? (
                      <ChevronUpIcon className="w-4 h-4" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4" />
                    )}
                  </button>
                </div>

                {showIntelligence && (
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {/* Payment Intelligence */}
                    {intelligenceData.paymentAdvice && (
                      <div className="bg-white dark:bg-gray-800 border border-green-300 dark:border-green-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-green-900 dark:text-green-100">Payment Terms</p>
                            <p className="text-green-700 dark:text-green-300">{intelligenceData.paymentAdvice}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Tax Advice */}
                    {intelligenceData.taxAdvice && (
                      <div className="bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-blue-900 dark:text-blue-100">VAT Guidance</p>
                            <p className="text-blue-700 dark:text-blue-300">{intelligenceData.taxAdvice}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Due Date Suggestion */}
                    {intelligenceData.dueDateSuggestion && (
                      <div className="bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-purple-900 dark:text-purple-100">Due Date</p>
                            <p className="text-purple-700 dark:text-purple-300">{intelligenceData.dueDateSuggestion}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Business Advice */}
                    {intelligenceData.businessAdvice && intelligenceData.businessAdvice.length > 0 && (
                      <div className="bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-purple-900 dark:text-purple-100">Business Advice</p>
                            <ul className="text-purple-700 dark:text-purple-300 space-y-0.5 mt-1">
                              {intelligenceData.businessAdvice.map((advice: string, index: number) => (
                                <li key={index} className="text-xs">• {advice}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Risk Factors */}
                    {intelligenceData.riskFactors && intelligenceData.riskFactors.length > 0 && (
                      <div className="bg-white dark:bg-gray-800 border border-red-300 dark:border-red-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-red-900 dark:text-red-100">Risk Factors</p>
                            <ul className="text-red-700 dark:text-red-300 space-y-0.5 mt-1">
                              {intelligenceData.riskFactors.map((risk: string, index: number) => (
                                <li key={index} className="text-xs">⚠️ {risk}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}



            {/* Line Items */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-900 dark:text-white">
                  Line Items
                </label>
                <Button
                  size="sm"
                  onClick={addLineItem}
                  className="text-xs bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <PlusIcon className="w-3 h-3 mr-1" />
                  Add Item
                </Button>
              </div>
              
              <div className="space-y-3">
                {lineItems.map((item, index) => (
                  <div key={item.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                    <div className="flex items-start justify-between mb-2">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                        Item {index + 1}
                      </span>
                      {lineItems.length > 1 && (
                        <button
                          onClick={() => removeLineItem(item.id)}
                          className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <Textarea
                        value={item.description}
                        onChange={(e) => updateLineItem(item.id, 'description', e.target.value)}
                        placeholder="Description of work completed..."
                        rows={2}
                        className="w-full text-sm"
                      />
                      
                      <div className="grid grid-cols-3 gap-2">
                        <div>
                          <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">Qty</label>
                          <Input
                            type="number"
                            min="1"
                            step="1"
                            value={item.quantity}
                            onChange={(e) => updateLineItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                            className="text-sm"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">Unit Price (£)</label>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) => updateLineItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                            className="text-sm"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">Total (£)</label>
                          <div className="text-sm font-medium text-gray-900 dark:text-white py-2 px-3 bg-gray-50 dark:bg-gray-800 rounded border">
                            {item.total.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* VAT Section */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-900 dark:text-white">
                  VAT/Tax
                </label>
              </div>
              
              <div className="flex items-center space-x-3 mb-3">
                <input
                  type="checkbox"
                  id="includeVAT"
                  checked={includeVAT}
                  onChange={(e) => setIncludeVAT(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="includeVAT" className="text-sm text-gray-900 dark:text-white">
                  Include VAT (20%)
                </label>
              </div>

              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded border space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>£{subtotal.toFixed(2)}</span>
                </div>
                {includeVAT && (
                  <div className="flex justify-between">
                    <span>VAT (20%):</span>
                    <span>£{vatAmount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between font-medium border-t pt-2">
                  <span>Total:</span>
                  <span>£{total.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* Due Date */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-900 dark:text-white">
                  Due Date
                </label>
                <CalendarIcon className="w-4 h-4 text-gray-400 dark:text-gray-500" />
              </div>
              <Input
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
                className="w-full"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col space-y-3">
              <Button
                onClick={improveLineItemsWithAI}
                disabled={lineItems.length === 0}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                <SparklesIcon className="w-4 h-4 mr-2" />
                Improve with Dex
              </Button>
              
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={handlePreview}
                  disabled={lineItems.length === 0}
                  className="flex-1 border-purple-300 text-purple-700 hover:bg-purple-50 dark:border-purple-600 dark:text-purple-400 dark:hover:bg-purple-900/20"
                >
                  <EyeIcon className="w-4 h-4 mr-2" />
                  Preview
                </Button>
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-800"
                >
                  Cancel
                </Button>
              </div>
              
              <Button
                onClick={handleSaveInvoice}
                disabled={lineItems.length === 0 || !dueDate || isSubmitting}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckCircleIcon className="w-4 h-4 mr-2" />
                {isSubmitting 
                  ? (editingInvoice ? 'Updating...' : 'Saving...') 
                  : (editingInvoice ? 'Update Invoice' : 'Save Invoice')
                }
              </Button>
            </div>
          </div>
        );

      case 'parsing':
        return (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-4">
              <SparklesIcon className="w-8 h-8 text-blue-600 dark:text-blue-400 animate-pulse" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Improving Invoice
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Dex is making your invoice more professional...
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full mb-4">
              <CheckCircleIcon className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {editingInvoice ? 'Invoice Updated!' : 'Invoice Created!'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Your invoice has been {editingInvoice ? 'updated' : 'saved'} successfully.
            </p>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Closing automatically...
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="space-y-6">
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
              <div className="flex items-start space-x-3">
                <ExclamationCircleIcon className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-red-800 dark:text-red-200 mb-1">
                    Error Processing Invoice
                  </p>
                  <p className="text-red-700 dark:text-red-300">
                    {error}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setStep('input')}
                className="flex-1"
              >
                Try Again
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <InvoicePreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        job={job}
        lineItems={lineItems}
        includeVAT={includeVAT}
        vatAmount={vatAmount}
        dueDate={dueDate}
      />
      
      <Drawer
        isOpen={isOpen}
        onClose={onClose}
        side="right"
        size="xl"
        showCloseButton={false}
        className="top-16 h-[calc(100%-4rem)] md:left-1/2 md:-translate-x-1/2 md:right-auto"
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <CurrencyPoundIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  {editingInvoice ? 'Edit Invoice' : 'Create Invoice'}
                </h2>
                {job && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    For "{job.title}" • {job.client?.name || 'Unknown Client'}
                  </p>
                )}
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <XMarkIcon className="w-5 h-5" />
            </Button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {renderContent()}
          </div>
        </div>
      </Drawer>
    </>
  );
}; 