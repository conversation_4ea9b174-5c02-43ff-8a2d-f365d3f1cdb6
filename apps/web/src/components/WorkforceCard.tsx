'use client';

import React, { useState } from 'react';
import { 
  UsersIcon, 
  CogIcon, 
  EllipsisVerticalIcon,
  StarIcon,
  UserGroupIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { Workforce } from '@/hooks/useWorkforce';

interface WorkforceCardProps {
  workforce: Workforce;
  isOwner: boolean;
  onSettingsClick: () => void;
  onMembersClick: () => void;
}

export default function WorkforceCard({ 
  workforce, 
  isOwner, 
  onSettingsClick, 
  onMembersClick 
}: WorkforceCardProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="card-interactive">
      {/* Card Header */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white text-lg font-semibold shadow-md">
              <UserGroupIcon className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors truncate">
                {workforce.name}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                {isOwner && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200">
                    <StarIcon className="w-3 h-3 mr-1" />
                    Owner
                  </span>
                )}
                <span className="text-sm text-secondary-600 dark:text-secondary-400">
                  Active Team
                </span>
              </div>
            </div>
          </div>

          {/* Menu Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300 rounded-xl hover:bg-secondary-100 dark:hover:bg-secondary-700 transition-all duration-200 hover:scale-105 focus-ring"
              aria-label="More options"
            >
              <EllipsisVerticalIcon className="w-5 h-5" />
            </button>

            {isMenuOpen && (
              <div className="absolute right-0 top-10 w-48 bg-white dark:bg-secondary-800 rounded-xl shadow-strong ring-1 ring-secondary-200 dark:ring-secondary-700 z-10 animate-fade-in">
                <div className="py-2">
                  <button
                    onClick={() => {
                      setIsMenuOpen(false);
                      onMembersClick();
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-secondary-700 dark:text-secondary-200 hover:bg-secondary-100 dark:hover:bg-secondary-700 flex items-center transition-colors"
                  >
                    <UsersIcon className="w-4 h-4 mr-3 text-secondary-400" />
                    Manage Members
                  </button>
                  {isOwner && (
                    <button
                      onClick={() => {
                        setIsMenuOpen(false);
                        onSettingsClick();
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-secondary-700 dark:text-secondary-200 hover:bg-secondary-100 dark:hover:bg-secondary-700 flex items-center transition-colors"
                    >
                      <CogIcon className="w-4 h-4 mr-3 text-secondary-400" />
                      Team Settings
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Team Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center p-3 bg-secondary-50 dark:bg-secondary-700 rounded-xl">
            <div className="text-2xl font-bold text-secondary-900 dark:text-secondary-50">
              1
            </div>
            <div className="text-sm text-secondary-600 dark:text-secondary-400">
              Members
            </div>
          </div>
          <div className="text-center p-3 bg-secondary-50 dark:bg-secondary-700 rounded-xl">
            <div className="text-2xl font-bold text-secondary-900 dark:text-secondary-50">
              0
            </div>
            <div className="text-sm text-secondary-600 dark:text-secondary-400">
              Jobs Assigned
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-secondary-900 dark:text-secondary-50">
            Recent Activity
          </h4>
          <div className="text-sm text-secondary-600 dark:text-secondary-400">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="w-4 h-4" />
              <span>Team created recently</span>
            </div>
          </div>
        </div>
        {/* Card Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-secondary-200 dark:border-secondary-700">
          <div className="text-sm text-secondary-500 dark:text-secondary-400">
            Last updated today
          </div>
          <div className="flex space-x-2">
            <button
              onClick={onMembersClick}
              className="p-2 rounded-xl text-secondary-600 dark:text-secondary-400 hover:bg-secondary-100 dark:hover:bg-secondary-700 hover:text-secondary-700 dark:hover:text-secondary-200 transition-all duration-200 hover:scale-105 focus-ring"
              title="Manage Members"
              aria-label="Manage Members"
            >
              <UsersIcon className="w-5 h-5" />
            </button>
            {isOwner && (
              <button
                onClick={onSettingsClick}
                className="p-2 rounded-xl text-primary-600 dark:text-primary-400 hover:bg-primary-100 dark:hover:bg-primary-700 hover:text-primary-700 dark:hover:text-primary-200 transition-all duration-200 hover:scale-105 focus-ring"
                title="Team Settings"
                aria-label="Team Settings"
              >
                <CogIcon className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Click outside to close menu */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setIsMenuOpen(false)}
        />
      )}
    </div>
  );
}