'use client';

import React from 'react';
import { useDrawer } from '@/contexts/DrawerContext';
import { useDeleteInvoice } from '@/hooks/useDeleteInvoice';
import { useToast } from '@/contexts/ToastContext';
import { NewJobDrawer } from './NewJobDrawer';
import { NewClientDrawer } from './NewClientDrawer';
import { RequestReviewDrawer } from './RequestReviewDrawer';
import { EditClientDrawer } from './EditClientDrawer';
import AskDexDrawer from './AskDexDrawer';
import NotificationsDrawer from './NotificationsDrawer';
import WorkforceDrawer from './WorkforceDrawer';
import { SendEmailModal } from './SendEmailModal';
import { CreateInvoiceDrawer } from './CreateInvoiceDrawer';
import { InvoiceViewModal } from './InvoiceViewModal';
import { DeleteConfirmationModal } from './DeleteConfirmationModal';
import { JobDetailsDrawer } from './JobDetailsDrawer';
import ScheduleJobDrawer from './ScheduleJobDrawer';

export const GlobalDrawers: React.FC = () => {
  const {
    isNewJobDrawerOpen,
    closeNewJobDrawer,
    preSelectedClientId,
    isNewClientDrawerOpen,
    closeNewClientDrawer,
    isRequestReviewDrawerOpen,
    openRequestReviewDrawer,
    closeRequestReviewDrawer,
    requestReviewClientData,
    isEditClientDrawerOpen,
    closeEditClientDrawer,
    editClientData,
    openNewClientDrawer,
    isAskDexDrawerOpen,
    closeAskDexDrawer,
    askDexContext,
    onClientCreated,
    onJobCreated,
    isNotificationsDrawerOpen,
    closeNotificationsDrawer,
    isWorkforceDrawerOpen,
    closeWorkforceDrawer,
    isSendEmailModalOpen,
    closeSendEmailModal,
    emailJobData,
    isCreateInvoiceDrawerOpen,
    closeCreateInvoiceDrawer,
    openCreateInvoiceDrawer,
    createInvoiceJobData,
    isInvoicePreviewModalOpen,
    closeInvoicePreviewModal,
    previewInvoiceData,
    isDeleteConfirmationModalOpen,
    openDeleteConfirmationModal,
    closeDeleteConfirmationModal,
    deleteConfirmationData,
    isJobDetailsDrawerOpen,
    closeJobDetailsDrawer,
    jobDetailsData,
    isScheduleJobDrawerOpen,
    closeScheduleJobDrawer,
    scheduleJobId,
  } = useDrawer();

  const { deleteInvoice, isDeleting } = useDeleteInvoice();
  const { showSuccess, showError } = useToast();

  const handleEditInvoice = (invoice: any) => {
    closeInvoicePreviewModal();
    openCreateInvoiceDrawer(invoice);
  };

  const handleDeleteInvoice = (invoice: any) => {
    openDeleteConfirmationModal({
      title: 'Delete Invoice',
      message: 'Are you sure you want to delete this invoice? This will permanently remove it from your records.',
      itemName: `INV-${invoice.id.slice(-3).toUpperCase()} - £${invoice.amount.toLocaleString('en-GB', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      onConfirm: async () => {
        const result = await deleteInvoice(invoice.id);
        if (result.success) {
          showSuccess('Invoice deleted successfully');
          closeInvoicePreviewModal();
          closeDeleteConfirmationModal();
          // Trigger refetch in invoice page if needed
          if (onJobCreated) {
            onJobCreated();
          }
        } else {
          showError(result.error || 'Failed to delete invoice');
          closeDeleteConfirmationModal();
        }
      }
    });
  };

  return (
    <>
      <NewJobDrawer
        isOpen={isNewJobDrawerOpen}
        onClose={closeNewJobDrawer}
        onRequestNewClient={openNewClientDrawer}
        onJobCreated={onJobCreated}
        preSelectedClientId={preSelectedClientId}
      />
      <NewClientDrawer
        isOpen={isNewClientDrawerOpen}
        onClose={closeNewClientDrawer}
        onClientCreated={onClientCreated}
      />
      <RequestReviewDrawer
        isOpen={isRequestReviewDrawerOpen}
        onClose={closeRequestReviewDrawer}
        clientId={requestReviewClientData?.id}
        clientName={requestReviewClientData?.name || 'Client'}
        clientEmail={requestReviewClientData?.email}
        clientPhone={requestReviewClientData?.phone}
      />
      <EditClientDrawer
        isOpen={isEditClientDrawerOpen}
        onClose={closeEditClientDrawer}
        clientId={editClientData?.id || null}
        clientData={editClientData || undefined}
        onClientUpdated={onClientCreated}
      />
      <AskDexDrawer
        isOpen={isAskDexDrawerOpen}
        onClose={closeAskDexDrawer}
        context={askDexContext}
      />
      {isNotificationsDrawerOpen && (
        <NotificationsDrawer
          isOpen={isNotificationsDrawerOpen}
          onClose={closeNotificationsDrawer}
        />
      )}
      {isWorkforceDrawerOpen && (
        <WorkforceDrawer
          isOpen={isWorkforceDrawerOpen}
          onClose={closeWorkforceDrawer}
        />
      )}
      {emailJobData && (
        <SendEmailModal
          isOpen={isSendEmailModalOpen}
          onClose={closeSendEmailModal}
          job={emailJobData}
        />
      )}
      {isCreateInvoiceDrawerOpen && (
        <CreateInvoiceDrawer
          isOpen={isCreateInvoiceDrawerOpen}
          onClose={closeCreateInvoiceDrawer}
          job={createInvoiceJobData}
          editingInvoice={createInvoiceJobData && typeof createInvoiceJobData === 'object' && 'id' in createInvoiceJobData ? createInvoiceJobData : undefined}
        />
      )}
      {previewInvoiceData && (
        <InvoiceViewModal
          isOpen={isInvoicePreviewModalOpen}
          onClose={closeInvoicePreviewModal}
          invoice={previewInvoiceData}
          onEdit={handleEditInvoice}
          onDelete={handleDeleteInvoice}
        />
      )}
      {deleteConfirmationData && (
        <DeleteConfirmationModal
          isOpen={isDeleteConfirmationModalOpen}
          onClose={closeDeleteConfirmationModal}
          onConfirm={deleteConfirmationData.onConfirm}
          title={deleteConfirmationData.title}
          message={deleteConfirmationData.message}
          itemName={deleteConfirmationData.itemName}
          isLoading={isDeleting}
        />
      )}
      <JobDetailsDrawer
        isOpen={isJobDetailsDrawerOpen}
        onClose={closeJobDetailsDrawer}
        job={jobDetailsData}
      />
      <ScheduleJobDrawer
        isOpen={isScheduleJobDrawerOpen}
        onClose={closeScheduleJobDrawer}
        preselectedJobId={scheduleJobId || undefined}
        onJobScheduled={onJobCreated}
      />
    </>
  );
}; 