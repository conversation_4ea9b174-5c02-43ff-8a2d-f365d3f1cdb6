'use client';

import React from 'react';
import { usePushNotifications } from '@/hooks/usePushNotifications';
import { Button } from '@deskbelt/ui';
import { 
  BellIcon, 
  BellSlashIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

interface PushNotificationSettingsProps {
  className?: string;
}

export const PushNotificationSettings: React.FC<PushNotificationSettingsProps> = ({
  className = ''
}) => {
  const {
    isSupported,
    isSubscribed,
    isLoading,
    error,
    permission,
    requestPermission,
    subscribe,
    unsubscribe,
    sendTestNotification
  } = usePushNotifications();

  const getPermissionStatus = () => {
    switch (permission) {
      case 'granted':
        return {
          icon: <CheckCircleIcon className="w-5 h-5 text-green-600" />,
          text: 'Notifications allowed',
          color: 'text-green-600'
        };
      case 'denied':
        return {
          icon: <XCircleIcon className="w-5 h-5 text-red-600" />,
          text: 'Notifications blocked',
          color: 'text-red-600'
        };
      default:
        return {
          icon: <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />,
          text: 'Permission not requested',
          color: 'text-yellow-600'
        };
    }
  };

  const handleToggleSubscription = async () => {
    if (isSubscribed) {
      await unsubscribe();
    } else {
      await subscribe();
    }
  };

  if (!isSupported) {
    return (
      <div className={`bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4 ${className}`}>
        <div className="flex items-start space-x-3">
          <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Push Notifications Not Supported
            </h3>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
              Your browser doesn't support push notifications. Please use a modern browser like Chrome, Firefox, or Safari.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const permissionStatus = getPermissionStatus();

  return (
    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <BellIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Push Notifications
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Get notified about important updates and new messages
            </p>
          </div>
        </div>
      </div>

      {/* Permission Status */}
      <div className="flex items-center space-x-2 mb-4">
        {permissionStatus.icon}
        <span className={`text-sm font-medium ${permissionStatus.color}`}>
          {permissionStatus.text}
        </span>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-3 mb-4">
          <div className="flex items-start space-x-2">
            <XCircleIcon className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        {permission === 'default' && (
          <Button
            onClick={requestPermission}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            <BellIcon className="w-4 h-4 mr-2" />
            {isLoading ? 'Requesting...' : 'Enable Notifications'}
          </Button>
        )}

        {permission === 'granted' && (
          <div className="flex space-x-3">
            <Button
              onClick={handleToggleSubscription}
              disabled={isLoading}
              variant={isSubscribed ? 'outline' : 'primary'}
              className="flex-1"
            >
              {isSubscribed ? (
                <>
                  <BellSlashIcon className="w-4 h-4 mr-2" />
                  {isLoading ? 'Unsubscribing...' : 'Disable Push'}
                </>
              ) : (
                <>
                  <BellIcon className="w-4 h-4 mr-2" />
                  {isLoading ? 'Subscribing...' : 'Enable Push'}
                </>
              )}
            </Button>

            {isSubscribed && (
              <Button
                onClick={sendTestNotification}
                variant="outline"
                className="flex-shrink-0"
              >
                Test
              </Button>
            )}
          </div>
        )}

        {permission === 'denied' && (
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Notifications are blocked. To enable them:
            </p>
            <ol className="text-sm text-gray-600 dark:text-gray-400 list-decimal list-inside space-y-1">
              <li>Click the lock icon in your browser's address bar</li>
              <li>Change notifications from "Block" to "Allow"</li>
              <li>Refresh this page</li>
            </ol>
          </div>
        )}
      </div>

      {/* Subscription Status */}
      {permission === 'granted' && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">
              Push notifications:
            </span>
            <span className={`font-medium ${
              isSubscribed 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-gray-500 dark:text-gray-400'
            }`}>
              {isSubscribed ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};