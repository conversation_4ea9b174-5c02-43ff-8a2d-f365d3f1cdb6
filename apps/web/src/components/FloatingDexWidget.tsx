'use client';

import { useState, useEffect } from 'react';
import { useDrawer } from '@/contexts/DrawerContext';

export default function FloatingDexWidget() {
  const { openAskDexDrawer } = useDrawer();
  const [shouldPulse, setShouldPulse] = useState(true);

  // Stop the initial pulse after a short delay
  useEffect(() => {
    const timer = setTimeout(() => setShouldPulse(false), 2000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <button
      onClick={() => {
        openAskDexDrawer();
        setShouldPulse(false);
      }}
      className={`fixed bottom-6 right-6 w-14 h-14 bg-orange-500 hover:bg-orange-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50 hover:scale-105 p-1 ${
        shouldPulse ? 'animate-pulse' : ''
      }`}
    >
      <div className="relative w-12 h-12">
        <img src="/assets/avatar.png" alt="Dex Avatar" className="w-full h-full rounded-full object-cover" />
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white" />
      </div>
    </button>
  );
} 