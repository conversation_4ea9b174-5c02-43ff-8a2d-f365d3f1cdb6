'use client';

import { useState, useEffect } from 'react';
import { useProfile } from '@/hooks/useProfile';
import { 
  XMarkIcon, 
  PencilIcon, 
  CheckIcon,
  ShareIcon,
  EnvelopeIcon,
  ChatBubbleLeftIcon,
  MapPinIcon,
  PhoneIcon,
  GlobeAltIcon,
  BuildingOfficeIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface ProfileDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ProfileDrawer: React.FC<ProfileDrawerProps> = ({ isOpen, onClose }) => {
  const { data: profile, isLoading: profileLoading, updateProfile } = useProfile();
  
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    full_name: '',
    company_name: '',
    phone: '',
    website: '',
    address: '',
    country: 'United Kingdom',
    vat_number: ''
  });
  const [isSaving, setIsSaving] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);

  useEffect(() => {
    if (profile) {
      setEditData({
        full_name: profile.full_name || '',
        company_name: profile.company_name || '',
        phone: profile.phone || '',
        website: profile.website || '',
        address: profile.address || '',
        country: profile.country || 'United Kingdom',
        vat_number: profile.vat_number || ''
      });
    }
  }, [profile]);

  const handleEditToggle = () => {
    if (isEditing) {
      setEditData({
        full_name: profile?.full_name || '',
        company_name: profile?.company_name || '',
        phone: profile?.phone || '',
        website: profile?.website || '',
        address: profile?.address || '',
        country: profile?.country || 'United Kingdom',
        vat_number: profile?.vat_number || ''
      });
    }
    setIsEditing(!isEditing);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updateProfile(editData);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleShareBusinessCard = (method: 'email' | 'message') => {
    if (!profile) return;
    
    const businessCardText = `🏢 ${profile.full_name}
${profile.company_name ? `📍 ${profile.company_name}` : ''}
${profile.phone ? `📞 ${profile.phone}` : ''}
${profile.email ? `📧 ${profile.email}` : ''}
${profile.website ? `🌐 ${profile.website}` : ''}
${profile.address ? `📍 ${profile.address}` : ''}`.trim();

    if (method === 'email') {
      const subject = `Business Card - ${profile.full_name}`;
      window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(businessCardText)}`, '_self');
    } else {
      prompt('Copy this business card text to send via message:', businessCardText);
    }
    
    setShowShareMenu(false);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (!isOpen) return null;

  return (
    <>
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
        onClick={onClose}
      />
      
      <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 z-50 transform transition-transform duration-300 ease-out rounded-t-2xl shadow-2xl max-h-[85vh] flex flex-col">
        <div className="flex justify-center pt-3 pb-1">
          <div className="w-12 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        </div>

        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {isEditing ? 'Edit Profile' : 'Business Card'}
            </h2>
            {!isEditing && profile && profile.created_at && (
              <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full">
                Member since {(() => {
                  try {
                    const date = new Date(profile.created_at);
                    return isNaN(date.getTime()) 
                      ? 'Aug 2023' 
                      : date.toLocaleDateString('en-GB', { 
                          month: 'short', 
                          year: 'numeric' 
                        });
                  } catch {
                    return 'Aug 2023';
                  }
                })()}
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {!isEditing && (
              <div className="relative">
                <button
                  onClick={() => setShowShareMenu(!showShareMenu)}
                  className="p-2 rounded-lg bg-gray-600 text-white hover:bg-gray-700 transition-colors"
                >
                  <ShareIcon className="w-5 h-5" />
                </button>
                
                {showShareMenu && (
                  <div className="absolute right-0 top-full mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50 min-w-[160px]">
                    <button
                      onClick={() => handleShareBusinessCard('email')}
                      className="flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <EnvelopeIcon className="w-4 h-4" />
                      <span>Send via Email</span>
                    </button>
                    <button
                      onClick={() => handleShareBusinessCard('message')}
                      className="flex items-center space-x-3 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <ChatBubbleLeftIcon className="w-4 h-4" />
                      <span>Send via Message</span>
                    </button>
                  </div>
                )}
              </div>
            )}
            
            <button
              onClick={isEditing ? handleEditToggle : () => setIsEditing(true)}
              disabled={isSaving}
              className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <PencilIcon className="w-5 h-5" />
            </button>
            
            <button
              onClick={onClose}
              className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {profileLoading ? (
            <div className="flex items-center justify-center p-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : profile ? (
            <div className="p-6 max-w-2xl mx-auto">
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-8 shadow-sm">
                <div className="flex items-start space-x-6 mb-8">
                  <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-xl flex items-center justify-center text-gray-700 dark:text-gray-300 text-2xl font-bold">
                    {getInitials(profile.full_name || 'User')}
                  </div>
                  
                  <div className="flex-1">
                    {isEditing ? (
                      <div className="space-y-3">
                        <input
                          type="text"
                          value={editData.full_name}
                          onChange={(e) => setEditData(prev => ({ ...prev, full_name: e.target.value }))}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Your full name"
                        />
                        <input
                          type="text"
                          value={editData.company_name}
                          onChange={(e) => setEditData(prev => ({ ...prev, company_name: e.target.value }))}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Company name"
                        />
                      </div>
                    ) : (
                      <div>
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                          {profile.full_name || 'Your Name'}
                        </h1>
                        <p className="text-gray-600 dark:text-gray-400 text-lg">
                          {profile.company_name || 'Your Company'}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                      <PhoneIcon className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex-1">
                      {isEditing ? (
                        <input
                          type="tel"
                          value={editData.phone}
                          onChange={(e) => setEditData(prev => ({ ...prev, phone: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                          placeholder="+44 7700 900000"
                        />
                      ) : (
                        <div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Phone</div>
                          <div className="text-gray-900 dark:text-white">
                            {profile.phone || 'Not set'}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                      <EnvelopeIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Email</div>
                      <div className="text-gray-900 dark:text-white truncate">
                        {profile.email}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                      <GlobeAltIcon className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="flex-1">
                      {isEditing ? (
                        <input
                          type="url"
                          value={editData.website}
                          onChange={(e) => setEditData(prev => ({ ...prev, website: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                          placeholder="https://yourwebsite.com"
                        />
                      ) : (
                        <div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Website</div>
                          <div className="text-gray-900 dark:text-white truncate">
                            {profile.website || 'Not set'}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                      <BuildingOfficeIcon className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div className="flex-1">
                      {isEditing ? (
                        <select
                          value={editData.country}
                          onChange={(e) => setEditData(prev => ({ ...prev, country: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        >
                          <option value="United Kingdom">United Kingdom</option>
                          <option value="Ireland">Ireland</option>
                          <option value="United States">United States</option>
                          <option value="Canada">Canada</option>
                          <option value="Australia">Australia</option>
                        </select>
                      ) : (
                        <div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Country</div>
                          <div className="text-gray-900 dark:text-white">
                            {profile.country || 'Not set'}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                      <DocumentTextIcon className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div className="flex-1">
                      {isEditing ? (
                        <input
                          type="text"
                          value={editData.vat_number}
                          onChange={(e) => setEditData(prev => ({ ...prev, vat_number: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                          placeholder="GB123456789"
                        />
                      ) : (
                        <div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">VAT Number</div>
                          <div className="text-gray-900 dark:text-white">
                            {profile.vat_number || 'Not set'}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {(profile.address || isEditing) && (
                  <div className="mt-6 flex items-start space-x-3">
                    <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                      <MapPinIcon className="w-5 h-5 text-red-600 dark:text-red-400" />
                    </div>
                    <div className="flex-1">
                      {isEditing ? (
                        <textarea
                          value={editData.address}
                          onChange={(e) => setEditData(prev => ({ ...prev, address: e.target.value }))}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm resize-none"
                          placeholder="Your business address"
                        />
                      ) : (
                        <div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Address</div>
                          <div className="text-gray-900 dark:text-white text-sm leading-relaxed">
                            {profile.address}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {isEditing && (
                <div className="flex space-x-3 justify-end mt-6">
                  <button
                    onClick={handleEditToggle}
                    disabled={isSaving}
                    className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors disabled:opacity-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                  >
                    <CheckIcon className="w-4 h-4 mr-2" />
                    {isSaving ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              )}
            </div>
          ) : null}
        </div>
      </div>
    </>
  );
}; 