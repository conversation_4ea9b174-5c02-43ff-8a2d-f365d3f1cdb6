'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';

export const DashboardTabs: React.FC = () => {
  const pathname = usePathname();

  const tabs = [
    { href: '/dashboard', label: 'Overview', color: 'primary' },
    { href: '/dashboard/jobs', label: 'Jobs', color: 'blue' },
    { href: '/dashboard/clients', label: 'Clients', color: 'green' },
    { href: '/dashboard/teams', label: 'Teams', color: 'indigo' },
    { href: '/dashboard/stats', label: 'Stats', color: 'purple' },
    { href: '/dashboard/archived', label: 'Archived', color: 'gray' },
  ];

  const getTabClasses = (href: string, color: string) => {
    const isActive = pathname === href;
    
    if (isActive) {
      const activeColors = {
        primary: 'border-primary-500 text-primary-700 dark:text-primary-400 bg-primary-50/50 dark:bg-primary-900/20',
        blue: 'border-blue-500 text-blue-700 dark:text-blue-400 bg-blue-50/50 dark:bg-blue-900/20',
        green: 'border-green-500 text-green-700 dark:text-green-400 bg-green-50/50 dark:bg-green-900/20',
        indigo: 'border-indigo-500 text-indigo-700 dark:text-indigo-400 bg-indigo-50/50 dark:bg-indigo-900/20',
        purple: 'border-purple-500 text-purple-700 dark:text-purple-400 bg-purple-50/50 dark:bg-purple-900/20',
        gray: 'border-gray-500 text-gray-700 dark:text-gray-300 bg-gray-50/50 dark:bg-gray-900/20',
      };
      return `py-3 px-4 border-b-2 rounded-t-lg ${activeColors[color as keyof typeof activeColors]} whitespace-nowrap font-semibold text-sm transition-all duration-200`;
    }
    
    return 'py-3 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50/50 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-500 dark:hover:bg-gray-800/20 whitespace-nowrap font-medium text-sm transition-all duration-200 rounded-t-lg';
  };

  return (
    <div className="border-b border-gray-200 dark:border-gray-700">
      <nav className="-mb-px flex space-x-1">
        {tabs.map((tab) => (
          <Link
            key={tab.href}
            href={tab.href}
            className={getTabClasses(tab.href, tab.color)}
          >
            {tab.label}
          </Link>
        ))}
      </nav>
    </div>
  );
}; 