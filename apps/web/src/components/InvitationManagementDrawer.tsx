import React, { useState, useEffect } from 'react';
import { XMarkIcon, EnvelopeIcon, ClockIcon, CheckCircleIcon, XCircleIcon, ArrowPathIcon, TrashIcon, EyeIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/contexts/ToastContext';

interface InvitationManagementDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  teamId: string | null;
}

interface Invitation {
  id: string;
  email: string;
  role: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired' | 'cancelled';
  sent_at: string;
  expires_at: string;
  personal_message?: string;
  invitation_method: string;
  can_manage_jobs: boolean;
  can_manage_clients: boolean;
  can_manage_invoices: boolean;
  can_manage_quotes: boolean;
  can_view_reports: boolean;
  can_manage_team: boolean;
  invited_by_user: {
    full_name: string | null;
    email: string;
  };
}

const STATUS_CONFIG = {
  pending: { label: 'Pending', color: 'yellow', icon: ClockIcon },
  accepted: { label: 'Accepted', color: 'green', icon: CheckCircleIcon },
  declined: { label: 'Declined', color: 'red', icon: XCircleIcon },
  expired: { label: 'Expired', color: 'gray', icon: ClockIcon },
  cancelled: { label: 'Cancelled', color: 'gray', icon: XCircleIcon }
};

export default function InvitationManagementDrawer({ isOpen, onClose, teamId }: InvitationManagementDrawerProps) {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedInvitation, setSelectedInvitation] = useState<Invitation | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'accepted' | 'expired'>('all');

  const { authenticatedGet, authenticatedPost, authenticatedDelete } = useAuthenticatedFetch();
  const { showSuccess, showError } = useToast();

  useEffect(() => {
    if (isOpen && teamId) {
      fetchInvitations();
    }
  }, [isOpen, teamId]);

  const fetchInvitations = async () => {
    if (!teamId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await authenticatedGet(`/api/workforce/${teamId}/invitations`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch invitations');
      }

      const data = await response.json();
      setInvitations(data);
    } catch (err) {
      console.error('Error fetching invitations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch invitations');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendInvitation = async (invitationId: string) => {
    if (!teamId) return;

    setActionLoading(invitationId);
    try {
      const response = await authenticatedPost(`/api/workforce/${teamId}/invitations/${invitationId}/resend`, {});
      
      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.message || errorData.error || 'Failed to resend invitation';
        throw new Error(errorMessage);
      }

      await fetchInvitations();
      showSuccess('Invitation resent successfully!', 'The invitation email has been sent again with a new link.');
    } catch (err) {
      console.error('Error resending invitation:', err);
      showError('Failed to resend invitation', err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    if (!teamId) return;

    setActionLoading(invitationId);
    try {
      const response = await authenticatedDelete(`/api/workforce/${teamId}/invitations/${invitationId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.message || errorData.error || 'Failed to cancel invitation';
        throw new Error(errorMessage);
      }

      await fetchInvitations();
      showSuccess('Invitation cancelled successfully!', 'The invitation has been cancelled and can no longer be used.');
    } catch (err) {
      console.error('Error cancelling invitation:', err);
      showError('Failed to cancel invitation', err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setActionLoading(null);
    }
  };

  const getPermissionCount = (invitation: Invitation) => {
    return [
      invitation.can_manage_jobs,
      invitation.can_manage_clients,
      invitation.can_manage_invoices,
      invitation.can_manage_quotes,
      invitation.can_view_reports,
      invitation.can_manage_team
    ].filter(Boolean).length;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isExpiringSoon = (expiresAt: string) => {
    const expiryDate = new Date(expiresAt);
    const now = new Date();
    const hoursDiff = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    return hoursDiff < 24 && hoursDiff > 0;
  };

  const filteredInvitations = invitations.filter(invitation => {
    if (filter === 'all') return true;
    return invitation.status === filter;
  });

  const getStatusBadge = (status: Invitation['status']) => {
    const config = STATUS_CONFIG[status];
    const Icon = config.icon;
    
    const colorClasses = {
      yellow: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
      green: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
      red: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
      gray: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[config.color]}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </span>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-50">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="relative w-full max-w-4xl bg-white dark:bg-gray-900 shadow-xl flex flex-col h-full overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <EnvelopeIcon className="w-6 h-6 mr-3" />
              <div>
                <h2 className="text-lg font-semibold">Invitation Management</h2>
                <p className="text-indigo-100 text-sm">
                  View and manage all workforce invitations
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex space-x-8 px-6 py-3">
            {[
              { id: 'all' as const, label: 'All', count: invitations.length },
              { id: 'pending' as const, label: 'Pending', count: invitations.filter(i => i.status === 'pending').length },
              { id: 'accepted' as const, label: 'Accepted', count: invitations.filter(i => i.status === 'accepted').length },
              { id: 'expired' as const, label: 'Expired', count: invitations.filter(i => i.status === 'expired').length }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setFilter(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  filter === tab.id
                    ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <span>{tab.label}</span>
                {tab.count > 0 && (
                  <span className={`inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full ${
                    filter === tab.id
                      ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300'
                      : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">Loading invitations...</p>
              </div>
            </div>
          ) : error ? (
            <div className="p-6">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="text-red-800 dark:text-red-200 text-sm">{error}</div>
              </div>
            </div>
          ) : filteredInvitations.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <EnvelopeIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Invitations Found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {filter === 'all' ? 'No invitations have been sent yet.' : `No ${filter} invitations found.`}
                </p>
              </div>
            </div>
          ) : (
            <div className="p-6 space-y-4">
              {filteredInvitations.map((invitation) => (
                <div key={invitation.id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          {invitation.email}
                        </h3>
                        {getStatusBadge(invitation.status)}
                        {invitation.status === 'pending' && isExpiringSoon(invitation.expires_at) && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300">
                            <ClockIcon className="w-3 h-3 mr-1" />
                            Expires Soon
                          </span>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Role</p>
                          <p className="text-sm text-gray-900 dark:text-white capitalize">{invitation.role}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Sent</p>
                          <p className="text-sm text-gray-900 dark:text-white">{formatDate(invitation.sent_at)}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Expires</p>
                          <p className="text-sm text-gray-900 dark:text-white">{formatDate(invitation.expires_at)}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 mb-4">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">Method:</span> {invitation.invitation_method}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">Permissions:</span> {getPermissionCount(invitation)} enabled
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">Invited by:</span> {invitation.invited_by_user.full_name || invitation.invited_by_user.email}
                        </div>
                      </div>

                      {invitation.personal_message && (
                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Personal Message</p>
                          <p className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            "{invitation.personal_message}"
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => setSelectedInvitation(selectedInvitation?.id === invitation.id ? null : invitation)}
                        className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        title="View Details"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>

                      {invitation.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleResendInvitation(invitation.id)}
                            disabled={actionLoading === invitation.id}
                            className="p-2 text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors disabled:opacity-50"
                            title="Resend Invitation"
                          >
                            {actionLoading === invitation.id ? (
                              <div className="w-4 h-4 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin" />
                            ) : (
                              <ArrowPathIcon className="w-4 h-4" />
                            )}
                          </button>
                          
                          <button
                            onClick={() => handleCancelInvitation(invitation.id)}
                            disabled={actionLoading === invitation.id}
                            className="p-2 text-red-400 hover:text-red-600 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors disabled:opacity-50"
                            title="Cancel Invitation"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Expanded Details */}
                  {selectedInvitation?.id === invitation.id && (
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">Permission Details</h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {[
                          { key: 'can_manage_jobs', label: 'Manage Jobs', value: invitation.can_manage_jobs },
                          { key: 'can_manage_clients', label: 'Manage Clients', value: invitation.can_manage_clients },
                          { key: 'can_manage_invoices', label: 'Manage Invoices', value: invitation.can_manage_invoices },
                          { key: 'can_manage_quotes', label: 'Manage Quotes', value: invitation.can_manage_quotes },
                          { key: 'can_view_reports', label: 'View Reports', value: invitation.can_view_reports },
                          { key: 'can_manage_team', label: 'Manage Team', value: invitation.can_manage_team }
                        ].map((permission) => (
                          <div key={permission.key} className="flex items-center space-x-2">
                            <div className={`w-3 h-3 rounded-full ${permission.value ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'}`} />
                            <span className={`text-sm ${permission.value ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}`}>
                              {permission.label}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 p-4">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {filteredInvitations.length} invitation{filteredInvitations.length !== 1 ? 's' : ''} 
              {filter !== 'all' && ` (${filter})`}
            </div>
            <button
              onClick={fetchInvitations}
              disabled={isLoading}
              className="flex items-center px-4 py-2 text-sm font-medium text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-lg transition-colors disabled:opacity-50"
            >
              <ArrowPathIcon className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}