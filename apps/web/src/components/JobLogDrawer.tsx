'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useJob<PERSON>og, JobLogEntry } from '../hooks/useJobLog';
import { Drawer } from '@deskbelt/ui';
import { 
  XMarkIcon,
  ClockIcon,
  UserIcon,
  CalendarIcon,
  DocumentTextIcon,
  CurrencyPoundIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';

interface JobLogDrawerProps {
  jobId: string | null;
  jobTitle?: string;
  onClose: () => void;
  readOnly?: boolean; // For archived jobs
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  } else {
    return date.toLocaleDateString('en-GB', { 
      day: '2-digit', 
      month: 'short', 
      year: 'numeric'
    });
  }
};

const getLogEntryIcon = (type: string, action: string) => {
  if (type === 'system') {
    if (action.includes('📧') || action.includes('Email sent')) {
      return <EnvelopeIcon className="w-4 h-4" />;
    } else if (action.includes('assigned') || action.includes('user')) {
      return <UserIcon className="w-4 h-4" />;
    } else if (action.includes('scheduled') || action.includes('date')) {
      return <CalendarIcon className="w-4 h-4" />;
    } else if (action.includes('quote') || action.includes('invoice') || action.includes('contract')) {
      return <DocumentTextIcon className="w-4 h-4" />;
    } else if (action.includes('payment') || action.includes('cost')) {
      return <CurrencyPoundIcon className="w-4 h-4" />;
    } else if (action.includes('completed') || action.includes('finished')) {
      return <CheckCircleIcon className="w-4 h-4" />;
    } else if (action.includes('issue') || action.includes('problem')) {
      return <ExclamationTriangleIcon className="w-4 h-4" />;
    } else {
      return <InformationCircleIcon className="w-4 h-4" />;
    }
  } else {
    return <UserIcon className="w-4 h-4" />;
  }
};

const getLogEntryColor = (type: string, action: string) => {
  if (type === 'system') {
    if (action.includes('📧') || action.includes('Email sent')) {
      return 'text-orange-600 dark:text-orange-400';
    } else if (action.includes('completed') || action.includes('finished')) {
      return 'text-green-600 dark:text-green-400';
    } else if (action.includes('issue') || action.includes('problem')) {
      return 'text-red-600 dark:text-red-400';
    } else if (action.includes('quote') || action.includes('invoice')) {
      return 'text-blue-600 dark:text-blue-400';
    } else {
      return 'text-gray-600 dark:text-gray-400';
    }
  } else {
    return 'text-blue-600 dark:text-blue-400';
  }
};

export const JobLogDrawer: React.FC<JobLogDrawerProps> = ({ 
  jobId, 
  jobTitle,
  onClose,
  readOnly = false
}) => {
  const [newLogEntry, setNewLogEntry] = useState('');
  const { data: logEntries, isLoading, error, addLogEntry } = useJobLog(jobId);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Auto scroll to bottom when new entries are added
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [logEntries]);

  const handleAddLogEntry = async () => {
    if (!newLogEntry.trim() || !jobId) return;
    
    try {
      await addLogEntry({
        type: 'user',
        action: newLogEntry.trim(),
        jobId
      });
      setNewLogEntry('');
    } catch (error) {
      console.error('Failed to add log entry:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddLogEntry();
    }
  };

  if (!jobId) return null;

  return (
    <Drawer
      isOpen={!!jobId}
      onClose={onClose}
      side="right"
      size="sm"
      showCloseButton={false}
      className="top-0 h-full w-3/4 sm:w-80"
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                Job Log: {jobTitle || 'Job Details'}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Track all updates and actions for this job
              </p>
            </div>
            <button
              onClick={onClose}
              className="ml-3 p-1.5 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex-shrink-0"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto" ref={scrollRef}>
          {isLoading && (
            <div className="p-4 space-y-4">
              <div className="animate-pulse space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex space-x-3">
                    <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {error && (
            <div className="p-4">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="w-5 h-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-400">
                      Failed to load job log
                    </h3>
                    <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {!isLoading && !error && (
            <div className="p-4 space-y-4">
              {logEntries && logEntries.length > 0 ? (
                logEntries.map((entry) => (
                  <div key={entry.id} className="flex space-x-3">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      entry.type === 'user' 
                        ? 'bg-blue-100 dark:bg-blue-900/20' 
                        : 'bg-gray-100 dark:bg-gray-700'
                    }`}>
                      <div className={getLogEntryColor(entry.type, entry.action)}>
                        {getLogEntryIcon(entry.type, entry.action)}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className={`inline-block px-3 py-2 rounded-lg max-w-full ${
                        entry.type === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                      }`}>
                        <p className="text-sm break-words">{entry.action}</p>
                        {entry.details && (
                          <p className="text-xs mt-1 opacity-80">{entry.details}</p>
                        )}
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDate(entry.createdAt)}
                        </p>
                        {entry.userName && (
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {entry.userName}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <ClockIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No activity yet
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    Job updates and actions will appear here.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Add Note Input (hidden for read-only archived jobs) */}
        {!readOnly && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="space-y-2">
              <textarea
                value={newLogEntry}
                onChange={(e) => setNewLogEntry(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Add a note about this job..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                rows={3}
              />
              <div className="flex items-center justify-between">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Press Enter to send, Shift+Enter for new line
                </p>
                <button
                  onClick={handleAddLogEntry}
                  disabled={!newLogEntry.trim()}
                  className="px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Send
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Drawer>
  );
}; 