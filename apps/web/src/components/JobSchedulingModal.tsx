import React, { useState, useCallback, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Job } from '@/types/Job';
import { 
  CalendarIcon, 
  ClockIcon, 
  XMarkIcon,
  ChevronLeftIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import ModernDatePicker from '@/components/ModernDatePicker';
import ModernTimePicker from '@/components/ModernTimePicker';

interface JobSchedulingModalProps {
  job: Job;
  isOpen: boolean;
  onClose: () => void;
  onScheduleUpdate: (jobId: string, schedule: {
    date: string;
    startTime?: string | null;
    endTime?: string | null;
    duration?: string | null;
    notes?: string | null;
  }) => void;
}

type SchedulingStep = 'date' | 'time' | 'confirm';

const JobSchedulingModal: React.FC<JobSchedulingModalProps> = ({
  job,
  isOpen,
  onClose,
  onScheduleUpdate
}) => {
  const [currentStep, setCurrentStep] = useState<SchedulingStep>('date');
  const [selectedDate, setSelectedDate] = useState<string>(
    job.scheduled_at ? new Date(job.scheduled_at).toISOString().split('T')[0] : ''
  );
  const [selectedTime, setSelectedTime] = useState<string>(
    job.scheduled_start_time || ''
  );
  const [notes, setNotes] = useState<string>(job.scheduling_notes || '');
  
  const modalRef = useRef<HTMLDivElement>(null);
  const firstFocusableRef = useRef<HTMLButtonElement>(null);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setCurrentStep('date');
      setSelectedDate(job.scheduled_at ? new Date(job.scheduled_at).toISOString().split('T')[0] : '');
      setSelectedTime(job.scheduled_start_time || '');
      setNotes(job.scheduling_notes || '');
    }
  }, [isOpen, job]);

  // Focus management
  useEffect(() => {
    if (isOpen && firstFocusableRef.current) {
      firstFocusableRef.current.focus();
    }
  }, [isOpen, currentStep]);

  // Keyboard handling
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  const handleDateSelect = useCallback((date: string) => {
    setSelectedDate(date);
    // Auto-advance to time selection if date is set
    setCurrentStep('time');
  }, []);

  const handleTimeSelect = useCallback((time: string) => {
    setSelectedTime(time);
    // Auto-advance to confirm step
    setCurrentStep('confirm');
  }, []);

  const handleSkipTime = useCallback(() => {
    setSelectedTime('');
    setCurrentStep('confirm');
  }, []);

  const handleConfirm = useCallback(() => {
    if (!selectedDate) return;
    
    onScheduleUpdate(job.id, {
      date: selectedDate,
      startTime: selectedTime || null,
      endTime: null,
      duration: null,
      notes: notes || null
    });
    
    onClose();
  }, [job.id, selectedDate, selectedTime, notes, onScheduleUpdate, onClose]);

  const handleBack = useCallback(() => {
    if (currentStep === 'time') {
      setCurrentStep('date');
    } else if (currentStep === 'confirm') {
      setCurrentStep(selectedTime ? 'time' : 'date');
    }
  }, [currentStep, selectedTime]);

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-GB', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '';
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/20 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div 
        ref={modalRef}
        className="relative bg-white dark:bg-secondary-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 max-h-[90vh] overflow-hidden"
        role="dialog"
        aria-modal="true"
        aria-labelledby="scheduling-title"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-secondary-200 dark:border-secondary-700">
          <div className="flex items-center space-x-3">
            {currentStep !== 'date' && (
              <button
                onClick={handleBack}
                className="p-1 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 transition-colors"
                aria-label="Go back"
              >
                <ChevronLeftIcon className="w-5 h-5 text-secondary-600 dark:text-secondary-400" />
              </button>
            )}
            <div>
              <h3 id="scheduling-title" className="text-lg font-semibold text-secondary-900 dark:text-secondary-100">
                Schedule Job
              </h3>
              <p className="text-sm text-secondary-600 dark:text-secondary-400">
                {job.title}
              </p>
            </div>
          </div>
          
          <button
            ref={firstFocusableRef}
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 transition-colors"
            aria-label="Close scheduling"
          >
            <XMarkIcon className="w-5 h-5 text-secondary-500 dark:text-secondary-400" />
          </button>
        </div>

        {/* Progress Indicator */}
        <div className="px-6 py-4 border-b border-secondary-100 dark:border-secondary-700">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 ${
              currentStep === 'date' ? 'text-primary-600 dark:text-primary-400' : 
              selectedDate ? 'text-success-600 dark:text-success-400' : 'text-secondary-400'
            }`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                selectedDate ? 'bg-success-100 dark:bg-success-900' : 
                currentStep === 'date' ? 'bg-primary-100 dark:bg-primary-900' : 'bg-secondary-100 dark:bg-secondary-800'
              }`}>
                {selectedDate ? <CheckIcon className="w-4 h-4" /> : <CalendarIcon className="w-4 h-4" />}
              </div>
              <span className="text-sm font-medium">Date</span>
            </div>

            <div className={`h-px flex-1 ${selectedDate ? 'bg-success-300' : 'bg-secondary-200 dark:bg-secondary-700'}`} />

            <div className={`flex items-center space-x-2 ${
              currentStep === 'time' ? 'text-primary-600 dark:text-primary-400' : 
              selectedTime ? 'text-success-600 dark:text-success-400' : 'text-secondary-400'
            }`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                selectedTime ? 'bg-success-100 dark:bg-success-900' : 
                currentStep === 'time' ? 'bg-primary-100 dark:bg-primary-900' : 'bg-secondary-100 dark:bg-secondary-800'
              }`}>
                {selectedTime ? <CheckIcon className="w-4 h-4" /> : <ClockIcon className="w-4 h-4" />}
              </div>
              <span className="text-sm font-medium">Time</span>
            </div>

            <div className={`h-px flex-1 ${selectedTime && selectedDate ? 'bg-success-300' : 'bg-secondary-200 dark:bg-secondary-700'}`} />

            <div className={`flex items-center space-x-2 ${
              currentStep === 'confirm' ? 'text-primary-600 dark:text-primary-400' : 'text-secondary-400'
            }`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === 'confirm' ? 'bg-primary-100 dark:bg-primary-900' : 'bg-secondary-100 dark:bg-secondary-800'
              }`}>
                <CheckIcon className="w-4 h-4" />
              </div>
              <span className="text-sm font-medium">Confirm</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {currentStep === 'date' && (
            <div>
              <p className="text-sm text-secondary-600 dark:text-secondary-400 mb-4">
                When would you like to schedule this job?
              </p>
              <ModernDatePicker
                selectedDate={selectedDate}
                onDateSelect={handleDateSelect}
                onClose={() => {}}
                compact={false}
                minDate={new Date().toISOString().split('T')[0]}
              />
            </div>
          )}

          {currentStep === 'time' && (
            <div>
              <p className="text-sm text-secondary-600 dark:text-secondary-400 mb-4">
                What time works best? (Optional)
              </p>
              <ModernTimePicker
                selectedTime={selectedTime}
                onTimeSelect={handleTimeSelect}
                onClose={() => {}}
                compact={false}
                format24Hour={false}
              />
              <div className="mt-4 pt-4 border-t border-secondary-200 dark:border-secondary-700">
                <button
                  onClick={handleSkipTime}
                  className="w-full text-center text-sm text-secondary-600 dark:text-secondary-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                >
                  Skip time selection
                </button>
              </div>
            </div>
          )}

          {currentStep === 'confirm' && (
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium text-secondary-900 dark:text-secondary-100 mb-3">
                  Scheduling Summary
                </h4>
                
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 p-3 bg-secondary-50 dark:bg-secondary-800 rounded-lg">
                    <CalendarIcon className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                    <div>
                      <p className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                        {formatDate(selectedDate)}
                      </p>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400">
                        Date selected
                      </p>
                    </div>
                  </div>

                  {selectedTime && (
                    <div className="flex items-center space-x-3 p-3 bg-secondary-50 dark:bg-secondary-800 rounded-lg">
                      <ClockIcon className="w-5 h-5 text-success-600 dark:text-success-400" />
                      <div>
                        <p className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                          {formatTime(selectedTime)}
                        </p>
                        <p className="text-xs text-secondary-500 dark:text-secondary-400">
                          Start time
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                  Additional Notes (Optional)
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Any special instructions or notes for this appointment..."
                  rows={3}
                  className="w-full px-3 py-2 border border-secondary-300 dark:border-secondary-600 rounded-lg bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100 placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                />
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-secondary-200 dark:border-secondary-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:text-secondary-900 dark:hover:text-secondary-100 transition-colors"
          >
            Cancel
          </button>

          <div className="flex items-center space-x-3">
            {currentStep === 'confirm' && (
              <button
                onClick={handleConfirm}
                disabled={!selectedDate}
                className="px-6 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 disabled:bg-secondary-400 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                Schedule Job
              </button>
            )}
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default JobSchedulingModal;