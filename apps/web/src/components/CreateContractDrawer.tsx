'use client';

import React, { useState, useEffect } from 'react';
import { JobDetails } from '@/types/Job';
import { CreateContractData } from '@/types/Document';
import { useJobQuotes } from '@/hooks/useJobQuotes';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { Textarea } from '@deskbelt/ui';
import { ContractPreviewModal } from './ContractPreviewModal';
import { 
  XMarkIcon,
  DocumentIcon,
  SparklesIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  EyeIcon,
  PencilIcon
} from '@heroicons/react/24/outline';

interface CreateContractDrawerProps {
  job: JobDetails | null;
  isOpen: boolean;
  onClose: () => void;
  onContractCreated?: (contract: CreateContractData) => void;
  onContractSaved?: () => void;
  editingContract?: any; // Contract object when editing existing contract
}

type DrawerStep = 'input' | 'processing' | 'success' | 'error';

const DEFAULT_TERMS = `TERMS AND CONDITIONS

1. PAYMENT TERMS
Payment due within 30 days of invoice date. Late payment may incur charges as per statutory rights.

2. VARIATIONS
Any changes to the agreed work must be confirmed in writing and may affect the price and completion date.

3. LIABILITY
Our liability is limited to the value of the contract. We maintain appropriate public liability insurance.

4. CANCELLATION
Either party may cancel with 48 hours written notice. Payment due for work completed.

5. HEALTH & SAFETY
All work carried out in accordance with current health and safety regulations.

6. MATERIALS
All materials will be of good quality and fit for purpose. Manufacturer warranties apply.

7. COMPLETION
Work to be completed within agreed timeframe, subject to weather and unforeseen circumstances.`;

export const CreateContractDrawer: React.FC<CreateContractDrawerProps> = ({
  job,
  isOpen,
  onClose,
  onContractCreated,
  onContractSaved,
  editingContract
}) => {
  const { authenticatedPost, authenticatedFetch } = useAuthenticatedFetch();
  const [step, setStep] = useState<DrawerStep>('input');
  const [contractTerms, setContractTerms] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isProcessingAI, setIsProcessingAI] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Get real quotes data for context
  const { quotes } = useJobQuotes(job?.id || null);
  const acceptedQuotes = quotes.filter(quote => quote.status === 'accepted');

  // Auto-populate from job description when drawer opens
  useEffect(() => {
    if (isOpen && job && job.description && !editingContract && !contractTerms) {
      generateContractTerms(job.description, acceptedQuotes);
    }
  }, [isOpen, job?.description, editingContract, acceptedQuotes]);

  // Populate form when editing existing contract
  useEffect(() => {
    if (isOpen && editingContract) {
      setContractTerms(editingContract.terms || DEFAULT_TERMS);
    }
  }, [isOpen, editingContract]);

  // Reset state when drawer opens/closes
  useEffect(() => {
    if (!isOpen) {
      setStep('input');
      setContractTerms('');
      setIsSubmitting(false);
      setIsProcessingAI(false);
      setError(null);
    }
  }, [isOpen]);

  // Generate contract terms from job description using AI
  const generateContractTerms = async (jobDescription: string, quotes: any[]) => {
    if (!jobDescription.trim()) return;

    setIsProcessingAI(true);
    try {
      const quoteInfo = quotes.length > 0 
        ? `\n\nAccepted Quotes:\n${quotes.map(q => `- ${q.details} (£${q.amount})`).join('\n')}`
        : '';
      
      const response = await authenticatedPost('/api/ai/chat-response', { 
        prompt: `You are Dex, a professional UK tradesperson creating comprehensive contract terms based on a job description and any accepted quotes.

Create detailed contract terms that:
- Are UK-compliant and legally sound
- Include specific scope of work based on the job description
- Reference pricing from quotes if available
- Include standard trade protections
- Use professional language suitable for client contracts
- Cover payment terms, variations, liability, and completion
- Include relevant health & safety and materials clauses
- Are comprehensive but readable

Job Description: "${jobDescription}"${quoteInfo}

Client: ${job?.client.name || 'Client'}
Job Type: ${job?.title || 'Service'}

Create professional contract terms that protect both parties while being fair and clear. Include specific work scope, timeline expectations, and payment structure.

Return only the contract terms text, no additional formatting or explanations.`,
        input: jobDescription
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.message) {
          setContractTerms(result.message);
        } else {
          setContractTerms(DEFAULT_TERMS);
        }
      } else {
        setContractTerms(DEFAULT_TERMS);
      }
    } catch (error) {
      console.error('AI contract generation error:', error);
      setContractTerms(DEFAULT_TERMS);
    } finally {
      setIsProcessingAI(false);
    }
  };

  const handleGenerateWithAI = async () => {
    if (!job?.description) return;
    
    setIsProcessingAI(true);
    try {
      await generateContractTerms(job.description, acceptedQuotes);
    } finally {
      setIsProcessingAI(false);
    }
  };

  const handleSaveContract = async () => {
    if (!contractTerms.trim() || !job) return;

    console.log('💾 Starting contract save for job:', job?.id, job?.title);
    setIsSubmitting(true);
    setError(null);

    try {
      const contractData = {
        terms: contractTerms.trim(),
        status: 'draft'
      };

      const url = editingContract 
        ? `/api/jobs/${job?.id}/contracts/${editingContract.id}`
        : `/api/jobs/${job?.id}/contracts`;
      
      const method = editingContract ? 'PUT' : 'POST';
      
      console.log('🌐 Making API call:', method, url);
      console.log('📦 Contract data:', contractData);

      const response = await authenticatedFetch(url, {
        method,
        body: JSON.stringify(contractData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Contract saved successfully:', result);

        setStep('success');
        onContractCreated?.({
          job_id: job?.id || '',
          terms: contractTerms
        });
        
        // Refresh contracts list immediately
        onContractSaved?.();
        
        // Auto-close after success
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        console.error('Failed to save contract:', response.status, response.statusText);
        setStep('error');
        setError('Failed to save contract. Please try again.');
      }
    } catch (error) {
      console.error('Error saving contract:', error);
      setStep('error');
      setError('Error saving contract. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewContract = () => {
    setShowPreview(true);
  };

  const renderContent = () => {
    switch (step) {
      case 'input':
        return (
          <div className="space-y-6">
            {/* AI Processing Indicator */}
            {isProcessingAI && (
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div className="flex items-center space-x-3">
                  <SparklesIcon className="w-5 h-5 text-blue-600 dark:text-blue-400 animate-pulse" />
                  <div className="text-sm text-blue-800 dark:text-blue-200">
                    <p className="font-medium">Dex is generating contract terms...</p>
                    <p className="text-blue-700 dark:text-blue-300">
                      Creating UK-compliant terms based on your job details
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Contract Terms */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-900 dark:text-white">
                  Contract Terms
                </label>
                <PencilIcon className="w-4 h-4 text-gray-400 dark:text-gray-500" />
              </div>
              <Textarea
                value={contractTerms}
                onChange={(e) => setContractTerms(e.target.value)}
                placeholder="Enter your contract terms and conditions..."
                rows={12}
                maxLength={10000}
                className="w-full font-mono text-sm"
              />
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {contractTerms.length}/10,000 characters
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                onClick={handleGenerateWithAI}
                disabled={!job?.description || isProcessingAI}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                <SparklesIcon className="w-4 h-4 mr-2" />
                {isProcessingAI ? 'Generating...' : 'Improve with Dex'}
              </Button>
              
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={handleViewContract}
                  disabled={!contractTerms.trim()}
                  className="flex-1 border-purple-300 text-purple-700 hover:bg-purple-50 dark:border-purple-600 dark:text-purple-400 dark:hover:bg-purple-900/20"
                >
                  <EyeIcon className="w-4 h-4 mr-2" />
                  Preview
                </Button>
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-800"
                >
                  Cancel
                </Button>
              </div>
              
              <Button
                onClick={handleSaveContract}
                disabled={!contractTerms.trim() || isSubmitting}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckCircleIcon className="w-4 h-4 mr-2" />
                {isSubmitting 
                  ? (editingContract ? 'Updating...' : 'Saving...') 
                  : (editingContract ? 'Update Contract' : 'Save Contract')
                }
              </Button>
            </div>
          </div>
        );

      case 'processing':
        return (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-4">
              <SparklesIcon className="w-8 h-8 text-blue-600 dark:text-blue-400 animate-pulse" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Processing Contract
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Dex is creating your contract terms...
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full mb-4">
              <CheckCircleIcon className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Contract {editingContract ? 'Updated' : 'Created'}!
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Your contract has been {editingContract ? 'updated' : 'created'} successfully and is ready for use.
            </p>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Closing automatically...
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="space-y-6">
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
              <div className="flex items-start space-x-3">
                <ExclamationCircleIcon className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-red-800 dark:text-red-200 mb-1">
                    Error Processing Contract
                  </p>
                  <p className="text-red-700 dark:text-red-300">
                    {error}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setStep('input')}
                className="flex-1"
              >
                Try Again
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <ContractPreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        job={job}
        contractTerms={contractTerms}
      />
      
      <Drawer
        isOpen={isOpen}
        onClose={onClose}
        side="right"
        size="xl"
        showCloseButton={false}
        className="top-16 h-[calc(100%-4rem)] md:left-1/2 md:-translate-x-1/2 md:right-auto"
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <DocumentIcon className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  {editingContract ? 'Edit Contract' : 'Create Contract'}
                </h2>
                {job && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    For "{job.title}" • {job.client.name}
                  </p>
                )}
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <XMarkIcon className="w-5 h-5" />
            </Button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {renderContent()}
          </div>
        </div>
      </Drawer>
    </>
  );
}; 