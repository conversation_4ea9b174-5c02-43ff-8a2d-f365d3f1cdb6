'use client';

import React, { useState, useCallback } from 'react';
import { Invoice } from '@/hooks/useInvoices';
import {
  DocumentTextIcon,
  PencilIcon,
  TrashIcon,
  EllipsisVerticalIcon,
  EyeIcon,
  EnvelopeIcon,
  PrinterIcon,
  DocumentDuplicateIcon,
  ClockIcon,
  CalendarDaysIcon,
  CurrencyPoundIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PaperAirplaneIcon,
  BanknotesIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import {
  ExclamationCircleIcon as ExclamationCircleIconSolid,
  ClockIcon as ClockIconSolid,
  CheckCircleIcon as CheckCircleIconSolid
} from '@heroicons/react/24/solid';

interface InvoiceCardProps {
  invoice: Invoice;
  onView: (invoice: Invoice) => void;
  onEdit: (invoice: Invoice) => void;
  onDelete: (invoice: Invoice) => void;
  onSendEmail?: (invoice: Invoice) => void;
  onPrint?: (invoice: Invoice) => void;
  onDuplicate?: (invoice: Invoice) => void;
  onMarkPaid?: (invoice: Invoice) => void;
}

export const InvoiceCard: React.FC<InvoiceCardProps> = ({
  invoice,
  onView,
  onEdit,
  onDelete,
  onSendEmail,
  onPrint,
  onDuplicate,
  onMarkPaid
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const cardClickBlockedRef = React.useRef(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return `£${amount.toLocaleString('en-GB', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const getInvoiceNumber = () => {
    return `INV-${invoice.id.slice(-6).toUpperCase()}`;
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'paid':
        return {
          label: 'Paid',
          className: 'status-badge-paid',
          icon: <CheckCircleIconSolid className="w-3 h-3" />,
          dotColor: 'bg-green-500'
        };
      case 'sent':
        return {
          label: 'Sent',
          className: 'status-badge-pending',
          icon: <PaperAirplaneIcon className="w-3 h-3" />,
          dotColor: 'bg-blue-500'
        };
      case 'overdue':
        return {
          label: 'Overdue',
          className: 'status-badge-overdue',
          icon: <ExclamationCircleIconSolid className="w-3 h-3" />,
          dotColor: 'bg-red-500'
        };
      case 'draft':
        return {
          label: 'Draft',
          className: 'status-badge-draft',
          icon: <DocumentTextIcon className="w-3 h-3" />,
          dotColor: 'bg-gray-500'
        };
      default:
        return {
          label: 'Unknown',
          className: 'status-badge-neutral',
          icon: <ClockIconSolid className="w-3 h-3" />,
          dotColor: 'bg-gray-400'
        };
    }
  };

  const isDraft = invoice.status === 'draft';
  const isPaid = invoice.status === 'paid';
  const isOverdue = invoice.status === 'overdue';
  const statusConfig = getStatusConfig(invoice.status);
  
  // Calculate days until/since due date
  const dueDate = new Date(invoice.due_date);
  const today = new Date();
  const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 3600 * 24));

  const handleCardClick = (e: React.MouseEvent) => {
    if (cardClickBlockedRef.current) {
      cardClickBlockedRef.current = false;
      return;
    }
    onView(invoice);
  };

  const handleViewClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    onView(invoice);
  }, [invoice, onView]);

  const handleEditClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    onEdit(invoice);
    setIsDropdownOpen(false);
  }, [invoice, onEdit]);

  const handleDeleteClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    onDelete(invoice);
    setIsDropdownOpen(false);
  }, [invoice, onDelete]);

  const handleSendEmailClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    onSendEmail?.(invoice);
    setIsDropdownOpen(false);
  }, [invoice, onSendEmail]);

  const handlePrintClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    onPrint?.(invoice);
    setIsDropdownOpen(false);
  }, [invoice, onPrint]);

  const handleDuplicateClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    onDuplicate?.(invoice);
    setIsDropdownOpen(false);
  }, [invoice, onDuplicate]);

  const handleMarkPaidClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    onMarkPaid?.(invoice);
    setIsDropdownOpen(false);
  }, [invoice, onMarkPaid]);

  return (
    <div
      className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm hover:shadow-md hover:scale-[1.01] cursor-pointer active:scale-[0.99] transform transition-all duration-200 group/card animate-fade-in-up hover:border-orange-200 dark:hover:border-orange-700 h-full relative p-6"
      onClick={handleCardClick}
      role="article"
      aria-label={`Invoice ${getInvoiceNumber()} for ${invoice.client?.name || 'Unknown Client'}`}
    >
      {/* Dropdown Menu - Positioned absolutely in top right */}
      <div className="absolute top-4 right-4 z-10">
        <button
          onClick={(e) => {
            cardClickBlockedRef.current = true;
            e.stopPropagation();
            setIsDropdownOpen(!isDropdownOpen);
          }}
          className="p-1.5 text-secondary-400 hover:text-secondary-600 dark:text-secondary-500 dark:hover:text-secondary-300 transition-colors border-0 bg-transparent focus:outline-none rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-800"
        >
          <EllipsisVerticalIcon className="w-5 h-5" />
        </button>
        
        {isDropdownOpen && (
          <div className="absolute right-0 top-full mt-2 w-56 bg-white dark:bg-secondary-800 rounded-xl shadow-lg border border-secondary-200 dark:border-secondary-700 py-2 z-20 animate-fade-in-up">
            <button
              onClick={handleViewClick}
              className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <EyeIcon className="w-4 h-4 mr-3 text-secondary-500 dark:text-secondary-400" />
              <span className="font-medium">View Invoice</span>
            </button>
            
            <button
              onClick={handleEditClick}
              className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <PencilIcon className="w-4 h-4 mr-3 text-secondary-500 dark:text-secondary-400" />
              <span className="font-medium">Edit Invoice</span>
            </button>

            {!isPaid && onMarkPaid && (
              <button
                onClick={handleMarkPaidClick}
                className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
              >
                <CheckCircleIcon className="w-4 h-4 mr-3 text-green-500 dark:text-green-400" />
                <span className="font-medium">Mark as Paid</span>
              </button>
            )}

            {!isDraft && onSendEmail && (
              <button
                onClick={handleSendEmailClick}
                className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
              >
                <EnvelopeIcon className="w-4 h-4 mr-3 text-blue-500 dark:text-blue-400" />
                <span className="font-medium">Send Email</span>
              </button>
            )}

            {onPrint && (
              <button
                onClick={handlePrintClick}
                className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
              >
                <PrinterIcon className="w-4 h-4 mr-3 text-secondary-500 dark:text-secondary-400" />
                <span className="font-medium">Print Invoice</span>
              </button>
            )}

            {onDuplicate && (
              <button
                onClick={handleDuplicateClick}
                className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
              >
                <DocumentDuplicateIcon className="w-4 h-4 mr-3 text-secondary-500 dark:text-secondary-400" />
                <span className="font-medium">Duplicate Invoice</span>
              </button>
            )}

            <div className="h-px bg-secondary-200 dark:bg-secondary-600 my-2 mx-4" />
            
            <button
              onClick={handleDeleteClick}
              className="w-full px-4 py-3 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <TrashIcon className="w-4 h-4 mr-3 text-red-500 dark:text-red-400" />
              <span className="font-medium">Delete Invoice</span>
            </button>
          </div>
        )}
      </div>

      {/* Header with clean layout */}
      <div className="flex items-start justify-between mb-4 pr-10">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center text-white text-lg font-semibold shadow-md">
            <DocumentTextIcon className="w-6 h-6" />
          </div>
          <div className="flex-1 min-w-0">
            <button
              onClick={handleViewClick}
              className="text-left group/title focus-ring rounded-lg -m-1 p-1"
            >
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50 truncate group-hover/title:text-orange-600 dark:group-hover/title:text-orange-400 transition-colors duration-200 mb-1">
                {getInvoiceNumber()}
              </h3>
            </button>
            <p className="text-sm text-secondary-500 dark:text-secondary-400">
              {formatCurrency(invoice.amount)}
            </p>
            <div className="mt-2">
              <span className={`${statusConfig.className} hover:scale-105 transition-all duration-200`}>
                <span className={`w-2 h-2 rounded-full ${statusConfig.dotColor} mr-2 animate-pulse-soft`}></span>
                {statusConfig.label}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Client Info */}
      <div className="mb-4">
        <div className="group/client focus-ring rounded-lg p-2 hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors duration-200">
          <div className="flex items-center space-x-2">
            <UserIcon className="w-4 h-4 text-secondary-400" />
            <span className="text-sm font-medium text-secondary-700 dark:text-secondary-300 group-hover/client:text-orange-600 dark:group-hover/client:text-orange-400 transition-colors">
              {invoice.client?.name || 'Unknown Client'}
            </span>
          </div>
        </div>
      </div>

      {/* Job Info */}
      <div className="mb-4">
        <div className="group/job focus-ring rounded-lg p-2 hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors duration-200">
          <div className="flex items-center space-x-2">
            <BanknotesIcon className="w-4 h-4 text-secondary-400" />
            <span className="text-sm font-medium text-secondary-700 dark:text-secondary-300 group-hover/job:text-orange-600 dark:group-hover/job:text-orange-400 transition-colors truncate">
              {invoice.job?.title || 'No job title'}
            </span>
          </div>
        </div>
      </div>

      {/* Date Information */}
      <div className="mb-4 space-y-2">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2">
            <CalendarDaysIcon className="w-4 h-4 text-secondary-400" />
            <span className="text-secondary-600 dark:text-secondary-400">
              Issued: {formatDate(invoice.created_at)}
            </span>
          </div>
        </div>
        
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2">
            <ClockIcon className="w-4 h-4 text-secondary-400" />
            <span className="text-secondary-600 dark:text-secondary-400">
              Due: {formatDate(invoice.due_date)}
            </span>
          </div>
          {!isPaid && (
            <div className={`text-xs px-2 py-1 rounded-full ${
              isOverdue 
                ? 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400' 
                : daysDiff <= 3 
                ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
                : 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
            }`}>
              {isOverdue 
                ? `${Math.abs(daysDiff)} days overdue`
                : daysDiff <= 0
                ? 'Due today'
                : `${daysDiff} days left`
              }
            </div>
          )}
        </div>
      </div>

      {/* Action Icons */}
      <div className="flex items-center justify-center space-x-4 pt-4 border-t border-secondary-100 dark:border-secondary-700">
        {/* View Invoice */}
        <button
          onClick={handleViewClick}
          className="p-2 text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20 rounded-lg transition-colors"
          title="View invoice"
          aria-label="View invoice"
        >
          <EyeIcon className="w-5 h-5" />
        </button>

        {/* Edit Invoice */}
        <button
          onClick={handleEditClick}
          className="p-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
          title="Edit invoice"
          aria-label="Edit invoice"
        >
          <PencilIcon className="w-5 h-5" />
        </button>

        {/* Send Email (only if not draft) */}
        {!isDraft && onSendEmail && (
          <button
            onClick={handleSendEmailClick}
            className="p-2 text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-lg transition-colors"
            title="Send email"
            aria-label="Send invoice via email"
          >
            <EnvelopeIcon className="w-5 h-5" />
          </button>
        )}

        {/* Mark as Paid (only if not paid) */}
        {!isPaid && onMarkPaid && (
          <button
            onClick={handleMarkPaidClick}
            className="p-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
            title="Mark as paid"
            aria-label="Mark invoice as paid"
          >
            <CheckCircleIcon className="w-5 h-5" />
          </button>
        )}

        {/* Print Invoice */}
        {onPrint && (
          <button
            onClick={handlePrintClick}
            className="p-2 text-secondary-600 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-900/20 rounded-lg transition-colors"
            title="Print invoice"
            aria-label="Print invoice"
          >
            <PrinterIcon className="w-5 h-5" />
          </button>
        )}
      </div>

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default InvoiceCard;