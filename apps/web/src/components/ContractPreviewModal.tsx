'use client';

import React from 'react';
import { JobDetails } from '@/types/Job';
import { useProfile } from '@/hooks/useProfile';
import { Modal } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
//import { Tabs, TabItem } from '@deskbelt/ui';
import { 
  XMarkIcon,
  DocumentIcon,
  PrinterIcon,
  ShareIcon
} from '@heroicons/react/24/outline';

interface ContractPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: JobDetails | null;
  contractTerms: string;
}

export const ContractPreviewModal: React.FC<ContractPreviewModalProps> = ({
  isOpen,
  onClose,
  job,
  contractTerms
}) => {
  const { data: profile } = useProfile();

  // Parse UK address to extract postcode and separate street/city
  const parseUKAddress = (address: string) => {
    if (!address) return { street: '', city: '', postcode: '' };
    
    // UK postcode regex pattern
    const postcodeRegex = /([A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2})$/i;
    const postcodeMatch = address.match(postcodeRegex);
    
    if (postcodeMatch) {
      const postcode = postcodeMatch[1];
      const addressWithoutPostcode = address.replace(postcodeRegex, '').trim();
      
      // Split remaining address - last part is likely city
      const parts = addressWithoutPostcode.split(',').map(p => p.trim()).filter(p => p);
      const city = parts.length > 1 ? parts.pop() : '';
      const street = parts.join(', ');
      
      return { street, city, postcode };
    }
    
    // Fallback if no postcode found
    const parts = address.split(',').map(p => p.trim()).filter(p => p);
    return {
      street: parts.slice(0, -1).join(', '),
      city: parts[parts.length - 1] || '',
      postcode: ''
    };
  };

  const businessAddress = parseUKAddress(profile?.address || '');

  const handlePrint = () => {
    window.print();
  };

  const handleShare = () => {
    // TODO: Implement sharing functionality
    console.log('Share contract');
  };

  if (!job) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <DocumentIcon className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Contract Preview
            </h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <PrinterIcon className="w-4 h-4 mr-2" />
              Print
            </Button>
            <Button variant="outline" size="sm" onClick={handleShare}>
              <ShareIcon className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <XMarkIcon className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Contract Content */}
        <div className="p-8 overflow-y-auto max-h-[calc(90vh-80px)] bg-white dark:bg-gray-900">
          <div className="max-w-4xl mx-auto bg-white dark:bg-gray-900 print:shadow-none">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                SERVICE AGREEMENT
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Contract Date: {new Date().toLocaleDateString('en-GB')}
              </p>
            </div>

            {/* Parties */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              {/* Contractor Details */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  CONTRACTOR
                </h3>
                <div className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                  <p className="font-medium">{profile?.company_name || profile?.full_name || 'Your Business Name'}</p>
                  {businessAddress.street && <p>{businessAddress.street}</p>}
                  {businessAddress.city && <p>{businessAddress.city}</p>}
                  {businessAddress.postcode && <p>{businessAddress.postcode}</p>}
                  {profile?.phone && <p>Tel: {profile.phone}</p>}
                  {profile?.email && <p>Email: {profile.email}</p>}
                  {profile?.website && <p>Web: {profile.website}</p>}
                  {profile?.vat_number && <p>VAT: {profile.vat_number}</p>}
                </div>
              </div>

              {/* Client Details */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  CLIENT
                </h3>
                <div className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                  <p className="font-medium">{job.client.name}</p>
                  {/* Note: business_name and address not available in basic client object */}
                  {job.client.phone && <p>Tel: {job.client.phone}</p>}
                  {job.client.email && <p>Email: {job.client.email}</p>}
                </div>
              </div>
            </div>

            {/* Job Details */}
            <div className="mb-8">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                PROJECT DETAILS
              </h3>
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                <p className="font-medium text-gray-900 dark:text-white mb-2">{job.title}</p>
                {job.description && (
                  <p className="text-sm text-gray-700 dark:text-gray-300">{job.description}</p>
                )}
                {/* Note: address not available in basic job object */}
              </div>
            </div>

            {/* Contract Terms */}
            <div className="mb-8">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                TERMS AND CONDITIONS
              </h3>
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <div className="whitespace-pre-wrap text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                  {contractTerms}
                </div>
              </div>
            </div>

            {/* Signatures */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
                  CONTRACTOR SIGNATURE
                </h4>
                <div className="border-b border-gray-300 dark:border-gray-600 mb-2 h-12"></div>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {profile?.full_name || 'Contractor Name'}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Date: _______________
                </p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
                  CLIENT SIGNATURE
                </h4>
                <div className="border-b border-gray-300 dark:border-gray-600 mb-2 h-12"></div>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {job.client.name}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Date: _______________
                </p>
              </div>
            </div>

            {/* Footer */}
            <div className="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700 text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                This agreement is governed by the laws of England and Wales
              </p>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
}; 