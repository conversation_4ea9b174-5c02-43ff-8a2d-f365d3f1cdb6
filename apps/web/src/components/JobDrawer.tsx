'use client';

import React from 'react';
import { Button } from '@deskbelt/ui';
import { Drawer } from '@deskbelt/ui';
import { useJobDetails } from '@/hooks/useJobDetails';
import { useJobQuotes } from '@/hooks/useJobQuotes';
import { useJobInvoices } from '@/hooks/useJobInvoices';
import { useJobContracts } from '@/hooks/useJobContracts';
import { 
  DocumentTextIcon, 
  BanknotesIcon, 
  DocumentIcon,
  CheckCircleIcon,
  UserGroupIcon,
  ArchiveBoxIcon,
  TrashIcon,
  PhoneIcon,
  ChatBubbleLeftIcon,
  EnvelopeIcon,
  UserCircleIcon,
  XMarkIcon,
  DocumentDuplicateIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { CreateQuoteDrawer } from './CreateQuoteDrawer';
import { CreateInvoiceDrawer } from './CreateInvoiceDrawer';
import { CreateContractDrawer } from './CreateContractDrawer';
import { QuotePreviewModal } from './QuotePreviewModal';
import { InvoicePreviewModal } from './InvoicePreviewModal';
import { ContractPreviewModal } from './ContractPreviewModal';
import { Input } from '@deskbelt/ui';
import { Textarea } from '@deskbelt/ui';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useDrawer } from '@/contexts/DrawerContext';

interface JobDrawerProps {
  jobId: string | null;
  onClose: () => void;
  onClientClick?: (clientId: string) => void;
  onJobUpdated?: () => void;
}

export function JobDrawer({ jobId, onClose, onClientClick, onJobUpdated }: JobDrawerProps) {
  const { data: job, isLoading } = useJobDetails(jobId);
  const { draftQuotes, isLoading: quotesLoading, refetch: refetchQuotes } = useJobQuotes(jobId);
  const { invoices, draftInvoices, isLoading: invoicesLoading, refetch: refetchInvoices } = useJobInvoices(jobId);
  const { contracts, loading: contractsLoading, refetch: refetchContracts } = useJobContracts(jobId);
  const { authenticatedFetch } = useAuthenticatedFetch();
  const { openJobDetailsDrawer } = useDrawer();
  
  // Debug logging
  console.log('🏠 JobDrawer - Job ID:', jobId);
  console.log('📋 JobDrawer - All invoices:', invoices);
  console.log('📝 JobDrawer - Draft invoices:', draftInvoices);

  // New: local state to control document drawers
  const [isQuoteDrawerOpen, setQuoteDrawerOpen] = React.useState(false);
  const [isInvoiceDrawerOpen, setInvoiceDrawerOpen] = React.useState(false);
  const [isContractDrawerOpen, setContractDrawerOpen] = React.useState(false);
  
  // Quote editing and preview state
  const [editingQuote, setEditingQuote] = React.useState<any>(null);
  const [isQuotePreviewOpen, setQuotePreviewOpen] = React.useState(false);
  const [previewQuote, setPreviewQuote] = React.useState<any>(null);
  
  // Invoice editing and preview state
  const [editingInvoice, setEditingInvoice] = React.useState<any>(null);
  const [isInvoicePreviewOpen, setInvoicePreviewOpen] = React.useState(false);
  const [previewInvoice, setPreviewInvoice] = React.useState<any>(null);
  
  // Contract editing and preview state
  const [editingContract, setEditingContract] = React.useState<any>(null);
  const [isContractPreviewOpen, setContractPreviewOpen] = React.useState(false);
  const [previewContract, setPreviewContract] = React.useState<any>(null);

  if (!jobId) return null;

  const handleCreateQuote = () => {
    if (!job) return;
    setEditingQuote(null); // Clear any existing quote being edited
    setQuoteDrawerOpen(true);
  };

  const handleEditQuote = (quote: any) => {
    if (!job) return;
    setEditingQuote(quote);
    setQuoteDrawerOpen(true);
  };

  const handleViewQuote = (quote: any) => {
    if (!job) return;
    setPreviewQuote(quote);
    setQuotePreviewOpen(true);
  };

  const handleDeleteQuote = async (quote: any) => {
    if (!job) return;
    
    // Confirm deletion
    if (!confirm('Are you sure you want to delete this quote? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await authenticatedFetch(getApiUrl(`/api/jobs/${job.id}/quotes/${quote.id}`), {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log('Quote deleted:', quote.id);
        refetchQuotes(); // Refresh quotes list
        // TODO: Show success toast
      } else {
        console.error('Failed to delete quote');
        // TODO: Show error toast
      }
    } catch (error) {
      console.error('Error deleting quote:', error);
      // TODO: Show error toast
    }
  };

  const handleCreateInvoice = () => {
    if (!job) return;
    setEditingInvoice(null); // Clear any existing invoice being edited
    setInvoiceDrawerOpen(true);
  };

  const handleEditInvoice = (invoice: any) => {
    if (!job) return;
    setEditingInvoice(invoice);
    setInvoiceDrawerOpen(true);
  };

  const handleViewInvoice = (invoice: any) => {
    if (!job) return;
    setPreviewInvoice(invoice);
    setInvoicePreviewOpen(true);
  };

  const handleDeleteInvoice = async (invoice: any) => {
    if (!job) return;
    
    // Confirm deletion
    if (!confirm('Are you sure you want to delete this invoice? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await authenticatedFetch(getApiUrl(`/api/jobs/${job.id}/invoices/${invoice.id}`), {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log('Invoice deleted:', invoice.id);
        refetchInvoices(); // Refresh invoices list
        // TODO: Show success toast
      } else {
        console.error('Failed to delete invoice');
        // TODO: Show error toast
      }
    } catch (error) {
      console.error('Error deleting invoice:', error);
      // TODO: Show error toast
    }
  };

  const handleCreateContract = () => {
    if (!job) return;
    setEditingContract(null); // Clear any existing contract being edited
    setContractDrawerOpen(true);
  };

  const handleEditContract = (contract: any) => {
    if (!job) return;
    setEditingContract(contract);
    setContractDrawerOpen(true);
  };

  const handleViewContract = (contract: any) => {
    if (!job) return;
    setPreviewContract(contract);
    setContractPreviewOpen(true);
  };

  const handleDeleteContract = async (contract: any) => {
    if (!job) return;
    
    // Confirm deletion
    if (!confirm('Are you sure you want to delete this contract? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await authenticatedFetch(getApiUrl(`/api/jobs/${job.id}/contracts/${contract.id}`), {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log('Contract deleted:', contract.id);
        refetchContracts(); // Refresh contracts list
        // TODO: Show success toast
      } else {
        console.error('Failed to delete contract');
        // TODO: Show error toast
      }
    } catch (error) {
      console.error('Error deleting contract:', error);
      // TODO: Show error toast
    }
  };

  const handleMarkCompleted = () => {
    if (!job) return;
    console.log('Marking job as completed:', job.id);
    // TODO: Update job status
  };

  const handleAssignTeamMember = () => {
    if (!job) return;
    console.log('Assigning team member to job:', job.id);
    // TODO: Open team member assignment drawer/modal
  };

  const handleArchiveJob = async () => {
    if (!job) return;
    
    // Confirm archiving
    if (!confirm('Are you sure you want to archive this job? Archived jobs can be found in the archived dashboard and can be unarchived later.')) {
      return;
    }

    try {
      const response = await authenticatedFetch(getApiUrl(`/api/jobs/${job.id}`), {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'archived'
        }),
      });

      if (response.ok) {
        console.log('Job archived successfully:', job.id);
        // Close the drawer after successful archiving
        onClose();
        // Refresh the jobs dashboard
        if (onJobUpdated) {
          onJobUpdated();
        }
        // TODO: Show success toast
      } else {
        console.error('Failed to archive job');
        // TODO: Show error toast
      }
    } catch (error) {
      console.error('Error archiving job:', error);
      // TODO: Show error toast
    }
  };

  const handleDeleteJob = () => {
    if (!job) return;
    console.log('Deleting job:', job.id);
    // TODO: Show confirmation modal and delete job
  };

  const handleCall = () => {
    if (!job) return;
    console.log('Calling client for job:', job.id);
    // TODO: Initiate phone call
  };

  const handleMessage = () => {
    if (!job) return;
    console.log('Messaging client for job:', job.id);
    // TODO: Open messaging interface
  };

  const handleEmail = () => {
    if (!job) return;
    console.log('Emailing client for job:', job.id);
    // TODO: Open email interface
  };

  const handleProfile = () => {
    if (!job) return;
    console.log('Viewing client profile for job:', job.id);
    // TODO: Open client profile
  };

  const handleSave = () => {
    if (!job) return;
    console.log('Saving client:', job.id);
    // TODO: Implement save logic
  };

  const handleViewMoreDetails = () => {
    if (!job) return;
    openJobDetailsDrawer(job);
  };

  const isValid = true; // Replace with actual validation logic

  return (
    <>
    <Drawer
      isOpen={!!jobId}
      onClose={onClose}
      side="right"
      size="sm"
      closeOnOverlayClick={true}
      closeOnEscape={true}
      showCloseButton={true}
      title={isLoading ? 'Loading...' : job?.title || 'Job Details'}
    >

        {/* Client Info */}
        {job && (
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
            <p className="text-sm text-gray-600 dark:text-gray-300">
              <span className="font-medium">Client:</span> {job.client.name}
            </p>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          {isLoading && (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Loading job details...</p>
            </div>
          )}
          
          {!isLoading && !job && (
            <div className="p-4 text-center">
              <p className="text-sm text-red-500">Job not found</p>
            </div>
          )}
          
          {job && (
            <>
              {/* Job Details Section */}
              <div className="px-4 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
                  Job Details
                </h3>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Job Number:</span>
                    <span className="text-gray-600 dark:text-gray-400 ml-2">#{job.id.slice(-6).toUpperCase()}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Scheduled:</span>
                    <span className="text-gray-600 dark:text-gray-400 ml-2">
                      {job.scheduled_at 
                        ? new Date(job.scheduled_at).toLocaleString('en-GB', { 
                            day: '2-digit', 
                            month: 'short', 
                            year: 'numeric', 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          }) 
                        : 'Not scheduled'}
                    </span>
                  </div>
                  {job.description && (
                    <div>
                      <span className="font-medium text-gray-700 dark:text-gray-300">Description:</span>
                      <p className="text-gray-600 dark:text-gray-400 whitespace-pre-wrap mt-1">
                        {job.description}
                      </p>
                    </div>
                  )}
                </div>
                
                {/* View More Details Button */}
                <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={handleViewMoreDetails}
                    className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200 border border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700"
                  >
                    <EyeIcon className="w-4 h-4 mr-2" />
                    View More Details
                  </button>
                </div>
              </div>

          {/* CREATE DOCUMENTS Section */}
          <div className="px-4 py-4">
            <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
              CREATE DOCUMENTS
            </h3>
            <div className="space-y-1">
              <button
                className="w-full flex items-center px-0 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors"
                onClick={handleCreateQuote}
              >
                <DocumentTextIcon className="w-4 h-4 mr-3 text-gray-500 dark:text-gray-400" />
                Create Quote
              </button>
              <button
                className="w-full flex items-center px-0 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors"
                onClick={handleCreateInvoice}
              >
                <BanknotesIcon className="w-4 h-4 mr-3 text-gray-500 dark:text-gray-400" />
                Create Invoice
              </button>
              <button
                className="w-full flex items-center px-0 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors"
                onClick={handleCreateContract}
              >
                <DocumentIcon className="w-4 h-4 mr-3 text-gray-500 dark:text-gray-400" />
                Create Contract
              </button>
            </div>
          </div>

          {/* DRAFT QUOTES Section */}
          {draftQuotes.length > 0 && (
            <div className="px-4 py-4">
              <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
                Draft Quotes ({draftQuotes.length})
              </h3>
              <div className="space-y-2">
                {draftQuotes.map((quote) => (
                  <div
                    key={quote.id}
                    className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          Draft Quote
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Created {new Date(quote.created_at).toLocaleDateString('en-GB')}
                        </p>
                      </div>
                      {quote.amount > 0 && (
                        <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                          £{quote.amount.toFixed(2)}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                      {quote.details}
                    </p>
                    <div className="flex space-x-2">
                      <button
                        className="flex-1 flex items-center justify-center px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors"
                        onClick={() => handleEditQuote(quote)}
                      >
                        <DocumentDuplicateIcon className="w-3 h-3 mr-1" />
                        Edit
                      </button>
                      <button
                        className="flex-1 flex items-center justify-center px-2 py-1 text-xs text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded transition-colors"
                        onClick={() => handleViewQuote(quote)}
                      >
                        <EyeIcon className="w-3 h-3 mr-1" />
                        View
                      </button>
                      <button
                        className="flex-1 flex items-center justify-center px-2 py-1 text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                        onClick={() => handleDeleteQuote(quote)}
                      >
                        <TrashIcon className="w-3 h-3 mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* DRAFT INVOICES Section */}
          {draftInvoices.length > 0 && (
            <div className="px-4 py-4">
              <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
                Draft Invoices ({draftInvoices.length})
              </h3>
              <div className="space-y-2">
                {draftInvoices.map((invoice) => (
                  <div
                    key={invoice.id}
                    className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          Draft Invoice
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Created {new Date(invoice.created_at).toLocaleDateString('en-GB')}
                        </p>
                      </div>
                      {invoice.amount > 0 && (
                        <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                          £{(invoice.amount + invoice.tax).toFixed(2)}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                      {invoice.details}
                    </p>
                    <div className="flex space-x-2">
                      <button
                        className="flex-1 flex items-center justify-center px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors"
                        onClick={() => handleEditInvoice(invoice)}
                      >
                        <DocumentDuplicateIcon className="w-3 h-3 mr-1" />
                        Edit
                      </button>
                      <button
                        className="flex-1 flex items-center justify-center px-2 py-1 text-xs text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded transition-colors"
                        onClick={() => handleViewInvoice(invoice)}
                      >
                        <EyeIcon className="w-3 h-3 mr-1" />
                        View
                      </button>
                      <button
                        className="flex-1 flex items-center justify-center px-2 py-1 text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                        onClick={() => handleDeleteInvoice(invoice)}
                      >
                        <TrashIcon className="w-3 h-3 mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* CONTRACTS Section */}
          {contracts.length > 0 && (
            <div className="px-4 py-4">
              <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
                Contracts ({contracts.length})
              </h3>
              <div className="space-y-2">
                {contracts.map((contract) => (
                  <div
                    key={contract.id}
                    className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {contract.status === 'draft' ? 'Draft Contract' : 
                           contract.status === 'sent' ? 'Sent Contract' :
                           contract.status === 'signed' ? 'Signed Contract' : 'Contract'}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Created {new Date(contract.created_at).toLocaleDateString('en-GB')}
                          {contract.signed_at && (
                            <span className="ml-2 text-green-600 dark:text-green-400">
                              • Signed {new Date(contract.signed_at).toLocaleDateString('en-GB')}
                            </span>
                          )}
                        </p>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        contract.status === 'draft' ? 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300' :
                        contract.status === 'sent' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                        contract.status === 'signed' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                        'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                      }`}>
                        {contract.status}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                      {contract.terms.substring(0, 100)}...
                    </p>
                    <div className="flex space-x-2">
                      <button
                        className="flex-1 flex items-center justify-center px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors"
                        onClick={() => handleEditContract(contract)}
                      >
                        <DocumentDuplicateIcon className="w-3 h-3 mr-1" />
                        Edit
                      </button>
                      <button
                        className="flex-1 flex items-center justify-center px-2 py-1 text-xs text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded transition-colors"
                        onClick={() => handleViewContract(contract)}
                      >
                        <EyeIcon className="w-3 h-3 mr-1" />
                        View
                      </button>
                      <button
                        className="flex-1 flex items-center justify-center px-2 py-1 text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                        onClick={() => handleDeleteContract(contract)}
                      >
                        <TrashIcon className="w-3 h-3 mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* JOB ACTIONS Section */}
          <div className="px-4 py-4">
            <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
              JOB ACTIONS
            </h3>
            <div className="space-y-1">
              <button
                className="w-full flex items-center px-0 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors"
                onClick={handleMarkCompleted}
              >
                <CheckCircleIcon className="w-4 h-4 mr-3 text-gray-500 dark:text-gray-400" />
                Mark as Completed
              </button>
              <button
                className="w-full flex items-center px-0 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors"
                onClick={handleAssignTeamMember}
              >
                <UserGroupIcon className="w-4 h-4 mr-3 text-gray-500 dark:text-gray-400" />
                Assign to Team Member
              </button>
            </div>
          </div>

          {/* Separator */}
          <div className="mx-4 border-t border-gray-200 dark:border-gray-700"></div>

          {/* Destructive Actions */}
          <div className="px-4 py-4">
            <div className="space-y-1">
              <button
                className="w-full flex items-center px-0 py-2 text-left text-sm text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20 rounded-md transition-colors"
                onClick={handleArchiveJob}
              >
                <ArchiveBoxIcon className="w-4 h-4 mr-3" />
                Archive Job
              </button>
              <button
                className="w-full flex items-center px-0 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors"
                onClick={handleDeleteJob}
              >
                <TrashIcon className="w-4 h-4 mr-3" />
                Delete Job
              </button>
            </div>
          </div>
            </>
          )}
        </div>

        {/* Bottom Action Bar */}
        {job && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800/50">
            <div className="flex justify-between">
              <button
                className="flex-1 mx-1 h-12 flex items-center justify-center text-green-600 dark:text-green-500 hover:text-green-700 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-all"
                onClick={handleCall}
              >
                <PhoneIcon className="w-5 h-5" />
              </button>
              <button
                className="flex-1 mx-1 h-12 flex items-center justify-center text-blue-600 dark:text-blue-500 hover:text-blue-700 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all"
                onClick={handleMessage}
              >
                <ChatBubbleLeftIcon className="w-5 h-5" />
              </button>
              <button
                className="flex-1 mx-1 h-12 flex items-center justify-center text-purple-600 dark:text-purple-500 hover:text-purple-700 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-lg transition-all"
                onClick={handleEmail}
              >
                <EnvelopeIcon className="w-5 h-5" />
              </button>
              <button
                className="flex-1 mx-1 h-12 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all"
                onClick={handleProfile}
              >
                <UserCircleIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
    </Drawer>

    {/* Document Drawers */}
    {job && (
        <>
          <CreateQuoteDrawer
            job={job}
            isOpen={isQuoteDrawerOpen}
            onClose={() => {
              setQuoteDrawerOpen(false);
              setEditingQuote(null); // Clear editing state when closing
            }}
            onQuoteSaved={refetchQuotes}
            editingQuote={editingQuote}
          />
          <CreateInvoiceDrawer
            job={job}
            isOpen={isInvoiceDrawerOpen}
            onClose={() => {
              setInvoiceDrawerOpen(false);
              setEditingInvoice(null); // Clear editing state when closing
            }}
            onInvoiceSaved={() => {
          console.log('🔄 Refreshing invoices after save...');
          refetchInvoices();
        }}
            editingInvoice={editingInvoice}
          />
          <CreateContractDrawer
            job={job}
            isOpen={isContractDrawerOpen}
            onClose={() => {
              setContractDrawerOpen(false);
              setEditingContract(null); // Clear editing state when closing
            }}
            onContractSaved={refetchContracts}
            editingContract={editingContract}
          />
          
          {/* Quote Preview Modal */}
          {previewQuote && (
            <QuotePreviewModal
              job={job}
              quoteDescription={previewQuote.details}
              termsAndConditions={previewQuote.terms || ''}
              amount={previewQuote.amount}
              marketRateWorkType={previewQuote.market_rate_work_type}
              marketRateSuggestion={previewQuote.market_rate_suggestion}
              marketRateEstimatedRange={previewQuote.market_rate_estimated_range}
              isOpen={isQuotePreviewOpen}
              onClose={() => {
                setQuotePreviewOpen(false);
                setPreviewQuote(null);
              }}
            />
          )}
          
          {/* Invoice Preview Modal */}
          {previewInvoice && (
            <InvoicePreviewModal
              job={job}
              lineItems={previewInvoice.line_items && previewInvoice.line_items.length > 0 
                ? previewInvoice.line_items.map((item: any, index: number) => ({
                    id: `${index + 1}`,
                    description: item.description,
                    quantity: item.quantity,
                    unitPrice: item.unitPrice,
                    total: item.total
                  }))
                : [{
                    id: '1',
                    description: previewInvoice.details,
                    quantity: 1,
                    unitPrice: previewInvoice.amount,
                    total: previewInvoice.amount
                  }]
              }
              includeVAT={previewInvoice.tax > 0}
              vatAmount={previewInvoice.tax}
              dueDate={previewInvoice.due_date}
              isOpen={isInvoicePreviewOpen}
              onClose={() => {
                setInvoicePreviewOpen(false);
                setPreviewInvoice(null);
              }}
            />
          )}
          
          {/* Contract Preview Modal */}
          {previewContract && (
            <ContractPreviewModal
              job={job}
              contractTerms={previewContract.terms}
              isOpen={isContractPreviewOpen}
              onClose={() => {
                setContractPreviewOpen(false);
                setPreviewContract(null);
              }}
            />
          )}
        </>
      )}
    </>
  );
} 