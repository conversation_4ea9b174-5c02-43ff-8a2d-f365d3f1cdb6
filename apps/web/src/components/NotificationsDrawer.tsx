import React, { useState } from 'react';
import { useNotifications } from '@/hooks/useNotifications';
import { Notification, NotificationType } from '@/types';
import { 
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  BriefcaseIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

interface NotificationsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'success':
    case 'quote_accepted':
    case 'contract_signed':
    case 'payment_received':
      return CheckCircleIcon;
    case 'warning':
      return ExclamationTriangleIcon;
    case 'error':
      return XCircleIcon;
    case 'job_assigned':
      return BriefcaseIcon;
    case 'team_invite':
      return UsersIcon;
    case 'info':
    default:
      return InformationCircleIcon;
  }
};

const getNotificationColor = (type: NotificationType, isRead: boolean) => {
  const baseOpacity = isRead ? 'opacity-60' : '';
  
  switch (type) {
    case 'success':
    case 'quote_accepted':
    case 'contract_signed':
    case 'payment_received':
      return `text-green-500 ${baseOpacity}`;
    case 'warning':
      return `text-yellow-500 ${baseOpacity}`;
    case 'error':
      return `text-red-500 ${baseOpacity}`;
    case 'job_assigned':
      return `text-blue-500 ${baseOpacity}`;
    case 'team_invite':
      return `text-purple-500 ${baseOpacity}`;
    case 'info':
    default:
      return `text-blue-500 ${baseOpacity}`;
  }
};

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  onClick?: (notification: Notification) => void;
}

function NotificationItem({ notification, onMarkAsRead, onDelete, onClick }: NotificationItemProps) {
  const IconComponent = getNotificationIcon(notification.type);
  const iconColor = getNotificationColor(notification.type, notification.is_read);

  const handleClick = () => {
    if (!notification.is_read) {
      onMarkAsRead(notification.id);
    }
    if (onClick) {
      onClick(notification);
    }
  };

  return (
    <div 
      className={`
        p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 
        transition-colors cursor-pointer
        ${!notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
      `}
      onClick={handleClick}
    >
      <div className="flex items-start space-x-3">
        {/* Unread indicator */}
        <div className="flex-shrink-0 pt-1">
          {!notification.is_read && (
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          )}
        </div>

        {/* Icon */}
        <div className="flex-shrink-0">
          <IconComponent className={`h-6 w-6 ${iconColor}`} />
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className={`text-sm font-medium text-gray-900 dark:text-white ${notification.is_read ? 'opacity-70' : ''}`}>
                {notification.title}
              </h3>
              <p className={`text-sm text-gray-600 dark:text-gray-300 mt-1 ${notification.is_read ? 'opacity-60' : ''}`}>
                {notification.message}
              </p>
              <div className="flex items-center space-x-4 mt-2">
                <span className={`text-xs text-gray-500 dark:text-gray-400 ${notification.is_read ? 'opacity-50' : ''}`}>
                  {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                </span>
                {notification.type !== 'info' && (
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    notification.type === 'success' || notification.type === 'quote_accepted' || notification.type === 'contract_signed' || notification.type === 'payment_received'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                      : notification.type === 'warning'
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
                      : notification.type === 'error'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
                      : notification.type === 'job_assigned'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
                      : 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'
                  }`}>
                    {notification.type.replace('_', ' ').toUpperCase()}
                  </span>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2 ml-4">
              {!notification.is_read && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onMarkAsRead(notification.id);
                  }}
                  className="w-6 h-6 rounded-full bg-green-100 hover:bg-green-200 dark:bg-green-900/30 dark:hover:bg-green-800/50 flex items-center justify-center transition-colors"
                  title="Mark as read"
                >
                  <CheckIcon className="h-3 w-3 text-green-600 dark:text-green-400" />
                </button>
              )}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(notification.id);
                }}
                className="w-6 h-6 rounded-full bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-800/50 flex items-center justify-center transition-colors"
                title="Delete notification"
              >
                <XMarkIcon className="h-3 w-3 text-red-600 dark:text-red-400" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function NotificationsDrawer({ isOpen, onClose }: NotificationsDrawerProps) {
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');
  
  const {
    notifications: allNotifications,
    stats,
    isLoading,
    error,
    hasMore,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    loadMore
  } = useNotifications({
    includeRead: filter === 'all' || filter === 'read',
    autoRefresh: true
  });

  // Filter notifications based on the selected filter
  const notifications = React.useMemo(() => {
    if (filter === 'read') {
      return allNotifications.filter(notification => notification.is_read);
    }
    if (filter === 'unread') {
      return allNotifications.filter(notification => !notification.is_read);
    }
    return allNotifications;
  }, [allNotifications, filter]);

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all as read:', error);
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (notification.action_url) {
      // Navigate to the relevant content
      window.location.href = notification.action_url;
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className="fixed top-0 right-0 h-full w-96 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 z-50 transform transition-transform duration-300 ease-in-out">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Notifications</h2>
          <button
            onClick={onClose}
            className="p-1 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Filter Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setFilter('all')}
            className={`flex-1 px-3 py-3 text-sm font-medium transition-colors ${
              filter === 'all'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter('unread')}
            className={`flex-1 px-3 py-3 text-sm font-medium transition-colors relative ${
              filter === 'unread'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <span className="flex items-center justify-center gap-2">
              Unread
              {stats && stats.unread > 0 && (
                <span className="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]">
                  {stats.unread > 99 ? '99+' : stats.unread}
                </span>
              )}
            </span>
          </button>
          <button
            onClick={() => setFilter('read')}
            className={`flex-1 px-3 py-3 text-sm font-medium transition-colors ${
              filter === 'read'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <span className="flex items-center justify-center gap-2">
              Read
              {stats && (stats.total - stats.unread) > 0 && (
                <span className="bg-gray-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]">
                  {(stats.total - stats.unread) > 99 ? '99+' : (stats.total - stats.unread)}
                </span>
              )}
            </span>
          </button>
        </div>

        {/* Actions Bar */}
        {stats && stats.unread > 0 && (
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={handleMarkAllAsRead}
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
            >
              Mark all as read ({stats.unread})
            </button>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : error ? (
            <div className="p-4 text-center text-red-600 dark:text-red-400">
              <p>{error}</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-6h5v6z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {filter === 'read' ? 'No read notifications' : 'No notifications yet'}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {filter === 'unread' 
                  ? "You're all caught up! No unread notifications."
                  : filter === 'read'
                  ? "Read notifications will appear here after you mark them as read."
                  : "When you receive notifications, they'll appear here."
                }
              </p>
            </div>
          ) : (
            <>
              {notifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={markAsRead}
                  onDelete={deleteNotification}
                  onClick={handleNotificationClick}
                />
              ))}
              
              {hasMore && (
                <div className="p-4 text-center">
                  <button
                    onClick={loadMore}
                    className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                  >
                    Load more notifications
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
} 