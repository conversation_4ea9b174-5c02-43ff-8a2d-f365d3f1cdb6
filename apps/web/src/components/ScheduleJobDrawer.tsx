'use client';

import React, { useState, useEffect } from 'react';
import { XMarkIcon, CalendarIcon, ClockIcon } from '@heroicons/react/24/outline';
import { Job } from '@/types/Job';
import { useJobs } from '@/hooks/useJobs';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/contexts/ToastContext';
import ModernDatePicker from './ModernDatePicker';
import ModernTimePicker from './ModernTimePicker';

interface ScheduleJobDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onJobScheduled?: () => void;
  preselectedJobId?: string;
}

export default function ScheduleJobDrawer({
  isOpen,
  onClose,
  onJobScheduled,
  preselectedJobId
}: ScheduleJobDrawerProps) {
  const [selectedJobId, setSelectedJobId] = useState<string>(preselectedJobId || '');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [startTime, setStartTime] = useState<string>('');
  const [endTime, setEndTime] = useState<string>('');
  const [isAllDay, setIsAllDay] = useState<boolean>(false);
  const [schedulingNotes, setSchedulingNotes] = useState<string>('');
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const { data: jobs, isLoading: jobsLoading } = useJobs();
  const { authenticatedFetch } = useAuthenticatedFetch();
  const { showSuccess, showError } = useToast();

  // Filter unscheduled jobs or jobs that can be rescheduled
  const availableJobs = jobs?.filter(job =>
    !job.scheduled_at || job.id === preselectedJobId
  ) || [];

  // Filter jobs based on search term
  const filteredJobs = availableJobs.filter(job =>
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.client?.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    if (preselectedJobId) {
      setSelectedJobId(preselectedJobId);
      // If job is already scheduled, pre-fill the date/time
      const job = jobs?.find(j => j.id === preselectedJobId);
      if (job?.scheduled_at) {
        // Parse date consistently to avoid timezone issues
        const scheduledAtStr = job.scheduled_at.split('T')[0]; // Get just the date part
        const [year, month, day] = scheduledAtStr.split('-').map(Number);
        const scheduledDate = new Date(year, month - 1, day); // month is 0-indexed
        setStartDate(scheduledDate);

        // Check if it's an all-day event (no start/end times)
        if (!job.scheduled_start_time && !job.scheduled_end_time) {
          setIsAllDay(true);
          setEndDate(new Date(year, month - 1, day)); // Same day for all-day events
        } else {
          setIsAllDay(false);
          if (job.scheduled_start_time) {
            setStartTime(job.scheduled_start_time);
          }
          if (job.scheduled_end_time) {
            setEndTime(job.scheduled_end_time);
          }
          // For now, set end date same as start date - could be enhanced later
          setEndDate(new Date(year, month - 1, day));
        }
      }
    }
  }, [preselectedJobId, jobs]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedJobId || !startDate) {
      showError('Validation Error', 'Please select a job and start date');
      return;
    }

    // Validation for time-based scheduling
    if (!isAllDay) {
      if (!startTime) {
        showError('Validation Error', 'Please set a start time or enable "All Day"');
        return;
      }

      if (endTime && startTime && endDate && startDate) {
        // Check if end is before start - use local timezone formatting
        const startDateTime = new Date(`${formatDateForInput(startDate)}T${startTime}`);
        const endDateTime = new Date(`${formatDateForInput(endDate)}T${endTime}`);

        if (endDateTime <= startDateTime) {
          showError('Validation Error', 'End time must be after start time');
          return;
        }
      }
    }

    setIsSaving(true);
    try {
      const scheduleData: any = {
        scheduled_at: formatDateForInput(startDate), // Use local timezone date format (YYYY-MM-DD)
        scheduling_notes: schedulingNotes || null,
      };

      if (isAllDay) {
        // All day event - clear times
        scheduleData.scheduled_start_time = null;
        scheduleData.scheduled_end_time = null;
      } else {
        // Timed event
        scheduleData.scheduled_start_time = startTime || null;
        scheduleData.scheduled_end_time = endTime || null;
      }

      const response = await authenticatedFetch(`/api/jobs/${selectedJobId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scheduleData),
      });

      if (!response.ok) {
        throw new Error('Failed to schedule job');
      }

      const scheduleType = isAllDay ? 'all-day' : 'timed';
      showSuccess('Job Scheduled', `Job scheduled successfully as ${scheduleType} event!`);
      onJobScheduled?.();
      onClose();
      resetForm();
    } catch (error) {
      console.error('Error scheduling job:', error);
      showError('Scheduling Failed', 'Failed to schedule job. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const resetForm = () => {
    if (!preselectedJobId) {
      setSelectedJobId('');
    }
    setStartDate(null);
    setEndDate(null);
    setStartTime('');
    setEndTime('');
    setIsAllDay(false);
    setSchedulingNotes('');
    setSearchTerm('');
    setShowStartDatePicker(false);
    setShowEndDatePicker(false);
    setShowStartTimePicker(false);
    setShowEndTimePicker(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const formatDateDisplay = (date: Date) => {
    return date.toLocaleDateString('en-GB', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Helper function to format date for input (YYYY-MM-DD) in local timezone
  const formatDateForInput = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const handleAllDayToggle = (enabled: boolean) => {
    setIsAllDay(enabled);
    if (enabled) {
      // Clear times when switching to all-day
      setStartTime('');
      setEndTime('');
      // Set end date to same as start date for all-day events
      if (startDate) {
        setEndDate(startDate);
      }
    }
  };

  const handleStartDateChange = (dateStr: string) => {
    // Create date in local timezone to avoid timezone issues
    const [year, month, day] = dateStr.split('-').map(Number);
    const newStartDate = new Date(year, month - 1, day); // month is 0-indexed
    setStartDate(newStartDate);

    // Auto-set end date if not set or if it's before start date
    if (!endDate || endDate < newStartDate) {
      setEndDate(new Date(year, month - 1, day)); // Create new instance for end date
    }

    setShowStartDatePicker(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={handleClose} />

        <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
          <div className="pointer-events-auto relative w-screen max-w-md">
            <div className="flex h-full flex-col overflow-y-scroll bg-white dark:bg-gray-900 py-6 shadow-xl">
              <div className="px-4 sm:px-6">
                <div className="flex items-start justify-between">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                    Schedule Job
                  </h2>
                  <button
                    type="button"
                    className="ml-3 flex h-7 items-center justify-center rounded-md bg-white dark:bg-gray-900 text-gray-400 hover:text-gray-500 focus:outline-none"
                    onClick={handleClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>

              <div className="relative mt-6 flex-1 px-4 sm:px-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Job Selection */}
                  {!preselectedJobId && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Select Job to Schedule
                      </label>

                      {/* Search Input */}
                      <input
                        type="text"
                        placeholder="Search jobs by title or client..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full px-3 py-2 mb-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                      />

                      {/* Jobs List */}
                      <div className="max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md">
                        {jobsLoading ? (
                          <div className="p-4 text-center text-gray-500">Loading jobs...</div>
                        ) : filteredJobs.length === 0 ? (
                          <div className="p-4 text-center text-gray-500">
                            {searchTerm ? 'No jobs found matching your search' : 'No unscheduled jobs available'}
                          </div>
                        ) : (
                          filteredJobs.map((job) => (
                            <label
                              key={job.id}
                              className={`block p-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer border-b border-gray-200 dark:border-gray-700 last:border-b-0 ${
                                selectedJobId === job.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                              }`}
                            >
                              <input
                                type="radio"
                                name="job"
                                value={job.id}
                                checked={selectedJobId === job.id}
                                onChange={(e) => setSelectedJobId(e.target.value)}
                                className="sr-only"
                              />
                              <div className="flex items-start">
                                <div className="flex-1">
                                  <p className="font-medium text-gray-900 dark:text-white">
                                    {job.title}
                                  </p>
                                  <p className="text-sm text-gray-500 dark:text-gray-400">
                                    {job.client?.name}
                                  </p>
                                  <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                    Status: {job.status.replace('_', ' ')}
                                  </p>
                                </div>
                                {selectedJobId === job.id && (
                                  <div className="ml-2">
                                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </label>
                          ))
                        )}
                      </div>
                    </div>
                  )}

                  {/* Selected Job Display */}
                  {preselectedJobId && selectedJobId && (
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Scheduling:</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {jobs?.find(j => j.id === selectedJobId)?.title}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {jobs?.find(j => j.id === selectedJobId)?.client?.name}
                      </p>
                    </div>
                  )}

                  {/* All Day Toggle */}
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        All Day Event
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Schedule without specific times
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={isAllDay}
                        onChange={(e) => handleAllDayToggle(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {/* Start Date Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Start Date *
                    </label>
                    <button
                      type="button"
                      onClick={() => setShowStartDatePicker(!showStartDatePicker)}
                      className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-left focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <span className={startDate ? 'text-gray-900 dark:text-white' : 'text-gray-400'}>
                        {startDate ? formatDateDisplay(startDate) : 'Select start date'}
                      </span>
                      <CalendarIcon className="h-5 w-5 text-gray-400" />
                    </button>

                    {showStartDatePicker && (
                      <div className="mt-2">
                        <ModernDatePicker
                          selectedDate={startDate ? formatDateForInput(startDate) : null}
                          onDateSelect={handleStartDateChange}
                          compact={true}
                        />
                      </div>
                    )}
                  </div>

                  {/* End Date Selection */}
                  {startDate && !isAllDay && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        End Date
                      </label>
                      <button
                        type="button"
                        onClick={() => setShowEndDatePicker(!showEndDatePicker)}
                        className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-left focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <span className={endDate ? 'text-gray-900 dark:text-white' : 'text-gray-400'}>
                          {endDate ? formatDateDisplay(endDate) : 'Select end date (optional)'}
                        </span>
                        <CalendarIcon className="h-5 w-5 text-gray-400" />
                      </button>

                      {showEndDatePicker && (
                        <div className="mt-2">
                          <ModernDatePicker
                            selectedDate={endDate ? formatDateForInput(endDate) : null}
                            onDateSelect={(dateStr) => {
                              // Create date in local timezone to avoid timezone issues
                              const [year, month, day] = dateStr.split('-').map(Number);
                              setEndDate(new Date(year, month - 1, day)); // month is 0-indexed
                              setShowEndDatePicker(false);
                            }}
                            compact={true}
                            minDate={startDate ? formatDateForInput(startDate) : undefined}
                          />
                        </div>
                      )}
                    </div>
                  )}

                  {/* Time Selection - Only show if not all day */}
                  {startDate && !isAllDay && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Start Time *
                        </label>
                        <button
                          type="button"
                          onClick={() => setShowStartTimePicker(!showStartTimePicker)}
                          className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-left focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <span className={startTime ? 'text-gray-900 dark:text-white' : 'text-gray-400'}>
                            {startTime || 'Select start time'}
                          </span>
                          <ClockIcon className="h-5 w-5 text-gray-400" />
                        </button>

                        {showStartTimePicker && (
                          <div className="mt-2">
                            <ModernTimePicker
                              selectedTime={startTime}
                              onTimeSelect={(time) => {
                                setStartTime(time);
                                setShowStartTimePicker(false);
                              }}
                              onClose={() => setShowStartTimePicker(false)}
                            />
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          End Time
                        </label>
                        <button
                          type="button"
                          onClick={() => setShowEndTimePicker(!showEndTimePicker)}
                          className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-left focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <span className={endTime ? 'text-gray-900 dark:text-white' : 'text-gray-400'}>
                            {endTime || 'Select end time (optional)'}
                          </span>
                          <ClockIcon className="h-5 w-5 text-gray-400" />
                        </button>

                        {showEndTimePicker && (
                          <div className="mt-2">
                            <ModernTimePicker
                              selectedTime={endTime}
                              onTimeSelect={(time) => {
                                setEndTime(time);
                                setShowEndTimePicker(false);
                              }}
                              onClose={() => setShowEndTimePicker(false)}
                            />
                          </div>
                        )}
                      </div>
                    </>
                  )}

                  {/* Scheduling Notes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Scheduling Notes (Optional)
                    </label>
                    <textarea
                      value={schedulingNotes}
                      onChange={(e) => setSchedulingNotes(e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                      placeholder="Add any notes about this scheduled job..."
                    />
                  </div>

                  {/* Actions */}
                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={handleClose}
                      className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={!selectedJobId || !startDate || (!isAllDay && !startTime) || isSaving}
                      className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      {isSaving ? 'Scheduling...' : `Schedule ${isAllDay ? 'All Day' : 'Timed'} Job`}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}