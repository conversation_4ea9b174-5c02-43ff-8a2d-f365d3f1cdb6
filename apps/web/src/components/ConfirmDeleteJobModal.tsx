import React, { useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Job } from '@/types/Job';
import { 
  ExclamationTriangleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface ConfirmDeleteJobModalProps {
  job: Job | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (jobId: string) => void;
  isLoading?: boolean;
}

const ConfirmDeleteJobModal: React.FC<ConfirmDeleteJobModalProps> = ({
  job,
  isOpen,
  onClose,
  onConfirm,
  isLoading = false
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const firstFocusableRef = useRef<HTMLButtonElement>(null);
  const confirmButtonRef = useRef<HTMLButtonElement>(null);

  // Focus management
  useEffect(() => {
    if (isOpen && confirmButtonRef.current) {
      // Focus the confirm button for keyboard accessibility
      confirmButtonRef.current.focus();
    }
  }, [isOpen]);

  // Keyboard handling
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      if (e.key === 'Escape' && !isLoading) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, isLoading]);

  const handleConfirm = () => {
    if (!job || isLoading) return;
    onConfirm(job.id);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isLoading) {
      onClose();
    }
  };

  if (!isOpen || !job) return null;

  return createPortal(
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/30 backdrop-blur-sm"
        onClick={handleBackdropClick}
      />
      
      {/* Modal */}
      <div 
        ref={modalRef}
        className="relative bg-white dark:bg-secondary-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 max-h-[90vh] overflow-hidden animate-scale-in"
        role="dialog"
        aria-modal="true"
        aria-labelledby="delete-modal-title"
        aria-describedby="delete-modal-description"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-secondary-200 dark:border-secondary-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-error-100 dark:bg-error-900 flex items-center justify-center">
              <ExclamationTriangleIcon className="w-6 h-6 text-error-600 dark:text-error-400" />
            </div>
            <div>
              <h3 id="delete-modal-title" className="text-lg font-semibold text-secondary-900 dark:text-secondary-100">
                Delete Job
              </h3>
              <p className="text-sm text-secondary-600 dark:text-secondary-400">
                This action cannot be undone
              </p>
            </div>
          </div>
          
          <button
            ref={firstFocusableRef}
            onClick={onClose}
            disabled={isLoading}
            className="p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 transition-colors disabled:opacity-50"
            aria-label="Close delete confirmation"
          >
            <XMarkIcon className="w-5 h-5 text-secondary-500 dark:text-secondary-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div id="delete-modal-description" className="space-y-4">
            {/* Job Details */}
            <div className="p-4 bg-secondary-50 dark:bg-secondary-900 rounded-lg">
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-jobs-500 to-jobs-600 flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
                  {job.title.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 truncate">
                    {job.title}
                  </h4>
                  <p className="text-sm text-secondary-600 dark:text-secondary-400">
                    Client: {job.client.name}
                  </p>
                  <p className="text-xs text-secondary-500 dark:text-secondary-500 mt-1">
                    #{job.id.slice(-6).toUpperCase()}
                  </p>
                </div>
              </div>
            </div>

            {/* Warning Message */}
            <div className="bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-lg p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="w-5 h-5 text-error-600 dark:text-error-400 flex-shrink-0 mt-0.5" />
                <div className="ml-3">
                  <h5 className="font-medium text-error-900 dark:text-error-300">
                    Warning
                  </h5>
                  <p className="text-sm text-error-700 dark:text-error-400 mt-1">
                    Deleting this job will permanently remove:
                  </p>
                  <ul className="text-sm text-error-700 dark:text-error-400 mt-2 list-disc list-inside space-y-1">
                    <li>Job details and history</li>
                    <li>Associated quotes and invoices</li>
                    <li>Job log entries and notes</li>
                    <li>Scheduling information</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Confirmation Input */}
            <div>
              <p className="text-sm text-secondary-700 dark:text-secondary-300 mb-2">
                Are you absolutely sure you want to delete this job?
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-secondary-200 dark:border-secondary-700 bg-secondary-50 dark:bg-secondary-900">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:text-secondary-900 dark:hover:text-secondary-100 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>

          <button
            ref={confirmButtonRef}
            onClick={handleConfirm}
            disabled={isLoading}
            className={`
              px-6 py-2 text-sm font-medium text-white rounded-lg transition-all duration-200
              ${isLoading 
                ? 'bg-error-400 cursor-not-allowed' 
                : 'bg-error-600 hover:bg-error-700 active:bg-error-800 hover:scale-[1.02] active:scale-[0.98]'
              }
              focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2 dark:focus:ring-offset-secondary-800
            `}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>Deleting...</span>
              </div>
            ) : (
              'Delete Job'
            )}
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default ConfirmDeleteJobModal;