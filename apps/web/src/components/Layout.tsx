'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useDrawer } from '@/contexts/DrawerContext';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigationCounts } from '@/hooks/useNavigationCounts';
import { ProfileDrawer } from './ProfileDrawer';
import FloatingDexWidget from './FloatingDexWidget';
import EnhancedTopNav from './EnhancedTopNav';

// Modern Drawer component
const Drawer = ({ isOpen, onClose, side, title, size, children }: {
  isOpen: boolean;
  onClose: () => void;
  side: 'left' | 'right';
  title: string;
  size: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}) => {
  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop with blur */}
      <div 
        className="fixed inset-0 bg-secondary-900/50 backdrop-blur-sm z-40 animate-fade-in"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className={`
        fixed top-0 ${side === 'left' ? 'left-0' : 'right-0'} h-full bg-white dark:bg-secondary-900 
        border-${side === 'left' ? 'r' : 'l'} border-secondary-200 dark:border-secondary-700 z-50
        ${size === 'sm' ? 'w-48' : size === 'md' ? 'w-80' : 'w-96'}
        transform transition-all duration-300 ease-gentle shadow-xl animate-slide-in-${side === 'left' ? 'left' : 'right'}
      `}>
        {/* Modern Header */}
        <div className="flex items-center justify-between p-6 border-b border-secondary-200 dark:border-secondary-700 bg-secondary-25 dark:bg-secondary-800">
          <h2 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50">{title}</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-xl text-secondary-500 dark:text-secondary-400 hover:bg-secondary-200 dark:hover:bg-secondary-700 hover:text-secondary-700 dark:hover:text-secondary-200 transition-all duration-200 hover:scale-105 focus-ring"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {children}
        </div>
      </div>
    </>
  );
};

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  // Get drawer context
  const { openWorkforceDrawer } = useDrawer();

  // Get auth context and router
  const { signOut, profile } = useAuth();
  const router = useRouter();

  const [isLeftDrawerOpen, setIsLeftDrawerOpen] = useState(false);
  const [isRightDrawerOpen, setIsRightDrawerOpen] = useState(false);
  const [isProfileDrawerOpen, setIsProfileDrawerOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    if (isLoggingOut) return;
    
    try {
      setIsLoggingOut(true);
      // Redirect immediately to avoid 401 errors during logout
      router.push('/login');
      // Sign out after redirect is initiated
      await signOut();
    } catch (error) {
      console.error('Logout error:', error);
      setIsLoggingOut(false);
    }
  };

  // Get navigation counts
  const { counts } = useNavigationCounts();

  const navigationItems = [
    { href: '/dashboard', label: 'Overview', icon: 'home', type: 'link' },
    { href: '/dashboard/jobs', label: 'Jobs', icon: 'briefcase', type: 'link', badge: counts.jobs },
    { href: '/dashboard/clients', label: 'Clients', icon: 'user-group', type: 'link', badge: counts.clients },
    { href: '/dashboard/teams', label: 'Team', icon: 'users', type: 'link', badge: counts.team },
  ];

  const businessItems = [
    { href: '/dashboard/invoicing', label: 'Invoicing', icon: 'currency', type: 'link', badge: counts.invoices },
    { href: '/dashboard/stats', label: 'Analytics', icon: 'chart', type: 'link' },
    { href: '/dashboard/schedule', label: 'Schedule', icon: 'calendar', type: 'link' },
  ];

  const toolItems = [
    { href: '/settings', label: 'Settings', icon: 'cog', type: 'link' },
  ];

  const bottomLinks = [
    { href: '/help', label: 'Help' },
    { href: '/about', label: 'About' },
  ];

  const getIcon = (iconName: string) => {
    const baseClasses = "w-5 h-5";
    
    switch (iconName) {
      case 'home':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        );
      case 'user-group':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        );
      case 'briefcase':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4a2 2 0 00-2-2H8a2 2 0 00-2 2v2M7 8h10l1.33 6H5.67L7 8z" />
          </svg>
        );
      case 'users':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
        );
      case 'document':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'currency':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        );
      case 'chart':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      case 'calendar':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      case 'sparkles':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3l1.5 1.5L5 6M12 3l1.5 1.5L12 6m7-3l-1.5 1.5L19 6m-7 9l1.5 1.5L12 18m7-3l-1.5 1.5L19 18m-14-3l1.5 1.5L5 18" />
          </svg>
        );
      case 'cog':
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
      default:
        return (
          <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/20 via-slate-50/10 to-indigo-50/15 dark:from-secondary-950 dark:via-slate-950/30 dark:to-blue-950/20" style={{ backgroundColor: 'rgb(59 130 246 / 3%)' }}>
      {/* Enhanced Top Navigation Bar */}
      <EnhancedTopNav onMenuClick={() => setIsLeftDrawerOpen(true)} />

      {/* Modern Desktop Sidebar */}
      <aside className="hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 lg:left-0 lg:w-64 lg:bg-white lg:dark:bg-secondary-900 lg:border-r lg:border-secondary-200 lg:dark:border-secondary-700 lg:pt-16 lg:z-20">
        {/* Main Navigation */}
        <nav className="flex-1 p-4 space-y-6">
          {/* Dashboard Section */}
          <div>
            <h3 className="text-xs font-semibold text-secondary-500 dark:text-secondary-400 uppercase tracking-wider mb-2 px-2">
              Dashboard
            </h3>
            <div className="space-y-1">
              {navigationItems.map((item, index) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="nav-item animate-slide-in-left text-sm"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="w-4 h-4 flex items-center justify-center">
                    {getIcon(item.icon)}
                  </div>
                  <span className="font-medium">{item.label}</span>
                  {item.badge && item.badge > 0 && (
                    <span className="ml-auto inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-medium text-white bg-primary-600 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </Link>
              ))}
            </div>
          </div>

          {/* Business Section */}
          <div>
            <h3 className="text-xs font-semibold text-secondary-500 dark:text-secondary-400 uppercase tracking-wider mb-2 px-2">
              Business
            </h3>
            <div className="space-y-1">
              {businessItems.map((item, index) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="nav-item animate-slide-in-left text-sm"
                  style={{ animationDelay: `${(navigationItems.length + index) * 50}ms` }}
                >
                  <div className="w-4 h-4 flex items-center justify-center">
                    {getIcon(item.icon)}
                  </div>
                  <span className="font-medium">{item.label}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* Tools Section */}
          <div>
            <h3 className="text-xs font-semibold text-secondary-500 dark:text-secondary-400 uppercase tracking-wider mb-2 px-2">
              Tools
            </h3>
            <div className="space-y-1">
              {toolItems.map((item, index) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="nav-item animate-slide-in-left text-sm"
                  style={{ animationDelay: `${(navigationItems.length + businessItems.length + index) * 50}ms` }}
                >
                  <div className="w-4 h-4 flex items-center justify-center">
                    {getIcon(item.icon)}
                  </div>
                  <span className="font-medium">{item.label}</span>
                </Link>
              ))}
            </div>
          </div>
        </nav>
        
        {/* Modern User Profile Section */}
        <div className="p-4 border-t border-secondary-200 dark:border-secondary-700">
          {/* Profile Card */}
          <div className="card p-4 mb-4 animate-fade-in-up">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center text-white text-sm font-bold shadow-md">
                {profile?.full_name ? profile.full_name.split(' ').map(n => n[0]).join('').toUpperCase() : 'U'}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wide mb-1">
                  Logged in as
                </div>
                <div className="text-sm font-semibold text-secondary-900 dark:text-secondary-100 truncate">
                  {profile?.company_name || profile?.full_name || 'User'}
                </div>
              </div>
            </div>
          </div>
          
          {/* Logout Button */}
          <button
            className="flex btn-ghost items-center w-full mb-4 justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleLogout}
            disabled={isLoggingOut}
          >
            {isLoggingOut ? (
              <svg className="w-4 h-4 animate-spin mr-2" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                <path strokeLinecap="round" strokeLinejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            )}
            <span className="font-medium">
              {isLoggingOut ? 'Logging out...' : 'Logout'}
            </span>
          </button>
          
          {/* Footer Links */}
          <div className="flex items-center justify-center space-x-6 text-xs">
            <Link
              href="/help"
              className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors hover:scale-105"
            >
              Help
            </Link>
            <Link
              href="/about"
              className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors hover:scale-105"
            >
              About
            </Link>
          </div>
        </div>
      </aside>

      {/* Left Navigation Drawer (mobile) */}
      <Drawer
        isOpen={isLeftDrawerOpen}
        onClose={() => setIsLeftDrawerOpen(false)}
        side="left"
        title="Menu"
        size="md"
      >
        <div className="flex flex-col h-full">
          {/* Navigation Items */}
          <nav className="flex-1 p-4 space-y-6">
            {/* Dashboard Section */}
            <div>
              <h3 className="text-xs font-semibold text-secondary-500 dark:text-secondary-400 uppercase tracking-wider mb-2 px-2">
                Dashboard
              </h3>
              <div className="space-y-1">
                {navigationItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-center space-x-3 px-3 py-2 rounded-xl text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800 hover:text-secondary-900 dark:hover:text-secondary-50 transition-all duration-200 text-sm"
                    onClick={() => setIsLeftDrawerOpen(false)}
                  >
                    <div className="w-4 h-4 flex items-center justify-center">
                      {getIcon(item.icon)}
                    </div>
                    <span>{item.label}</span>
                    {item.badge && item.badge > 0 && (
                      <span className="ml-auto inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-medium text-white bg-primary-600 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                ))}
              </div>
            </div>

            {/* Business Section */}
            <div>
              <h3 className="text-xs font-semibold text-secondary-500 dark:text-secondary-400 uppercase tracking-wider mb-2 px-2">
                Business
              </h3>
              <div className="space-y-1">
                {businessItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-center space-x-3 px-3 py-2 rounded-xl text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800 hover:text-secondary-900 dark:hover:text-secondary-50 transition-all duration-200 text-sm"
                    onClick={() => setIsLeftDrawerOpen(false)}
                  >
                    <div className="w-4 h-4 flex items-center justify-center">
                      {getIcon(item.icon)}
                    </div>
                    <span>{item.label}</span>
                    {item.badge && item.badge > 0 && (
                      <span className="ml-auto inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-medium text-white bg-primary-600 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                ))}
              </div>
            </div>

            {/* Tools Section */}
            <div>
              <h3 className="text-xs font-semibold text-secondary-500 dark:text-secondary-400 uppercase tracking-wider mb-2 px-2">
                Tools
              </h3>
              <div className="space-y-1">
                {toolItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-center space-x-3 px-3 py-2 rounded-xl text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800 hover:text-secondary-900 dark:hover:text-secondary-50 transition-all duration-200 text-sm"
                    onClick={() => setIsLeftDrawerOpen(false)}
                  >
                    <div className="w-4 h-4 flex items-center justify-center">
                      {getIcon(item.icon)}
                    </div>
                    <span>{item.label}</span>
                  </Link>
                ))}
              </div>
            </div>
          </nav>
          
          {/* User Profile Section */}
          <div className="p-4 border-t border-secondary-200 dark:border-secondary-700 mt-auto">
            {/* Profile Image and User Info */}
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center text-white text-sm font-bold shadow-md">
                {profile?.full_name ? profile.full_name.split(' ').map(n => n[0]).join('').toUpperCase() : 'U'}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-xs text-secondary-500 dark:text-secondary-400 mb-1">
                  Logged in as:
                </div>
                <div className="text-sm text-secondary-700 dark:text-secondary-300 truncate">
                  {profile?.company_name || profile?.full_name || 'User'}
                </div>
              </div>
            </div>
            
            {/* Logout Button */}
            <button
              className="flex items-center justify-center space-x-2 w-full px-3 py-2 mb-3 rounded-xl text-secondary-600 dark:text-secondary-400 hover:bg-secondary-100 dark:hover:bg-secondary-800 hover:text-secondary-900 dark:hover:text-secondary-50 transition-all duration-200 border border-secondary-200 dark:border-secondary-700 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 focus-ring"
              onClick={() => {
                setIsLeftDrawerOpen(false);
                handleLogout();
              }}
              disabled={isLoggingOut}
            >
              {isLoggingOut ? (
                <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              )}
              <span className="text-sm font-normal">
                {isLoggingOut ? 'Logging out...' : 'Logout'}
              </span>
            </button>
            
            {/* Help and About Links */}
            <div className="flex items-center justify-center space-x-4 text-xs">
              <Link
                href="/help"
                className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors hover:scale-105"
                onClick={() => setIsLeftDrawerOpen(false)}
              >
                Help
              </Link>
              <Link
                href="/about"
                className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors hover:scale-105"
                onClick={() => setIsLeftDrawerOpen(false)}
              >
                About
              </Link>
            </div>
          </div>
        </div>
      </Drawer>

      {/* Right Drawer Container (placeholder for job/client details) */}
      <Drawer
        isOpen={isRightDrawerOpen}
        onClose={() => setIsRightDrawerOpen(false)}
        side="right"
        title="Details"
        size="lg"
      >
        <div className="p-4">
          <p className="text-secondary-600 dark:text-secondary-400">
            This drawer will be used for job details, client information, and other contextual content.
          </p>
        </div>
      </Drawer>

      {/* Profile Drawer */}
      <ProfileDrawer 
        isOpen={isProfileDrawerOpen}
        onClose={() => setIsProfileDrawerOpen(false)}
      />

      {/* Modern Main Content */}
      <main className="lg:ml-64 pt-16 animate-fade-in-up">
        <div className="px-6 py-6 max-w-none bg-white/30 dark:bg-secondary-900/30 backdrop-blur-sm rounded-tl-3xl lg:rounded-tl-none lg:rounded-none min-h-[calc(100vh-4rem)] border-l border-white/15 dark:border-secondary-700/20" style={{ backgroundColor: 'rgb(255 255 255 / 8%)' }}>
          {children}
        </div>
      </main>

      {/* Floating Dex Widget */}
      <FloatingDexWidget />
    </div>
  );
};

export default Layout; 