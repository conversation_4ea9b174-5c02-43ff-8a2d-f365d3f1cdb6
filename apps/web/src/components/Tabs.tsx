'use client';

import React, { useState, ReactNode } from 'react';

export interface TabItem {
  id: string;
  label: string;
  icon?: ReactNode;
  content: ReactNode;
  color?: 'primary' | 'blue' | 'green' | 'purple' | 'orange' | 'indigo' | 'gray';
}

export interface TabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  className?: string;
  variant?: 'default' | 'pills';
}

export const Tabs: React.FC<TabsProps> = ({ 
  tabs, 
  defaultTab, 
  className = '',
  variant = 'default'
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);

  const getTabClasses = (tab: TabItem, isActive: boolean) => {
    const baseClasses = "flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 whitespace-nowrap";
    
    if (variant === 'pills') {
      return `${baseClasses} rounded-lg ${
        isActive
          ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
          : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
      }`;
    }

    // Default variant with bottom border
    const colorMap = {
      primary: 'border-primary-500 text-primary-700 dark:text-primary-400 bg-primary-50/50 dark:bg-primary-900/20',
      blue: 'border-blue-500 text-blue-700 dark:text-blue-400 bg-blue-50/50 dark:bg-blue-900/20',
      green: 'border-green-500 text-green-700 dark:text-green-400 bg-green-50/50 dark:bg-green-900/20',
      purple: 'border-purple-500 text-purple-700 dark:text-purple-400 bg-purple-50/50 dark:bg-purple-900/20',
      orange: 'border-orange-500 text-orange-700 dark:text-orange-400 bg-orange-50/50 dark:bg-orange-900/20',
      indigo: 'border-indigo-500 text-indigo-700 dark:text-indigo-400 bg-indigo-50/50 dark:bg-indigo-900/20',
      gray: 'border-gray-500 text-gray-700 dark:text-gray-300 bg-gray-50/50 dark:bg-gray-900/20',
    };

    if (isActive) {
      const activeColor = colorMap[tab.color || 'primary'];
      return `${baseClasses} border-b-2 rounded-t-lg ${activeColor}`;
    }
    
    return `${baseClasses} border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50/50 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-500 dark:hover:bg-gray-800/20 rounded-t-lg`;
  };

  const getContainerClasses = () => {
    if (variant === 'pills') {
      return "flex flex-wrap gap-1 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg";
    }
    return "border-b border-gray-200 dark:border-gray-700";
  };

  const getNavClasses = () => {
    if (variant === 'pills') {
      return "";
    }
    return "-mb-px flex flex-wrap";
  };

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <div className={className}>
      {/* Tab Navigation */}
      <div className={getContainerClasses()}>
        <nav className={getNavClasses()}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={getTabClasses(tab, activeTab === tab.id)}
              role="tab"
              aria-selected={activeTab === tab.id}
              aria-controls={`tabpanel-${tab.id}`}
            >
              {tab.icon && <span className="w-5 h-5">{tab.icon}</span>}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTabData && (
          <div
            id={`tabpanel-${activeTabData.id}`}
            role="tabpanel"
            aria-labelledby={`tab-${activeTabData.id}`}
          >
            {activeTabData.content}
          </div>
        )}
      </div>
    </div>
  );
};

export default Tabs;