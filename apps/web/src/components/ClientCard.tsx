'use client';

import React, { useState } from 'react';
import { Client } from '@/types/Client';
import {
  MapPinIcon,
  EnvelopeIcon,
  PhoneIcon,
  CalendarIcon,
  StarIcon,
  BriefcaseIcon,
  ChatBubbleLeftIcon,
  PencilIcon,
  TrashIcon,
  ClipboardDocumentListIcon,
  TrophyIcon,
  DocumentTextIcon,
  EllipsisVerticalIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import {
  StarIcon as StarIconSolid,
  PhoneIcon as PhoneIconSolid,
  ChatBubbleLeftIcon as ChatBubbleLeftIconSolid
} from '@heroicons/react/24/solid';

interface ClientCardProps {
  client: Client;
  onClientClick?: (clientId: string) => void;
  onCreateJobClick?: (clientId: string) => void;
  onRequestReviewClick?: (clientId: string) => void;
  onCallClick?: (phone: string) => void;
  onMessageClick?: (clientId: string) => void;
  onEditClick?: (clientId: string) => void;
  onNotesClick?: (clientId: string) => void;
  onDeleteClick?: (clientId: string) => void;
  onRatingChange?: (clientId: string, rating: number) => void;
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', { 
    day: '2-digit', 
    month: 'short', 
    year: 'numeric'
  });
};

const renderStars = (rating: number, onRatingChange?: (rating: number) => void) => {
  const stars = [];
  for (let i = 1; i <= 5; i++) {
    stars.push(
      <button
        key={i}
        onClick={(e) => {
          e.stopPropagation();
          onRatingChange?.(i);
        }}
        className={`w-4 h-4 transition-colors ${
          onRatingChange ? 'hover:text-yellow-500 cursor-pointer' : ''
        } ${
          i <= rating 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300 dark:text-gray-600'
        }`}
        disabled={!onRatingChange}
      >
        <StarIconSolid className="w-full h-full" />
      </button>
    );
  }
  return stars;
};

export const ClientCard: React.FC<ClientCardProps> = ({
  client,
  onClientClick,
  onCreateJobClick,
  onRequestReviewClick,
  onCallClick,
  onMessageClick,
  onEditClick,
  onNotesClick,
  onDeleteClick,
  onRatingChange
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger card click if clicking on buttons or dropdowns
    if ((e.target as HTMLElement).closest('button') || (e.target as HTMLElement).closest('.dropdown')) {
      return;
    }
    onClientClick?.(client.id);
  };

  const handleRatingChange = (rating: number) => {
    onRatingChange?.(client.id, rating);
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getClientStatus = () => {
    return client.activeJobs > 0 ? 'active' : 'inactive';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'status-badge-active';
      case 'inactive': return 'status-badge-inactive';
      case 'potential': return 'status-badge-potential';
      default: return 'status-badge-neutral';
    }
  };

  const status = getClientStatus();

  return (
    <div 
      className="card-interactive group/card animate-fade-in-up hover:border-clients-200 dark:hover:border-clients-700 h-full relative"
      onClick={handleCardClick}
    >
      {/* Dropdown Menu - Positioned absolutely in top right */}
      <div className="absolute top-6 right-6 bg-transparent border-0 min-w-0 pb-0">
        <button
          onClick={(e) => {
            e.stopPropagation();
            setIsDropdownOpen(!isDropdownOpen);
          }}
          className="p-1.5 text-secondary-400 hover:text-secondary-600 dark:text-secondary-500 dark:hover:text-secondary-300 transition-colors border-0 bg-transparent focus:outline-none rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-800"
        >
          <EllipsisVerticalIcon className="w-5 h-5" />
        </button>
        
        {isDropdownOpen && (
          <div className="absolute right-0 top-full mt-2 w-56 bg-white dark:bg-secondary-800 rounded-xl shadow-lg border border-secondary-200 dark:border-secondary-700 py-2 z-10 animate-fade-in-up">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onNotesClick?.(client.id);
                setIsDropdownOpen(false);
              }}
              className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <DocumentTextIcon className="w-4 h-4 mr-3 text-secondary-500 dark:text-secondary-400" />
              <span className="font-medium">View Notes</span>
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onEditClick?.(client.id);
                setIsDropdownOpen(false);
              }}
              className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <PencilIcon className="w-4 h-4 mr-3 text-secondary-500 dark:text-secondary-400" />
              <span className="font-medium">Edit Client</span>
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onRequestReviewClick?.(client.id);
                setIsDropdownOpen(false);
              }}
              className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <TrophyIcon className="w-4 h-4 mr-3 text-secondary-500 dark:text-secondary-400" />
              <span className="font-medium">Request Review</span>
            </button>
            <div className="h-px bg-secondary-200 dark:bg-secondary-600 my-2 mx-4" />
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDeleteClick?.(client.id);
                setIsDropdownOpen(false);
              }}
              className="w-full px-4 py-3 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <TrashIcon className="w-4 h-4 mr-3 text-red-500 dark:text-red-400" />
              <span className="font-medium">Delete Client</span>
            </button>
          </div>
        )}
      </div>

      {/* Header with Avatar */}
      <div className="flex items-center space-x-3 mb-4 pr-10">
        <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
          {getInitials(client.name)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50 truncate">
              {client.name}
            </h3>
            <span className={getStatusColor(status)}>
              {status}
            </span>
          </div>
          {client.business_name && (
            <p className="text-sm text-secondary-600 dark:text-secondary-400 truncate mb-1">
              {client.business_name}
            </p>
          )}
          <div className="flex items-center space-x-1">
            {renderStars(client.rating, handleRatingChange)}
            {client.rating > 0 && (
              <span className="text-sm text-secondary-500 dark:text-secondary-400 ml-1">
                ({client.rating}/5)
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-2 mb-4">
        {client.email && (
          <div className="flex items-center space-x-2">
            <EnvelopeIcon className="w-4 h-4 text-secondary-400" />
            <a 
              href={`mailto:${client.email}`}
              className="text-sm text-secondary-600 dark:text-secondary-400 hover:text-green-600 dark:hover:text-green-400 transition-colors truncate"
              onClick={(e) => e.stopPropagation()}
            >
              {client.email}
            </a>
          </div>
        )}
        {client.phone && (
          <div className="flex items-center space-x-2">
            <PhoneIcon className="w-4 h-4 text-secondary-400" />
            <a 
              href={`tel:${client.phone}`}
              className="text-sm text-secondary-600 dark:text-secondary-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              {client.phone}
            </a>
          </div>
        )}
        {client.address && (
          <div className="flex items-start space-x-2">
            <MapPinIcon className="w-4 h-4 text-secondary-400 mt-0.5 flex-shrink-0" />
            <span className="text-sm text-secondary-600 dark:text-secondary-400 break-words">
              {client.address}
            </span>
          </div>
        )}
      </div>

      {/* Jobs Summary */}
      <div className="grid grid-cols-2 gap-4 text-sm mb-4">
        <div>
          <p className="text-secondary-500 dark:text-secondary-400">Total Jobs</p>
          <p className="font-semibold text-secondary-900 dark:text-secondary-50">
            {client.totalJobs}
          </p>
        </div>
        <div>
          <p className="text-secondary-500 dark:text-secondary-400">Active Jobs</p>
          <p className="font-semibold text-secondary-900 dark:text-secondary-50">
            {client.activeJobs}
          </p>
        </div>
      </div>

      {/* Client Since */}
      <div className="flex items-center space-x-2 mb-4">
        <CalendarIcon className="w-4 h-4 text-secondary-400" />
        <span className="text-sm text-secondary-600 dark:text-secondary-400">
          Client since {formatDate(client.created_at)}
        </span>
      </div>

      {/* Primary Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-secondary-200 dark:border-secondary-700">
        <div className="flex items-center space-x-2">
          {client.phone && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onCallClick?.(client.phone!); // ! means Only show the call button if phone exists
              }}
              className="p-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
              title="Call client"
            >
              <PhoneIcon className="w-5 h-5" />
            </button>
          )}
          {client.phone && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (client.phone) {
                  window.open(`https://wa.me/${client.phone.replace(/\D/g, '')}`, '_blank');
                }
              }}
              className="p-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
              title="Send WhatsApp message"
            >
              <ChatBubbleLeftIcon className="w-5 h-5" />
            </button>
          )}
          {client.email && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                window.open(`mailto:${client.email}`, '_self');
              }}
              className="p-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
              title="Send email"
            >
              <EnvelopeIcon className="w-5 h-5" />
            </button>
          )}
        </div>
        
        <button
          onClick={(e) => {
            e.stopPropagation();
            onCreateJobClick?.(client.id);
          }}
          className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors flex items-center"
        >
          <BriefcaseIcon className="w-5 h-5 mr-2" />
          Create Job
        </button>
      </div>

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
}; 