import React, { useState } from 'react';
import { XMarkIcon, PaperAirplaneIcon, UserPlusIcon, ShieldCheckIcon, EnvelopeIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

interface InviteTeamMemberDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  teamId: string | null;
  onInvitationSent?: () => void;
}

type InvitationMethod = 'email' | 'whatsapp' | 'sms';

interface InvitationFormData {
  email: string;
  role: 'member' | 'manager';
  personal_message: string;
  invitation_method: InvitationMethod;
  permissions: {
    can_manage_jobs: boolean;
    can_manage_clients: boolean;
    can_manage_invoices: boolean;
    can_manage_quotes: boolean;
    can_view_reports: boolean;
    can_manage_team: boolean;
  };
}

interface InvitationPreset {
  name: string;
  description: string;
  permissions: InvitationFormData['permissions'];
}

const INVITATION_PRESETS: InvitationPreset[] = [
  {
    name: 'Basic Worker',
    description: 'Can view assigned jobs and update their status',
    permissions: {
      can_manage_jobs: false,
      can_manage_clients: false,
      can_manage_invoices: false,
      can_manage_quotes: false,
      can_view_reports: false,
      can_manage_team: false
    }
  },
  {
    name: 'Field Manager',
    description: 'Can manage jobs and clients, create quotes',
    permissions: {
      can_manage_jobs: true,
      can_manage_clients: true,
      can_manage_invoices: false,
      can_manage_quotes: true,
      can_view_reports: false,
      can_manage_team: false
    }
  },
  {
    name: 'Office Administrator',
    description: 'Can handle all administrative tasks and reporting',
    permissions: {
      can_manage_jobs: true,
      can_manage_clients: true,
      can_manage_invoices: true,
      can_manage_quotes: true,
      can_view_reports: true,
      can_manage_team: false
    }
  },
  {
    name: 'Team Leader',
    description: 'Full access including team management',
    permissions: {
      can_manage_jobs: true,
      can_manage_clients: true,
      can_manage_invoices: true,
      can_manage_quotes: true,
      can_view_reports: true,
      can_manage_team: true
    }
  }
];

const INVITATION_METHODS = [
  { id: 'email' as const, name: 'Email', icon: EnvelopeIcon, description: 'Send invitation via email' },
  { id: 'whatsapp' as const, name: 'WhatsApp', icon: DevicePhoneMobileIcon, description: 'Share invitation link via WhatsApp' },
  { id: 'sms' as const, name: 'SMS', icon: DevicePhoneMobileIcon, description: 'Send invitation link via SMS' }
];

export default function InviteTeamMemberDrawer({ isOpen, onClose, teamId, onInvitationSent }: InviteTeamMemberDrawerProps) {
  const [step, setStep] = useState<'form' | 'preview' | 'success'>('form');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  const [invitationToken, setInvitationToken] = useState<string | null>(null);
  
  const { authenticatedPost } = useAuthenticatedFetch();

  const [formData, setFormData] = useState<InvitationFormData>({
    email: '',
    role: 'member',
    personal_message: '',
    invitation_method: 'email',
    permissions: {
      can_manage_jobs: false,
      can_manage_clients: false,
      can_manage_invoices: false,
      can_manage_quotes: false,
      can_view_reports: false,
      can_manage_team: false
    }
  });

  const handleClose = () => {
    setStep('form');
    setError(null);
    setSelectedPreset(null);
    setInvitationToken(null);
    setFormData({
      email: '',
      role: 'member',
      personal_message: '',
      invitation_method: 'email',
      permissions: {
        can_manage_jobs: false,
        can_manage_clients: false,
        can_manage_invoices: false,
        can_manage_quotes: false,
        can_view_reports: false,
        can_manage_team: false
      }
    });
    onClose();
  };

  const applyPreset = (preset: InvitationPreset) => {
    setFormData(prev => ({
      ...prev,
      permissions: { ...preset.permissions }
    }));
    setSelectedPreset(preset.name);
  };

  const handlePermissionChange = (permission: keyof InvitationFormData['permissions']) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permission]: !prev.permissions[permission]
      }
    }));
    setSelectedPreset(null); // Clear preset selection when manually changing permissions
  };

  const handleSubmit = async () => {
    if (!teamId) {
      setError('No team selected');
      return;
    }

    if (!formData.email.trim()) {
      setError('Email address is required');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await authenticatedPost(`/api/workforce/${teamId}/invite`, {
        email: formData.email.trim(),
        role: formData.role,
        personal_message: formData.personal_message.trim(),
        invitation_method: formData.invitation_method,
        permissions: formData.permissions
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send invitation');
      }

      setInvitationToken(result.invitation.token);
      setStep('success');
      onInvitationSent?.();
    } catch (err) {
      console.error('Error sending invitation:', err);
      setError(err instanceof Error ? err.message : 'Failed to send invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const generateInvitationLink = () => {
    if (!invitationToken) return '';
    return `${window.location.origin}/accept-invitation/${invitationToken}`;
  };

  const handleMethodAction = () => {
    const invitationLink = generateInvitationLink();
    
    switch (formData.invitation_method) {
      case 'email':
        // For email, we'll just show success - the backend handles email sending
        break;
        
      case 'whatsapp':
        const whatsappMessage = `Hi! You've been invited to join my workforce on DeskBelt. Click here to accept: ${invitationLink}`;
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(whatsappMessage)}`;
        window.open(whatsappUrl, '_blank');
        break;
        
      case 'sms':
        const smsMessage = `You've been invited to join my workforce on DeskBelt: ${invitationLink}`;
        const smsUrl = `sms:?body=${encodeURIComponent(smsMessage)}`;
        window.open(smsUrl);
        break;
    }
  };

  const getPermissionCount = (permissions: InvitationFormData['permissions']) => {
    return Object.values(permissions).filter(Boolean).length;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-50">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={handleClose} />
      
      <div className="relative w-full max-w-lg bg-white dark:bg-gray-900 shadow-xl flex flex-col h-full overflow-hidden">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <UserPlusIcon className="w-6 h-6 mr-3" />
              <div>
                <h2 className="text-lg font-semibold">
                  {step === 'success' ? 'Invitation Sent!' : 'Invite Team Member'}
                </h2>
                <p className="text-blue-100 text-sm">
                  {step === 'success' 
                    ? 'Your invitation has been created successfully' 
                    : 'Add a new member to your workforce'
                  }
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {step === 'form' && (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                  <EnvelopeIcon className="w-5 h-5 mr-2 text-blue-600" />
                  Basic Information
                </h3>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="role" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Role
                  </label>
                  <select
                    id="role"
                    value={formData.role}
                    onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as 'member' | 'manager' }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                  >
                    <option value="member">Member</option>
                    <option value="manager">Manager</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Personal Message (Optional)
                  </label>
                  <textarea
                    id="message"
                    rows={3}
                    value={formData.personal_message}
                    onChange={(e) => setFormData(prev => ({ ...prev, personal_message: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white resize-none"
                    placeholder="Add a personal welcome message..."
                  />
                </div>
              </div>

              {/* Invitation Method */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                  <DevicePhoneMobileIcon className="w-5 h-5 mr-2 text-green-600" />
                  Invitation Method
                </h3>

                <div className="grid grid-cols-1 gap-3">
                  {INVITATION_METHODS.map((method) => (
                    <label
                      key={method.id}
                      className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                        formData.invitation_method === method.id
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}
                    >
                      <input
                        type="radio"
                        name="invitation_method"
                        value={method.id}
                        checked={formData.invitation_method === method.id}
                        onChange={(e) => setFormData(prev => ({ ...prev, invitation_method: e.target.value as InvitationMethod }))}
                        className="sr-only"
                      />
                      <method.icon className={`w-5 h-5 mr-3 ${formData.invitation_method === method.id ? 'text-blue-600' : 'text-gray-400'}`} />
                      <div>
                        <div className={`font-medium ${formData.invitation_method === method.id ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-white'}`}>
                          {method.name}
                        </div>
                        <div className={`text-sm ${formData.invitation_method === method.id ? 'text-blue-700 dark:text-blue-200' : 'text-gray-500 dark:text-gray-400'}`}>
                          {method.description}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Permission Presets */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                  <ShieldCheckIcon className="w-5 h-5 mr-2 text-amber-600" />
                  Permission Presets
                </h3>

                <div className="grid grid-cols-1 gap-3">
                  {INVITATION_PRESETS.map((preset) => (
                    <button
                      key={preset.name}
                      type="button"
                      onClick={() => applyPreset(preset)}
                      className={`text-left p-3 border rounded-lg transition-colors ${
                        selectedPreset === preset.name
                          ? 'border-amber-500 bg-amber-50 dark:bg-amber-900/20'
                          : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}
                    >
                      <div className={`font-medium mb-1 ${selectedPreset === preset.name ? 'text-amber-900 dark:text-amber-100' : 'text-gray-900 dark:text-white'}`}>
                        {preset.name}
                      </div>
                      <div className={`text-sm ${selectedPreset === preset.name ? 'text-amber-700 dark:text-amber-200' : 'text-gray-500 dark:text-gray-400'}`}>
                        {preset.description}
                      </div>
                      <div className={`text-xs mt-1 ${selectedPreset === preset.name ? 'text-amber-600 dark:text-amber-300' : 'text-gray-400 dark:text-gray-500'}`}>
                        {getPermissionCount(preset.permissions)} permissions enabled
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Individual Permissions */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Individual Permissions
                  {!selectedPreset && (
                    <span className="text-sm font-normal text-gray-500 ml-2">
                      ({getPermissionCount(formData.permissions)} enabled)
                    </span>
                  )}
                </h3>

                <div className="space-y-3">
                  {Object.entries({
                    can_manage_jobs: { label: 'Manage Jobs', description: 'Create, edit, and delete jobs' },
                    can_manage_clients: { label: 'Manage Clients', description: 'Add, edit, and delete clients' },
                    can_manage_invoices: { label: 'Manage Invoices', description: 'Create and manage invoices' },
                    can_manage_quotes: { label: 'Manage Quotes', description: 'Create and manage quotes' },
                    can_view_reports: { label: 'View Reports', description: 'Access analytics and reports' },
                    can_manage_team: { label: 'Manage Team', description: 'Invite and manage team members' }
                  }).map(([key, info]) => (
                    <label
                      key={key}
                      className="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
                    >
                      <input
                        type="checkbox"
                        checked={formData.permissions[key as keyof InvitationFormData['permissions']]}
                        onChange={() => handlePermissionChange(key as keyof InvitationFormData['permissions'])}
                        className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <div className="ml-3 flex-1">
                        <div className="font-medium text-gray-900 dark:text-white">{info.label}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{info.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {error && (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="text-red-800 dark:text-red-200 text-sm">{error}</div>
                </div>
              )}
            </div>
          )}

          {step === 'success' && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <PaperAirplaneIcon className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Invitation Created Successfully!
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Your invitation has been sent to <strong>{formData.email}</strong>
                </p>
              </div>

              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Invitation Details</h4>
                  <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <p><strong>Email:</strong> {formData.email}</p>
                    <p><strong>Role:</strong> {formData.role}</p>
                    <p><strong>Method:</strong> {INVITATION_METHODS.find(m => m.id === formData.invitation_method)?.name}</p>
                    <p><strong>Permissions:</strong> {getPermissionCount(formData.permissions)} enabled</p>
                  </div>
                </div>

                {formData.invitation_method !== 'email' && (
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900 dark:text-white">Invitation Link</h4>
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <input
                        type="text"
                        value={generateInvitationLink()}
                        readOnly
                        className="w-full bg-transparent text-sm text-gray-600 dark:text-gray-400 select-all focus:outline-none"
                      />
                    </div>
                    <button
                      onClick={handleMethodAction}
                      className="w-full flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                    >
                      <span>Share via {INVITATION_METHODS.find(m => m.id === formData.invitation_method)?.name}</span>
                    </button>
                  </div>
                )}

                <div className="p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                  <p className="text-sm text-amber-800 dark:text-amber-200">
                    The invitation will expire in 7 days. You can resend or cancel it from the team management section.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {step === 'form' && (
          <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="flex space-x-3">
              <button
                onClick={handleClose}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={isLoading || !formData.email.trim()}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2" />
                    Sending...
                  </>
                ) : (
                  <>
                    <PaperAirplaneIcon className="w-4 h-4 mr-2" />
                    Send Invitation
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {step === 'success' && (
          <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <button
              onClick={handleClose}
              className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Done
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 