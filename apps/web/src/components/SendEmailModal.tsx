'use client';

import React, { useState, useEffect } from 'react';
import { Modal, Button, Input, Textarea } from '@deskbelt/ui';
import { 
  XMarkIcon, 
  EnvelopeIcon,
  PaperAirplaneIcon,
  BriefcaseIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { Job } from '@/types/Job';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/contexts/ToastContext';

interface SendEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: Job;
  defaultRecipient?: string;
  defaultSubject?: string;
}

interface EmailTemplate {
  id: string;
  title: string;
  subject: string;
  message: string;
  category: 'update' | 'appointment' | 'completion' | 'general';
}

const emailTemplates: EmailTemplate[] = [
  {
    id: 'job_update',
    title: 'Job Progress Update',
    subject: 'Update on your {job_title}',
    message: 'Hi {client_name},\n\nI wanted to give you a quick update on the progress of your {job_title}.\n\nI am currently working on {job_description} and everything is going smoothly. The work is progressing as planned and I expect to complete it on schedule.\n\nIf you have any questions or concerns, please don\'t hesitate to contact me.\n\nBest regards',
    category: 'update'
  },
  {
    id: 'appointment_confirmation',
    title: 'Appointment Confirmation',
    subject: 'Appointment confirmation for {job_title}',
    message: 'Hi {client_name},\n\nI wanted to confirm our appointment for your {job_title}.\n\nI\'ll be arriving as scheduled to work on {job_description}. Please ensure someone is available to provide access and discuss any specific requirements.\n\nIf you need to reschedule or have any questions, please let me know as soon as possible.\n\nLook forward to seeing you soon.',
    category: 'appointment'
  },
  {
    id: 'job_completion',
    title: 'Job Completion Notice',
    subject: 'Your {job_title} is now complete',
    message: 'Hi {client_name},\n\nI\'m pleased to inform you that your {job_title} has been completed successfully.\n\nThe work included {job_description} and everything has been tested and is working properly. I\'ve left the area clean and tidy.\n\nThank you for choosing my services. If you have any questions about the completed work or need any additional services in the future, please don\'t hesitate to contact me.\n\nBest regards',
    category: 'completion'
  },
  {
    id: 'general_communication',
    title: 'General Message',
    subject: 'Regarding your {job_title}',
    message: 'Hi {client_name},\n\nI hope this message finds you well.\n\nI wanted to reach out regarding your {job_title}.\n\n[Your message here]\n\nIf you have any questions or need any clarification, please feel free to contact me.\n\nBest regards',
    category: 'general'
  }
];

export const SendEmailModal: React.FC<SendEmailModalProps> = ({
  isOpen,
  onClose,
  job,
  defaultRecipient,
  defaultSubject
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [recipientEmail, setRecipientEmail] = useState('');
  const [recipientName, setRecipientName] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  
  const { authenticatedPost } = useAuthenticatedFetch();
  const { showSuccess, showError } = useToast();

  // Initialize form with job and client data
  useEffect(() => {
    if (isOpen && job) {
      setRecipientEmail(defaultRecipient || job.client?.email || '');
      setRecipientName(job.client?.name || '');
      setSubject(defaultSubject || `Regarding your ${job.title}`);
      setMessage('');
      setSelectedTemplate(null);
    }
  }, [isOpen, job, defaultRecipient, defaultSubject]);

  const handleTemplateSelect = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    
    // Replace placeholders in template
    const clientFirstName = recipientName.split(' ')[0] || 'there';
    const personalizedSubject = template.subject
      .replace('{job_title}', job.title)
      .replace('{client_name}', clientFirstName);
    
    const personalizedMessage = template.message
      .replace('{client_name}', clientFirstName)
      .replace('{job_title}', job.title)
      .replace('{job_description}', (job as any).description || 'the work');
    
    setSubject(personalizedSubject);
    setMessage(personalizedMessage);
  };

  const handleSendEmail = async () => {
    if (!recipientEmail.trim() || !subject.trim() || !message.trim()) {
      showError('Required Fields Missing', 'Please fill in all required fields');
      return;
    }

    setIsSending(true);

    try {
      console.log('📧 Sending job email...');
      console.log('📋 Job ID:', job.id);
      console.log('📧 Recipient:', recipientEmail.trim());
      console.log('📝 Subject:', subject.trim());
      
      const emailData = {
        recipientEmail: recipientEmail.trim(),
        recipientName: recipientName.trim() || undefined,
        subject: subject.trim(),
        message: message.trim(),
        businessName: 'DeskBelt', // You can get this from user profile later
        senderName: 'DeskBelt Team' // You can get this from user profile later
      };
      
      console.log('📤 Email payload:', emailData);
      
      const emailEndpoint = `/api/jobs/${job.id}/send-email`;
      console.log('🔗 Calling endpoint:', emailEndpoint);
      
      const response = await authenticatedPost(emailEndpoint, emailData);
      
      console.log('📥 Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        url: response.url
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Job email sent successfully!', data);
        
        // Show success toast
        showSuccess(
          'Email Sent Successfully!', 
          `Your message has been delivered to ${recipientName || recipientEmail} and logged to the job notes.`
        );
        
        // Reset form and close modal
        setSelectedTemplate(null);
        setMessage('');
        onClose();
        
      } else {
        let errorData: any = {};
        try {
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            errorData = await response.json();
          } else {
            // If response is not JSON, get text content
            const textContent = await response.text();
            errorData = { message: textContent || `HTTP ${response.status} ${response.statusText}` };
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
          errorData = { message: `HTTP ${response.status} ${response.statusText}` };
        }
        
        console.error('❌ Job email failed:', errorData);
        console.error('❌ Response status:', response.status);
        console.error('❌ Response headers:', Object.fromEntries(response.headers.entries()));
        
        // Show appropriate error toast based on status code
        if (response.status === 429) {
          showError('Rate Limit Reached', 'You can send up to 50 emails per hour. Please try again later.');
        } else if (response.status === 403) {
          showError('Access Denied', 'Please make sure you have permission to send emails for this job.');
        } else if (response.status === 400) {
          showError('Invalid Request', errorData.message || 'Please check your input and try again.');
        } else if (response.status === 404) {
          showError('Service Not Found', 'Email service not found. Please contact support.');
        } else if (errorData.message && errorData.message.includes('certificate')) {
          showError('Configuration Issue', 'Email service configuration issue. Please contact support or try again later.');
        } else {
          showError('Email Failed', errorData.message || 'Failed to send email. Please try again.');
        }
      }
      
    } catch (error) {
      console.error('💥 Network error sending job email:', error);
      showError('Network Error', 'Please check your connection and try again.');
    } finally {
      setIsSending(false);
    }
  };

  const handleClose = () => {
    onClose();
    // Reset state when closing
    setSelectedTemplate(null);
    setMessage('');
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'update': return '📋';
      case 'appointment': return '📅';
      case 'completion': return '✅';
      case 'general': return '💬';
      default: return '📧';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'update': return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400';
      case 'appointment': return 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400';
      case 'completion': return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400';
      case 'general': return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="xl">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                <EnvelopeIcon className="w-5 h-5 text-blue-600 dark:text-blue-500" />
              </div>
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  Send Email
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Communicate with your client about: {job.title}
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="p-2 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto max-h-[calc(90vh-120px)]">
          {!selectedTemplate ? (
            /* Template Selection */
            <div className="p-6">
              {/* Job Context */}
              <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-start space-x-3">
                  <BriefcaseIcon className="w-5 h-5 text-blue-600 dark:text-blue-500 mt-0.5" />
                  <div className="flex-1">
                    <h3 className="font-medium text-blue-900 dark:text-blue-100">{job.title}</h3>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">{(job as any).description || 'No description available'}</p>
                    <div className="flex items-center mt-2 text-sm text-blue-600 dark:text-blue-400">
                      <UserIcon className="w-4 h-4 mr-1" />
                      <span>{job.client?.name}</span>
                      {job.client?.email && (
                        <span className="ml-2">({job.client.email})</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Choose an email template:
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Select a template to get started, then customize it as needed.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {emailTemplates.map((template) => (
                  <button
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className="text-left p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/10 transition-all duration-200 group"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-lg">{getCategoryIcon(template.category)}</span>
                          <h4 className="font-medium text-gray-900 dark:text-white group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                            {template.title}
                          </h4>
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-3 line-clamp-2">
                          {template.message.substring(0, 120)}...
                        </p>
                        <div className="flex items-center">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(template.category)}`}>
                            {template.category}
                          </span>
                        </div>
                      </div>
                      <EnvelopeIcon className="w-5 h-5 text-gray-400 group-hover:text-blue-500 transition-colors flex-shrink-0 ml-3" />
                    </div>
                  </button>
                ))}
              </div>

              {/* Custom Email Option */}
              <div className="mt-4">
                <button
                  onClick={() => handleTemplateSelect({
                    id: 'custom',
                    title: 'Custom Email',
                    subject: `Regarding your ${job.title}`,
                    message: `Hi ${recipientName.split(' ')[0] || 'there'},\n\nI hope this message finds you well.\n\nI wanted to reach out regarding your ${job.title}.\n\n[Your message here]\n\nIf you have any questions, please feel free to contact me.\n\nBest regards`,
                    category: 'general'
                  })}
                  className="w-full text-left p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/10 transition-all duration-200 group"
                >
                  <div className="flex items-center justify-center space-x-2 text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                    <PaperAirplaneIcon className="w-5 h-5" />
                    <span className="font-medium">Write Custom Email</span>
                  </div>
                </button>
              </div>
            </div>
          ) : (
            /* Email Composition */
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                  Compose your email:
                </h3>
                <button
                  onClick={() => {
                    setSelectedTemplate(null);
                    setMessage('');
                  }}
                  className="text-sm text-blue-600 dark:text-blue-500 hover:text-blue-700 dark:hover:text-blue-400 transition-colors"
                >
                  ← Choose different template
                </button>
              </div>

              <div className="space-y-4">
                {/* Recipient */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Recipient Email *
                    </label>
                    <Input
                      type="email"
                      value={recipientEmail}
                      onChange={(e) => setRecipientEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Recipient Name
                    </label>
                    <Input
                      type="text"
                      value={recipientName}
                      onChange={(e) => setRecipientName(e.target.value)}
                      placeholder="Client Name"
                    />
                  </div>
                </div>

                {/* Subject */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Subject *
                  </label>
                  <Input
                    type="text"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    placeholder="Email subject"
                    required
                  />
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Message *
                  </label>
                  <Textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Write your message..."
                    rows={12}
                    className="w-full resize-none"
                    required
                  />
                  <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    {message.length}/5000 characters
                  </div>
                </div>

                {/* Send Button */}
                <div className="flex justify-end space-x-3 pt-4">
                  <Button
                    variant="outline"
                    onClick={handleClose}
                    disabled={isSending}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSendEmail}
                    disabled={!recipientEmail.trim() || !subject.trim() || !message.trim() || isSending}
                    className="flex items-center space-x-2"
                  >
                    {isSending ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Sending...</span>
                      </>
                    ) : (
                      <>
                        <PaperAirplaneIcon className="w-4 h-4" />
                        <span>Send Email</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};