'use client';
// Quick action bar for dashboard pages

import Link from 'next/link';
import { BriefcaseIcon, UserPlusIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { useDrawer } from '@/contexts/DrawerContext';

export function DashboardQuickActions() {
  const { openNewJobDrawer, openNewClientDrawer } = useDrawer();

  return (
    <div className="flex flex-col sm:flex-row gap-3">
      <button 
        onClick={() => openNewJobDrawer()}
        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center justify-center w-full sm:w-auto transition-colors font-medium"
      >
        <BriefcaseIcon className="w-4 h-4 mr-2" />
        New Job
      </button>
      <button 
        onClick={openNewClientDrawer}
        className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center justify-center w-full sm:w-auto transition-colors font-medium"
      >
        <UserPlusIcon className="w-4 h-4 mr-2" />
        New Client
              </button>
      </div>
  );
} 