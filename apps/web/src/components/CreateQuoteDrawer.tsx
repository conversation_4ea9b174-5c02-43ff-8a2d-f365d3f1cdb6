'use client';

import React, { useState, useEffect } from 'react';
import { JobDetails } from '@/types/Job';
import { QuoteParseResponse, CreateQuoteData } from '@/types/Document';
import { useProfile } from '@/hooks/useProfile';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { Textarea } from '@deskbelt/ui';
import { 
  XMarkIcon,
  DocumentTextIcon,
  SparklesIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  PencilIcon,
  LightBulbIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import { QuotePreviewModal } from './QuotePreviewModal';

interface CreateQuoteDrawerProps {
  job: JobDetails | null;
  isOpen: boolean;
  onClose: () => void;
  onQuoteCreated?: (quote: CreateQuoteData) => void;
  onQuoteSaved?: () => void;
  editingQuote?: any; // Quote object when editing existing quote
}

type DrawerStep = 'input' | 'parsing' | 'confirmation' | 'success' | 'error';

// Enhanced terms and conditions to protect tradesman
const DEFAULT_TERMS = `• Quote valid for 30 days from date of issue
• 10% deposit required before work commences
• Payment in stages: 10% deposit, 40% at halfway point, 50% upon completion
• All materials included unless otherwise specified
• Additional work outside agreed scope will be quoted separately
• Client must provide clear access to work areas and utilities
• Client responsible for obtaining all necessary permits and permissions
• Work may be suspended if payments are not received as scheduled
• Tradesman reserves right to charge for additional visits due to access issues
• All work guaranteed for 12 months from completion date
• Disputes to be resolved through local trade association mediation`;

export const CreateQuoteDrawer: React.FC<CreateQuoteDrawerProps> = ({
  job,
  isOpen,
  onClose,
  onQuoteCreated,
  onQuoteSaved,
  editingQuote
}) => {
  const { data: profile } = useProfile();
  const { authenticatedPost, authenticatedFetch } = useAuthenticatedFetch();
  const [step, setStep] = useState<DrawerStep>('input');
  const [quoteDescription, setQuoteDescription] = useState('');
  const [termsAndConditions, setTermsAndConditions] = useState(DEFAULT_TERMS);
  const [parsedQuote, setParsedQuote] = useState<QuoteParseResponse | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isProcessingAI, setIsProcessingAI] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showIntelligence, setShowIntelligence] = useState(false);
  const [intelligenceData, setIntelligenceData] = useState<any>(null);
  const [isLoadingIntelligence, setIsLoadingIntelligence] = useState(false);
  
  // Market rate intelligence state
  const [marketRateWorkType, setMarketRateWorkType] = useState<string>('');
  const [marketRateSuggestion, setMarketRateSuggestion] = useState<string>('');
  const [marketRateEstimatedRange, setMarketRateEstimatedRange] = useState<string>('');

  // Auto-populate quote description from job description when drawer opens
  useEffect(() => {
    if (isOpen && job?.description && !editingQuote) {
      generateQuoteDescription(job.description);
    }
  }, [isOpen, job?.description, editingQuote]);

  // Populate form when editing existing quote
  useEffect(() => {
    if (isOpen && editingQuote) {
      setQuoteDescription(editingQuote.details || '');
      setTermsAndConditions(editingQuote.terms || DEFAULT_TERMS);
      
      // Populate market rate fields if available
      setMarketRateWorkType(editingQuote.market_rate_work_type || '');
      setMarketRateSuggestion(editingQuote.market_rate_suggestion || '');
      setMarketRateEstimatedRange(editingQuote.market_rate_estimated_range || '');
    }
  }, [isOpen, editingQuote]);

  // Reset state when drawer opens/closes
  useEffect(() => {
    if (!isOpen) {
      setStep('input');
      setQuoteDescription('');
      setTermsAndConditions(DEFAULT_TERMS);
      setParsedQuote(null);
      setIsSubmitting(false);
      setIsProcessingAI(false);
      setError(null);
      setShowIntelligence(false);
      setIntelligenceData(null);
      setIsLoadingIntelligence(false);
      
      // Reset market rate state
      setMarketRateWorkType('');
      setMarketRateSuggestion('');
      setMarketRateEstimatedRange('');
    }
  }, [isOpen]);

  // Get contextual intelligence when quote description changes
  useEffect(() => {
    if (quoteDescription.length > 30) {
      // Add small delay to avoid triggering on every keystroke
      const timeoutId = setTimeout(() => {
        getContextualIntelligence();
      }, 1000); // 1 second delay after user stops typing

      return () => clearTimeout(timeoutId);
    } else {
      // Clear intelligence if description becomes too short
      setIntelligenceData(null);
      setShowIntelligence(false);
    }
  }, [quoteDescription]);

  // Get contextual intelligence based on AI analysis of quote content
  const getContextualIntelligence = async () => {
    setIsLoadingIntelligence(true);
    try {
      // Use AI to analyze the quote description and provide contextual insights
      const response = await authenticatedPost('/api/ai/quote-intelligence', { 
        description: quoteDescription,
        location: profile?.address?.includes('Manchester') ? 'Manchester' : 
                 profile?.address?.includes('Birmingham') ? 'Birmingham' : 
                 profile?.address?.includes('London') ? 'London' : 'UK',
        clientAddress: job?.client?.address || 'Unknown'
      });

      if (response.ok) {
        const result = await response.json();
        setIntelligenceData(result);
        setShowIntelligence(true); // Auto-show when intelligence is available
        
        // Capture market rate data for saving with quote
        if (result.marketRate) {
          setMarketRateWorkType(result.marketRate.workType || '');
          setMarketRateSuggestion(result.marketRate.suggestion || '');
          setMarketRateEstimatedRange(result.marketRate.estimatedRange || '');
        }
      } else {
        // If API fails, don't show intelligence - we want real AI analysis, not fallback keywords
        setIntelligenceData(null);
        setShowIntelligence(false);
      }
    } catch (error) {
      console.error('Intelligence fetch error:', error);
      // If AI fails, don't show intelligence - we want real AI analysis only
      setIntelligenceData(null);
      setShowIntelligence(false);
    } finally {
      setIsLoadingIntelligence(false);
    }
  };

  // Removed keyword-based analysis - using pure AI intelligence only

  // Generate quote description from job description using AI
  const generateQuoteDescription = async (jobDescription: string) => {
    if (!jobDescription.trim()) return;

    setIsProcessingAI(true);
    try {
      const response = await authenticatedPost('/api/ai/chat-response', { 
        prompt: `You are a professional UK tradesperson creating a quote based on a job description. 

Transform the job description into professional quote language that:
- Uses clear, professional terminology suitable for client-facing documents
- Focuses on deliverables and scope of work
- Maintains UK trade standards and terminology
- Is concise but comprehensive
- Sounds professional but not overly formal

Job Description: "${jobDescription}"

Return only the professional quote description, no additional formatting or JSON.`,
        input: jobDescription
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.message) {
          setQuoteDescription(result.message);
        } else {
          // Fallback: use job description as-is
          setQuoteDescription(jobDescription);
        }
      } else {
        // Fallback: use job description as-is
        setQuoteDescription(jobDescription);
      }
    } catch (error) {
      console.error('AI quote generation error:', error);
      // Fallback: use job description as-is
      setQuoteDescription(jobDescription);
    } finally {
      setIsProcessingAI(false);
    }
  };

  // AI function to professionalize the quote description
  const professionalizeQuoteWithAI = async (description: string): Promise<QuoteParseResponse> => {
    try {
      const response = await authenticatedPost('/api/ai/chat-response', { 
        prompt: `You are Dex, a professional UK tradesperson assistant. Take the user's quote description and make it more professional and client-ready while keeping all the original information intact.

Improve the description by:
- Making the language more professional and polished
- Ensuring proper UK trade terminology
- Maintaining all pricing information exactly as provided
- Keeping the same scope and details
- Making it sound confident and professional

Original Description: "${description}"

Return only the improved quote description, keeping all amounts and details exactly the same.`,
        input: description
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.message) {
          return {
            amount: 1, // Dummy amount to indicate success
            professionalizedDescription: result.message
          };
        }
      }
    } catch (error) {
      console.error('AI professionalization error:', error);
    }

    // Fallback: return original description
    return {
      amount: 1, // Dummy amount to indicate success
      professionalizedDescription: description
    };
  };

  const handleParseWithAI = async () => {
    if (!quoteDescription.trim()) return;

    setStep('parsing');
    setError(null);

    try {
      const response = await professionalizeQuoteWithAI(quoteDescription);
      setParsedQuote(response);
      
      if (response.professionalizedDescription) {
        setQuoteDescription(response.professionalizedDescription);
        setStep('input'); // Return to input with improved description
      } else {
        setStep('error');
        setError('Unable to improve quote description.');
      }
    } catch (err) {
      setStep('error');
      setError('Failed to process quote. Please try again.');
    }
  };

  const handleConfirmQuote = async () => {
    if (!job || !quoteDescription.trim()) return;

    setIsSubmitting(true);
    try {
      const url = editingQuote 
        ? `/api/jobs/${job.id}/quotes/${editingQuote.id}`
        : `/api/jobs/${job.id}/quotes`;
      
      const method = editingQuote ? 'PUT' : 'POST';
      
      const quoteData = {
        amount: 0, // No pricing calculations
        details: quoteDescription,
        terms: termsAndConditions,
        status: 'draft',
        
        // Market rate intelligence fields
        market_rate_work_type: marketRateWorkType || null,
        market_rate_suggestion: marketRateSuggestion || null,
        market_rate_estimated_range: marketRateEstimatedRange || null
      };

      const response = await authenticatedFetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(quoteData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log(editingQuote ? 'Quote updated:' : 'Quote saved:', result);
        onQuoteSaved?.(); // Refresh quotes list
        setStep('success');
        onQuoteCreated?.(result);
        
        // Auto-close after success
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        console.error(editingQuote ? 'Failed to update quote' : 'Failed to save quote');
        setStep('error');
        setError(editingQuote ? 'Failed to update quote' : 'Failed to save quote');
      }
    } catch (error) {
      console.error(editingQuote ? 'Error updating quote:' : 'Error saving quote:', error);
      setStep('error');
      setError(editingQuote ? 'Error updating quote' : 'Error saving quote');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewQuote = () => {
    setShowPreview(true);
  };

  const renderContent = () => {
    switch (step) {
      case 'input':
        return (
          <div className="space-y-6">
            {/* AI Processing Indicator */}
            {isProcessingAI && (
              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <div className="flex items-center space-x-3">
                  <SparklesIcon className="w-5 h-5 text-orange-600 dark:text-orange-400 animate-pulse" />
                  <div className="text-sm text-orange-800 dark:text-orange-200">
                    <p className="font-medium">Dex is preparing your quote description...</p>
                  </div>
                </div>
              </div>
            )}

            {/* Loading Intelligence */}
            {isLoadingIntelligence && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <div className="text-sm">
                    <span className="font-medium text-blue-900 dark:text-blue-100">Dex is analyzing your quote...</span>
                    <p className="text-blue-700 dark:text-blue-300 text-xs">Getting market insights and advice</p>
                  </div>
                </div>
              </div>
            )}

            {/* Contextual Intelligence */}
            {intelligenceData && (intelligenceData.marketRate || intelligenceData.materialAlert || intelligenceData.weatherAlert || intelligenceData.businessAdvice || intelligenceData.riskFactors) && (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">D</span>
                    </div>
                    <span className="text-sm font-medium text-blue-900 dark:text-blue-100">Dex's Advice</span>
                  </div>
                  <button
                    onClick={() => setShowIntelligence(!showIntelligence)}
                    className="p-1 text-blue-400 hover:text-blue-600 dark:hover:text-blue-300"
                  >
                    {showIntelligence ? (
                      <ChevronUpIcon className="w-4 h-4" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4" />
                    )}
                  </button>
                </div>

                {showIntelligence && (
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {/* Market Rate Intelligence */}
                    {intelligenceData.marketRate && (
                      <div className="bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-blue-900 dark:text-blue-100">
                              {intelligenceData.marketRate.workType} - Market Rate
                            </p>
                            <p className="text-blue-700 dark:text-blue-300">{intelligenceData.marketRate.suggestion}</p>
                            {intelligenceData.marketRate.estimatedRange && (
                              <p className="text-blue-600 dark:text-blue-400 text-xs mt-1">
                                Estimated: {intelligenceData.marketRate.estimatedRange}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Material Alert */}
                    {intelligenceData.materialAlert && (
                      <div className="bg-white dark:bg-gray-800 border border-amber-300 dark:border-amber-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-amber-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-amber-900 dark:text-amber-100">Material Update</p>
                            <p className="text-amber-700 dark:text-amber-300">{intelligenceData.materialAlert}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Weather Alert */}
                    {intelligenceData.weatherAlert && (
                      <div className="bg-white dark:bg-gray-800 border border-green-300 dark:border-green-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-green-900 dark:text-green-100">Weather Forecast</p>
                            <p className="text-green-700 dark:text-green-300">{intelligenceData.weatherAlert}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Business Advice */}
                    {intelligenceData.businessAdvice && intelligenceData.businessAdvice.length > 0 && (
                      <div className="bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-purple-900 dark:text-purple-100">Business Advice</p>
                            <ul className="text-purple-700 dark:text-purple-300 space-y-0.5 mt-1">
                              {intelligenceData.businessAdvice.map((advice: string, index: number) => (
                                <li key={index} className="text-xs">• {advice}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Risk Factors */}
                    {intelligenceData.riskFactors && intelligenceData.riskFactors.length > 0 && (
                      <div className="bg-white dark:bg-gray-800 border border-red-300 dark:border-red-600 rounded p-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div className="text-xs">
                            <p className="font-medium text-red-900 dark:text-red-100">Risk Factors</p>
                            <ul className="text-red-700 dark:text-red-300 space-y-0.5 mt-1">
                              {intelligenceData.riskFactors.map((risk: string, index: number) => (
                                <li key={index} className="text-xs">⚠️ {risk}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}



            {/* Quote Description */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-900 dark:text-white">
                  Quote Details
                </label>
                <PencilIcon className="w-4 h-4 text-gray-400 dark:text-gray-500" />
              </div>
              <Textarea
                value={`${quoteDescription}`}
                onChange={(e) => setQuoteDescription(e.target.value)}
                placeholder="Enter your quote details..."
                rows={6}
                maxLength={2000}
                className="w-full"
              />
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {quoteDescription.length}/2000 characters
              </div>
              
            </div>


            {/* Terms and Conditions */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-900 dark:text-white">
                  Terms & Conditions
                </label>
                <PencilIcon className="w-4 h-4 text-gray-400 dark:text-gray-500" />
              </div>
              <Textarea
                value={termsAndConditions}
                onChange={(e) => setTermsAndConditions(e.target.value)}
                placeholder="Enter terms and conditions..."
                rows={8}
                maxLength={2000}
                className="w-full"
              />
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {termsAndConditions.length}/2000 characters
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col space-y-3">
              <Button
                onClick={handleParseWithAI}
                disabled={!quoteDescription.trim()}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                <SparklesIcon className="w-4 h-4 mr-2" />
                Improve with Dex
              </Button>
              
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={handleViewQuote}
                  disabled={!quoteDescription.trim()}
                  className="flex-1 border-purple-300 text-purple-700 hover:bg-purple-50 dark:border-purple-600 dark:text-purple-400 dark:hover:bg-purple-900/20"
                >
                  <EyeIcon className="w-4 h-4 mr-2" />
                  Preview
                </Button>
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-800"
                >
                  Cancel
                </Button>
              </div>
              
              <Button
                onClick={handleConfirmQuote}
                disabled={!quoteDescription.trim() || isSubmitting}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckCircleIcon className="w-4 h-4 mr-2" />
                {isSubmitting 
                  ? (editingQuote ? 'Updating...' : 'Saving...') 
                  : (editingQuote ? 'Update Quote' : 'Save Quote')
                }
              </Button>
            </div>
          </div>
        );

      case 'parsing':
        return (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-4">
              <SparklesIcon className="w-8 h-8 text-blue-600 dark:text-blue-400 animate-pulse" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Improving Quote
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Dex is making your quote more professional...
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full mb-4">
              <CheckCircleIcon className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {editingQuote ? 'Quote Updated!' : 'Quote Created!'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Your quote has been {editingQuote ? 'updated' : 'saved'} successfully.
            </p>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Closing automatically...
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="space-y-6">
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
              <div className="flex items-start space-x-3">
                <ExclamationCircleIcon className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-red-800 dark:text-red-200 mb-1">
                    Error Processing Quote
                  </p>
                  <p className="text-red-700 dark:text-red-300">
                    {error}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setStep('input')}
                className="flex-1"
              >
                Try Again
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <QuotePreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        job={job}
        quoteDescription={quoteDescription}
        termsAndConditions={termsAndConditions}
        amount={0}
        marketRateWorkType={marketRateWorkType}
        marketRateSuggestion={marketRateSuggestion}
        marketRateEstimatedRange={marketRateEstimatedRange}
      />
      
      <Drawer
        isOpen={isOpen}
        onClose={onClose}
        side="right"
        size="xl"
        showCloseButton={false}
        className="top-16 h-[calc(100%-4rem)] md:left-1/2 md:-translate-x-1/2 md:right-auto"
      >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <DocumentTextIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <div>
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                {editingQuote ? 'Edit Quote' : 'Create Quote'}
              </h2>
              {job && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  For "{job.title}" • {job.client.name}
                </p>
              )}
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <XMarkIcon className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {renderContent()}
        </div>
      </div>
    </Drawer>
    </>
  );
}; 