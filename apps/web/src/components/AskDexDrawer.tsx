'use client';

import { useState, useRef, useEffect } from 'react';
import { XMarkIcon, PaperAirplaneIcon, TrashIcon, ArrowPathIcon, ArchiveBoxIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import { useDexChat } from '@/hooks/useDexChat';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { ChatContext } from '@/types/Chat';
import Image from 'next/image';

interface SampleQuestion {
  text: string;
  icon: string;
  category: string;
}

interface ArchivedChat {
  id: string;
  title: string;
  archived_at: string;
  message_count: number;
  preview: string;
}

interface AskDexDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  context?: ChatContext;
}

// Dex Avatar Component with fallback
const DexAvatar = ({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) => {
  const [imageError, setImageError] = useState(false);
  
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10', 
    lg: 'w-16 h-16'
  };
  
  const textSizes = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-2xl'
  };

  if (imageError) {
    return (
      <div className={`${sizeClasses[size]} bg-ai-500 rounded-full flex items-center justify-center flex-shrink-0`}>
        <span className={`text-white font-bold ${textSizes[size]}`}>D</span>
      </div>
    );
  }

  return (
    <div className={`${sizeClasses[size]} rounded-full overflow-hidden flex-shrink-0 bg-ai-500`}>
      <Image
        src="/avatar.png"
        alt="Dex Avatar"
        width={size === 'lg' ? 64 : size === 'md' ? 40 : 32}
        height={size === 'lg' ? 64 : size === 'md' ? 40 : 32}
        className="w-full h-full object-cover"
        onError={() => setImageError(true)}
      />
    </div>
  );
};

export default function AskDexDrawer({ isOpen, onClose, context }: AskDexDrawerProps) {
  const { authenticatedGet } = useAuthenticatedFetch();
  const [message, setMessage] = useState('');
  const [charCount, setCharCount] = useState(0);
  const [sampleQuestions, setSampleQuestions] = useState<SampleQuestion[]>([]);
  const [loadingSamples, setLoadingSamples] = useState(false);
  const [showArchives, setShowArchives] = useState(false);
  const [archivedChats, setArchivedChats] = useState<ArchivedChat[]>([]);
  const [loadingArchives, setLoadingArchives] = useState(false);
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [editingArchiveId, setEditingArchiveId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const { messages, isTyping, error, sendMessage, clearChat } = useDexChat(context);

  // Load sample questions when drawer opens
  useEffect(() => {
    if (isOpen && !showArchives && messages.length === 0) {
      loadSampleQuestions();
    }
  }, [isOpen, showArchives, messages.length]);

  // Load archived chats when switching to archives
  useEffect(() => {
    if (showArchives) {
      loadArchivedChats();
    }
  }, [showArchives]);

  const loadArchivedChats = async () => {
    setLoadingArchives(true);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const response = await fetch(`${apiUrl}/api/chat/archived`);
      
      if (response.ok) {
        const data = await response.json();
        setArchivedChats(data || []);
      }
    } catch (error) {
      console.error('Failed to load archived chats:', error);
    } finally {
      setLoadingArchives(false);
    }
  };

  const generateChatTitle = (messages: any[]) => {
    const firstUserMsg = messages.find(msg => msg.is_from_user && msg.message.length > 10);
    if (firstUserMsg) {
      const words = firstUserMsg.message.split(' ').slice(0, 4);
      return words.join(' ') + (firstUserMsg.message.split(' ').length > 4 ? '...' : '');
    }
    return `Chat from ${new Date().toLocaleDateString()}`;
  };

  const archiveCurrentChat = async () => {
    if (messages.length === 0) return;

    try {
      const title = generateChatTitle(messages);
      const preview = messages.find(msg => !msg.is_from_user)?.message || "Business consultation";
      
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const response = await fetch(`${apiUrl}/api/chat/archive`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title,
          preview,
          messages
        })
      });

      if (response.ok) {
        await clearChat();
        setShowClearDialog(false);
        if (showArchives) {
          loadArchivedChats();
        }
      }
    } catch (error) {
      console.error('Failed to archive chat:', error);
    }
  };

  const handleClearChat = async () => {
    await clearChat();
    setShowClearDialog(false);
  };

  const handleArchiveAction = () => {
    setShowClearDialog(true);
  };

  const updateArchiveTitle = async (archiveId: string, newTitle: string) => {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const response = await fetch(`${apiUrl}/api/chat/archived/${archiveId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: newTitle })
      });

      if (response.ok) {
        setArchivedChats(prev => 
          prev.map(chat => 
            chat.id === archiveId ? { ...chat, title: newTitle } : chat
          )
        );
        setEditingArchiveId(null);
        setEditingTitle('');
      }
    } catch (error) {
      console.error('Failed to update archive title:', error);
    }
  };

  const restoreArchivedChat = async (archiveId: string) => {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const response = await fetch(`${apiUrl}/api/chat/archived/${archiveId}/restore`, {
        method: 'POST'
      });

      if (response.ok) {
        setShowArchives(false);
      }
    } catch (error) {
      console.error('Failed to restore archived chat:', error);
    }
  };

  const deleteArchivedChat = async (archiveId: string) => {
    if (!window.confirm('Are you sure you want to permanently delete this archived chat?')) {
      return;
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const response = await fetch(`${apiUrl}/api/chat/archived/${archiveId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setArchivedChats(prev => prev.filter(chat => chat.id !== archiveId));
      }
    } catch (error) {
      console.error('Failed to delete archived chat:', error);
    }
  };

  const loadSampleQuestions = async () => {
    setLoadingSamples(true);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const response = await authenticatedGet('/api/ai/sample-questions');
      //const response = await authenticatedGet(`${apiUrl}/api/ai/sample-questions`);
      
      if (response.ok) {
        const data = await response.json();
        setSampleQuestions(data.questions || []);
      } else {
        setSampleQuestions([
          { text: "How should I price a kitchen rewiring job?", icon: "💰", category: "pricing" },
          { text: "What are standard payment terms for electrical work?", icon: "📋", category: "business" },
          { text: "How do I handle a difficult customer?", icon: "🤝", category: "customer" }
        ]);
      }
    } catch (error) {
      console.error('Failed to load sample questions:', error);
      setSampleQuestions([
        { text: "How should I price a kitchen rewiring job?", icon: "💰", category: "pricing" },
        { text: "What are standard payment terms for electrical work?", icon: "📋", category: "business" },
        { text: "How do I handle a difficult customer?", icon: "🤝", category: "customer" }
      ]);
    } finally {
      setLoadingSamples(false);
    }
  };

  const handleSampleQuestionClick = (question: string) => {
    setMessage(question);
    setTimeout(() => {
      handleSendMessage();
    }, 100);
  };

  const refreshSampleQuestions = () => {
    loadSampleQuestions();
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = Math.min(textareaRef.current.scrollHeight, 120) + 'px';
    }
  }, [message]);

  const handleSendMessage = async () => {
    if (!message.trim() || isTyping) return;
    
    await sendMessage(message);
    setMessage('');
    setCharCount(0);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= 2000) {
      setMessage(value);
      setCharCount(value.length);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-GB', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-25" onClick={onClose} />
      
      <div className="absolute right-0 top-0 h-full w-full max-w-lg bg-white dark:bg-gray-900 shadow-xl flex flex-col">
        {/* Header */}
        <div className="bg-ai-50 dark:bg-ai-900 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center space-x-3">
              <DexAvatar size="md" />
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">Ask Dex</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">Your business advisor</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {!showArchives && messages.length > 0 && (
                <button
                  onClick={handleArchiveAction}
                  className="px-3 py-1.5 bg-ai-100 dark:bg-ai-800 text-ai-700 dark:text-ai-300 text-sm rounded-md hover:bg-ai-200 dark:hover:bg-ai-700 transition-colors"
                  title="Start fresh chat"
                >
                  Start Fresh
                </button>
              )}
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>
          </div>
          
          {/* Toggle */}
          <div className="px-4 pb-3">
            <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => setShowArchives(false)}
                className={`flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  !showArchives 
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm' 
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <ChatBubbleLeftRightIcon className="w-4 h-4 mr-2" />
                Current Chat
              </button>
              <button
                onClick={() => setShowArchives(true)}
                className={`flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  showArchives 
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm' 
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <ArchiveBoxIcon className="w-4 h-4 mr-2" />
                Archives ({archivedChats.length})
              </button>
            </div>
          </div>
        </div>

        {/* Clear Dialog */}
        {showClearDialog && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 m-4 max-w-sm w-full">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Start Fresh Chat?
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                What would you like to do with your current conversation?
              </p>
              <div className="space-y-3">
                <button
                  onClick={archiveCurrentChat}
                  className="w-full px-4 py-2 bg-ai-500 text-white rounded-md hover:bg-ai-600 transition-colors"
                >
                  Archive current chat and start fresh
                </button>
                <button
                  onClick={handleClearChat}
                  className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                >
                  Clear current chat and start fresh
                </button>
                <button
                  onClick={() => setShowClearDialog(false)}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4" style={{
          backgroundImage: 'radial-gradient(circle, #e5e7eb 1px, transparent 1px)',
          backgroundSize: '20px 20px',
          backgroundPosition: '0 0, 10px 10px'
        }}>
          {!showArchives ? (
            <>
              {messages.length === 0 && !isTyping && (
                <div className="text-center py-8">
                  <div className="mx-auto mb-4">
                    <DexAvatar size="lg" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Hi! I'm Dex, your business advisor
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-sm mx-auto mb-6">
                    I'm here to help with job pricing, customer management, business advice, and trade-related questions. What would you like to discuss?
                  </p>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between mb-3">
                      <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Try asking about:</p>
                      <button
                        onClick={refreshSampleQuestions}
                        disabled={loadingSamples}
                        className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors disabled:opacity-50"
                        title="Get new suggestions"
                      >
                        <ArrowPathIcon className={`w-4 h-4 ${loadingSamples ? 'animate-spin' : ''}`} />
                      </button>
                    </div>
                    
                    {loadingSamples ? (
                      <div className="text-center py-4">
                        <div className="animate-spin w-5 h-5 border-2 border-ai-500 border-t-transparent rounded-full mx-auto"></div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">Loading suggestions...</p>
                      </div>
                    ) : (
                      sampleQuestions.map((question, index) => (
                        <button
                          key={index}
                          onClick={() => handleSampleQuestionClick(question.text)}
                          className="block w-full text-left px-4 py-3 text-sm bg-ai-50 dark:bg-ai-900 text-ai-700 dark:text-ai-300 rounded-lg hover:bg-ai-100 dark:hover:bg-ai-800 transition-colors border border-ai-100 dark:border-ai-800"
                        >
                          <span className="text-base mr-2">{question.icon}</span>
                          {question.text}
                        </button>
                      ))
                    )}
                  </div>
                </div>
              )}

              {messages.map((msg, index) => (
                <div key={index} className={`flex ${msg.is_from_user ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-sm px-4 py-2 rounded-2xl ${
                    msg.is_from_user
                      ? 'bg-blue-500 text-white rounded-br-md'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md'
                  }`}>
                    <p className="text-sm whitespace-pre-wrap">{msg.message}</p>
                    <p className={`text-xs mt-1 ${
                      msg.is_from_user 
                        ? 'text-blue-100' 
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {formatTime(msg.created_at)}
                    </p>
                  </div>
                </div>
              ))}

              {isTyping && (
                <div className="flex justify-start">
                  <div className="bg-gray-200 dark:bg-gray-700 px-4 py-2 rounded-2xl rounded-bl-md">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Dex is typing...</p>
                  </div>
                </div>
              )}

              {error && (
                <div className="text-center py-4">
                  <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
                </div>
              )}

              <div ref={messagesEndRef} />
            </>
          ) : (
            <div className="space-y-4">
              {loadingArchives ? (
                <div className="text-center py-8">
                  <div className="animate-spin w-8 h-8 border-2 border-ai-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Loading archived chats...</p>
                </div>
              ) : archivedChats.length === 0 ? (
                <div className="text-center py-8">
                  <ArchiveBoxIcon className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No archived chats yet
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    When you archive conversations with Dex, they'll appear here for future reference.
                  </p>
                </div>
              ) : (
                archivedChats.map((chat) => (
                  <div key={chat.id} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        {editingArchiveId === chat.id ? (
                          <div className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={editingTitle}
                              onChange={(e) => setEditingTitle(e.target.value)}
                              className="flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  updateArchiveTitle(chat.id, editingTitle);
                                }
                              }}
                              autoFocus
                            />
                            <button
                              onClick={() => updateArchiveTitle(chat.id, editingTitle)}
                              className="px-2 py-1 bg-ai-500 text-white text-xs rounded hover:bg-ai-600"
                            >
                              Save
                            </button>
                            <button
                              onClick={() => {
                                setEditingArchiveId(null);
                                setEditingTitle('');
                              }}
                              className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
                            >
                              Cancel
                            </button>
                          </div>
                        ) : (
                          <h4 
                            className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer hover:text-ai-600 dark:hover:text-ai-400"
                            onClick={() => {
                              setEditingArchiveId(chat.id);
                              setEditingTitle(chat.title);
                            }}
                          >
                            {chat.title}
                          </h4>
                        )}
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {chat.preview.substring(0, 100)}...
                        </p>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400 dark:text-gray-500">
                          <span>{new Date(chat.archived_at).toLocaleDateString()}</span>
                          <span>{chat.message_count} messages</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1 ml-4">
                        <button
                          onClick={() => restoreArchivedChat(chat.id)}
                          className="p-1.5 text-gray-400 hover:text-ai-600 dark:hover:text-ai-400 transition-colors"
                          title="Restore to current chat"
                        >
                          <ChatBubbleLeftRightIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => deleteArchivedChat(chat.id)}
                          className="p-1.5 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                          title="Delete permanently"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Input Area */}
        {!showArchives && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-end space-x-2">
              <div className="flex-1">
                <textarea
                  ref={textareaRef}
                  value={message}
                  onChange={handleMessageChange}
                  onKeyPress={handleKeyPress}
                  placeholder="Message..."
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-full bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-ai-500 focus:border-transparent resize-none"
                  rows={1}
                  style={{ minHeight: '44px', maxHeight: '120px' }}
                  disabled={isTyping}
                />
                {charCount > 1800 && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
                    {charCount}/2000
                  </p>
                )}
              </div>
              <button
                onClick={handleSendMessage}
                disabled={!message.trim() || isTyping}
                className="p-3 bg-ai-500 text-white rounded-full hover:bg-ai-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <PaperAirplaneIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 