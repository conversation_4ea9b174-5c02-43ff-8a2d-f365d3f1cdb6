'use client';

import React, { useState } from 'react';
import { Input, Field, Label, Fieldset, Legend } from '@headlessui/react';
import { CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { SectionHeader } from './SectionHeader';
import { CodeBlock } from './CodeBlock';

interface InputExample {
  type: string;
  label: string;
  placeholder: string;
}

interface FormExamples {
  inputs: InputExample[];
  states: string[];
}

interface FormSectionProps {
  examples: FormExamples;
  showCode?: boolean;
}

export const FormSection: React.FC<FormSectionProps> = ({
  examples,
  showCode = false
}) => {
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [validationState, setValidationState] = useState<Record<string, 'default' | 'error' | 'success'>>({});

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }));
    
    // Simple validation example
    if (key === 'email') {
      const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      setValidationState(prev => ({ ...prev, [key]: value ? (isValid ? 'success' : 'error') : 'default' }));
    } else if (key === 'password') {
      const isValid = value.length >= 8;
      setValidationState(prev => ({ ...prev, [key]: value ? (isValid ? 'success' : 'error') : 'default' }));
    } else {
      setValidationState(prev => ({ ...prev, [key]: value ? 'success' : 'default' }));
    }
  };

  const getInputClasses = (state: 'default' | 'error' | 'success' | 'disabled' = 'default') => {
    const baseClasses = "block w-full px-4 py-3 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 border rounded-xl shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none transition-all duration-200";
    
    switch (state) {
      case 'error':
        return `${baseClasses} border-red-300 dark:border-red-600 focus:ring-2 focus:ring-red-500/20 focus:border-red-500`;
      case 'success':
        return `${baseClasses} border-green-300 dark:border-green-600 focus:ring-2 focus:ring-green-500/20 focus:border-green-500`;
      case 'disabled':
        return `${baseClasses} border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 cursor-not-allowed opacity-60`;
      default:
        return `${baseClasses} border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 focus:scale-[1.01]`;
    }
  };

  const inputStates = [
    { name: 'Default', state: 'default' as const },
    { name: 'Focus', state: 'default' as const, focused: true },
    { name: 'Error', state: 'error' as const },
    { name: 'Success', state: 'success' as const },
    { name: 'Disabled', state: 'disabled' as const }
  ];

  const codeExample = `// Form Components with HeadlessUI
import { Input, Field, Label, Fieldset, Legend } from '@headlessui/react';

// Basic Input Field
<Field>
  <Label className="text-label">Email Address</Label>
  <Input
    type="email"
    className="input"
    placeholder="Enter your email"
  />
</Field>

// Input with Validation States
<Field>
  <Label className="text-label">Password</Label>
  <Input
    type="password"
    className={clsx(
      "input",
      hasError && "border-red-300 focus:ring-red-500/20 focus:border-red-500",
      isValid && "border-green-300 focus:ring-green-500/20 focus:border-green-500"
    )}
    placeholder="Enter your password"
  />
  {hasError && (
    <p className="mt-1 text-sm text-red-600 flex items-center">
      <ExclamationCircleIcon className="w-4 h-4 mr-1" />
      Password must be at least 8 characters
    </p>
  )}
</Field>

// Floating Label Input
<Field className="relative">
  <Input
    type="text"
    className="input-floating peer"
    placeholder=" "
  />
  <Label className="label-floating">
    Full Name
  </Label>
</Field>

// Fieldset for Grouped Fields
<Fieldset className="space-y-4">
  <Legend className="text-lg font-semibold text-gray-900 dark:text-white">
    Personal Information
  </Legend>
  
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <Field>
      <Label className="text-label">First Name</Label>
      <Input type="text" className="input" />
    </Field>
    
    <Field>
      <Label className="text-label">Last Name</Label>
      <Input type="text" className="input" />
    </Field>
  </div>
</Fieldset>`;

  return (
    <div>
      <SectionHeader
        title="Form Elements"
        description="Accessible form components with validation states and modern styling"
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        }
      />

      {/* Input States */}
      <div className="mb-12">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Input States
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {inputStates.map((state, index) => (
            <div key={index} className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {state.name}
              </label>
              <Input
                type="text"
                className={getInputClasses(state.state)}
                placeholder="Enter text..."
                disabled={state.state === 'disabled'}
                autoFocus={state.focused}
              />
              {state.state === 'error' && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center mt-1">
                  <ExclamationCircleIcon className="w-4 h-4 mr-1" />
                  This field has an error
                </p>
              )}
              {state.state === 'success' && (
                <p className="text-sm text-green-600 dark:text-green-400 flex items-center mt-1">
                  <CheckCircleIcon className="w-4 h-4 mr-1" />
                  Input is valid
                </p>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Interactive Form Example */}
      <div className="mb-12">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Interactive Form Example
        </h3>
        <div className="showcase-glass-card max-w-2xl">
          <Fieldset className="space-y-6">
            <Legend className="text-xl font-semibold text-gray-900 dark:text-white">
              Contact Information
            </Legend>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Field>
                <Label className="text-label">Full Name</Label>
                <Input
                  type="text"
                  className={getInputClasses(validationState.fullName)}
                  placeholder="Enter your full name"
                  value={formData.fullName || ''}
                  onChange={(e) => handleInputChange('fullName', e.target.value)}
                />
                {validationState.fullName === 'success' && (
                  <p className="text-sm text-green-600 dark:text-green-400 flex items-center mt-1">
                    <CheckCircleIcon className="w-4 h-4 mr-1" />
                    Looks good!
                  </p>
                )}
              </Field>

              <Field>
                <Label className="text-label">Email Address</Label>
                <Input
                  type="email"
                  className={getInputClasses(validationState.email)}
                  placeholder="Enter your email"
                  value={formData.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                />
                {validationState.email === 'error' && (
                  <p className="text-sm text-red-600 dark:text-red-400 flex items-center mt-1">
                    <ExclamationCircleIcon className="w-4 h-4 mr-1" />
                    Please enter a valid email address
                  </p>
                )}
                {validationState.email === 'success' && (
                  <p className="text-sm text-green-600 dark:text-green-400 flex items-center mt-1">
                    <CheckCircleIcon className="w-4 h-4 mr-1" />
                    Email format is valid
                  </p>
                )}
              </Field>
            </div>

            <Field>
              <Label className="text-label">Password</Label>
              <Input
                type="password"
                className={getInputClasses(validationState.password)}
                placeholder="Enter your password"
                value={formData.password || ''}
                onChange={(e) => handleInputChange('password', e.target.value)}
              />
              {validationState.password === 'error' && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center mt-1">
                  <ExclamationCircleIcon className="w-4 h-4 mr-1" />
                  Password must be at least 8 characters long
                </p>
              )}
              {validationState.password === 'success' && (
                <p className="text-sm text-green-600 dark:text-green-400 flex items-center mt-1">
                  <CheckCircleIcon className="w-4 h-4 mr-1" />
                  Password strength is good
                </p>
              )}
            </Field>
          </Fieldset>
        </div>
      </div>

      {/* Floating Label Example */}
      <div className="mb-12">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Floating Label Inputs
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl">
          <Field className="relative">
            <Input
              type="text"
              className="input-floating peer"
              placeholder=" "
            />
            <Label className="label-floating">
              First Name
            </Label>
          </Field>

          <Field className="relative">
            <Input
              type="email"
              className="input-floating peer"
              placeholder=" "
            />
            <Label className="label-floating">
              Email Address
            </Label>
          </Field>
        </div>
      </div>

      {showCode && (
        <CodeBlock
          code={codeExample}
          title="Form Component Usage"
        />
      )}
    </div>
  );
};