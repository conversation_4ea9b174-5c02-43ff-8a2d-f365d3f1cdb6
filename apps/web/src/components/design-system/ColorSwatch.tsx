'use client';

import React, { useState } from 'react';
import { Square2StackIcon } from '@heroicons/react/24/outline';

interface ColorSwatchProps {
  color: string;
  name?: string;
  showName?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const ColorSwatch: React.FC<ColorSwatchProps> = ({
  color,
  name,
  showName = true,
  size = 'md',
  className = ''
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(color);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy color:', err);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-12 h-12';
      case 'lg':
        return 'w-20 h-20';
      default:
        return 'w-16 h-16';
    }
  };

  return (
    <div className={`group ${className}`}>
      <div
        className={`
          ${getSizeClasses()}
          rounded-xl shadow-md transition-all duration-200 cursor-pointer
          border-2 border-white dark:border-gray-700
          hover:scale-110 hover:shadow-lg
          relative overflow-hidden
        `}
        style={{ backgroundColor: color }}
        onClick={handleCopy}
        title={`Click to copy ${color}`}
      >
        {/* Copy overlay */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
          <Square2StackIcon className="w-4 h-4 text-white" />
        </div>
        
        {/* Copied feedback */}
        {copied && (
          <div className="absolute inset-0 bg-green-500/90 flex items-center justify-center animate-fade-in">
            <span className="text-white text-xs font-medium">Copied!</span>
          </div>
        )}
      </div>
      
      {showName && (
        <div className="mt-2 text-center">
          {name && (
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {name}
            </div>
          )}
          <div className="text-xs text-gray-500 dark:text-gray-400 font-mono">
            {color.toUpperCase()}
          </div>
        </div>
      )}
    </div>
  );
};