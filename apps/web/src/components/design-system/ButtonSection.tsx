'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@headlessui/react';
import { SectionHeader } from './SectionHeader';
import { CodeBlock } from './CodeBlock';

interface ButtonExample {
  variant: 'primary' | 'secondary' | 'ghost' | 'danger';
  size: 'sm' | 'md' | 'lg';
  text: string;
}

interface ButtonSectionProps {
  examples: ButtonExample[];
  showCode?: boolean;
}

export const ButtonSection: React.FC<ButtonSectionProps> = ({
  examples,
  showCode = false
}) => {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  const handleClick = (key: string) => {
    setLoadingStates(prev => ({ ...prev, [key]: true }));
    setTimeout(() => {
      setLoadingStates(prev => ({ ...prev, [key]: false }));
    }, 2000);
  };

  const getButtonClasses = (variant: string, size: string, isLoading: boolean) => {
    const baseClasses = "relative overflow-hidden transition-all duration-200 font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";
    
    // Size classes
    const sizeClasses = {
      sm: "px-3 py-2 text-sm",
      md: "px-4 py-2.5 text-sm",
      lg: "px-6 py-3 text-base"
    };
    
    // Variant classes
    const variantClasses = {
      primary: "bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-[1.02] active:scale-[0.98] focus:ring-primary-500/20",
      secondary: "bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 shadow-sm hover:shadow-md transform hover:-translate-y-0.5 hover:scale-[1.02] active:scale-[0.98] focus:ring-gray-500/20",
      ghost: "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 hover:scale-[1.02] active:scale-[0.98] focus:ring-gray-500/20",
      danger: "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-[1.02] active:scale-[0.98] focus:ring-red-500/20"
    };
    
    return `${baseClasses} ${sizeClasses[size as keyof typeof sizeClasses]} ${variantClasses[variant as keyof typeof variantClasses]} ${isLoading ? 'cursor-not-allowed' : ''}`;
  };

  const buttonVariants = [
    { name: 'Primary', variant: 'primary', description: 'Main call-to-action buttons' },
    { name: 'Secondary', variant: 'secondary', description: 'Secondary actions and alternatives' },
    { name: 'Ghost', variant: 'ghost', description: 'Subtle actions and navigation' },
    { name: 'Danger', variant: 'danger', description: 'Destructive actions and warnings' }
  ];

  const buttonSizes = [
    { name: 'Small', size: 'sm', description: 'Compact spaces and secondary actions' },
    { name: 'Medium', size: 'md', description: 'Standard size for most use cases' },
    { name: 'Large', size: 'lg', description: 'Prominent actions and hero sections' }
  ];

  const buttonStates = [
    { name: 'Default', state: 'default' },
    { name: 'Hover', state: 'hover' },
    { name: 'Active', state: 'active' },
    { name: 'Disabled', state: 'disabled' },
    { name: 'Loading', state: 'loading' }
  ];

  const codeExample = `// Button Component Usage
import { Button } from '@headlessui/react';

// Primary Button
<Button className="btn-primary">
  Primary Action
</Button>

// Secondary Button
<Button className="btn-secondary">
  Secondary Action
</Button>

// Ghost Button
<Button className="btn-ghost">
  Ghost Action
</Button>

// Danger Button
<Button className="btn-danger">
  Danger Action
</Button>

// With Loading State
<Button 
  className="btn-primary"
  disabled={isLoading}
>
  {isLoading ? (
    <>
      <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      Loading...
    </>
  ) : (
    'Submit'
  )}
</Button>`;

  return (
    <div>
      <SectionHeader
        title="Button Components"
        description="Interactive button components with various styles, sizes, and states"
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
          </svg>
        }
      />

      {/* Button Variants */}
      <div className="mb-12">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Button Variants
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {buttonVariants.map((variant) => (
            <div key={variant.variant} className="showcase-glass-card">
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-1">
                  {variant.name}
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {variant.description}
                </p>
              </div>
              
              <div className="flex flex-wrap gap-3">
                {buttonSizes.map((size) => {
                  const key = `${variant.variant}-${size.size}`;
                  const isLoading = loadingStates[key];
                  
                  return (
                    <Button
                      key={key}
                      className={getButtonClasses(variant.variant, size.size, isLoading)}
                      onClick={() => handleClick(key)}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Loading...
                        </>
                      ) : (
                        `${variant.name} ${size.name}`
                      )}
                    </Button>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Button Sizes */}
      <div className="mb-12">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Button Sizes
        </h3>
        <div className="showcase-glass-card">
          <div className="flex flex-wrap items-center gap-4">
            {buttonSizes.map((size) => (
              <div key={size.size} className="text-center">
                <Button className={getButtonClasses('primary', size.size, false)}>
                  {size.name}
                </Button>
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  {size.description}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Button States */}
      <div className="mb-12">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Button States
        </h3>
        <div className="showcase-glass-card">
          <div className="flex flex-wrap items-center gap-4">
            <Button className={getButtonClasses('primary', 'md', false)}>
              Default
            </Button>
            <Button className={`${getButtonClasses('primary', 'md', false)} hover:from-primary-700 hover:to-primary-800`}>
              Hover
            </Button>
            <Button className={`${getButtonClasses('primary', 'md', false)} scale-[0.98]`}>
              Active
            </Button>
            <Button className={getButtonClasses('primary', 'md', false)} disabled>
              Disabled
            </Button>
            <Button className={getButtonClasses('primary', 'md', true)} disabled>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading
            </Button>
          </div>
        </div>
      </div>

      {showCode && (
        <CodeBlock
          code={codeExample}
          title="Button Usage Examples"
        />
      )}
    </div>
  );
};