'use client';

import React from 'react';
import { LanguageIcon } from '@heroicons/react/24/outline';
import { SectionHeader } from './SectionHeader';
import { CodeBlock } from './CodeBlock';

interface TypographyExamples {
  display: string;
  headline: string;
  title: string;
  subtitle: string;
  body: string;
  caption: string;
  label: string;
  small: string;
}

interface TypographySectionProps {
  examples: TypographyExamples;
  showCode?: boolean;
}

export const TypographySection: React.FC<TypographySectionProps> = ({
  examples,
  showCode = false
}) => {
  const typographyItems = [
    {
      name: 'Display',
      className: 'text-display',
      text: examples.display,
      description: '36px / Bold / -0.025em',
      usage: 'Large page titles and hero text'
    },
    {
      name: 'Headline',
      className: 'text-headline',
      text: examples.headline,
      description: '30px / Semibold / -0.025em',
      usage: 'Page titles and major section headers'
    },
    {
      name: 'Title',
      className: 'text-title',
      text: examples.title,
      description: '24px / Semibold',
      usage: 'Section titles and card headers'
    },
    {
      name: 'Subtitle',
      className: 'text-subtitle',
      text: examples.subtitle,
      description: '20px / Medium',
      usage: 'Secondary headings'
    },
    {
      name: 'Body',
      className: 'text-body',
      text: examples.body,
      description: '16px / Normal',
      usage: 'Standard text content'
    },
    {
      name: 'Caption',
      className: 'text-caption',
      text: examples.caption,
      description: '14px / Medium / Uppercase',
      usage: 'Small descriptive text and metadata'
    },
    {
      name: 'Label',
      className: 'text-label',
      text: examples.label,
      description: '14px / Medium',
      usage: 'Form labels and UI labels'
    },
    {
      name: 'Small',
      className: 'text-small',
      text: examples.small,
      description: '12px / Normal',
      usage: 'Fine print and secondary information'
    }
  ];

  const codeExample = `// Typography Classes
<h1 className="text-display">Display Text</h1>
<h2 className="text-headline">Headline Text</h2>
<h3 className="text-title">Title Text</h3>
<h4 className="text-subtitle">Subtitle Text</h4>
<p className="text-body">Body text for content</p>
<span className="text-caption">Caption Text</span>
<label className="text-label">Form Label</label>
<small className="text-small">Small text</small>`;

  return (
    <div>
      <SectionHeader
        title="Typography"
        description="Semantic typography scale with consistent hierarchy and spacing"
        icon={<LanguageIcon className="w-5 h-5" />}
      />

      <div className="grid gap-6">
        {typographyItems.map((item) => (
          <div
            key={item.name}
            className="typography-showcase-item group"
          >
            <div className="flex items-start justify-between mb-4">
              <div>
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-1">
                  {item.name}
                </h4>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {item.description}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  {item.usage}
                </p>
              </div>
              
              <div className="text-xs font-mono text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                .{item.className.replace('text-', '')}
              </div>
            </div>
            
            <div className={`${item.className} transition-all duration-200 group-hover:text-primary-600 dark:group-hover:text-primary-400`}>
              {item.text}
            </div>
          </div>
        ))}
      </div>

      {showCode && (
        <div className="mt-8">
          <CodeBlock
            code={codeExample}
            title="Typography Usage"
          />
        </div>
      )}
    </div>
  );
};