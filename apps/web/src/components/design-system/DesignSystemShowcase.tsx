'use client';

import React, { useState } from 'react';
import { Switch } from '@headlessui/react';
import { SunIcon, MoonIcon, CodeBracketIcon, EyeIcon } from '@heroicons/react/24/outline';
import { Tabs } from '../Tabs';
import { TypographySection } from './TypographySection';
import { ColorSection } from './ColorSection';
import { ButtonSection } from './ButtonSection';
import { CardSection } from './CardSection';
import { FormSection } from './FormSection';

interface DesignSystemShowcaseProps {
  currentTheme: 'light' | 'dark';
  currentSection: string;
  showCode: boolean;
  showVariations: boolean;
  typographyExamples: any;
  colorPalette: any;
  buttonExamples: any[];
  cardExamples: any[];
  formExamples: any;
  navigationItems: any[];
  feedbackExamples: any[];
  spacingTokens: any[];
  shadowExamples: any[];
}

export const DesignSystemShowcase: React.FC<DesignSystemShowcaseProps> = ({
  currentTheme,
  currentSection,
  showCode: initialShowCode,
  showVariations: initialShowVariations,
  typographyExamples,
  colorPalette,
  buttonExamples,
  cardExamples,
  formExamples,
  navigationItems,
  feedbackExamples,
  spacingTokens,
  shadowExamples
}) => {
  const [isDarkMode, setIsDarkMode] = useState(currentTheme === 'dark');
  const [showCode, setShowCode] = useState(initialShowCode);
  const [showVariations, setShowVariations] = useState(initialShowVariations);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    document.documentElement.classList.toggle('dark');
  };

  const tabs = [
    {
      id: 'typography',
      label: 'Typography',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
      content: <TypographySection examples={typographyExamples} showCode={showCode} />,
      color: 'blue' as const
    },
    {
      id: 'colors',
      label: 'Colors',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
        </svg>
      ),
      content: <ColorSection palette={colorPalette} showCode={showCode} />,
      color: 'green' as const
    },
    {
      id: 'buttons',
      label: 'Buttons',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
        </svg>
      ),
      content: <ButtonSection examples={buttonExamples} showCode={showCode} />,
      color: 'purple' as const
    },
    {
      id: 'cards',
      label: 'Cards',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      content: <CardSection examples={cardExamples} showCode={showCode} />,
      color: 'orange' as const
    },
    {
      id: 'forms',
      label: 'Forms',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      content: <FormSection examples={formExamples} showCode={showCode} />,
      color: 'indigo' as const
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-950 dark:via-gray-900 dark:to-blue-950">
      {/* Header */}
      <div className="sticky top-0 z-40 bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Design System Showcase
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Enterprise-grade components and design patterns
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Code Toggle */}
              <div className="flex items-center space-x-2">
                <CodeBracketIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <Switch
                  checked={showCode}
                  onChange={setShowCode}
                  className={`${
                    showCode ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'
                  } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2`}
                >
                  <span
                    className={`${
                      showCode ? 'translate-x-6' : 'translate-x-1'
                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                  />
                </Switch>
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Show Code
                </span>
              </div>
              
              {/* Variations Toggle */}
              <div className="flex items-center space-x-2">
                <EyeIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <Switch
                  checked={showVariations}
                  onChange={setShowVariations}
                  className={`${
                    showVariations ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'
                  } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2`}
                >
                  <span
                    className={`${
                      showVariations ? 'translate-x-6' : 'translate-x-1'
                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                  />
                </Switch>
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Variations
                </span>
              </div>
              
              {/* Theme Toggle */}
              <div className="flex items-center space-x-2">
                <SunIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <Switch
                  checked={isDarkMode}
                  onChange={toggleTheme}
                  className={`${
                    isDarkMode ? 'bg-primary-600' : 'bg-gray-200'
                  } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2`}
                >
                  <span
                    className={`${
                      isDarkMode ? 'translate-x-6' : 'translate-x-1'
                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                  />
                </Switch>
                <MoonIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <Tabs
          tabs={tabs}
          defaultTab="typography"
          variant="default"
          className="space-y-8"
        />
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Enterprise Design System
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Built with modern web technologies for scalable, accessible, and beautiful user interfaces.
            </p>
            <div className="mt-4 flex items-center justify-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
              <span>React 19</span>
              <span>•</span>
              <span>TypeScript</span>
              <span>•</span>
              <span>Tailwind CSS</span>
              <span>•</span>
              <span>HeadlessUI</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};