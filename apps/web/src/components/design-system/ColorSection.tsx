'use client';

import React from 'react';
import { EyeDropperIcon } from '@heroicons/react/24/outline';
import { SectionHeader } from './SectionHeader';
import { ColorSwatch } from './ColorSwatch';
import { CodeBlock } from './CodeBlock';

interface ColorPalette {
  primary: string[];
  secondary: string[];
  success: string[];
  warning: string[];
  error: string[];
}

interface ColorSectionProps {
  palette: ColorPalette;
  showCode?: boolean;
}

export const ColorSection: React.FC<ColorSectionProps> = ({
  palette,
  showCode = false
}) => {
  const colorGroups = [
    {
      name: 'Primary',
      description: 'Brand colors for primary actions and emphasis',
      colors: palette.primary,
      shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900']
    },
    {
      name: 'Secondary',
      description: 'Neutral colors for text, backgrounds, and borders',
      colors: palette.secondary,
      shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900']
    },
    {
      name: 'Success',
      description: 'Colors for positive states and success messages',
      colors: palette.success,
      shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900']
    },
    {
      name: 'Warning',
      description: 'Colors for warning states and attention',
      colors: palette.warning,
      shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900']
    },
    {
      name: 'Error',
      description: 'Colors for error states and destructive actions',
      colors: palette.error,
      shades: ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900']
    }
  ];

  const codeExample = `// Color Usage Examples
<div className="bg-primary-500 text-white">Primary Background</div>
<div className="text-primary-600">Primary Text</div>
<div className="border-primary-300">Primary Border</div>

// Semantic Colors
<div className="bg-success-100 text-success-800">Success State</div>
<div className="bg-warning-100 text-warning-800">Warning State</div>
<div className="bg-error-100 text-error-800">Error State</div>

// Dark Mode Support
<div className="bg-primary-500 dark:bg-primary-600">
  Responsive to theme
</div>`;

  return (
    <div>
      <SectionHeader
        title="Color Palette"
        description="Semantic color system with consistent scales and accessibility"
        icon={<EyeDropperIcon className="w-5 h-5" />}
      />

      <div className="space-y-12">
        {colorGroups.map((group) => (
          <div key={group.name}>
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {group.name}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {group.description}
              </p>
            </div>
            
            <div className="grid grid-cols-5 sm:grid-cols-10 gap-4">
              {group.colors.map((color, index) => (
                <ColorSwatch
                  key={index}
                  color={color}
                  name={group.shades[index]}
                  size="md"
                />
              ))}
            </div>
          </div>
        ))}
      </div>

      {showCode && (
        <div className="mt-12">
          <CodeBlock
            code={codeExample}
            title="Color Usage Examples"
          />
        </div>
      )}
    </div>
  );
};