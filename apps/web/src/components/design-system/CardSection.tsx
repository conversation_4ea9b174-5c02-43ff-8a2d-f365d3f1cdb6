'use client';

import React from 'react';
import { SectionHeader } from './SectionHeader';
import { CodeBlock } from './CodeBlock';

interface CardExample {
  title: string;
  description: string;
  type: 'basic' | 'interactive' | 'glass';
}

interface CardSectionProps {
  examples: CardExample[];
  showCode?: boolean;
}

export const CardSection: React.FC<CardSectionProps> = ({
  examples,
  showCode = false
}) => {
  const cardVariants = [
    {
      name: 'Basic Card',
      description: 'Standard card with subtle elevation and clean borders',
      className: 'bg-white dark:bg-gray-800 rounded-2xl shadow-soft border border-gray-100 dark:border-gray-700 p-6 transition-all duration-200',
      content: (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Basic Card
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            This is a basic card component with standard styling and subtle shadows.
          </p>
        </div>
      )
    },
    {
      name: 'Interactive Card',
      description: 'Hoverable card with enhanced animations and interactions',
      className: 'card-interactive group cursor-pointer',
      content: (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
            Interactive Card
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            This card responds to hover with smooth animations and color transitions.
          </p>
          <div className="mt-4 flex items-center text-primary-600 dark:text-primary-400 text-sm font-medium">
            <span>Click to interact</span>
            <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </div>
      )
    },
    {
      name: 'Glass Card',
      description: 'Modern glassmorphism effect with backdrop blur',
      className: 'showcase-glass-card hover:scale-[1.01] transition-all duration-300',
      content: (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Glass Effect Card
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Modern glassmorphism design with backdrop blur and translucent background.
          </p>
          <div className="mt-4 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300">
            Premium Design
          </div>
        </div>
      )
    },
    {
      name: 'Elevated Card',
      description: 'Card with stronger shadows and hover lift effect',
      className: 'bg-white dark:bg-gray-800 rounded-2xl shadow-medium hover:shadow-strong border border-gray-100 dark:border-gray-700 p-6 transform hover:-translate-y-2 hover:scale-[1.02] transition-all duration-300',
      content: (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Elevated Card
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            This card has enhanced elevation with stronger shadows and lift animations.
          </p>
          <div className="mt-4 flex items-center justify-between">
            <span className="text-xs text-gray-500 dark:text-gray-400">Hover for effect</span>
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
        </div>
      )
    }
  ];

  const codeExample = `// Card Component Examples

// Basic Card
<div className="bg-white dark:bg-gray-800 rounded-2xl shadow-soft border border-gray-100 dark:border-gray-700 p-6">
  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
    Card Title
  </h3>
  <p className="text-gray-600 dark:text-gray-400">
    Card content goes here...
  </p>
</div>

// Interactive Card
<div className="card-interactive group cursor-pointer">
  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 transition-colors">
    Interactive Card
  </h3>
  <p className="text-gray-600 dark:text-gray-400">
    This card responds to hover interactions.
  </p>
</div>

// Glass Effect Card
<div className="showcase-glass-card">
  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
    Glass Card
  </h3>
  <p className="text-gray-600 dark:text-gray-400">
    Modern glassmorphism design.
  </p>
</div>

// Elevated Card
<div className="bg-white dark:bg-gray-800 rounded-2xl shadow-medium hover:shadow-strong border border-gray-100 dark:border-gray-700 p-6 transform hover:-translate-y-2 hover:scale-[1.02] transition-all duration-300">
  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
    Elevated Card
  </h3>
  <p className="text-gray-600 dark:text-gray-400">
    Enhanced elevation with lift effects.
  </p>
</div>`;

  return (
    <div>
      <SectionHeader
        title="Card Components"
        description="Flexible card layouts with various elevations and interactive states"
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        }
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {cardVariants.map((variant, index) => (
          <div key={index} className="space-y-4">
            <div>
              <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-1">
                {variant.name}
              </h4>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {variant.description}
              </p>
            </div>
            
            <div className={variant.className}>
              {variant.content}
            </div>
          </div>
        ))}
      </div>

      {/* Card with Content Examples */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Content Examples
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Stats Card */}
          <div className="card-interactive">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Total Revenue
              </h3>
              <div className="w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/20 flex items-center justify-center">
                <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              $24,780
            </div>
            <div className="flex items-center text-sm">
              <span className="text-green-600 dark:text-green-400 font-medium">+12.5%</span>
              <span className="text-gray-500 dark:text-gray-400 ml-2">from last month</span>
            </div>
          </div>

          {/* Feature Card */}
          <div className="showcase-glass-card">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center flex-shrink-0">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Lightning Fast
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Optimized performance with modern web technologies and best practices.
                </p>
                <button className="text-primary-600 dark:text-primary-400 text-sm font-medium hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                  Learn more →
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showCode && (
        <CodeBlock
          code={codeExample}
          title="Card Usage Examples"
        />
      )}
    </div>
  );
};