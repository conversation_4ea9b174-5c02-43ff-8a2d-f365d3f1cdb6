// Mock data for design system showcase
export const mockRootProps = {
  currentTheme: 'light' as const,
  currentSection: 'typography' as const,
  showCode: false,
  showVariations: true,
  
  // Typography examples
  typographyExamples: {
    display: "Enterprise Design System",
    headline: "Professional Components",
    title: "Modern Interface Elements", 
    subtitle: "Sleek and Responsive",
    body: "This is body text demonstrating the typography scale and hierarchy used throughout the design system.",
    caption: "Caption Text",
    label: "Form Label",
    small: "Fine print and metadata"
  },
  
  // Color palette data
  colorPalette: {
    primary: ["#eff6ff", "#dbeafe", "#bfdbfe", "#93c5fd", "#60a5fa", "#3b82f6", "#2563eb", "#1d4ed8", "#1e40af", "#1e3a8a"],
    secondary: ["#f9fafb", "#f3f4f6", "#e5e7eb", "#d1d5db", "#9ca3af", "#6b7280", "#4b5563", "#374151", "#1f2937", "#111827"],
    success: ["#f0fdf4", "#dcfce7", "#bbf7d0", "#86efac", "#4ade80", "#22c55e", "#16a34a", "#15803d", "#166534", "#14532d"],
    warning: ["#fffbeb", "#fef3c7", "#fde68a", "#fcd34d", "#fbbf24", "#f59e0b", "#d97706", "#b45309", "#92400e", "#78350f"],
    error: ["#fef2f2", "#fee2e2", "#fecaca", "#fca5a5", "#f87171", "#ef4444", "#dc2626", "#b91c1c", "#991b1b", "#7f1d1d"]
  },
  
  // Button examples
  buttonExamples: [
    { variant: "primary" as const, size: "sm" as const, text: "Small Primary" },
    { variant: "primary" as const, size: "md" as const, text: "Medium Primary" },
    { variant: "primary" as const, size: "lg" as const, text: "Large Primary" },
    { variant: "secondary" as const, size: "md" as const, text: "Secondary" },
    { variant: "ghost" as const, size: "md" as const, text: "Ghost" },
    { variant: "danger" as const, size: "md" as const, text: "Danger" }
  ],
  
  // Card examples
  cardExamples: [
    {
      title: "Basic Card",
      description: "Standard card with elevation and hover effects",
      type: "basic" as const
    },
    {
      title: "Interactive Card", 
      description: "Card with enhanced interactions and animations",
      type: "interactive" as const
    },
    {
      title: "Glass Effect Card",
      description: "Modern glass morphism design with backdrop blur",
      type: "glass" as const
    }
  ],
  
  // Form examples
  formExamples: {
    inputs: [
      { type: "text", label: "Full Name", placeholder: "Enter your full name" },
      { type: "email", label: "Email Address", placeholder: "Enter your email" },
      { type: "password", label: "Password", placeholder: "Enter your password" }
    ],
    states: ["default", "focus", "error", "success", "disabled"]
  },
  
  // Navigation examples
  navigationItems: [
    { label: "Dashboard", active: true, icon: "home" },
    { label: "Projects", active: false, icon: "folder" },
    { label: "Team", active: false, icon: "users" },
    { label: "Settings", active: false, icon: "cog" }
  ],
  
  // Feedback examples
  feedbackExamples: [
    { type: "success" as const, message: "Operation completed successfully!" },
    { type: "warning" as const, message: "Please review the information before proceeding." },
    { type: "error" as const, message: "An error occurred while processing your request." },
    { type: "info" as const, message: "New features are now available." }
  ],
  
  // Spacing tokens
  spacingTokens: [
    { name: "xs", value: "0.5rem", pixels: "8px" },
    { name: "sm", value: "0.75rem", pixels: "12px" },
    { name: "md", value: "1rem", pixels: "16px" },
    { name: "lg", value: "1.5rem", pixels: "24px" },
    { name: "xl", value: "2rem", pixels: "32px" },
    { name: "2xl", value: "3rem", pixels: "48px" }
  ],
  
  // Shadow examples
  shadowExamples: [
    { name: "Elevation 1", class: "elevation-1", description: "Resting state cards" },
    { name: "Elevation 2", class: "elevation-2", description: "Hover state cards" },
    { name: "Elevation 3", class: "elevation-3", description: "Active states and modals" },
    { name: "Elevation 4", class: "elevation-4", description: "Floating elements" }
  ]
};