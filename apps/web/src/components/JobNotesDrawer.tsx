'use client';

import React, { useState } from 'react';
import { Button } from '@deskbelt/ui';
import { Textarea } from '@deskbelt/ui';
import { useJobDetails } from '../hooks/useJobDetails';
import { 
  XMarkIcon,
  ChatBubbleLeftIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline';

interface JobNotesDrawerProps {
  jobId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short' });
};

export function JobNotesDrawer({ jobId, isOpen, onClose }: JobNotesDrawerProps) {
  const { data: job, refetch } = useJobDetails(jobId);
  const [newNote, setNewNote] = useState('');
  const [isSubmittingNote, setIsSubmittingNote] = useState(false);

  // Handle note submission
  const handleNoteSubmit = async () => {
    if (!newNote.trim() || !job) return;
    
    setIsSubmittingNote(true);
    try {
      // TODO: Replace with actual API call
      console.log(`Adding note to job ${job.id}:`, newNote);
      
      setNewNote('');
      refetch();
    } catch (error) {
      console.error('Failed to add note:', error);
    } finally {
      setIsSubmittingNote(false);
    }
  };

  const renderJobNotes = () => {
    if (!job?.jobNotes || job.jobNotes.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          No notes yet. Start a conversation about this job.
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {job.jobNotes.map((note) => {
          const isSystem = note.message_type === 'system' || note.message_type === 'AI';
          const isCurrentUser = note.author.id === 'user-1'; // TODO: Get actual current user ID
          
          return (
            <div 
              key={note.id} 
              className={`flex ${isCurrentUser && !isSystem ? 'justify-end' : 'justify-start'}`}
            >
              <div 
                className={`max-w-[75%] rounded-lg p-3 ${
                  isSystem 
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-sm'
                    : isCurrentUser
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                }`}
              >
                {!isSystem && !isCurrentUser && (
                  <div className="text-xs font-medium mb-1 opacity-75">
                    {note.author.full_name}
                  </div>
                )}
                <div className="text-sm">{note.message}</div>
                <div className={`text-xs mt-1 ${
                  isCurrentUser && !isSystem ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {formatTimeAgo(note.created_at)}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  if (!isOpen || !jobId || !job) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex justify-end">
      <div className="bg-white w-96 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center">
            <ChatBubbleLeftIcon className="w-5 h-5 mr-2 text-gray-500" />
            <h2 className="text-lg font-semibold text-gray-900">Job Notes</h2>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-1 h-auto"
          >
            <XMarkIcon className="w-5 h-5" />
          </Button>
        </div>

        {/* Job Info */}
        <div className="px-4 py-3 border-b bg-gray-50">
          <h3 className="font-medium text-gray-900">{job.title}</h3>
          <p className="text-sm text-gray-600">
            Client: {job.client.name}
          </p>
        </div>

        {/* Notes Content */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1 overflow-y-auto p-4">
            {renderJobNotes()}
          </div>
          
          {/* Add Note Input */}
          <div className="p-4 border-t bg-gray-50">
            <div className="flex space-x-3">
              <Textarea
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                placeholder="Add a note to this job..."
                rows={1}
                maxLength={2000}
                className="flex-1 resize-none border-gray-300 dark:border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleNoteSubmit();
                  }
                }}
              />
              <Button
                onClick={handleNoteSubmit}
                disabled={!newNote.trim() || isSubmittingNote}
                className="px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
              >
                <PaperAirplaneIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 