import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { Job, formatJobSchedule, hasTimeScheduling } from '@/types/Job';
import JobSchedulingModal from '@/components/JobSchedulingModal';
import JobCardErrorBoundary from '@/components/JobCardErrorBoundary';
import { useMediaQuery } from '@/hooks/useJobCardModals';
import {
  ChevronDownIcon,
  ChevronRightIcon,
  DocumentTextIcon,
  PhoneIcon,
  ChatBubbleLeftIcon,
  EnvelopeIcon,
  UserIcon,
  UserGroupIcon,
  CalendarIcon,
  CalendarDaysIcon,
  CalendarDateRangeIcon,
  ClockIcon,
  TrashIcon,
  PencilIcon,
  EllipsisVerticalIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';

interface JobCardProps {
  job: Job;
  onJobClick?: (jobId: string) => void;
  onClientClick?: (clientId: string) => void;
  onNotesClick?: (jobId: string) => void;
  onCallClick?: (phone: string) => void;
  onMessageClick?: (clientId: string) => void;
  onEmailClick?: (email: string, job: Job) => void;
  onStatusChange?: (jobId: string, status: Job['status']) => void;
  onDateChange?: (jobId: string, date: string) => void;
  onScheduleChange?: (jobId: string, schedule: {
    date: string;
    startTime?: string | null;
    endTime?: string | null;
    duration?: string | null;
    notes?: string | null;
  }) => void;
  showStatusDropdown?: boolean;
  showDatePicker?: boolean;
  showTimePicker?: boolean;
  onStatusDropdownToggle?: () => void;
  onDateDropdownToggle?: () => void;
  onTimePickerToggle?: () => void;
  // New simplified scheduling interface
  onSchedulingClick?: (jobId: string) => void;
  // Job management actions
  onEditClick?: (jobId: string) => void;
  onDeleteClick?: (jobId: string) => void;
}

const JobCard: React.FC<JobCardProps> = ({
  job,
  onJobClick,
  onClientClick,
  onNotesClick,
  onCallClick,
  onMessageClick,
  onEmailClick,
  onStatusChange,
  onDateChange,
  onScheduleChange,
  showStatusDropdown = false,
  showDatePicker = false,
  showTimePicker = false,
  onStatusDropdownToggle,
  onDateDropdownToggle,
  onTimePickerToggle,
  onSchedulingClick,
  onEditClick,
  onDeleteClick,
}) => {
  const cardClickBlockedRef = React.useRef(false);
  const isMobile = useMediaQuery('(max-width: 768px)');
  const [isSchedulingModalOpen, setIsSchedulingModalOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const statusOptions = [
    { value: 'new' as const, label: 'New', color: 'bg-blue-500' },
    { value: 'quoted' as const, label: 'Quoted', color: 'bg-indigo-500' },
    { value: 'in_progress' as const, label: 'In Progress', color: 'bg-green-500' },
    { value: 'on_hold' as const, label: 'On Hold', color: 'bg-orange-500' },
    { value: 'completed' as const, label: 'Completed', color: 'bg-teal-500' },
  ];

  // Memoize expensive status configuration with standardized status colors
  const statusConfig = useMemo(() => ({
      new: { 
        color: 'status-badge-new', 
        dotColor: 'bg-blue-500',
        label: 'New' 
      },
      quoted: { 
        color: 'status-badge-quoted', 
        dotColor: 'bg-indigo-500',
        label: 'Quoted' 
      },
      in_progress: { 
        color: 'status-badge-in-progress', 
        dotColor: 'bg-blue-500',
        label: 'In Progress' 
      },
      on_hold: { 
        color: 'status-badge-on-hold', 
        dotColor: 'bg-amber-500',
        label: 'On Hold' 
      },
      completed: { 
        color: 'status-badge-completed', 
        dotColor: 'bg-emerald-500',
        label: 'Completed' 
      },
      cancelled: { 
        color: 'status-badge-cancelled', 
        dotColor: 'bg-red-500',
        label: 'Cancelled' 
      },
      scheduled: { 
        color: 'status-badge-scheduled', 
        dotColor: 'bg-purple-500',
        label: 'Scheduled' 
      },
      archived: { 
        color: 'status-badge-neutral', 
        dotColor: 'bg-gray-500',
        label: 'Archived' 
      },
    }), []);
  
  const getStatusBadge = useCallback((status: Job['status']) => {
    const config = statusConfig[status];
    return (
      <div className="relative">
        <button
          onClick={(e) => {
            cardClickBlockedRef.current = true;
            e.stopPropagation();
            e.preventDefault();
            onStatusDropdownToggle?.();
          }}
          className={`${config.color} hover:scale-105 transition-all duration-200 focus-ring`}
          aria-label={`Change status from ${config.label} for ${job.title}`}
          aria-expanded={showStatusDropdown}
          aria-haspopup="menu"
        >
          <span className={`w-2 h-2 rounded-full ${config.dotColor} mr-2 animate-pulse-soft`}></span>
          {config.label}
          <ChevronDownIcon className="w-3 h-3 ml-2 opacity-70" />
        </button>
        
        {/* Status Dropdown */}
        {showStatusDropdown && (
          <div className="dropdown animate-fade-in-up" style={{ zIndex: 9999 }} role="menu">
            {statusOptions.map((option) => (
              <button
                key={option.value}
                onClick={(e) => {
                  e.stopPropagation();
                  onStatusChange?.(job.id, option.value);
                }}
                className="dropdown-item"
                role="menuitem"
                aria-label={`Set status to ${option.label}`}
              >
                <span className={`w-2 h-2 rounded-full ${option.color} mr-3`}></span>
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>
    );
  }, [statusConfig, onStatusDropdownToggle, onStatusChange, job.id, job.title, showStatusDropdown]);

  // Memoize schedule formatting for performance
  const scheduleDisplay = useMemo(() => {
    // Fallback to simple date formatting to avoid issues with missing fields
    if (!job.scheduled_at) return 'Not scheduled';
    
    try {
      return formatJobSchedule(job);
    } catch (error) {
      console.error('Error formatting job schedule:', error);
      // Fallback to basic date formatting
      const date = new Date(job.scheduled_at);
      return date.toLocaleDateString('en-GB', { 
        day: '2-digit', 
        month: 'short', 
        year: 'numeric' 
      });
    }
  }, [job.scheduled_at, job.scheduled_start_time, job.scheduled_end_time, job.scheduling_notes]);

  const handleCardClick = (e: React.MouseEvent) => {
    if (cardClickBlockedRef.current) {
      cardClickBlockedRef.current = false;
      return;
    }
    onJobClick?.(job.id);
  };

  const handleTitleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onJobClick?.(job.id);
  };

  const handleClientClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClientClick?.(job.client.id);
  };

  const handleNotesClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onNotesClick?.(job.id);
  };

  const handleCallClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (job.client.phone) {
      onCallClick?.(job.client.phone);
    }
  };

  const handleMessageClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    // Create message options
    const options = [
      { label: 'SMS', action: () => window.open(`sms:${job.client.phone || ''}`, '_self') },
      { label: 'WhatsApp', action: () => window.open(`https://wa.me/${job.client.phone?.replace(/[^\d]/g, '')}`, '_blank') },
      { label: 'System Message', action: () => onMessageClick?.(job.client.id) }
    ];

    // Simple native menu simulation
    const choice = prompt(
      `Choose message method:\n1. SMS\n2. WhatsApp\n3. System Message\n\nEnter 1, 2, or 3:`
    );

    switch(choice) {
      case '1': options[0].action(); break;
      case '2': options[1].action(); break;
      case '3': options[2].action(); break;
    }
  };

  const handleEmailClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (job.client.email) {
      onEmailClick?.(job.client.email, job);
    }
  };

  // Simplified scheduling handler
  const handleSchedulingClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    e.preventDefault();
    
    if (onSchedulingClick) {
      onSchedulingClick(job.id);
    } else {
      // Fallback to internal modal
      setIsSchedulingModalOpen(true);
    }
  }, [job.id, onSchedulingClick]);

  const handleSchedulingModalClose = useCallback(() => {
    setIsSchedulingModalOpen(false);
  }, []);

  const handleScheduleUpdate = useCallback((jobId: string, schedule: any) => {
    onScheduleChange?.(jobId, schedule);
    setIsSchedulingModalOpen(false);
  }, [onScheduleChange]);

  const handleEditClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    e.preventDefault();
    onEditClick?.(job.id);
  }, [job.id, onEditClick]);

  const handleDeleteClick = useCallback((e: React.MouseEvent) => {
    cardClickBlockedRef.current = true;
    e.stopPropagation();
    e.preventDefault();
    onDeleteClick?.(job.id);
  }, [job.id, onDeleteClick]);

  // Note: Close dropdowns handled by parent component

  return (
    <div
      className="card-interactive group/card animate-fade-in-up hover:border-primary-200 dark:hover:border-primary-700 relative"
      onClick={handleCardClick}
      role="article"
      aria-label={`Job: ${job.title} for ${job.client.name}`}
    >
      {/* Dropdown Menu - Positioned absolutely in top right */}
      <div className="absolute top-4 right-4 z-10">
        <button
          onClick={(e) => {
            cardClickBlockedRef.current = true;
            e.stopPropagation();
            setIsDropdownOpen(!isDropdownOpen);
          }}
          className="p-1.5 text-secondary-400 hover:text-secondary-600 dark:text-secondary-500 dark:hover:text-secondary-300 transition-colors border-0 bg-transparent focus:outline-none rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-800"
        >
          <EllipsisVerticalIcon className="w-5 h-5" />
        </button>
        
        {isDropdownOpen && (
          <div className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-secondary-800 rounded-xl shadow-lg border border-secondary-200 dark:border-secondary-700 py-2 z-10 animate-fade-in-up">
            <button
              onClick={(e) => {
                cardClickBlockedRef.current = true;
                e.stopPropagation();
                onNotesClick?.(job.id);
                setIsDropdownOpen(false);
              }}
              className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <DocumentTextIcon className="w-4 h-4 mr-3 text-secondary-500 dark:text-secondary-400" />
              <span className="font-medium">View Job Log</span>
            </button>
            <button
              onClick={(e) => {
                cardClickBlockedRef.current = true;
                e.stopPropagation();
                onEditClick?.(job.id);
                setIsDropdownOpen(false);
              }}
              className="w-full px-4 py-3 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <PencilIcon className="w-4 h-4 mr-3 text-secondary-500 dark:text-secondary-400" />
              <span className="font-medium">Edit Job</span>
            </button>
            <div className="h-px bg-secondary-200 dark:bg-secondary-600 my-2 mx-4" />
            <button
              onClick={(e) => {
                cardClickBlockedRef.current = true;
                e.stopPropagation();
                onDeleteClick?.(job.id);
                setIsDropdownOpen(false);
              }}
              className="w-full px-4 py-3 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center transition-colors duration-150 rounded-lg mx-2"
            >
              <TrashIcon className="w-4 h-4 mr-3 text-red-500 dark:text-red-400" />
              <span className="font-medium">Delete Job</span>
            </button>
          </div>
        )}
      </div>

      {/* Header with clean layout */}
      <div className="flex items-start justify-between mb-4 pr-10">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-jobs-500 to-jobs-600 flex items-center justify-center text-white text-lg font-semibold shadow-md">
            {job.title.charAt(0).toUpperCase()}
          </div>
          <div className="flex-1 min-w-0">
            <button
              onClick={handleTitleClick}
              className="text-left group/title focus-ring rounded-lg -m-1 p-1"
            >
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50 truncate group-hover/title:text-primary-600 dark:group-hover/title:text-primary-400 transition-colors duration-200 mb-1">
                {job.title}
              </h3>
            </button>
            <p className="text-sm text-secondary-500 dark:text-secondary-400">
              #{job.id.slice(-6).toUpperCase()}
            </p>
            <div className="mt-2">
              {getStatusBadge(job.status)}
            </div>
          </div>
        </div>
      </div>

      {/* Client Info */}
      <div className="mb-4">
        <button
          onClick={handleClientClick}
          className="text-left group/client focus-ring rounded-lg p-2 hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors duration-200"
        >
          <div className="flex items-center space-x-2">
            <UserIcon className="w-4 h-4 text-secondary-400" />
            <span className="text-sm font-medium text-secondary-700 dark:text-secondary-300 group-hover/client:text-primary-600 dark:group-hover/client:text-primary-400 transition-colors">
              {job.client.name}
            </span>
          </div>
        </button>
      </div>

      {/* Schedule Info */}
      <div className="mb-4">
        <button
          onClick={handleSchedulingClick}
          className="text-left group/schedule focus-ring rounded-lg p-2 hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors duration-200"
        >
          <div className="flex items-center space-x-2">
            <CalendarIcon className="w-4 h-4 text-secondary-400" />
            <span className="text-sm font-medium text-secondary-700 dark:text-secondary-300 group-hover/schedule:text-primary-600 dark:group-hover/schedule:text-primary-400 transition-colors truncate">
              {scheduleDisplay}
            </span>
          </div>
        </button>
      </div>

      {/* Assigned To */}
      {job.assigned_to && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 p-2 text-sm">
            <UserGroupIcon className="w-4 h-4 text-secondary-400" />
            <span className="text-secondary-600 dark:text-secondary-400 truncate">
              {job.assigned_to.full_name}
            </span>
          </div>
        </div>
      )}

      {/* Action Icons */}
      <div className="flex items-center justify-center space-x-4 pt-4 border-t border-secondary-100 dark:border-secondary-700">
        {/* View Details */}
        <button
          onClick={handleTitleClick}
          className="p-2 text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-lg transition-colors"
          title="View details"
          aria-label="View job details"
        >
          <EyeIcon className="w-5 h-5" />
        </button>

        {/* Call Icon */}
        <button
          onClick={handleCallClick}
          disabled={!job.client.phone}
          className={`p-2 rounded-lg transition-colors ${
            job.client.phone
              ? 'text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20'
              : 'text-secondary-300 dark:text-secondary-600 cursor-not-allowed'
          }`}
          title={job.client.phone ? `Call ${job.client.phone}` : 'No phone number'}
          aria-label={job.client.phone ? `Call ${job.client.phone}` : 'No phone number available'}
        >
          <PhoneIcon className="w-5 h-5" />
        </button>

        {/* Message Icon */}
        <button
          onClick={handleMessageClick}
          className="p-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
          title="Send message"
          aria-label="Send message via SMS, WhatsApp, or system message"
        >
          <ChatBubbleLeftIcon className="w-5 h-5" />
        </button>

        {/* Email Icon */}
        <button
          onClick={handleEmailClick}
          disabled={!job.client.email}
          className={`p-2 rounded-lg transition-colors ${
            job.client.email
              ? 'text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20'
              : 'text-secondary-300 dark:text-secondary-600 cursor-not-allowed'
          }`}
          title={job.client.email ? `Email ${job.client.email}` : 'No email address'}
          aria-label={job.client.email ? `Email ${job.client.email}` : 'No email address available'}
        >
          <EnvelopeIcon className="w-5 h-5" />
        </button>

        {/* Schedule Icon */}
        <button
          onClick={handleSchedulingClick}
          className="p-2 text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-lg transition-colors"
          title="Schedule job"
          aria-label="Schedule job"
        >
          <CalendarIcon className="w-5 h-5" />
        </button>
      </div>

      {/* Scheduling Modal */}
      {isSchedulingModalOpen && (
        <JobSchedulingModal
          job={job}
          isOpen={isSchedulingModalOpen}
          onClose={handleSchedulingModalClose}
          onScheduleUpdate={handleScheduleUpdate}
        />
      )}

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
};

// Wrapped JobCard with Error Boundary
const JobCardWithErrorBoundary: React.FC<JobCardProps> = (props) => {
  return (
    <JobCardErrorBoundary jobId={props.job.id}>
      <JobCard {...props} />
    </JobCardErrorBoundary>
  );
};

export default JobCardWithErrorBoundary; 