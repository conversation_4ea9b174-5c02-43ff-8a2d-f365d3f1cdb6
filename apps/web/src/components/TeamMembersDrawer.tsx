// @ts-nocheck
import { useState } from 'react';
import { XMarkIcon, StarIcon, ShieldCheckIcon, UserIcon, TrashIcon, PlusIcon, CogIcon, CheckBadgeIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { TeamDetails, TeamMember, TeamInvite } from '../types/Team';
import { useTeamMembers } from '@/hooks/useTeamMembers';

interface TeamMembersDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  teamData: TeamDetails | null;
  onRemoveMember: (memberId: string) => void;
  onCancelInvite: (inviteId: string) => void;
  onResendInvite: (inviteId: string) => void;
  onOpenInviteDrawer: () => void;
  onOpenSettingsDrawer: () => void;
  currentUserId: string;
}

export default function TeamMembersDrawer({ 
  isOpen, 
  onClose, 
  teamData,
  onRemoveMember,
  onCancelInvite,
  onResendInvite,
  onOpenInviteDrawer,
  onOpenSettingsDrawer,
  currentUserId
}: TeamMembersDrawerProps) {
  const [removingMember, setRemovingMember] = useState<string | null>(null);
  const [showPermissionsModal, setShowPermissionsModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<string | null>(null);
  
  // Enhanced team members data with real API integration
  const { 
    members: enhancedMembers, 
    isLoading: membersLoading, 
    error: membersError,
    updateMemberPermissions,
    getPermissionSummary,
    getMemberDisplayName,
    getMemberInitials
  } = useTeamMembers(teamData?.id || null);

  const handleRemoveMember = async (memberId: string, memberRole: string) => {
    if (memberRole === 'owner') return; // Cannot remove owner
    
    setRemovingMember(memberId);
    try {
      // Removed artificial delay
    } finally {
      setRemovingMember(null);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <StarIcon className="w-4 h-4 text-yellow-600" />;
      case 'manager':
        return <ShieldCheckIcon className="w-4 h-4 text-blue-600" />;
      default:
        return <UserIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'manager':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatLastActive = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 5) return 'Active now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return formatDate(dateString);
  };

  const canRemoveMember = (member: TeamMember) => {
    // Cannot remove yourself if you're the owner
    if (member.user_id === currentUserId && member.role === 'owner') return false;
    // Cannot remove the owner
    if (member.role === 'owner') return false;
    // Can remove if current user is owner or manager
    return teamData?.role === 'owner' || teamData?.role === 'manager';
  };

  if (!isOpen || !teamData) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="fixed right-0 top-0 h-full w-full max-w-md bg-white dark:bg-gray-900 shadow-xl">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{teamData.name}</h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {teamData.membersCount} member{teamData.membersCount !== 1 ? 's' : ''}
                </p>
              </div>
              <button
                onClick={onClose}
                className="rounded-lg p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
            
            {/* Action Buttons */}
            <div className="flex space-x-3">
              <button
                onClick={onOpenInviteDrawer}
                className="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Invite Member
              </button>
              <button
                onClick={onOpenSettingsDrawer}
                className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm"
              >
                <CogIcon className="w-4 h-4 mr-2" />
                Team Settings
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Team Members */}
            <div className="px-6 py-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">Team Members</h3>
                {membersLoading && (
                  <div className="w-4 h-4 border-2 border-indigo-200 dark:border-indigo-800 border-t-indigo-600 dark:border-t-indigo-400 rounded-full animate-spin" />
                )}
              </div>

              {/* Error State */}
              {membersError && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg mb-4">
                  <div className="flex items-center">
                    <ExclamationTriangleIcon className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" />
                    <p className="text-sm text-red-700 dark:text-red-400">{membersError}</p>
                  </div>
                </div>
              )}

              {/* Members List */}
              <div className="space-y-3">
                {((enhancedMembers && enhancedMembers.length > 0) ? enhancedMembers : teamData?.members || []).map((member) => {
                  const isEnhanced = enhancedMembers && enhancedMembers.length > 0;
                  const displayName = isEnhanced ? getMemberDisplayName(member as any) : member.full_name;
                  const initials = isEnhanced ? getMemberInitials(member as any) : getInitials(member.full_name || member.email);
                  const email = isEnhanced ? (member as any).users?.email : member.email;
                  const role = member.role;
                  const userId = isEnhanced ? (member as any).user_id : member.user_id;
                  const permissionSummary = isEnhanced ? getPermissionSummary(member as any) : null;

                  return (
                    <div key={isEnhanced ? userId : member.id} className="group p-4 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-600 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                          {/* Enhanced Avatar */}
                          <div className="relative">
                            <div className="w-11 h-11 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center text-white text-sm font-semibold shadow-md">
                              {initials}
                            </div>
                            {role === 'owner' && (
                              <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                                <StarIcon className="w-3 h-3 text-white" />
                              </div>
                            )}
                          </div>
                          
                          {/* Enhanced Member Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <p className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                                {displayName}
                              </p>
                              {userId === currentUserId && (
                                <span className="px-2 py-0.5 text-xs font-medium text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">
                                  You
                                </span>
                              )}
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 truncate mb-2">{email}</p>
                            
                            {/* Role and Permissions */}
                            <div className="flex items-center space-x-2">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-lg text-xs font-semibold ${getRoleBadgeColor(role)}`}>
                                {getRoleIcon(role)}
                                <span className="ml-1.5 capitalize">{role}</span>
                              </span>
                              
                              {/* Permission Summary for enhanced members */}
                              {permissionSummary && role !== 'owner' && (
                                <span className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded-md">
                                  {permissionSummary}
                                </span>
                              )}
                            </div>
                            
                            {/* Last Active (for legacy members) */}
                            {!isEnhanced && member.last_active && (
                              <div className="mt-1">
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {formatLastActive(member.last_active)}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          {/* Permissions Button (for enhanced members only) */}
                          {isEnhanced && role !== 'owner' && (
                            <button
                              onClick={() => {
                                setSelectedMember(userId);
                                setShowPermissionsModal(true);
                              }}
                              className="p-2 text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-lg transition-colors"
                              title="Manage permissions"
                            >
                              <CheckBadgeIcon className="w-4 h-4" />
                            </button>
                          )}

                          {/* Remove Button */}
                          {canRemoveMember({ ...member, user_id: userId }) && (
                            <button
                              onClick={() => handleRemoveMember(isEnhanced ? userId : member.id, role)}
                              disabled={removingMember === (isEnhanced ? userId : member.id)}
                              className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors disabled:opacity-50"
                              title="Remove member"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Empty State */}
                {!membersLoading && (!enhancedMembers || enhancedMembers.length === 0) && (!teamData?.members || teamData.members.length === 0) && (
                  <div className="text-center py-8">
                    <UserIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">No team members yet</p>
                    <p className="text-xs text-gray-400 dark:text-gray-500">Invite members to start collaborating</p>
                  </div>
                )}
              </div>
            </div>

            {/* Pending Invites */}
            {teamData.pending_invites && teamData.pending_invites.length > 0 && (
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Pending Invites</h3>
                <div className="space-y-3">
                  {teamData.pending_invites.map((invite) => (
                    <div key={invite.id} className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                      <div className="flex items-center space-x-3">
                        {/* Avatar */}
                        <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                          {getInitials(invite.email)}
                        </div>
                        
                        {/* Invite Info */}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {invite.email}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getRoleBadgeColor(invite.role)}`}>
                              {getRoleIcon(invite.role)}
                              <span className="ml-1 capitalize">{invite.role}</span>
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              Expires {formatDate(invite.expires_at)}
                            </span>
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            Invited by {invite.invited_by}
                          </p>
                        </div>
                      </div>

                      {/* Invite Actions */}
                      <div className="flex space-x-2">
                        <button
                          onClick={() => onResendInvite(invite.id)}
                          className="px-2 py-1 text-xs font-medium text-yellow-700 dark:text-yellow-300 hover:text-yellow-800 dark:hover:text-yellow-200"
                        >
                          Resend
                        </button>
                        <button
                          onClick={() => onCancelInvite(invite.id)}
                          className="px-2 py-1 text-xs font-medium text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Simple Permissions Modal (placeholder for Phase 3) */}
      {showPermissionsModal && selectedMember && (
        <>
          <div className="fixed inset-0 bg-black bg-opacity-50 z-60" onClick={() => setShowPermissionsModal(false)} />
          <div className="fixed inset-0 z-60 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Member Permissions
                  </h3>
                  <button
                    onClick={() => setShowPermissionsModal(false)}
                    className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>
                
                <div className="text-center py-8">
                  <CheckBadgeIcon className="w-12 h-12 text-indigo-400 mx-auto mb-3" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Advanced permission management coming in Phase 3
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    Full granular permissions and role management
                  </p>
                </div>
                
                <button
                  onClick={() => setShowPermissionsModal(false)}
                  className="w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
} 