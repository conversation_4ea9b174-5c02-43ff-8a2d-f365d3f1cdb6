import React, { useState, useEffect, useRef } from 'react';
import { Job, JobStatus } from '@/types/Job';
import { useClients } from '@/hooks/useClients';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/contexts/ToastContext';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { Input } from '@deskbelt/ui';
import { Select } from '@deskbelt/ui';
import { Textarea } from '@deskbelt/ui';
import { 
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

interface EditJobDrawerProps {
  job: Job | null;
  isOpen: boolean;
  onClose: () => void;
  onJobUpdated?: (updatedJob: Job) => void;
}

interface FormData {
  title: string;
  description: string;
  status: JobStatus;
  client_id: string;
  scheduled_at: string;
  scheduled_start_time: string;
  scheduled_end_time: string;
  scheduling_notes: string;
}

interface FormErrors {
  title?: string;
  description?: string;
  client_id?: string;
  scheduled_at?: string;
  general?: string;
}

const statusOptions = [
  { value: 'new' as const, label: 'New', color: 'bg-blue-500' },
  { value: 'quoted' as const, label: 'Quoted', color: 'bg-indigo-500' },
  { value: 'in_progress' as const, label: 'In Progress', color: 'bg-green-500' },
  { value: 'on_hold' as const, label: 'On Hold', color: 'bg-orange-500' },
  { value: 'completed' as const, label: 'Completed', color: 'bg-teal-500' },
];

export const EditJobDrawer: React.FC<EditJobDrawerProps> = ({
  job,
  isOpen,
  onClose,
  onJobUpdated
}) => {
  const { authenticatedFetch } = useAuthenticatedFetch();
  const { showSuccess, showError } = useToast();
  const { data: clients, isLoading: clientsLoading } = useClients({ limit: 100 });
  
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    status: 'new',
    client_id: '',
    scheduled_at: '',
    scheduled_start_time: '',
    scheduled_end_time: '',
    scheduling_notes: ''
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingDetails, setIsFetchingDetails] = useState(false);
  
  const titleInputRef = useRef<HTMLInputElement>(null);

  // Reset form when drawer opens/closes or job changes
  useEffect(() => {
    if (isOpen && job) {
      // Fetch detailed job information to get description and other fields
      setIsFetchingDetails(true);
      fetchJobDetails(job.id);
    } else {
      // Reset form when closed
      setFormData({
        title: '',
        description: '',
        status: 'new',
        client_id: '',
        scheduled_at: '',
        scheduled_start_time: '',
        scheduled_end_time: '',
        scheduling_notes: ''
      });
      setErrors({});
      setIsLoading(false);
      setIsFetchingDetails(false);
    }
  }, [isOpen, job]);

  // Focus title input when drawer opens
  useEffect(() => {
    if (isOpen && titleInputRef.current) {
      setTimeout(() => titleInputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  const fetchJobDetails = async (jobId: string) => {
    try {
      const response = await authenticatedFetch(`/api/jobs/${jobId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch job details');
      }
      const jobDetails = await response.json();
      
      // Populate form with job details
      setFormData({
        title: jobDetails.title || '',
        description: jobDetails.description || '',
        status: jobDetails.status || 'new',
        client_id: jobDetails.client.id || '',
        scheduled_at: jobDetails.scheduled_at ? jobDetails.scheduled_at.split('T')[0] : '',
        scheduled_start_time: jobDetails.scheduled_start_time || '',
        scheduled_end_time: jobDetails.scheduled_end_time || '',
        scheduling_notes: jobDetails.scheduling_notes || ''
      });
      
    } catch (error) {
      console.error('Error fetching job details:', error);
      showError('Error', 'Failed to load job details');
    } finally {
      setIsFetchingDetails(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Job title is required';
    } else if (formData.title.trim().length < 3) {
      newErrors.title = 'Job title must be at least 3 characters';
    } else if (formData.title.trim().length > 100) {
      newErrors.title = 'Job title must be less than 100 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Job description is required';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = 'Job description must be at least 10 characters';
    }

    if (!formData.client_id) {
      newErrors.client_id = 'Client selection is required';
    }

    if (formData.scheduled_at) {
      const selectedDate = new Date(formData.scheduled_at);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (selectedDate < today) {
        newErrors.scheduled_at = 'Scheduled date cannot be in the past';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!job || !validateForm()) return;

    setIsLoading(true);
    setErrors({});

    try {
      const updateData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        status: formData.status,
        client_id: formData.client_id,
        scheduled_at: formData.scheduled_at || null,
        scheduled_start_time: formData.scheduled_start_time || null,
        scheduled_end_time: formData.scheduled_end_time || null,
        scheduling_notes: formData.scheduling_notes.trim() || null,
      };

      const response = await authenticatedFetch(`/api/jobs/${job.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to update job: ${response.status}`);
      }

      const updatedJob = await response.json();

      showSuccess('Job Updated', 'The job has been successfully updated.');
      onJobUpdated?.(updatedJob);
      onClose();

    } catch (error) {
      console.error('Error updating job:', error);
      setErrors({ general: error instanceof Error ? error.message : 'Failed to update job' });
      showError('Update Failed', error instanceof Error ? error.message : 'An unexpected error occurred while updating the job.');
    } finally {
      setIsLoading(false);
    }
  };

  const clientOptions = clients?.map(client => ({
    value: client.id,
    label: client.name
  })) || [];

  if (!job) return null;

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      side="right"
      size="lg"
      title="Edit Job"
      className="bg-white dark:bg-secondary-800"
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-secondary-200 dark:border-secondary-700">
          <div>
            <h2 className="text-xl font-semibold text-secondary-900 dark:text-secondary-100">
              Edit Job
            </h2>
            <p className="text-sm text-secondary-600 dark:text-secondary-400 mt-1">
              #{job.id.slice(-6).toUpperCase()}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 transition-colors"
            aria-label="Close edit job drawer"
          >
            <XMarkIcon className="w-5 h-5 text-secondary-500 dark:text-secondary-400" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {isFetchingDetails ? (
            <div className="flex items-center justify-center py-12">
              <div className="w-6 h-6 border-2 border-primary-600 border-t-transparent rounded-full animate-spin" />
              <span className="ml-2 text-secondary-600 dark:text-secondary-400">Loading job details...</span>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* General Error */}
              {errors.general && (
                <div className="p-4 bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-lg">
                  <div className="flex">
                    <ExclamationTriangleIcon className="w-5 h-5 text-error-600 dark:text-error-400 flex-shrink-0" />
                    <p className="ml-2 text-sm text-error-700 dark:text-error-400">{errors.general}</p>
                  </div>
                </div>
              )}

              {/* Job Title */}
              <div>
                <Input
                  ref={titleInputRef}
                  label="Job Title"
                  placeholder="Enter job title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  error={errors.title}
                  required
                  maxLength={100}
                />
              </div>

              {/* Job Description */}
              <div>
                <Textarea
                  label="Job Description"
                  placeholder="Describe the work to be done..."
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  error={errors.description}
                  required
                  rows={4}
                  maxLength={1000}
                />
                <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                  {formData.description.length}/1000 characters
                </p>
              </div>

              {/* Client Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Client</label>
                <Select
                  value={formData.client_id}
                  onChange={(value) => handleInputChange('client_id', value)}
                  options={clientOptions}
                  placeholder="Select a client..."
                  error={!!errors.client_id}
                />
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                <Select
                  value={formData.status}
                  onChange={(value) => handleInputChange('status', value as JobStatus)}
                  options={statusOptions.map(option => ({ value: option.value, label: option.label }))}
                />
              </div>

              {/* Scheduling Section */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <CalendarIcon className="w-5 h-5 text-secondary-500" />
                  <h3 className="text-lg font-medium text-secondary-900 dark:text-secondary-100">
                    Scheduling
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Scheduled Date */}
                  <div>
                    <Input
                      label="Scheduled Date"
                      type="date"
                      value={formData.scheduled_at}
                      onChange={(e) => handleInputChange('scheduled_at', e.target.value)}
                      error={errors.scheduled_at}
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>

                  {/* Start Time */}
                  <div>
                    <Input
                      label="Start Time (Optional)"
                      type="time"
                      value={formData.scheduled_start_time}
                      onChange={(e) => handleInputChange('scheduled_start_time', e.target.value)}
                    />
                  </div>
                </div>

                {/* End Time */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Input
                      label="End Time (Optional)"
                      type="time"
                      value={formData.scheduled_end_time}
                      onChange={(e) => handleInputChange('scheduled_end_time', e.target.value)}
                      min={formData.scheduled_start_time || undefined}
                    />
                  </div>
                </div>

                {/* Scheduling Notes */}
                <div>
                  <Textarea
                    label="Scheduling Notes (Optional)"
                    placeholder="Any special instructions or notes about the schedule..."
                    value={formData.scheduling_notes}
                    onChange={(e) => handleInputChange('scheduling_notes', e.target.value)}
                    rows={2}
                    maxLength={500}
                  />
                  <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                    {formData.scheduling_notes.length}/500 characters
                  </p>
                </div>
              </div>
            </form>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-secondary-200 dark:border-secondary-700 p-6">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>

            <div className="flex items-center space-x-3">
              <Button
                type="submit"
                onClick={handleSubmit}
                isLoading={isLoading}
                disabled={isLoading || isFetchingDetails}
                className="bg-primary-600 hover:bg-primary-700 text-white"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckIcon className="w-4 h-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};