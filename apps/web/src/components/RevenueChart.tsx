'use client';

import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { MonthlyInvoiceValue } from '@/hooks/useDashboardStats';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface InvoiceValueChartProps {
  data: MonthlyInvoiceValue[];
  isDarkMode?: boolean;
}

export default function InvoiceValueChart({ data, isDarkMode = false }: InvoiceValueChartProps) {
  const chartData = {
    labels: data.map(item => item.month),
    datasets: [
      {
        label: 'Invoice Value',
        data: data.map(item => item.value),
        borderColor: '#3B82F6',
        backgroundColor: isDarkMode 
          ? 'rgba(59, 130, 246, 0.1)' 
          : 'rgba(59, 130, 246, 0.05)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#3B82F6',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
        pointHoverBackgroundColor: '#3B82F6',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 2,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
        titleColor: isDarkMode ? '#f9fafb' : '#111827',
        bodyColor: isDarkMode ? '#d1d5db' : '#374151',
        borderColor: isDarkMode ? '#374151' : '#d1d5db',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context: any) {
            return `£${context.parsed.y.toLocaleString()}`;
          }
        }
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
        ticks: {
          color: isDarkMode ? '#9ca3af' : '#6b7280',
          font: {
            size: 12,
          },
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: isDarkMode ? '#374151' : '#f3f4f6',
          borderDash: [5, 5],
        },
        border: {
          display: false,
        },
        ticks: {
          color: isDarkMode ? '#9ca3af' : '#6b7280',
          font: {
            size: 12,
          },
          callback: function(value: any) {
            return `£${value.toLocaleString()}`;
          },
        },
      },
    },
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
  };

  const totalValue = data.reduce((sum, item) => sum + item.value, 0);

  return (
    <div className="card-interactive group/chart animate-fade-in-up hover:border-primary-200 dark:hover:border-primary-700">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50 group-hover/chart:text-primary-600 dark:group-hover/chart:text-primary-400 transition-colors duration-200">
          Monthly Invoice Value
        </h3>
        <p className="text-sm text-secondary-600 dark:text-secondary-400 group-hover/chart:text-secondary-700 dark:group-hover/chart:text-secondary-300 transition-colors duration-200">
          Total value of invoices sent: £{totalValue.toLocaleString()}
        </p>
      </div>
      <div className="h-80 transform group-hover/chart:scale-[1.01] transition-transform duration-300">
        <Line data={chartData} options={options} />
      </div>
    </div>
  );
} 