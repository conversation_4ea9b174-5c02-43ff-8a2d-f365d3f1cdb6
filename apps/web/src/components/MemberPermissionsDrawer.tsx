import React, { useState, useEffect } from 'react';
import { XMarkIcon, ShieldCheckIcon, UserIcon, CheckIcon, XMarkIcon as XIcon } from '@heroicons/react/24/outline';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

interface MemberPermissionsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  teamId: string | null;
  memberId: string | null;
  memberName: string;
  memberEmail: string;
  currentPermissions: MemberPermissions;
  onPermissionsUpdated?: () => void;
}

interface MemberPermissions {
  can_manage_jobs: boolean;
  can_manage_clients: boolean;
  can_manage_invoices: boolean;
  can_manage_quotes: boolean;
  can_view_reports: boolean;
  can_manage_team: boolean;
}

interface PermissionTemplate {
  name: string;
  description: string;
  permissions: MemberPermissions;
  color: string;
}

const PERMISSION_TEMPLATES: PermissionTemplate[] = [
  {
    name: 'View Only',
    description: 'Can view assigned work but cannot make changes',
    permissions: {
      can_manage_jobs: false,
      can_manage_clients: false,
      can_manage_invoices: false,
      can_manage_quotes: false,
      can_view_reports: false,
      can_manage_team: false
    },
    color: 'gray'
  },
  {
    name: 'Field Worker',
    description: 'Can manage assigned jobs and view client information',
    permissions: {
      can_manage_jobs: true,
      can_manage_clients: false,
      can_manage_invoices: false,
      can_manage_quotes: false,
      can_view_reports: false,
      can_manage_team: false
    },
    color: 'blue'
  },
  {
    name: 'Client Manager',
    description: 'Can manage clients and create quotes for sales',
    permissions: {
      can_manage_jobs: false,
      can_manage_clients: true,
      can_manage_invoices: false,
      can_manage_quotes: true,
      can_view_reports: false,
      can_manage_team: false
    },
    color: 'green'
  },
  {
    name: 'Administrator',
    description: 'Full access to jobs, clients, and financial documents',
    permissions: {
      can_manage_jobs: true,
      can_manage_clients: true,
      can_manage_invoices: true,
      can_manage_quotes: true,
      can_view_reports: true,
      can_manage_team: false
    },
    color: 'purple'
  },
  {
    name: 'Manager',
    description: 'Complete access including team management',
    permissions: {
      can_manage_jobs: true,
      can_manage_clients: true,
      can_manage_invoices: true,
      can_manage_quotes: true,
      can_view_reports: true,
      can_manage_team: true
    },
    color: 'amber'
  }
];

const PERMISSION_DETAILS = {
  can_manage_jobs: {
    title: 'Manage Jobs',
    description: 'Create, edit, delete jobs and update job status',
    category: 'Operations',
    icon: '🛠️'
  },
  can_manage_clients: {
    title: 'Manage Clients',
    description: 'Add, edit, delete clients and manage client relationships',
    category: 'Customer Management',
    icon: '👥'
  },
  can_manage_invoices: {
    title: 'Manage Invoices',
    description: 'Create, send, and manage invoices and payments',
    category: 'Financial',
    icon: '📄'
  },
  can_manage_quotes: {
    title: 'Manage Quotes',
    description: 'Create and manage quotes and proposals',
    category: 'Sales',
    icon: '💰'
  },
  can_view_reports: {
    title: 'View Reports',
    description: 'Access business analytics and performance reports',
    category: 'Analytics',
    icon: '📊'
  },
  can_manage_team: {
    title: 'Manage Team',
    description: 'Invite members, manage permissions, and oversee workforce',
    category: 'Management',
    icon: '👨‍💼'
  }
};

export default function MemberPermissionsDrawer({ 
  isOpen, 
  onClose, 
  teamId, 
  memberId, 
  memberName, 
  memberEmail, 
  currentPermissions,
  onPermissionsUpdated 
}: MemberPermissionsDrawerProps) {
  const [permissions, setPermissions] = useState<MemberPermissions>(currentPermissions);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  const { authenticatedPut } = useAuthenticatedFetch();

  // Reset form when props change or drawer opens
  useEffect(() => {
    if (isOpen) {
      setPermissions(currentPermissions);
      setError(null);
      setHasChanges(false);
      setSelectedTemplate(null);
    }
  }, [isOpen, currentPermissions]);

  // Check if current permissions match a template
  useEffect(() => {
    const matchingTemplate = PERMISSION_TEMPLATES.find(template => 
      Object.keys(template.permissions).every(key => 
        template.permissions[key as keyof MemberPermissions] === permissions[key as keyof MemberPermissions]
      )
    );
    setSelectedTemplate(matchingTemplate?.name || null);
  }, [permissions]);

  // Check for changes
  useEffect(() => {
    const hasChanges = Object.keys(permissions).some(key => 
      permissions[key as keyof MemberPermissions] !== currentPermissions[key as keyof MemberPermissions]
    );
    setHasChanges(hasChanges);
  }, [permissions, currentPermissions]);

  const handlePermissionToggle = (permission: keyof MemberPermissions) => {
    setPermissions(prev => ({
      ...prev,
      [permission]: !prev[permission]
    }));
  };

  const applyTemplate = (template: PermissionTemplate) => {
    setPermissions({ ...template.permissions });
    setSelectedTemplate(template.name);
  };

  const handleSave = async () => {
    if (!teamId || !memberId) {
      setError('Missing team or member information');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await authenticatedPut(
        `/api/workforce/${teamId}/members/${memberId}/permissions`,
        permissions
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update permissions');
      }

      onPermissionsUpdated?.();
      showToast('Permissions updated successfully!', 'success');
      onClose();
    } catch (err) {
      console.error('Error updating permissions:', err);
      setError(err instanceof Error ? err.message : 'Failed to update permissions');
    } finally {
      setIsLoading(false);
    }
  };

  const showToast = (message: string, type: 'success' | 'error') => {
    // In a real app, this would trigger a proper toast component
    console.log(`[${type.toUpperCase()}] ${message}`);
  };

  const getPermissionCount = (perms: MemberPermissions) => {
    return Object.values(perms).filter(Boolean).length;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-50">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="relative w-full max-w-2xl bg-white dark:bg-gray-900 shadow-xl flex flex-col h-full overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ShieldCheckIcon className="w-6 h-6 mr-3" />
              <div>
                <h2 className="text-lg font-semibold">Manage Permissions</h2>
                <p className="text-indigo-100 text-sm">
                  Configure access levels for {memberName}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Member Info */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
              <UserIcon className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">{memberName}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">{memberEmail}</p>
              <div className="flex items-center mt-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  Current: {getPermissionCount(currentPermissions)} permissions enabled
                </span>
                {hasChanges && (
                  <span className="ml-3 px-2 py-0.5 bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300 text-xs rounded-full">
                    {getPermissionCount(permissions)} pending
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Permission Templates */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Quick Templates
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {PERMISSION_TEMPLATES.map((template) => {
                const isSelected = selectedTemplate === template.name;
                const colorClasses = {
                  gray: isSelected ? 'border-gray-500 bg-gray-50 dark:bg-gray-800/50' : 'border-gray-300 hover:border-gray-400',
                  blue: isSelected ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 hover:border-blue-300',
                  green: isSelected ? 'border-green-500 bg-green-50 dark:bg-green-900/20' : 'border-gray-300 hover:border-green-300',
                  purple: isSelected ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20' : 'border-gray-300 hover:border-purple-300',
                  amber: isSelected ? 'border-amber-500 bg-amber-50 dark:bg-amber-900/20' : 'border-gray-300 hover:border-amber-300'
                };

                return (
                  <button
                    key={template.name}
                    type="button"
                    onClick={() => applyTemplate(template)}
                    className={`p-3 border rounded-lg text-left transition-colors ${colorClasses[template.color as keyof typeof colorClasses]} dark:border-gray-600 dark:hover:border-gray-500`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h4 className={`font-medium ${isSelected ? 'text-gray-900 dark:text-white' : 'text-gray-800 dark:text-gray-200'}`}>
                        {template.name}
                      </h4>
                      {isSelected && (
                        <CheckIcon className="w-4 h-4 text-blue-600" />
                      )}
                    </div>
                    <p className={`text-sm ${isSelected ? 'text-gray-700 dark:text-gray-300' : 'text-gray-600 dark:text-gray-400'}`}>
                      {template.description}
                    </p>
                    <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                      {getPermissionCount(template.permissions)} permissions
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Individual Permissions */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Individual Permissions
            </h3>
            <div className="space-y-4">
              {Object.entries(PERMISSION_DETAILS).map(([key, details]) => {
                const isEnabled = permissions[key as keyof MemberPermissions];
                const wasChanged = permissions[key as keyof MemberPermissions] !== currentPermissions[key as keyof MemberPermissions];

                return (
                  <div
                    key={key}
                    className={`p-4 border rounded-lg transition-colors ${
                      isEnabled 
                        ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' 
                        : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
                    } ${wasChanged ? 'ring-2 ring-blue-500/20' : ''}`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <span className="text-lg mr-2">{details.icon}</span>
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">
                              {details.title}
                            </h4>
                            <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                              {details.category}
                            </span>
                          </div>
                          {wasChanged && (
                            <span className="ml-2 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {details.description}
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={() => handlePermissionToggle(key as keyof MemberPermissions)}
                        className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ml-4 ${
                          isEnabled ? 'bg-green-600' : 'bg-gray-200 dark:bg-gray-700'
                        }`}
                        role="switch"
                        aria-checked={isEnabled}
                      >
                        <span
                          aria-hidden="true"
                          className={`inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                            isEnabled ? 'translate-x-5' : 'translate-x-0'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {error && (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="text-red-800 dark:text-red-200 text-sm">{error}</div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {hasChanges ? (
                <span className="flex items-center">
                  <span className="w-2 h-2 bg-amber-500 rounded-full mr-2 animate-pulse"></span>
                  Unsaved changes
                </span>
              ) : (
                'No changes'
              )}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isLoading || !hasChanges}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckIcon className="w-4 h-4 mr-2" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}