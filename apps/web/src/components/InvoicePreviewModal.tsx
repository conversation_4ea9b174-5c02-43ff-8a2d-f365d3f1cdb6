'use client';

import React, { useState } from 'react';
import { JobDetails } from '@/types/Job';
import { useProfile } from '@/hooks/useProfile';
import { 
  XMarkIcon,
  PrinterIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon
} from '@heroicons/react/24/outline';

interface InvoicePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: JobDetails | null;
  lineItems: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  includeVAT: boolean;
  vatAmount: number;
  dueDate: string;
  invoiceNumber?: string;
}

// Helper function to parse address into components
const parseAddress = (address: string | null) => {
  if (!address) return { street: '', city: '', postcode: '' };
  
  const lines = address.split('\n').map(line => line.trim()).filter(Boolean);
  if (lines.length === 0) return { street: '', city: '', postcode: '' };
  
  // Try to extract postcode (UK format: letters/numbers at end)
  const lastLine = lines[lines.length - 1];
  const postcodeMatch = lastLine.match(/([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})$/i);
  
  if (postcodeMatch) {
    const postcode = postcodeMatch[1];
    const remainingLastLine = lastLine.replace(postcodeMatch[0], '').trim();
    const city = remainingLastLine || (lines.length > 1 ? lines[lines.length - 2] : '');
    const street = lines.slice(0, -1).join(', ') || remainingLastLine;
    
    return { street, city, postcode };
  }
  
  // Fallback: assume last line is city, rest is street
  return {
    street: lines.slice(0, -1).join(', '),
    city: lines[lines.length - 1],
    postcode: ''
  };
};

export const InvoicePreviewModal: React.FC<InvoicePreviewModalProps> = ({
  isOpen,
  onClose,
  job,
  lineItems,
  includeVAT,
  vatAmount,
  dueDate,
  invoiceNumber = "INV-001"
}) => {
  const [zoom, setZoom] = useState(100);
  const { data: profile } = useProfile();

  if (!isOpen || !job) return null;

  // Parse business details from user profile
  const addressParts = parseAddress(profile?.address || null);
  const businessDetails = {
    name: profile?.company_name || profile?.full_name || "Your Business Name",
    street: addressParts.street || "123 Business Street",
    city: addressParts.city || "Your City",
    postcode: addressParts.postcode || "AB1 2CD",
    phone: profile?.phone || "************",
    email: profile?.email || "<EMAIL>",
    website: profile?.website || "www.yourbusiness.co.uk",
    vatNumber: profile?.vat_number || "GB123456789"
  };

  const subtotal = lineItems.reduce((sum, item) => sum + item.total, 0);
  const total = subtotal + (includeVAT ? vatAmount : 0);

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // TODO: Implement PDF download
    console.log('Download invoice as PDF');
  };

  const handleShare = () => {
    // TODO: Implement sharing functionality
    console.log('Share invoice');
  };

  const zoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200));
  };

  const zoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 50));
  };

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />
      
      {/* Modal */}
      <div className="relative w-full h-full flex flex-col">
        {/* Header */}
        <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Invoice Preview
            </h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={zoomOut}
                disabled={zoom <= 50}
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 disabled:opacity-50"
              >
                <MagnifyingGlassMinusIcon className="w-4 h-4" />
              </button>
              <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[3rem] text-center">
                {zoom}%
              </span>
              <button
                onClick={zoomIn}
                disabled={zoom >= 200}
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 disabled:opacity-50"
              >
                <MagnifyingGlassPlusIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePrint}
              className="flex items-center space-x-2 px-3 py-1.5 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <PrinterIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Print</span>
            </button>
            <button
              onClick={handleDownload}
              className="flex items-center space-x-2 px-3 py-1.5 text-sm bg-green-600 text-white rounded hover:bg-green-700"
            >
              <ArrowDownTrayIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Download</span>
            </button>
            <button
              onClick={handleShare}
              className="flex items-center space-x-2 px-3 py-1.5 text-sm bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              <ShareIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Share</span>
            </button>
            <button
              onClick={onClose}
              className="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto bg-gray-100 dark:bg-gray-800 p-2 sm:p-4">
          <div 
            className="max-w-4xl mx-auto bg-white shadow-lg"
            style={{ 
              transform: `scale(${zoom / 100})`, 
              transformOrigin: 'top center',
              minWidth: '320px'
            }}
          >
            {/* Invoice Document */}
            <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
              {/* Header */}
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start space-y-4 sm:space-y-0">
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">INVOICE</h1>
                  <p className="text-base sm:text-lg text-gray-600">#{invoiceNumber}</p>
                </div>
                <div className="text-left sm:text-right">
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900">{businessDetails.name}</h2>
                  <div className="text-xs sm:text-sm text-gray-600 mt-1">
                    {businessDetails.street && <p>{businessDetails.street}</p>}
                    <p>{businessDetails.city} {businessDetails.postcode}</p>
                    <p>Tel: {businessDetails.phone}</p>
                    <p>Email: {businessDetails.email}</p>
                    {businessDetails.vatNumber && (
                      <p>VAT: {businessDetails.vatNumber}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Invoice Details */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8">
                <div>
                  <h3 className="text-xs sm:text-sm font-semibold text-gray-900 mb-2">BILL TO:</h3>
                  <div className="text-xs sm:text-sm text-gray-600">
                    <p className="font-medium">{job.client.name}</p>
                    {job.client.address && <p>{job.client.address}</p>}
                    {job.client.email && <p>{job.client.email}</p>}
                    {job.client.phone && <p>{job.client.phone}</p>}
                  </div>
                </div>
                <div>
                  <div className="space-y-2 text-xs sm:text-sm">
                    <div className="flex justify-between">
                      <span className="font-medium">Invoice Date:</span>
                      <span>{new Date().toLocaleDateString('en-GB')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Due Date:</span>
                      <span>{new Date(dueDate).toLocaleDateString('en-GB')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Job:</span>
                      <span className="truncate ml-2">{job.title}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Line Items */}
              <div className="border-t-2 border-gray-400 pt-4">
                <div className="overflow-x-auto">
                  <table className="w-full min-w-[300px]">
                    <thead>
                      <tr className="border-b-2 border-gray-400">
                        <th className="text-left py-2 text-xs sm:text-sm font-semibold text-gray-900">Description</th>
                        <th className="text-center py-2 text-xs sm:text-sm font-semibold text-gray-900 w-12 sm:w-20">Qty</th>
                        <th className="text-right py-2 text-xs sm:text-sm font-semibold text-gray-900 w-16 sm:w-24">Unit Price</th>
                        <th className="text-right py-2 text-xs sm:text-sm font-semibold text-gray-900 w-16 sm:w-24">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {lineItems.map((item, index) => (
                        <tr key={item.id} className="border-b border-gray-300">
                          <td className="py-3 text-xs sm:text-sm text-gray-900 pr-2">{item.description}</td>
                          <td className="py-3 text-xs sm:text-sm text-gray-600 text-center">{item.quantity}</td>
                          <td className="py-3 text-xs sm:text-sm text-gray-600 text-right">£{item.unitPrice.toFixed(2)}</td>
                          <td className="py-3 text-xs sm:text-sm text-gray-900 text-right font-medium">£{item.total.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Totals */}
              <div className="flex justify-end">
                <div className="w-48 sm:w-64 space-y-2">
                  <div className="flex justify-between text-xs sm:text-sm">
                    <span>Subtotal:</span>
                    <span>£{subtotal.toFixed(2)}</span>
                  </div>
                  {includeVAT && (
                    <div className="flex justify-between text-xs sm:text-sm">
                      <span>VAT (20%):</span>
                      <span>£{vatAmount.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between text-base sm:text-lg font-bold border-t-2 border-gray-400 pt-2">
                    <span>Total:</span>
                    <span>£{total.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Payment Terms */}
              <div className="border-t-2 border-gray-400 pt-6">
                <h3 className="text-xs sm:text-sm font-semibold text-gray-900 mb-2">PAYMENT TERMS</h3>
                <div className="text-xs sm:text-sm text-gray-600 space-y-1">
                  <p>Payment is due within 30 days of invoice date.</p>
                  <p>Late payments may incur additional charges.</p>
                  <p>Please reference invoice number #{invoiceNumber} when making payment.</p>
                </div>
              </div>

              {/* Footer */}
              <div className="text-center text-xs text-gray-500 border-t-2 border-gray-400 pt-4">
                <p>Thank you for your business!</p>
                {businessDetails.website && (
                  <p className="mt-1">{businessDetails.website}</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 