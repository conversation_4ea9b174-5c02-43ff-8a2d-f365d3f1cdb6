import { useState } from 'react';
import { XMarkIcon, TrashIcon } from '@heroicons/react/24/outline';
import { TeamDetails, UpdateTeamData } from '../types/Team';

interface TeamSettingsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  teamData: TeamDetails | null;
  onUpdateTeam: (data: UpdateTeamData) => void;
  onDeleteTeam: () => void;
}

export default function TeamSettingsDrawer({ 
  isOpen, 
  onClose, 
  teamData,
  onUpdateTeam,
  onDeleteTeam
}: TeamSettingsDrawerProps) {
  const [formData, setFormData] = useState<UpdateTeamData>({
    name: teamData?.name || '',
    allow_invites: teamData?.allow_invites || true,
    require_job_approval: teamData?.require_job_approval || false,
    auto_assign_jobs: teamData?.auto_assign_jobs || true,
    default_job_visibility: teamData?.default_job_visibility || 'team_only'
  });
  const [isSaving, setIsSaving] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Update form data when teamData changes
  useState(() => {
    if (teamData) {
      setFormData({
        name: teamData.name,
        allow_invites: teamData.allow_invites,
        require_job_approval: teamData.require_job_approval,
        auto_assign_jobs: teamData.auto_assign_jobs,
        default_job_visibility: teamData.default_job_visibility
      });
    }
  });

  const handleSave = async () => {
    if (!formData.name?.trim()) return;

    setIsSaving(true);
    try {
      // Removed artificial delay
      onUpdateTeam(formData);
      onClose();
    } catch (error) {
      console.error('Failed to update team:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    setIsSaving(true);
    try {
      // Removed artificial delay
      onDeleteTeam();
      onClose();
    } catch (error) {
      console.error('Failed to delete team:', error);
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    if (!isSaving) {
      setShowDeleteConfirm(false);
      onClose();
    }
  };

  // Only owners can delete team
  const canDeleteTeam = teamData?.role === 'owner';
  // Only owners and managers can modify settings
  const canModifySettings = teamData?.role === 'owner' || teamData?.role === 'manager';

  if (!isOpen || !teamData) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={handleClose} />
      
      <div className="fixed right-0 top-0 h-full w-full max-w-md bg-white dark:bg-gray-900 shadow-xl">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Team Settings</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Configure team preferences and permissions
              </p>
            </div>
            <button
              onClick={handleClose}
              disabled={isSaving}
              className="rounded-lg p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto px-6 py-6">
            {!canModifySettings ? (
              <div className="text-center py-8">
                <p className="text-gray-600 dark:text-gray-400">
                  You don't have permission to modify team settings.
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Team Name */}
                <div>
                  <label htmlFor="teamName" className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Team Name
                  </label>
                  <input
                    type="text"
                    id="teamName"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                    disabled={isSaving}
                  />
                </div>

                {/* Team Preferences */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">Team Preferences</h3>
                  
                  {/* Allow Members to Invite Others */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm text-gray-900 dark:text-white">Allow Members to Invite Others</label>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Let team members send invitations to new members</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, allow_invites: !prev.allow_invites }))}
                      disabled={isSaving}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        formData.allow_invites ? 'bg-green-600' : 'bg-gray-200 dark:bg-gray-700'
                      } disabled:opacity-50`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          formData.allow_invites ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  {/* Require Approval for Job Changes */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm text-gray-900 dark:text-white">Require Approval for Job Changes</label>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Team members need approval to modify job details</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, require_job_approval: !prev.require_job_approval }))}
                      disabled={isSaving}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        formData.require_job_approval ? 'bg-orange-600' : 'bg-gray-200 dark:bg-gray-700'
                      } disabled:opacity-50`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          formData.require_job_approval ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  {/* Auto-Assign New Jobs */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm text-gray-900 dark:text-white">Auto-Assign New Jobs</label>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Automatically assign new jobs to available team members</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, auto_assign_jobs: !prev.auto_assign_jobs }))}
                      disabled={isSaving}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        formData.auto_assign_jobs ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                      } disabled:opacity-50`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          formData.auto_assign_jobs ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>

                {/* Default Job Visibility */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Default Job Visibility
                  </label>
                  <select
                    value={formData.default_job_visibility}
                    onChange={(e) => setFormData(prev => ({ ...prev, default_job_visibility: e.target.value as 'owner_only' | 'team_only' | 'public' }))}
                    disabled={isSaving}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                  >
                    <option value="owner_only">Visible to entire team</option>
                  </select>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    Who can see new jobs by default
                  </p>
                </div>
              </div>
            )}

            {/* Delete Team Section */}
            {canDeleteTeam && (
              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-sm font-medium text-red-600 dark:text-red-400 mb-2">Danger Zone</h3>
                {!showDeleteConfirm ? (
                  <button
                    onClick={() => setShowDeleteConfirm(true)}
                    disabled={isSaving}
                    className="flex items-center px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg disabled:opacity-50"
                  >
                    <TrashIcon className="w-4 h-4 mr-2" />
                    Delete Team
                  </button>
                ) : (
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Are you sure you want to delete this team? This action cannot be undone.
                    </p>
                    <div className="flex space-x-2">
                      <button
                        onClick={handleDelete}
                        disabled={isSaving}
                        className="px-3 py-2 text-sm text-white bg-red-600 hover:bg-red-700 rounded-lg disabled:opacity-50"
                      >
                        {isSaving ? 'Deleting...' : 'Yes, Delete'}
                      </button>
                      <button
                        onClick={() => setShowDeleteConfirm(false)}
                        disabled={isSaving}
                        className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg disabled:opacity-50"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          {canModifySettings && (
            <div className="border-t border-gray-200 dark:border-gray-700 px-6 py-4">
              <button
                onClick={handleSave}
                disabled={!formData.name?.trim() || isSaving}
                className="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? 'Saving Team Settings...' : 'Save Team Settings'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 