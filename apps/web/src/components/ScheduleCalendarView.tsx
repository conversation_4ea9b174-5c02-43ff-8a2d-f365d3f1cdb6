import React, { useState, useMemo } from 'react';
import { Job, formatJobSchedule, hasTimeScheduling } from '@/types/Job';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CalendarIcon,
  ClockIcon,
  UserIcon,
} from '@heroicons/react/24/outline';
import {
  CalendarDaysIcon,
} from '@heroicons/react/24/solid';

interface ScheduleCalendarViewProps {
  jobs: Job[];
  onJobClick?: (jobId: string) => void;
  onDateClick?: (date: Date) => void;
  isLoading?: boolean;
}

const ScheduleCalendarView: React.FC<ScheduleCalendarViewProps> = ({
  jobs,
  onJobClick,
  onDateClick,
  isLoading = false
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  // Get current month info
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  const today = new Date();

  // Calculate calendar days
  const calendarDays = useMemo(() => {
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
    const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
    const firstCalendarDay = new Date(firstDayOfMonth);
    firstCalendarDay.setDate(firstCalendarDay.getDate() - firstDayOfMonth.getDay());

    const days = [];
    const currentCalendarDay = new Date(firstCalendarDay);

    // Generate 42 days (6 weeks) for the calendar grid
    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentCalendarDay));
      currentCalendarDay.setDate(currentCalendarDay.getDate() + 1);
    }

    return days;
  }, [currentMonth, currentYear]);

  // Group jobs by date
  const jobsByDate = useMemo(() => {
    const grouped: Record<string, Job[]> = {};
    
    jobs.forEach(job => {
      if (job.scheduled_at) {
        const dateKey = new Date(job.scheduled_at).toDateString();
        if (!grouped[dateKey]) {
          grouped[dateKey] = [];
        }
        grouped[dateKey].push(job);
      }
    });

    // Sort jobs by time for each date
    Object.keys(grouped).forEach(dateKey => {
      grouped[dateKey].sort((a, b) => {
        if (a.scheduled_start_time && b.scheduled_start_time) {
          return a.scheduled_start_time.localeCompare(b.scheduled_start_time);
        }
        if (a.scheduled_start_time && !b.scheduled_start_time) return -1;
        if (!a.scheduled_start_time && b.scheduled_start_time) return 1;
        return 0;
      });
    });

    return grouped;
  }, [jobs]);

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const isToday = (date: Date) => {
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentMonth;
  };

  const getJobsForDate = (date: Date) => {
    return jobsByDate[date.toDateString()] || [];
  };

  const getStatusColor = (status: Job['status']) => {
    const colors = {
      new: 'bg-blue-500',
      quoted: 'bg-indigo-500',
      in_progress: 'bg-green-500',
      on_hold: 'bg-orange-500',
      completed: 'bg-teal-500',
      archived: 'bg-gray-500',
    };
    return colors[status];
  };

  const handleDateClick = (date: Date) => {
    onDateClick?.(date);
  };

  const handleJobClick = (e: React.MouseEvent, jobId: string) => {
    e.stopPropagation();
    onJobClick?.(jobId);
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-6"></div>
          <div className="grid grid-cols-7 gap-1 mb-4">
            {Array.from({ length: 7 }).map((_, i) => (
              <div key={i} className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-7 gap-1">
            {Array.from({ length: 35 }).map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700">
      {/* Calendar Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <CalendarDaysIcon className="w-6 h-6 text-blue-600 dark:text-blue-400 mr-2" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {currentDate.toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}
            </h2>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={goToToday}
              className="px-3 py-1.5 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
            >
              Today
            </button>
            <button
              onClick={() => navigateMonth('prev')}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            <button
              onClick={() => navigateMonth('next')}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Day Labels */}
        <div className="grid grid-cols-7 gap-1">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-600 dark:text-gray-400">
              {day}
            </div>
          ))}
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-6">
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((date, index) => {
            const dayJobs = getJobsForDate(date);
            const isCurrentMonthDay = isCurrentMonth(date);
            const isTodayDay = isToday(date);
            
            return (
              <div
                key={index}
                onClick={() => handleDateClick(date)}
                className={`min-h-24 p-1 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer transition-colors ${
                  isTodayDay
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-700'
                    : isCurrentMonthDay
                    ? 'hover:bg-gray-50 dark:hover:bg-gray-700'
                    : 'bg-gray-50 dark:bg-gray-800 opacity-50'
                }`}
              >
                {/* Date Number */}
                <div className="flex justify-between items-start mb-1">
                  <span
                    className={`text-sm font-medium ${
                      isTodayDay
                        ? 'text-blue-600 dark:text-blue-400'
                        : isCurrentMonthDay
                        ? 'text-gray-900 dark:text-white'
                        : 'text-gray-400 dark:text-gray-600'
                    }`}
                  >
                    {date.getDate()}
                  </span>
                  {dayJobs.length > 0 && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {dayJobs.length}
                    </span>
                  )}
                </div>

                {/* Jobs for this date */}
                <div className="space-y-1">
                  {dayJobs.slice(0, 3).map((job, jobIndex) => (
                    <div
                      key={job.id}
                      onClick={(e) => handleJobClick(e, job.id)}
                      className={`p-1 rounded text-xs hover:opacity-80 transition-opacity cursor-pointer ${
                        job.status === 'completed' 
                          ? 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400'
                          : job.status === 'in_progress'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : job.status === 'quoted'
                          ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                      }`}
                      title={`${job.title} - ${job.client.name}${hasTimeScheduling(job) ? ` (${job.scheduled_start_time})` : ''}`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center min-w-0 flex-1">
                          <span className={`w-1.5 h-1.5 rounded-full flex-shrink-0 mr-1 ${getStatusColor(job.status)}`} />
                          <span className="truncate flex-1" title={job.title}>
                            {job.title}
                          </span>
                        </div>
                        {hasTimeScheduling(job) && (
                          <ClockIcon className="w-3 h-3 ml-1 flex-shrink-0" />
                        )}
                      </div>
                      {hasTimeScheduling(job) && (
                        <div className="text-xs opacity-70 mt-0.5 ml-2.5">
                          {job.scheduled_start_time}
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {/* Show "+X more" if there are more jobs */}
                  {dayJobs.length > 3 && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 pl-1">
                      +{dayJobs.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Calendar Legend */}
      <div className="px-6 pb-6">
        <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-blue-500 mr-1"></span>
              New
            </div>
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-indigo-500 mr-1"></span>
              Quoted
            </div>
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
              In Progress
            </div>
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-teal-500 mr-1"></span>
              Completed
            </div>
          </div>
          <div className="flex items-center">
            <ClockIcon className="w-3 h-3 mr-1" />
            Time scheduled
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleCalendarView;