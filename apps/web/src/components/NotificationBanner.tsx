'use client';

import { useEffect, useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  InformationCircleIcon, 
  XCircleIcon 
} from '@heroicons/react/24/solid';
import { NotificationType } from '@/types';

interface NotificationBannerProps {
  id: string;
  message: string;
  type: NotificationType;
  duration?: number; // Auto-dismiss duration in milliseconds
  onDismiss: (id: string) => void;
}

const getTypeConfig = (type: NotificationType) => {
  switch (type) {
    case 'success':
    case 'quote_accepted':
    case 'contract_signed':
    case 'payment_received':
      return {
        bgColor: 'bg-green-50 dark:bg-green-900/20',
        borderColor: 'border-green-200 dark:border-green-800',
        textColor: 'text-green-800 dark:text-green-200',
        iconColor: 'text-green-400',
        icon: CheckCircleIcon
      };
    case 'warning':
      return {
        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
        borderColor: 'border-yellow-200 dark:border-yellow-800',
        textColor: 'text-yellow-800 dark:text-yellow-200',
        iconColor: 'text-yellow-400',
        icon: ExclamationTriangleIcon
      };
    case 'error':
      return {
        bgColor: 'bg-red-50 dark:bg-red-900/20',
        borderColor: 'border-red-200 dark:border-red-800',
        textColor: 'text-red-800 dark:text-red-200',
        iconColor: 'text-red-400',
        icon: XCircleIcon
      };
    case 'job_assigned':
      return {
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        borderColor: 'border-blue-200 dark:border-blue-800',
        textColor: 'text-blue-800 dark:text-blue-200',
        iconColor: 'text-blue-400',
        icon: InformationCircleIcon
      };
    case 'team_invite':
      return {
        bgColor: 'bg-purple-50 dark:bg-purple-900/20',
        borderColor: 'border-purple-200 dark:border-purple-800',
        textColor: 'text-purple-800 dark:text-purple-200',
        iconColor: 'text-purple-400',
        icon: InformationCircleIcon
      };
    case 'info':
    default:
      return {
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        borderColor: 'border-blue-200 dark:border-blue-800',
        textColor: 'text-blue-800 dark:text-blue-200',
        iconColor: 'text-blue-400',
        icon: InformationCircleIcon
      };
  }
};

export default function NotificationBanner({
  id,
  message,
  type,
  duration = 5000,
  onDismiss
}: NotificationBannerProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [isAnimating, setIsAnimating] = useState(false);

  const config = getTypeConfig(type);
  const IconComponent = config.icon;

  useEffect(() => {
    // Start entrance animation
    setIsAnimating(true);
    
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration]);

  const handleDismiss = () => {
    setIsVisible(false);
    // Wait for exit animation to complete before removing from DOM
    setTimeout(() => {
      onDismiss(id);
    }, 200);
  };

  if (!isVisible && !isAnimating) {
    return null;
  }

  return (
    <div
      className={`
        fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4
        transition-all duration-200 ease-in-out
        ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'}
      `}
    >
      <div
        className={`
          ${config.bgColor} ${config.borderColor} ${config.textColor}
          border rounded-lg shadow-lg p-4
        `}
      >
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <IconComponent 
              className={`h-5 w-5 ${config.iconColor}`} 
              aria-hidden="true" 
            />
          </div>
          <div className="ml-3 w-0 flex-1">
            <p className="text-sm font-medium">
              {message}
            </p>
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <button
              type="button"
              className={`
                rounded-md inline-flex ${config.textColor} hover:${config.textColor}/80
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
              `}
              onClick={handleDismiss}
            >
              <span className="sr-only">Close</span>
              <XMarkIcon className="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Notification container component to handle stacking
interface NotificationContainerProps {
  notifications: Array<{
    id: string;
    message: string;
    type: NotificationType;
    duration?: number;
  }>;
  onDismiss: (id: string) => void;
}

export function NotificationContainer({ notifications, onDismiss }: NotificationContainerProps) {
  return (
    <div className="fixed top-0 left-0 right-0 z-50 pointer-events-none">
      <div className="flex flex-col items-center space-y-2 pt-4">
        {notifications.map((notification, index) => (
          <div
            key={notification.id}
            className="pointer-events-auto"
            style={{
              transform: `translateY(${index * 10}px)`,
              zIndex: 50 - index
            }}
          >
            <NotificationBanner
              id={notification.id}
              message={notification.message}
              type={notification.type}
              duration={notification.duration}
              onDismiss={onDismiss}
            />
          </div>
        ))}
      </div>
    </div>
  );
} 