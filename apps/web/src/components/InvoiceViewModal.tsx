'use client';

import React, { useState } from 'react';
import { Invoice } from '@/hooks/useInvoices';
import { useProfile } from '@/hooks/useProfile';
import { 
  XMarkIcon,
  PrinterIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

interface InvoiceViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: Invoice;
  onEdit?: (invoice: Invoice) => void;
  onDelete?: (invoice: Invoice) => void;
}

// Helper function to parse address into components
const parseAddress = (address: string | null) => {
  if (!address) return { street: '', city: '', postcode: '' };
  
  const lines = address.split('\n').map(line => line.trim()).filter(Boolean);
  if (lines.length === 0) return { street: '', city: '', postcode: '' };
  
  // Try to extract postcode (UK format: letters/numbers at end)
  const lastLine = lines[lines.length - 1];
  const postcodeMatch = lastLine.match(/([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})$/i);
  
  if (postcodeMatch) {
    const postcode = postcodeMatch[1];
    const remainingLastLine = lastLine.replace(postcodeMatch[0], '').trim();
    const city = remainingLastLine || (lines.length > 1 ? lines[lines.length - 2] : '');
    const street = lines.slice(0, -1).join(', ') || remainingLastLine;
    
    return { street, city, postcode };
  }
  
  // Fallback: assume last line is city, everything else is street
  if (lines.length === 1) {
    return { street: lines[0], city: '', postcode: '' };
  }
  
  return {
    street: lines.slice(0, -1).join(', '),
    city: lines[lines.length - 1],
    postcode: ''
  };
};

export const InvoiceViewModal: React.FC<InvoiceViewModalProps> = ({
  isOpen,
  onClose,
  invoice,
  onEdit,
  onDelete
}) => {
  const { profile } = useProfile();
  const [zoom, setZoom] = useState(100);

  if (!isOpen) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'text-green-600';
      case 'sent': return 'text-blue-600';
      case 'overdue': return 'text-red-600';
      case 'draft': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'sent': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const subtotal = invoice.amount - (invoice.tax || 0);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Invoice Preview
            </h2>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(invoice.status)}`}>
              {invoice.status.toUpperCase()}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Zoom Controls */}
            <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg">
              <button
                onClick={() => setZoom(Math.max(50, zoom - 10))}
                className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors"
                title="Zoom Out"
              >
                <MagnifyingGlassMinusIcon className="w-4 h-4" />
              </button>
              <span className="px-2 text-sm text-gray-600 dark:text-gray-300 min-w-[4rem] text-center">
                {zoom}%
              </span>
              <button
                onClick={() => setZoom(Math.min(200, zoom + 10))}
                className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors"
                title="Zoom In"
              >
                <MagnifyingGlassPlusIcon className="w-4 h-4" />
              </button>
            </div>

            {/* Action Buttons */}
            {onEdit && (
              <button
                onClick={() => onEdit(invoice)}
                className="p-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-lg transition-colors"
                title="Edit Invoice"
              >
                <PencilIcon className="w-5 h-5" />
              </button>
            )}

            {onDelete && (
              <button
                onClick={() => onDelete(invoice)}
                className="p-2 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                title="Delete Invoice"
              >
                <TrashIcon className="w-5 h-5" />
              </button>
            )}

            <button
              onClick={() => window.print()}
              className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Print"
            >
              <PrinterIcon className="w-5 h-5" />
            </button>

            <button
              onClick={onClose}
              className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Invoice Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div 
            className="mx-auto bg-white shadow-lg rounded-lg overflow-hidden"
            style={{ 
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'top center',
              width: `${(100 / zoom) * 100}%`
            }}
          >
            {/* Invoice Header */}
            <div className="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white p-8">
              <div className="flex justify-between items-start">
                <div>
                  <h1 className="text-3xl font-bold mb-2">INVOICE</h1>
                  <p className="text-indigo-200">Invoice #{invoice.id.slice(0, 8).toUpperCase()}</p>
                </div>
                <div className="text-right">
                  <h2 className="text-xl font-semibold">{profile?.company_name || profile?.full_name || 'Your Business'}</h2>
                  {profile?.phone && <p className="text-indigo-200">{profile.phone}</p>}
                  {profile?.email && <p className="text-indigo-200">{profile.email}</p>}
                </div>
              </div>
            </div>

            {/* Invoice Details */}
            <div className="p-8">
              <div className="grid grid-cols-2 gap-8 mb-8">
                {/* Bill To */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Bill To:</h3>
                  <div className="text-gray-700">
                    <p className="font-medium">{invoice.client?.name || 'Client Name'}</p>
                    {invoice.client?.email && <p>{invoice.client.email}</p>}
                    {invoice.client?.phone && <p>{invoice.client.phone}</p>}
                  </div>
                </div>

                {/* Invoice Info */}
                <div className="text-right">
                  <div className="space-y-2">
                    <div>
                      <span className="text-gray-600">Invoice Date:</span>
                      <span className="ml-2 font-medium">{formatDate(invoice.created_at)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Due Date:</span>
                      <span className={`ml-2 font-medium ${getStatusColor(invoice.status)}`}>
                        {formatDate(invoice.due_date)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Job:</span>
                      <span className="ml-2 font-medium">{invoice.job?.title || 'N/A'}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Line Items */}
              <div className="mb-8">
                <table className="w-full">
                  <thead>
                    <tr className="border-b-2 border-gray-200">
                      <th className="text-left py-3 font-semibold text-gray-900">Description</th>
                      <th className="text-center py-3 font-semibold text-gray-900 w-20">Qty</th>
                      <th className="text-right py-3 font-semibold text-gray-900 w-24">Rate</th>
                      <th className="text-right py-3 font-semibold text-gray-900 w-24">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoice.line_items && invoice.line_items.length > 0 ? (
                      invoice.line_items.map((item, index) => (
                        <tr key={index} className="border-b border-gray-100">
                          <td className="py-3 text-gray-700">{item.description}</td>
                          <td className="py-3 text-center text-gray-700">{item.quantity}</td>
                          <td className="py-3 text-right text-gray-700">£{item.unitPrice.toFixed(2)}</td>
                          <td className="py-3 text-right text-gray-700">£{item.total.toFixed(2)}</td>
                        </tr>
                      ))
                    ) : (
                      <tr className="border-b border-gray-100">
                        <td className="py-3 text-gray-700">{invoice.details || 'Service Provided'}</td>
                        <td className="py-3 text-center text-gray-700">1</td>
                        <td className="py-3 text-right text-gray-700">£{subtotal.toFixed(2)}</td>
                        <td className="py-3 text-right text-gray-700">£{subtotal.toFixed(2)}</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Totals */}
              <div className="flex justify-end">
                <div className="w-64">
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-600">Subtotal:</span>
                      <span className="font-medium">£{subtotal.toFixed(2)}</span>
                    </div>
                    {invoice.tax > 0 && (
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-600">VAT (20%):</span>
                        <span className="font-medium">£{invoice.tax.toFixed(2)}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                      <span>Total:</span>
                      <span>£{invoice.amount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div className="mt-8 pt-8 border-t border-gray-200">
                <h4 className="font-semibold text-gray-900 mb-3">Payment Terms</h4>
                <p className="text-gray-600 text-sm">
                  Payment is due within 30 days of invoice date. Late payments may incur additional charges.
                  Please include the invoice number with your payment.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
