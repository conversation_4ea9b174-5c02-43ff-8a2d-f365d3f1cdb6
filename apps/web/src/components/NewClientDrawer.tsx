'use client';

import React, { useState } from 'react';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { Input } from '@deskbelt/ui';
import { Textarea } from '@deskbelt/ui';
import { XMarkIcon, UserPlusIcon, SparklesIcon } from '@heroicons/react/24/outline';

interface NewClientDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  /**
   * Called after the client is created successfully. Receives the client object
   * returned from the backend so caller can refresh lists, etc.
   */
  onClientCreated?: (client: any) => void;
}

interface FormData {
  name: string;
  business_name: string;
  address: string;
  phone: string;
  email: string;
}

interface FormErrors {
  name?: string;
  business_name?: string;
  address?: string;
  phone?: string;
  email?: string;
}

/**
 * A lean, single-page form for quickly adding a client.
 * 
 * Required fields: name & address
 * Optional fields: business_name, phone, email
 */
export const NewClientDrawer: React.FC<NewClientDrawerProps> = ({ 
  isOpen, 
  onClose, 
  onClientCreated 
}) => {
  const { authenticatedPost } = useAuthenticatedFetch();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    business_name: '',
    address: '',
    phone: '',
    email: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  
  // AI parsing states
  const [quickCreateText, setQuickCreateText] = useState('');
  const [isParsingAI, setIsParsingAI] = useState(false);
  const [showAIParsing, setShowAIParsing] = useState(false);
  const [parseError, setParseError] = useState<string | null>(null);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Required fields
    if (!formData.name.trim()) {
      newErrors.name = 'Client name is required';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }

    // Email validation (if provided)
    if (formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email.trim())) {
        newErrors.email = 'Please enter a valid email address';
      }
    }

    // Phone validation (if provided) - UK format
    if (formData.phone.trim()) {
      const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,15}$/;
      if (!phoneRegex.test(formData.phone.trim())) {
        newErrors.phone = 'Please enter a valid phone number';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // Clear submit error when user makes changes
    if (submitError) {
      setSubmitError(null);
    }
  };

  // AI parsing function
  const handleParseWithAI = async () => {
    if (!quickCreateText.trim()) {
      setParseError('Please enter some client information to parse');
      return;
    }

    setIsParsingAI(true);
    setParseError(null);

    try {
      const response = await authenticatedPost('/api/ai/parse-client', {
        input: quickCreateText.trim()
      });

      if (response.ok) {
        const parsedData = await response.json();
        
        // Auto-populate form fields with parsed data
        setFormData(prev => ({
          name: parsedData.name || prev.name,
          business_name: parsedData.business_name || prev.business_name,
          address: parsedData.address || prev.address,
          phone: parsedData.phone || prev.phone,
          email: parsedData.email || prev.email
        }));

        // Clear any existing errors since we're updating fields
        setErrors({});
        setSubmitError(null);
        
        // Hide AI parsing section and show populated form
        setShowAIParsing(false);
        setQuickCreateText('');
      } else {
        const errorData = await response.json();
        setParseError(errorData.error || 'Failed to parse client information. Please try manual entry.');
      }
    } catch (error) {
      console.error('AI parsing error:', error);
      setParseError('Unable to connect to AI service. Please try manual entry.');
    } finally {
      setIsParsingAI(false);
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setSubmitError(null);
    setIsSubmitting(true);

    try {
      const response = await authenticatedPost('/api/clients', {
        name: formData.name.trim(),
        business_name: formData.business_name.trim() || null,
        address: formData.address.trim(),
        phone: formData.phone.trim() || null,
        email: formData.email.trim() || null,
      });

      if (response.ok) {
        const result = await response.json();
        const newClient = result.client || result;
        
        // Reset form
        setFormData({
          name: '',
          business_name: '',
          address: '',
          phone: '',
          email: ''
        });
        setErrors({});
        setSubmitError(null);
        setQuickCreateText('');
        setShowAIParsing(false);
        setParseError(null);
        
        // Notify parent component
        onClientCreated?.(newClient);
        
        // Close drawer
        onClose();
      } else {
        console.error('Response not OK:', {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries())
        });
        
        let errorData;
        try {
          errorData = await response.json();
          console.error('Error response data:', errorData);
        } catch (parseError) {
          console.error('Failed to parse error response as JSON:', parseError);
          errorData = { error: `Server error: ${response.status} ${response.statusText}` };
        }
        
        setSubmitError(errorData.error || errorData.message || `Server error: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error creating client:', error);
      setSubmitError('Unable to connect to server. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form when closing
    setFormData({
      name: '',
      business_name: '',
      address: '',
      phone: '',
      email: ''
    });
    setErrors({});
    setSubmitError(null);
    setQuickCreateText('');
    setShowAIParsing(false);
    setParseError(null);
    onClose();
  };

  const isFormValid = formData.name.trim() && formData.address.trim();

  return (
    <Drawer
      isOpen={isOpen}
      onClose={handleClose}
      side="right"
      size="xl"
      showCloseButton={false}
      className="top-16 h-[calc(100%-4rem)] md:left-1/2 md:-translate-x-1/2 md:right-auto"
    >
      <div className="h-full flex flex-col bg-gradient-to-b from-green-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="h-full flex flex-col max-w-5xl mx-auto w-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white/80 backdrop-blur-sm">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                <UserPlusIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">New Client</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">Quickly add a new client</p>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={handleClose}>
              <XMarkIcon className="w-5 h-5" />
            </Button>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="max-w-2xl mx-auto space-y-6">
              
              {/* AI Quick Create Section */}
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <SparklesIcon className="w-5 h-5 text-orange-500" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">Quick Create with AI</h3>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAIParsing(!showAIParsing)}
                    className="text-orange-600 hover:text-orange-700"
                  >
                    {showAIParsing ? 'Hide' : 'Show'}
                  </Button>
                </div>

                {showAIParsing && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Paste client info or business card text
                      </label>
                      <Textarea
                        value={quickCreateText}
                        onChange={(e) => {
                          setQuickCreateText(e.target.value);
                          if (parseError) setParseError(null);
                        }}
                        placeholder="e.g., John Smith Electrical Services, 123 Main St, London, <EMAIL>, 020 7946 0958"
                        rows={3}
                        className="w-full"
                      />
                    </div>

                    {parseError && (
                      <div className="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                        {parseError}
                      </div>
                    )}

                    <Button
                      variant="primary"
                      onClick={handleParseWithAI}
                      disabled={isParsingAI || !quickCreateText.trim()}
                      className="bg-orange-600 hover:bg-orange-700 text-white"
                    >
                      <SparklesIcon className="w-4 h-4 mr-2" />
                      {isParsingAI ? 'Parsing...' : 'Parse with AI'}
                    </Button>
                  </div>
                )}

                {!showAIParsing && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Let AI extract client details from business cards or contact info automatically.
                  </p>
                )}
              </div>

              {/* Manual Form Fields */}
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Client Details</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Client Name - Required */}
                  <div className="md:col-span-2">
                    <Input
                      label="Client Name *"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      error={errors.name}
                      placeholder="John Smith"
                      className="w-full"
                    />
                  </div>

                  {/* Business Name - Optional */}
                  <div className="md:col-span-2">
                    <Input
                      label="Business Name"
                      value={formData.business_name}
                      onChange={(e) => handleInputChange('business_name', e.target.value)}
                      error={errors.business_name}
                      placeholder="Smith Electrical Services Ltd"
                      className="w-full"
                    />
                  </div>

                  {/* Address - Required */}
                  <div className="md:col-span-2">
                    <Textarea
                      label="Address *"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      error={errors.address}
                      placeholder="123 Main Street, London, SW1A 1AA"
                      rows={2}
                      className="w-full"
                    />
                  </div>

                  {/* Phone - Optional */}
                  <div>
                    <Input
                      label="Phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      error={errors.phone}
                      placeholder="020 7946 0958"
                      className="w-full"
                    />
                  </div>

                  {/* Email - Optional */}
                  <div>
                    <Input
                      label="Email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      error={errors.email}
                      placeholder="<EMAIL>"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* Submit Error */}
              {submitError && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-red-600 dark:text-red-400 text-sm">{submitError}</p>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 bg-white/80 backdrop-blur-sm">
            <div className="flex items-center justify-between p-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Fields marked with * are required
              </p>
              <div className="flex space-x-3">
                <Button variant="ghost" onClick={handleClose}>
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  theme="clients"
                  onClick={handleSubmit}
                  disabled={!isFormValid || isSubmitting}
                  isLoading={isSubmitting}
                >
                  {isSubmitting ? 'Creating...' : 'Create Client'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  );
}; 