'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';
import { useNotifications } from '@/hooks/useNotifications';
import { useDrawer } from '@/contexts/DrawerContext';
import { useAuth } from '@/contexts/AuthContext';
import { BellIcon, SunIcon, MoonIcon, UserIcon, CogIcon, ArrowRightOnRectangleIcon, ChevronDownIcon, Bars3Icon } from '@heroicons/react/24/outline';
import { MoonIcon as MoonIconSolid } from '@heroicons/react/24/solid';

interface Notification {
  id: string;
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'job_assigned' | 'team_invite';
  actionUrl?: string;
}

interface EnhancedTopNavProps {
  onMenuClick?: () => void;
}

const EnhancedTopNav: React.FC<EnhancedTopNavProps> = ({ onMenuClick }) => {
  const { theme, toggleTheme } = useTheme();
  const { stats, notifications, markAsRead } = useNotifications({
    includeRead: false, 
    autoRefresh: true 
  });
  const { openNotificationsDrawer } = useDrawer();
  const { signOut, profile } = useAuth();
  const router = useRouter();
  
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  
  const profileRef = useRef<HTMLDivElement>(null);
  const notificationsRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setIsProfileOpen(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setIsNotificationsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    if (isLoggingOut) return;
    
    try {
      setIsLoggingOut(true);
      // Redirect immediately to avoid 401 errors during logout
      router.push('/login');
      // Sign out after redirect is initiated
      await signOut();
    } catch (error) {
      console.error('Logout error:', error);
      setIsLoggingOut(false);
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id);
    if (notification.actionUrl) {
      router.push(notification.actionUrl);
    }
    setIsNotificationsOpen(false);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return (
          <div className="p-1.5 rounded-full bg-green-100 dark:bg-green-900/50">
            <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'warning':
        return (
          <div className="p-1.5 rounded-full bg-yellow-100 dark:bg-yellow-900/50">
            <svg className="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
        );
      case 'error':
        return (
          <div className="p-1.5 rounded-full bg-red-100 dark:bg-red-900/50">
            <svg className="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
      case 'job_assigned':
        return (
          <div className="p-1.5 rounded-full bg-blue-100 dark:bg-blue-900/50">
            <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
        );
      case 'team_invite':
        return (
          <div className="p-1.5 rounded-full bg-purple-100 dark:bg-purple-900/50">
            <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="p-1.5 rounded-full bg-gray-100 dark:bg-gray-700">
            <svg className="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <header className="sticky top-0 z-40 w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left: Hamburger Menu + Logo */}
          <div className="flex items-center space-x-4">
            {/* Hamburger Menu Button - Shows on tablet/mobile (<1024px), hidden on laptop+ (≥1024px) */}
            <button
              onClick={onMenuClick}
              className="lg:hidden p-2.5 rounded-lg text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
              aria-label="Open navigation menu"
              data-testid="hamburger-menu-button"
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link href="/dashboard" className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center shadow-md">
                  <span className="text-white text-sm font-bold">DB</span>
                </div>
                <span className="ml-2 text-xl font-bold bg-gradient-to-r from-primary-600 to-primary-700 dark:from-primary-400 dark:to-primary-300 bg-clip-text text-transparent">
                  DeskBelt
                </span>
              </Link>
            </div>
          </div>


          {/* Right: Navigation Items */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
              <div className="relative" ref={notificationsRef}>
                  <button
                      onClick={() => {
                          setIsNotificationsOpen(!isNotificationsOpen);
                          setIsProfileOpen(false);
                      }}

                      className="flex relative p-2.5 rounded-lg text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                      aria-label="Notifications">

                      <BellIcon className="h-6 w-6"/>
                      {(stats?.unread ?? 0) >= 0 && (
                          // <span className="absolute -top-2 -right-2 h-6 w-6 bg-green-500 text-white text-xs font-bold rounded-full flex items-center justify-center ring-2 ring-white dark:ring-gray-900 shadow-lg">
                           <span className="absolute top-1 right-1 bg-red-600 text-white text-xs font-bold rounded-full px-1.5 py-0.5 leading-none">
                             {(stats?.unread ?? 0) > 9 ? '9+' : stats?.unread}
                           </span>
                      )}

                  </button>



                  {/* Notifications Dropdown Menu */}
                  {isNotificationsOpen && (
                      <div
                          className="absolute right-0 mt-3 w-80 bg-white dark:bg-gray-800 rounded-xl shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden z-50 border border-gray-200 dark:border-gray-700">
                          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                              <div className="flex items-center justify-between">
                                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Notifications</h3>
                                  <button
                                      onClick={() => openNotificationsDrawer()}
                                      className="text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 transition-colors"
                                  >
                                      View all
                                  </button>
                              </div>
                          </div>
                          <div className="max-h-96 overflow-y-auto">
                              {notifications && notifications.length > 0 ? (
                                  notifications.map((notification) => (
                                      <div
                                          key={notification.id}
                                          onClick={() => handleNotificationClick(notification)}
                                          className={`px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer transition-all duration-150 ${!notification.read ? 'bg-blue-50/50 dark:bg-blue-900/20' : ''}`}
                                      >
                                          <div className="flex items-start">
                                              <div className="flex-shrink-0 pt-0.5">
                                                  {getNotificationIcon(notification.type)}
                                              </div>
                                              <div className="ml-3 flex-1">
                                                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                                                      {notification.title}
                                                  </p>
                                                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                                                      {notification.message}
                                                  </p>
                                                  <p className="mt-1 text-xs text-gray-400 dark:text-gray-500">
                                                      {formatDate(notification.createdAt)}
                                                  </p>
                                              </div>
                                          </div>
                                      </div>
                                  ))
                              ) : (
                                  <div className="px-4 py-8 text-center">
                                      <svg
                                          className="mx-auto h-12 w-12 text-gray-400"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          stroke="currentColor"
                                      >
                                          <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              strokeWidth={1.5}
                                              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                                          />
                                      </svg>
                                      <h3 className="mt-3 text-sm font-medium text-gray-900 dark:text-white">No
                                          notifications</h3>
                                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                          You're all caught up!
                                      </p>
                                  </div>
                              )}
                          </div>
                      </div>
                  )}
              </div>

              {/* Theme Toggle */}
              <button
                  onClick={() => toggleTheme(theme === 'dark' ? 'light' : 'dark')}
                  className="p-2.5 rounded-lg text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                  aria-label="Toggle theme"
              >
                  {theme === 'dark' ? (
                      <SunIcon className="h-6 w-6"/>
                  ) : (
                      <MoonIconSolid className="h-6 w-6"/>
                      // <MoonIcon className="h-6 w-6 bg-gray-100 dark:bg-gray-800 rounded-full" />
                  )}
              </button>

              {/* Profile Dropdown */}
              <div className="relative" ref={profileRef}>
                  <button
                      onClick={() => {
                          setIsProfileOpen(!isProfileOpen);
                          setIsNotificationsOpen(false);
                      }}
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                id="user-menu"
                aria-expanded={isProfileOpen}
                aria-haspopup="true"
              >
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center text-white text-sm font-semibold shadow-sm">
                  {profile?.full_name ? profile.full_name.charAt(0).toUpperCase() : 'U'}
                </div>
                <ChevronDownIcon 
                  className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${
                    isProfileOpen ? 'transform rotate-180' : ''
                  }`} 
                  aria-hidden="true" 
                />
              </button>

              {/* Profile Dropdown Menu */}
              {isProfileOpen && (
                <div className="origin-top-right absolute right-0 mt-3 w-56 rounded-xl shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border border-gray-200 dark:border-gray-700">
                  <div className="py-2">
                    <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">
                        {profile?.full_name || 'User'}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                        {profile?.email || '<EMAIL>'}
                      </p>
                    </div>
                    <div className="py-1">
                      <Link
                        href="/profile"
                        className="group flex items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <UserIcon className="mr-3 h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors" />
                        Your Profile
                      </Link>
                      <Link
                        href="/settings"
                        className="group flex items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <CogIcon className="mr-3 h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors" />
                        Settings
                      </Link>
                    </div>
                    <div className="border-t border-gray-200 dark:border-gray-700">
                      <button
                        onClick={handleLogout}
                        disabled={isLoggingOut}
                        className="group w-full flex items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 transition-colors duration-150 disabled:opacity-50"
                      >
                        <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors" />
                        {isLoggingOut ? 'Signing out...' : 'Sign out'}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default EnhancedTopNav;
