'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { AuthRecovery, debugSession } from '@/lib/authRecovery';
import { 
  ExclamationTriangleIcon, 
  ArrowPathIcon, 
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';

interface SessionRecoveryProps {
  onRecoveryComplete?: () => void;
  showDebugInfo?: boolean;
}

export const SessionRecovery: React.FC<SessionRecoveryProps> = ({
  onRecoveryComplete,
  showDebugInfo = false
}) => {
  const { recoverSession } = useAuth();
  const [isRecovering, setIsRecovering] = useState(false);
  const [recoveryStatus, setRecoveryStatus] = useState<{
    type: 'idle' | 'success' | 'error' | 'info';
    message?: string;
  }>({ type: 'idle' });
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleQuickRecovery = async () => {
    setIsRecovering(true);
    setRecoveryStatus({ type: 'info', message: 'Attempting to recover your session...' });

    try {
      const result = await recoverSession();
      
      if (result.success) {
        setRecoveryStatus({ 
          type: 'success', 
          message: 'Session recovered successfully! Redirecting...' 
        });
        
        // Give user feedback before redirecting
        setTimeout(() => {
          onRecoveryComplete?.();
        }, 1500);
      } else {
        setRecoveryStatus({ 
          type: 'error', 
          message: result.error || 'Recovery failed. Please try logging in again.' 
        });
      }
    } catch (error) {
      setRecoveryStatus({ 
        type: 'error', 
        message: 'An unexpected error occurred during recovery.' 
      });
    } finally {
      setIsRecovering(false);
    }
  };

  const handleAdvancedRecovery = async () => {
    setIsRecovering(true);
    setRecoveryStatus({ type: 'info', message: 'Running diagnostic and recovery...' });

    try {
      const diagnostics = await AuthRecovery.diagnoseSession();
      
      console.group('🔧 Advanced Session Recovery');
      console.log('Diagnostics:', diagnostics);
      
      if (!diagnostics.hasValidSession) {
        const result = await AuthRecovery.recoverSession();
        console.log('Recovery result:', result);
        
        if (result.success) {
          setRecoveryStatus({ 
            type: 'success', 
            message: `Recovery successful via ${result.method}!` 
          });
          setTimeout(() => onRecoveryComplete?.(), 1500);
        } else {
          setRecoveryStatus({ 
            type: 'error', 
            message: `Recovery failed: ${result.error}` 
          });
        }
      } else {
        setRecoveryStatus({ 
          type: 'success', 
          message: 'Session is already valid!' 
        });
      }
      
      console.groupEnd();
    } catch (error) {
      setRecoveryStatus({ 
        type: 'error', 
        message: 'Advanced recovery failed.' 
      });
    } finally {
      setIsRecovering(false);
    }
  };

  const handleDebugSession = async () => {
    await debugSession();
    setRecoveryStatus({ 
      type: 'info', 
      message: 'Debug information logged to console. Press F12 to view.' 
    });
  };

  const getStatusIcon = () => {
    switch (recoveryStatus.type) {
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'info':
        return <InformationCircleIcon className="w-5 h-5 text-blue-500" />;
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getStatusBg = () => {
    switch (recoveryStatus.type) {
      case 'success':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'error':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
      case 'info':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
      default:
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white dark:bg-secondary-800 rounded-xl shadow-lg border border-secondary-200 dark:border-secondary-700">
      <div className="text-center mb-6">
        <ExclamationTriangleIcon className="w-12 h-12 text-yellow-500 mx-auto mb-3" />
        <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50 mb-2">
          Session Issue Detected
        </h3>
        <p className="text-sm text-secondary-600 dark:text-secondary-400">
          Your session appears to have been interrupted. Let us help you get back in.
        </p>
      </div>

      {/* Status Message */}
      {recoveryStatus.message && (
        <div className={`mb-4 p-3 rounded-lg border flex items-center space-x-2 ${getStatusBg()}`}>
          {getStatusIcon()}
          <span className="text-sm font-medium">{recoveryStatus.message}</span>
        </div>
      )}

      {/* Quick Recovery */}
      <div className="space-y-3 mb-4">
        <button
          onClick={handleQuickRecovery}
          disabled={isRecovering}
          className="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
        >
          {isRecovering ? (
            <ArrowPathIcon className="w-4 h-4 animate-spin" />
          ) : (
            <ArrowPathIcon className="w-4 h-4" />
          )}
          <span>{isRecovering ? 'Recovering...' : 'Quick Recovery'}</span>
        </button>

        <button
          onClick={() => window.location.href = '/login'}
          className="w-full px-4 py-3 border border-secondary-300 dark:border-secondary-600 text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 rounded-lg font-medium transition-colors"
        >
          Go to Login Page
        </button>
      </div>

      {/* Advanced Options */}
      <div className="border-t border-secondary-200 dark:border-secondary-700 pt-4">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="text-sm text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300 transition-colors flex items-center space-x-1"
        >
          <WrenchScrewdriverIcon className="w-4 h-4" />
          <span>Advanced Options</span>
        </button>

        {showAdvanced && (
          <div className="mt-3 space-y-2">
            <button
              onClick={handleAdvancedRecovery}
              disabled={isRecovering}
              className="w-full px-3 py-2 text-sm border border-secondary-300 dark:border-secondary-600 text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 rounded-lg transition-colors"
            >
              Advanced Recovery
            </button>

            {showDebugInfo && (
              <button
                onClick={handleDebugSession}
                className="w-full px-3 py-2 text-sm border border-secondary-300 dark:border-secondary-600 text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 rounded-lg transition-colors"
              >
                Debug Session
              </button>
            )}

            <button
              onClick={() => {
                AuthRecovery.cleanRestart();
                setTimeout(() => window.location.href = '/login', 1000);
              }}
              className="w-full px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
            >
              Clean Restart & Login
            </button>
          </div>
        )}
      </div>

      {/* Help Text */}
      <div className="mt-4 text-xs text-secondary-500 dark:text-secondary-400 text-center">
        If you continue having issues, try using a different browser or clearing your browser cache.
      </div>
    </div>
  );
};

export default SessionRecovery;