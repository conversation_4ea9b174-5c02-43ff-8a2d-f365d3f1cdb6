'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useClientDetails } from '@/hooks/useClientDetails';
import { ClientNote } from '@/types/Client';
import { Drawer } from '@deskbelt/ui';
import { Button } from '@deskbelt/ui';
import { 
  XMarkIcon, 
  PaperAirplaneIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

interface ClientNotesDrawerProps {
  clientId: string | null;
  onClose: () => void;
}

const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short' });
};

export const ClientNotesDrawer: React.FC<ClientNotesDrawerProps> = ({ 
  clientId, 
  onClose
}) => {
  const { data: client, isLoading, error, refetch } = useClientDetails(clientId);
  const [newNote, setNewNote] = useState('');
  const [isSubmittingNote, setIsSubmittingNote] = useState(false);
  const { authenticatedPost } = useAuthenticatedFetch();
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom whenever notes change
  useEffect(() => {
    const scrollToBottom = () => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };
    
    requestAnimationFrame(() => {
      setTimeout(scrollToBottom, 100);
    });
  }, [client?.notes]);

  // Focus input when drawer opens
  useEffect(() => {
    if (clientId) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [clientId]);

  // Handle note submission
  const handleNoteSubmit = async () => {
    if (!newNote.trim() || !client) return;
    
    setIsSubmittingNote(true);
    try {
      const response = await authenticatedPost(getApiUrl(`/api/clients/${client.id}/notes`), {
        message: newNote.trim()
      });

      if (!response.ok) {
        throw new Error('Failed to add note');
      }

      const result = await response.json();
      console.log('Note added successfully:', result);
      
      setNewNote('');
      refetch();
    } catch (error) {
      console.error('Failed to add note:', error);
      // TODO: Show error toast/notification
    } finally {
      setIsSubmittingNote(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleNoteSubmit();
    }
  };

  const renderNotes = () => {
    if (!client?.notes || client.notes.length === 0) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <UserIcon className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">No notes yet.</p>
            <p className="text-xs mt-1">Start adding notes about this client.</p>
          </div>
        </div>
      );
    }

    return (
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
        {client.notes.map((note: ClientNote) => (
          <div key={note.id} className="flex justify-center">
            <div className="max-w-md w-full">
              <div className="bg-blue-600 text-white rounded-2xl px-4 py-3 shadow-sm">
                <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                  {note.message}
                </p>
              </div>
              <div className="text-center mt-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formatTimeAgo(note.created_at)}
                </span>
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
    );
  };

  if (!clientId) return null;

  return (
    <Drawer 
      isOpen={!!clientId} 
      onClose={onClose}
      title={client ? `Notes: ${client.name}` : 'Client Notes'}
      size="md"
      zIndex={50}
    >
      <div className="flex flex-col h-full">
        {/* Subtitle */}
        <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Add notes and updates about this client
          </p>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Loading notes...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-red-500 dark:text-red-400">
              <p className="text-sm">Failed to load client notes</p>
              <button 
                onClick={() => refetch()}
                className="text-xs underline mt-1 hover:no-underline"
              >
                Try again
              </button>
            </div>
          </div>
        )}

        {/* Notes Content */}
        {!isLoading && !error && renderNotes()}

        {/* Input Area */}
        {!isLoading && !error && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-end space-x-3">
              <div className="flex-1">
                <textarea
                  ref={inputRef}
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Add a note about this client..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm"
                  rows={3}
                  disabled={isSubmittingNote}
                />
              </div>
              <Button
                onClick={handleNoteSubmit}
                disabled={!newNote.trim() || isSubmittingNote}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center justify-center min-w-[44px] h-[44px]"
              >
                {isSubmittingNote ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <PaperAirplaneIcon className="w-4 h-4" />
                )}
              </Button>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Press Enter to send, Shift+Enter for new line
            </p>
          </div>
        )}
      </div>
    </Drawer>
  );
}; 