import React, { useState, useEffect } from 'react';
import { ClockIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { COMMON_TIME_SLOTS, COMMON_DURATIONS } from '@/types/Job';

interface TimeSlotPickerProps {
  startTime?: string | null;
  endTime?: string | null;
  duration?: string | null;
  onTimeChange: (startTime: string | null, endTime: string | null, duration?: string | null) => void;
  onClose?: () => void;
  disabled?: boolean;
  compact?: boolean;
}

const TimeSlotPicker: React.FC<TimeSlotPickerProps> = ({
  startTime,
  endTime,
  duration,
  onTimeChange,
  onClose,
  disabled = false,
  compact = false
}) => {
  const [selectedStartTime, setSelectedStartTime] = useState(startTime || '');
  const [selectedEndTime, setSelectedEndTime] = useState(endTime || '');
  const [selectedDuration, setSelectedDuration] = useState(duration || '');
  const [activeTab, setActiveTab] = useState<'slots' | 'custom' | 'duration'>('slots');

  // Calculate duration when times change
  useEffect(() => {
    if (selectedStartTime && selectedEndTime) {
      const start = new Date(`2000-01-01T${selectedStartTime}:00`);
      const end = new Date(`2000-01-01T${selectedEndTime}:00`);
      
      if (end > start) {
        const diffMs = end.getTime() - start.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        
        let calculatedDuration = '';
        if (diffHours > 0 && diffMinutes > 0) {
          calculatedDuration = `${diffHours} hour${diffHours > 1 ? 's' : ''} ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
        } else if (diffHours > 0) {
          calculatedDuration = `${diffHours} hour${diffHours > 1 ? 's' : ''}`;
        } else if (diffMinutes > 0) {
          calculatedDuration = `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
        }
        
        setSelectedDuration(calculatedDuration);
      }
    }
  }, [selectedStartTime, selectedEndTime]);

  const handleApply = () => {
    onTimeChange(
      selectedStartTime || null, 
      selectedEndTime || null, 
      selectedDuration || null
    );
    onClose?.();
  };

  const handleClear = () => {
    setSelectedStartTime('');
    setSelectedEndTime('');
    setSelectedDuration('');
    onTimeChange(null, null, null);
  };

  const handleQuickSlot = (slot: typeof COMMON_TIME_SLOTS[0]) => {
    setSelectedStartTime(slot.start);
    setSelectedEndTime(slot.end);
    setActiveTab('slots');
  };

  const handleDurationSelect = (durationValue: string) => {
    setSelectedDuration(durationValue);
    
    // If start time is set, calculate end time
    if (selectedStartTime) {
      const start = new Date(`2000-01-01T${selectedStartTime}:00`);
      let durationMs = 0;
      
      // Parse duration string
      if (durationValue.includes('hour')) {
        const hours = parseInt(durationValue.match(/(\d+)\s*hour/)?.[1] || '0');
        durationMs += hours * 60 * 60 * 1000;
      }
      if (durationValue.includes('minute')) {
        const minutes = parseInt(durationValue.match(/(\d+)\s*minute/)?.[1] || '0');
        durationMs += minutes * 60 * 1000;
      }
      if (durationValue.includes('day')) {
        const days = parseInt(durationValue.match(/(\d+)\s*day/)?.[1] || '0');
        durationMs += days * 24 * 60 * 60 * 1000;
      }
      
      if (durationMs > 0) {
        const end = new Date(start.getTime() + durationMs);
        const endTimeStr = end.toTimeString().slice(0, 5);
        setSelectedEndTime(endTimeStr);
      }
    }
  };

  const formatTimeForDisplay = (time: string) => {
    if (!time) return '';
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  if (compact) {
    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 min-w-80">
        <div className="space-y-3">
          {/* Time inputs */}
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Start Time
              </label>
              <input
                type="time"
                value={selectedStartTime}
                onChange={(e) => setSelectedStartTime(e.target.value)}
                disabled={disabled}
                className="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                End Time
              </label>
              <input
                type="time"
                value={selectedEndTime}
                onChange={(e) => setSelectedEndTime(e.target.value)}
                disabled={disabled}
                className="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Duration display */}
          {selectedDuration && (
            <div className="text-xs text-gray-600 dark:text-gray-400 text-center py-1 bg-gray-50 dark:bg-gray-700 rounded">
              Duration: {selectedDuration}
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-between gap-2">
            <button
              onClick={handleClear}
              className="px-3 py-1.5 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              disabled={disabled}
            >
              Clear
            </button>
            <button
              onClick={handleApply}
              className="px-4 py-1.5 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
              disabled={disabled}
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg p-6 min-w-96">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <ClockIcon className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Schedule Time
          </h3>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="flex mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
        {[
          { id: 'slots', label: 'Quick Slots' },
          { id: 'custom', label: 'Custom Time' },
          { id: 'duration', label: 'Duration' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
              activeTab === tab.id
                ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
            }`}
            disabled={disabled}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="min-h-48">
        {/* Quick Slots Tab */}
        {activeTab === 'slots' && (
          <div className="space-y-4">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Select from common time slots:
            </p>
            <div className="grid grid-cols-1 gap-2">
              {COMMON_TIME_SLOTS.map((slot, index) => (
                <button
                  key={index}
                  onClick={() => handleQuickSlot(slot)}
                  disabled={disabled}
                  className={`p-3 text-left border rounded-lg transition-all ${
                    selectedStartTime === slot.start && selectedEndTime === slot.end
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="font-medium">{slot.label}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">1 hour duration</div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Custom Time Tab */}
        {activeTab === 'custom' && (
          <div className="space-y-6">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Set custom start and end times:
            </p>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Start Time
                </label>
                <input
                  type="time"
                  value={selectedStartTime}
                  onChange={(e) => setSelectedStartTime(e.target.value)}
                  disabled={disabled}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {selectedStartTime && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {formatTimeForDisplay(selectedStartTime)}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  End Time
                </label>
                <input
                  type="time"
                  value={selectedEndTime}
                  onChange={(e) => setSelectedEndTime(e.target.value)}
                  disabled={disabled}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {selectedEndTime && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {formatTimeForDisplay(selectedEndTime)}
                  </p>
                )}
              </div>

              {/* Duration display */}
              {selectedDuration && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <ClockIcon className="w-4 h-4 inline mr-1" />
                    Duration: {selectedDuration}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Duration Tab */}
        {activeTab === 'duration' && (
          <div className="space-y-4">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Select duration or enter custom:
            </p>
            
            <div className="grid grid-cols-2 gap-2 mb-4">
              {COMMON_DURATIONS.map((dur, index) => (
                <button
                  key={index}
                  onClick={() => handleDurationSelect(dur.value)}
                  disabled={disabled}
                  className={`p-2 text-sm border rounded-lg transition-all ${
                    selectedDuration === dur.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  {dur.label}
                </button>
              ))}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Custom Duration
              </label>
              <input
                type="text"
                value={selectedDuration}
                onChange={(e) => setSelectedDuration(e.target.value)}
                placeholder="e.g., 2 hours 30 minutes"
                disabled={disabled}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-500 dark:placeholder-gray-400"
              />
            </div>

            {selectedStartTime && (
              <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Start: {formatTimeForDisplay(selectedStartTime)}
                </p>
                {selectedEndTime && (
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    End: {formatTimeForDisplay(selectedEndTime)}
                  </p>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleClear}
          className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          disabled={disabled}
        >
          Clear All
        </button>
        <div className="flex gap-2">
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              disabled={disabled}
            >
              Cancel
            </button>
          )}
          <button
            onClick={handleApply}
            className="px-6 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            disabled={disabled}
          >
            Apply
          </button>
        </div>
      </div>
    </div>
  );
};

export default TimeSlotPicker;