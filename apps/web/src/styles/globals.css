/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Import Enterprise Design System Styles - MUST come before @tailwind */
@import './typography.css';
@import './focus-rings.css';
@import './shadows.css';
@import './components.css';
@import './animations.css';
@import './status-colors.css';
@import './loading-states.css';
@import './interactive-states.css';
@import './responsive-typography.css';
@import './accessibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for enhanced theming */
:root {
  --foreground-rgb: 15, 23, 42;
  --background-start-rgb: 248, 250, 252;
  --background-end-rgb: 255, 255, 255;
  
  /* Glass effect variables */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Gradient variables */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 248, 250, 252;
    --background-start-rgb: 15, 23, 42;
    --background-end-rgb: 2, 6, 23;
    
    /* Dark mode glass effect */
    --glass-bg: rgba(15, 23, 42, 0.25);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }
}

/* Enhanced body with subtle gradient */
body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      135deg,
      rgb(var(--background-start-rgb)) 0%,
      rgb(var(--background-end-rgb)) 100%
    );
  min-height: 100vh;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }
  
  * {
    @apply border-secondary-200 dark:border-secondary-700;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply text-secondary-900 dark:text-secondary-50;
    font-weight: 600;
    line-height: 1.2;
  }
  
  p {
    @apply text-secondary-700 dark:text-secondary-300;
    line-height: 1.6;
  }
}

@layer components {
  /* Modern Button Styles with Enhanced Interactions */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-medium py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-[1.02] transition-all duration-200 ease-out active:scale-[0.98] active:translate-y-0 focus:ring-4 focus:ring-primary-500/20;
  }
  
  .btn-secondary {
    @apply bg-white dark:bg-secondary-800 hover:bg-secondary-50 dark:hover:bg-secondary-700 text-secondary-700 dark:text-secondary-200 font-medium py-3 px-6 rounded-xl border border-secondary-300 dark:border-secondary-600 shadow-sm hover:shadow-md transform hover:-translate-y-0.5 hover:scale-[1.02] transition-all duration-200 ease-out active:scale-[0.98] focus:ring-4 focus:ring-secondary-500/20;
  }
  
  .btn-accent {
    @apply bg-gradient-to-r from-accent-600 to-accent-700 hover:from-accent-700 hover:to-accent-800 text-white font-medium py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-[1.02] transition-all duration-200 ease-out active:scale-[0.98] focus:ring-4 focus:ring-accent-500/20;
  }
  
  .btn-ghost {
    @apply text-secondary-600 dark:text-secondary-400 hover:text-secondary-900 dark:hover:text-secondary-100 hover:bg-secondary-100 dark:hover:bg-secondary-800 font-medium py-3 px-6 rounded-xl transition-all duration-200 ease-out hover:scale-[1.02] active:scale-[0.98] focus:ring-4 focus:ring-secondary-500/20;
  }
  
  .btn-danger {
    @apply bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-medium py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-[1.02] transition-all duration-200 ease-out active:scale-[0.98] focus:ring-4 focus:ring-red-500/20;
  }
  
  /* Modern Card Styles with Enhanced Animations */
  .card {
    @apply bg-white dark:bg-secondary-800 rounded-2xl shadow-soft hover:shadow-medium border border-secondary-100 dark:border-secondary-700 p-6 transition-all duration-300 ease-out;
  }
  
  .card-elevated {
    @apply bg-white dark:bg-secondary-800 rounded-2xl shadow-medium hover:shadow-strong border border-secondary-100 dark:border-secondary-700 p-6 transform hover:shadow-xl transition-all duration-300 ease-out overflow-hidden;
  }
  
  .card-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    @apply rounded-2xl p-6 transition-all duration-300 ease-out hover:scale-[1.01];
  }
  
  .card-interactive {
    @apply bg-white dark:bg-secondary-800 rounded-2xl shadow-soft hover:shadow-medium border border-secondary-100 dark:border-secondary-700 p-6 cursor-pointer transform hover:-translate-y-0.5 hover:scale-[1.01] transition-all duration-200 ease-out active:scale-[0.99] min-w-0 w-full;
  }
  
  .card-hover-lift {
    @apply bg-white dark:bg-secondary-800 rounded-2xl shadow-soft hover:shadow-strong border border-secondary-100 dark:border-secondary-700 p-6 transform hover:-translate-y-2 hover:scale-[1.02] transition-all duration-300 ease-out;
  }
  
  /* Dashboard specific styles */
  .dashboard-stats-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
    gap: clamp(1.5rem, 4vw, 3rem);
    padding: 1rem 0;
    margin: 1rem 0;
  }

  .dashboard-section-grid {
    @apply grid grid-cols-1 lg:grid-cols-2;
    gap: clamp(2rem, 5vw, 3rem);
    padding: 1rem 0;
    margin: 1rem 0;
  }

  /* Status Badge Styles */
  .badge {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset;
  }
  
  .badge-primary {
    @apply bg-primary-50 dark:bg-primary-950 text-primary-700 dark:text-primary-300 ring-primary-600/20;
  }
  
  .badge-success {
    @apply bg-success-50 dark:bg-success-950 text-success-700 dark:text-success-300 ring-success-600/20;
  }
  
  .badge-warning {
    @apply bg-warning-50 dark:bg-warning-950 text-warning-700 dark:text-warning-300 ring-warning-600/20;
  }
  
  .badge-error {
    @apply bg-error-50 dark:bg-error-950 text-error-700 dark:text-error-300 ring-error-600/20;
  }
  
  .badge-info {
    @apply bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-300 ring-blue-600/20;
  }

  /* === SEMANTIC STATUS UTILITIES === */
  
  /* Job Status Badges */
  .status-badge-new {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-jobs-50 dark:bg-jobs-950 text-jobs-700 dark:text-jobs-300 ring-jobs-600/20 transition-all duration-150;
  }
  
  .status-badge-quoted {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-indigo-50 dark:bg-indigo-950 text-indigo-700 dark:text-indigo-300 ring-indigo-600/20 transition-all duration-150;
  }
  
  .status-badge-in-progress {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-success-50 dark:bg-success-950 text-success-700 dark:text-success-300 ring-success-600/20 transition-all duration-150;
  }
  
  .status-badge-on-hold {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-warning-50 dark:bg-warning-950 text-warning-700 dark:text-warning-300 ring-warning-600/20 transition-all duration-150;
  }
  
  .status-badge-completed {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-success-50 dark:bg-success-950 text-success-700 dark:text-success-300 ring-success-600/20 transition-all duration-150;
  }
  
  .status-badge-cancelled {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-error-50 dark:bg-error-950 text-error-700 dark:text-error-300 ring-error-600/20 transition-all duration-150;
  }

  /* Status Dot Colors for consistent visual indicators */
  .status-dot-new { @apply bg-jobs-500; }
  .status-dot-quoted { @apply bg-indigo-500; }
  .status-dot-in-progress { @apply bg-success-500; }
  .status-dot-on-hold { @apply bg-warning-500; }
  .status-dot-completed { @apply bg-success-500; }
  .status-dot-cancelled { @apply bg-error-500; }

  /* Module Context Badges */
  .badge-jobs {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-jobs-50 dark:bg-jobs-950 text-jobs-700 dark:text-jobs-300 ring-jobs-600/20 transition-all duration-150;
  }
  
  .badge-clients {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-clients-50 dark:bg-clients-950 text-clients-700 dark:text-clients-300 ring-clients-600/20 transition-all duration-150;
  }
  
  .badge-schedule {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-accent-50 dark:bg-accent-950 text-accent-700 dark:text-accent-300 ring-accent-600/20 transition-all duration-150;
  }
  
  .badge-invoicing {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-warning-50 dark:bg-warning-950 text-warning-700 dark:text-warning-300 ring-warning-600/20 transition-all duration-150;
  }
  
  .badge-analytics {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-cyan-50 dark:bg-cyan-950 text-cyan-700 dark:text-cyan-300 ring-cyan-600/20 transition-all duration-150;
  }
  
  .badge-team {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-pink-50 dark:bg-pink-950 text-pink-700 dark:text-pink-300 ring-pink-600/20 transition-all duration-150;
  }
  
  .badge-ai {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset bg-ai-50 dark:bg-ai-950 text-ai-700 dark:text-ai-300 ring-ai-600/20 transition-all duration-150;
  }
  
  /* Enhanced Form Input Styles with Micro-animations */
  .input {
    @apply block w-full px-4 py-3 text-secondary-900 dark:text-secondary-100 bg-white dark:bg-secondary-800 border border-secondary-300 dark:border-secondary-600 rounded-xl shadow-sm placeholder-secondary-400 dark:placeholder-secondary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:shadow-lg focus:scale-[1.01] transition-all duration-200 ease-out;
  }
  
  .input-floating {
    @apply block w-full px-4 pt-6 pb-3 text-secondary-900 dark:text-secondary-100 bg-white dark:bg-secondary-800 border border-secondary-300 dark:border-secondary-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:shadow-lg focus:scale-[1.01] transition-all duration-200 ease-out;
  }
  
  .label-floating {
    @apply absolute left-4 top-4 text-secondary-500 dark:text-secondary-400 text-sm transition-all duration-200 ease-out peer-focus:-translate-y-2 peer-focus:scale-75 peer-focus:text-primary-600 peer-focus:font-medium peer-[:not(:placeholder-shown)]:-translate-y-2 peer-[:not(:placeholder-shown)]:scale-75;
  }
  
  /* Enhanced Navigation Styles with Micro-animations */
  .nav-item {
    @apply flex items-center space-x-3 px-3 py-2 rounded-lg text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800 hover:text-secondary-900 dark:hover:text-secondary-100 transition-all duration-200 ease-out;
  }
  
  .nav-item-active {
    @apply flex items-center space-x-3 px-4 py-3 rounded-xl bg-primary-50 dark:bg-primary-950 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 scale-[1.02] shadow-soft transition-all duration-200 ease-out;
  }
  
  /* Dropdown Styles */
  .dropdown {
    @apply absolute right-0 top-full mt-2 bg-white dark:bg-secondary-800 border border-secondary-200 dark:border-secondary-700 rounded-xl shadow-lg py-2 min-w-48;
    z-index: 99999;
  }

  /* Enhanced Picker Styles with Better Mobile Support */
  .picker-backdrop {
    z-index: 9998;
    transition: opacity 0.2s ease-out;
  }

  .picker-container {
    z-index: 9999;
    isolation: isolate;
    position: fixed;
    transform-origin: top right;
  }
  
  @media (max-width: 768px) {
    .picker-container {
      inset: 1rem;
      transform-origin: center;
    }
  }

  /* Job Card Z-Index Management */
  .job-card-active {
    z-index: 40;
    isolation: isolate;
  }
  
  /* Improved Focus Ring */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-secondary-900 transition-shadow duration-200;
  }
  
  /* Enhanced Schedule Section Styles */
  .schedule-section-button {
    @apply relative overflow-hidden;
  }
  
  .schedule-section-button:active {
    @apply scale-[0.98] transition-transform duration-100;
  }
  
  /* Touch-Friendly Mobile Styles */
  @media (max-width: 768px) {
    .card-interactive {
      @apply p-4;
    }
    
    .min-touch-target {
      min-height: 44px;
      min-width: 44px;
    }
  }

  /* Job Card Layout Improvements */
  .job-card-content {
    @apply min-w-0 w-full;
  }

  .job-card-text {
    @apply break-words overflow-wrap-anywhere;
  }
  
  .dropdown-item {
    @apply block w-full px-4 py-2.5 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-700 hover:text-secondary-900 dark:hover:text-secondary-100 transition-colors duration-150 ease-out;
  }
  
  /* Loading & Skeleton Styles */
  .skeleton {
    @apply animate-pulse bg-secondary-200 dark:bg-secondary-700 rounded;
  }
  
  .loading-dots {
    @apply inline-flex space-x-1;
  }
  
  .loading-dots > div {
      i: 1;
      @apply w-2 h-2 bg-current rounded-full animate-bounce;
    animation-delay: calc(var(--i) * 0.1s);
  }
  
  /* Utility Classes */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }

  /* Layout Container Optimization */
  .main-content-container {
    @apply px-6 py-6 max-w-none;
  }

  /* Mobile-optimized spacing for content areas */
  @media (max-width: 640px) {
    .main-content-container {
      @apply px-4 py-4;
    }
  }
  
  .border-gradient {
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #667eea 0%, #764ba2 100%) border-box;
    border: 2px solid transparent;
  }
  
  .shadow-colored {
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
  }
  
  .backdrop-blur-glass {
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
  }
  
  /* Focus ring improvements */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900;
  }
  
  /* Enhanced Hover and Interaction Utilities */
  .hover-scale {
    @apply transform transition-transform duration-200 ease-out hover:scale-105 active:scale-95;
  }
  
  .hover-lift {
    @apply transform transition-all duration-200 ease-out hover:-translate-y-1 hover:shadow-lg active:translate-y-0 active:shadow-md;
  }
  
  .hover-glow {
    @apply transition-all duration-200 ease-out hover:shadow-colored hover:shadow-lg;
  }
  
  .hover-rotate {
    @apply transform transition-transform duration-200 ease-out hover:rotate-3;
  }
  
  .group-hover-lift {
    @apply transform transition-all duration-200 ease-out group-hover:-translate-y-1 group-hover:scale-105;
  }
  
  .interactive-element {
    @apply transform transition-all duration-200 ease-out hover:scale-[1.02] hover:-translate-y-0.5 hover:shadow-md active:scale-[0.98] active:translate-y-0;
  }
  
  /* Professional Table Styles */
  .table-modern {
    @apply w-full border-collapse;
  }
  
  .table-modern th {
    @apply px-6 py-4 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider border-b border-secondary-200 dark:border-secondary-700 bg-secondary-50 dark:bg-secondary-800/50;
  }
  
  .table-modern td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-secondary-900 dark:text-secondary-100 border-b border-secondary-100 dark:border-secondary-800;
  }
  
  .table-modern tbody tr:hover {
    @apply bg-secondary-50 dark:bg-secondary-800/50;
  }
  
  /* Professional Modal Styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4;
  }
  
  .modal-content {
    @apply bg-white dark:bg-secondary-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto;
  }
  
  /* Professional Drawer Styles */
  .drawer-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-40;
  }
  
  .drawer-content {
    @apply fixed right-0 top-0 h-full w-full max-w-md bg-white dark:bg-secondary-800 shadow-2xl transform transition-transform duration-300 ease-out;
  }
  
  /* Professional Alert Styles */
  .alert {
    @apply p-4 rounded-xl border-l-4;
  }
  
  .alert-info {
    @apply bg-blue-50 dark:bg-blue-950 border-blue-500 text-blue-700 dark:text-blue-300;
  }
  
  .alert-success {
    @apply bg-green-50 dark:bg-green-950 border-green-500 text-green-700 dark:text-green-300;
  }
  
  .alert-warning {
    @apply bg-yellow-50 dark:bg-yellow-950 border-yellow-500 text-yellow-700 dark:text-yellow-300;
  }
  
  .alert-error {
    @apply bg-red-50 dark:bg-red-950 border-red-500 text-red-700 dark:text-red-300;
  }
}

@layer utilities {
  /* Custom utilities for modern effects */
  .blur-backdrop {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }
  
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  /* Professional spacing utilities */
  .section-padding {
    @apply py-12 px-6;
  }
  
  .container-padding {
    @apply px-6 py-8;
  }
  
  /* Professional typography utilities */
  .heading-xl {
    @apply text-4xl font-bold tracking-tight;
  }
  
  .heading-lg {
    @apply text-3xl font-semibold tracking-tight;
  }
  
  .heading-md {
    @apply text-2xl font-semibold tracking-tight;
  }
  
  .heading-sm {
    @apply text-xl font-semibold tracking-tight;
  }
  
  .body-lg {
    @apply text-lg leading-relaxed;
  }
  
  .body-md {
    @apply text-base leading-relaxed;
  }
  
  .body-sm {
    @apply text-sm leading-relaxed;
  }
  
  .caption {
    @apply text-xs font-medium uppercase tracking-wider;
  }
}
