@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background-start: hsl(0 0% 100%);
    --background-end: hsl(0 0% 98%);
    --foreground: hsl(222.2 84% 4.9%);
    --card: hsl(0 0% 100%);
    --card-foreground: hsl(222.2 84% 4.9%);
    --popover: hsl(0 0% 100%);
    --popover-foreground: hsl(222.2 84% 4.9%);
    --primary: hsl(221.2 83.2% 53.3%);
    --primary-foreground: hsl(210 40% 98%);
    --secondary: hsl(210 40% 96.1%);
    --secondary-foreground: hsl(222.2 47.4% 11.2%);
    --muted: hsl(210 40% 96.1%);
    --muted-foreground: hsl(215.4 16.3% 46.9%);
    --accent: hsl(210 40% 96.1%);
    --accent-foreground: hsl(222.2 47.4% 11.2%);
    --destructive: hsl(0 84.2% 60.2%);
    --destructive-foreground: hsl(210 40% 98%);
    --border: hsl(214.3 31.8% 91.4%);
    --input: hsl(214.3 31.8% 91.4%);
    --ring: hsl(221.2 83.2% 53.3%);
    --radius: 0.5rem;
    --animate-duration: 200ms;
    --animate-timing: cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'cv02' 1, 'cv03' 1, 'cv04' 1, 'cv11' 1;
  }

  .dark {
    --background-start: hsl(222.2 84% 4.9%);
    --background-end: hsl(222.2 84% 3.7%);
    --foreground: hsl(210 40% 98%);
    --card: hsl(222.2 84% 4.9%);
    --card-foreground: hsl(210 40% 98%);
    --popover: hsl(222.2 84% 4.9%);
    --popover-foreground: hsl(210 40% 98%);
    --primary: hsl(217.2 91.2% 59.8%);
    --primary-foreground: hsl(222.2 47.4% 11.2%);
    --secondary: hsl(217.2 32.6% 17.5%);
    --secondary-foreground: hsl(210 40% 98%);
    --muted: hsl(217.2 32.6% 17.5%);
    --muted-foreground: hsl(215 20.2% 65.1%);
    --accent: hsl(217.2 32.6% 17.5%);
    --accent-foreground: hsl(210 40% 98%);
    --destructive: hsl(0 62.8% 30.6%);
    --destructive-foreground: hsl(210 40% 98%);
    --border: hsl(217.2 32.6% 17.5%);
    --input: hsl(217.2 32.6% 17.5%);
    --ring: hsl(224.3 76.3% 48%);
  }
}

@layer base {
  body {
    background: linear-gradient(
      to bottom,
      transparent,
      var(--background-end)
    ) var(--background-start);
    color: var(--foreground);
  }
}

@layer components {
  .btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25rem;
    border-radius: 0.5rem;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
  }

  .btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px hsl(0 0% 100%), 0 0 0 4px hsl(221.2 83.2% 53.3% / 0.5);
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
    box-shadow: 0 10px 15px -3px hsl(221.2 83.2% 53.3% / 0.2);
  }

  .btn-primary:hover {
    background-color: hsl(221.2 83.2% 48.3%);
    transform: scale(1.02);
  }

  .btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
    border: 1px solid var(--border);
  }

  .btn-secondary:hover {
    background-color: var(--muted);
    transform: scale(1.02);
  }

  .card {
    background-color: var(--card);
    color: var(--card-foreground);
    border: 1px solid var(--border);
    border-radius: calc(var(--radius) * 2);
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card:hover {
    box-shadow: 0 25px 30px -12px rgb(0 0 0 / 0.25);
    transform: scale(1.02);
  }

  .glass {
    background-color: hsl(0 0% 100% / 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid hsl(0 0% 100% / 0.2);
    box-shadow: 0 8px 32px 0 rgb(0 0 0 / 0.1);
  }

  .dark .glass {
    background-color: hsl(222.2 84% 4.9% / 0.8);
    border-color: hsl(217.2 32.6% 17.5% / 0.2);
  }

  .nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    color: var(--foreground);
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .nav-item:hover {
    background-color: var(--muted);
  }

  .input {
    display: block;
    width: 100%;
    padding: 0.625rem 1rem;
    background-color: var(--background-start);
    color: var(--foreground);
    border: 1px solid var(--border);
    border-radius: var(--radius);
  }

  .input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px hsl(221.2 83.2% 53.3% / 0.2);
  }
           disabled:opacity-50 disabled:cursor-not-allowed
           transition-all duration-200;
  }
}

@layer utilities {
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.2s ease-out;
  }

  .shimmer {
    @apply relative overflow-hidden;
  }

  .shimmer::after {
    @apply absolute inset-0;
    content: '';
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    transform: translateX(-100%);
    animation: shimmer 1.5s infinite;
  }
}

/* Enhanced Animation Keyframes */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulseSubtle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

@layer components {
  /* Enhanced Buttons */
  .btn-primary {
    @apply bg-gradient-to-b from-primary-500 to-primary-600 
           hover:from-primary-600 hover:to-primary-700
           shadow-lg shadow-primary-500/20
           text-white font-medium py-2 px-4 rounded-lg 
           transition-all duration-200
           hover:scale-[1.02];
  }
  
  .btn-secondary {
    @apply bg-gradient-to-b from-gray-50 to-gray-100 
           hover:from-gray-100 hover:to-gray-200 
           shadow-md shadow-gray-400/10
           text-gray-700 font-medium py-2 px-4 rounded-lg 
           transition-all duration-200
           hover:scale-[1.02];
  }
  
  /* Enhanced Card */
  .card {
    @apply bg-white/90 dark:bg-gray-800/90
           backdrop-blur-sm
           border border-gray-200 dark:border-gray-700
           shadow-xl shadow-gray-200/20 dark:shadow-gray-900/30
           rounded-xl p-6
           hover:shadow-2xl hover:scale-[1.02]
           transition-all duration-200;
  }

  /* Glass Effect */
  .glass-effect {
    @apply bg-white/80 dark:bg-gray-800/80 
           backdrop-blur-lg 
           border border-white/20 dark:border-gray-700/20
           shadow-soft-xl;
  }

  /* Navigation Item */
  .nav-item {
    @apply flex items-center space-x-3 px-3 py-2 rounded-xl 
           text-gray-700 dark:text-gray-300 
           hover:bg-gray-100 dark:hover:bg-gray-800 
           hover:text-gray-900 dark:hover:text-white 
           transition-all duration-200
           hover:scale-[1.02];
  }

  /* Input Fields */
  .input {
    @apply block w-full px-4 py-2 
           bg-white dark:bg-gray-800 
           border border-gray-200 dark:border-gray-700 
           rounded-lg 
           focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 
           dark:focus:ring-primary-400/20 dark:focus:border-primary-400
           transition-all duration-200;
  }

  /* Loading Effects */
  .loading-shimmer {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700
           relative overflow-hidden;
  }

  .loading-shimmer::after {
    @apply absolute inset-0 
           -translate-x-full
           animate-[shimmer_2s_infinite]
           bg-gradient-to-r from-transparent via-white/10 to-transparent;
    content: '';
  }
}

/* Animation Classes */
.animate-slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out forwards;
}

.shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.4s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.animate-pulse-soft {
  animation: pulseSubtle 2s ease-in-out infinite;
}

.animate-wiggle {
  animation: wiggle 1s ease-in-out;
}

.animate-bounce-subtle {
  animation: bounce 2s infinite;
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Custom CSS Variables for enhanced theming */
:root {
  --foreground-rgb: 15, 23, 42;
  --background-start-rgb: 248, 250, 252;
  --background-end-rgb: 255, 255, 255;
  
  /* Glass effect variables */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Gradient variables */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 248, 250, 252;
    --background-start-rgb: 15, 23, 42;
    --background-end-rgb: 2, 6, 23;
    
    /* Dark mode glass effect */
    --glass-bg: rgba(15, 23, 42, 0.25);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }
}

/* Enhanced body with subtle gradient */
body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      135deg,
      rgb(var(--background-start-rgb)) 0%,
      rgb(var(--background-end-rgb)) 100%
    );
  min-height: 100vh;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }
  
  * {
    @apply border-secondary-200 dark:border-secondary-700;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply text-secondary-900 dark:text-secondary-50;
    font-weight: 600;
    line-height: 1.2;
  }
  
  p {
    @apply text-secondary-700 dark:text-secondary-300;
    line-height: 1.6;
  }
}

@layer components {
  /* Modern Button Styles with Enhanced Interactions */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-medium py-2.5 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-[1.02] transition-all duration-200 ease-out active:scale-[0.98] active:translate-y-0;
  }
  
  .btn-secondary {
    @apply bg-white dark:bg-secondary-800 hover:bg-secondary-50 dark:hover:bg-secondary-700 text-secondary-700 dark:text-secondary-200 font-medium py-2.5 px-6 rounded-xl border border-secondary-300 dark:border-secondary-600 shadow-sm hover:shadow-md transform hover:-translate-y-0.5 hover:scale-[1.02] transition-all duration-200 ease-out active:scale-[0.98];
  }
  
  .btn-accent {
    @apply bg-gradient-to-r from-accent-600 to-accent-700 hover:from-accent-700 hover:to-accent-800 text-white font-medium py-2.5 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-[1.02] transition-all duration-200 ease-out active:scale-[0.98];
  }
  
  .btn-ghost {
    @apply text-secondary-600 dark:text-secondary-400 hover:text-secondary-900 dark:hover:text-secondary-100 hover:bg-secondary-100 dark:hover:bg-secondary-800 font-medium py-2.5 px-6 rounded-xl transition-all duration-200 ease-out hover:scale-[1.02] active:scale-[0.98];
  }
  
  /* Modern Card Styles with Enhanced Animations */
  .card {
    @apply bg-white dark:bg-secondary-800 rounded-2xl shadow-soft hover:shadow-medium border border-secondary-100 dark:border-secondary-700 p-6 transition-all duration-300 ease-out;
    animation: fadeInUp 0.4s ease-out;
  }
  
  .card-elevated {
    @apply bg-white dark:bg-secondary-800 rounded-2xl shadow-medium hover:shadow-strong border border-secondary-100 dark:border-secondary-700 p-6 transform hover:-translate-y-1 hover:scale-[1.02] transition-all duration-300 ease-out;
    animation: fadeInUp 0.5s ease-out;
  }
  
  .card-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    @apply rounded-2xl p-6 transition-all duration-300 ease-out hover:scale-[1.01];
    animation: scaleIn 0.3s ease-out;
  }
  
  .card-interactive {
    @apply bg-white dark:bg-secondary-800 rounded-2xl shadow-soft hover:shadow-medium border border-secondary-100 dark:border-secondary-700 p-6 cursor-pointer transform hover:-translate-y-0.5 hover:scale-[1.01] transition-all duration-200 ease-out active:scale-[0.99] min-w-0 w-full;
  }
  
  /* Status Badge Styles */
  .badge {
    @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ring-1 ring-inset;
  }
  
  .badge-primary {
    @apply bg-primary-50 dark:bg-primary-950 text-primary-700 dark:text-primary-300 ring-primary-600/20;
  }
  
  .badge-success {
    @apply bg-success-50 dark:bg-success-950 text-success-700 dark:text-success-300 ring-success-600/20;
  }
  
  .badge-warning {
    @apply bg-warning-50 dark:bg-warning-950 text-warning-700 dark:text-warning-300 ring-warning-600/20;
  }
  
  .badge-error {
    @apply bg-error-50 dark:bg-error-950 text-error-700 dark:text-error-300 ring-error-600/20;
  }
  
  /* Enhanced Form Input Styles with Micro-animations */
  .input {
    @apply block w-full px-4 py-3 text-secondary-900 dark:text-secondary-100 bg-white dark:bg-secondary-800 border border-secondary-300 dark:border-secondary-600 rounded-xl shadow-sm placeholder-secondary-400 dark:placeholder-secondary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:shadow-lg focus:scale-[1.01] transition-all duration-200 ease-out;
  }
  
  .input-floating {
    @apply block w-full px-4 pt-6 pb-3 text-secondary-900 dark:text-secondary-100 bg-white dark:bg-secondary-800 border border-secondary-300 dark:border-secondary-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:shadow-lg focus:scale-[1.01] transition-all duration-200 ease-out;
  }
  
  .label-floating {
    @apply absolute left-4 top-4 text-secondary-500 dark:text-secondary-400 text-sm transition-all duration-200 ease-out peer-focus:-translate-y-2 peer-focus:scale-75 peer-focus:text-primary-600 peer-focus:font-medium peer-[:not(:placeholder-shown)]:-translate-y-2 peer-[:not(:placeholder-shown)]:scale-75;
  }
  
  /* Enhanced Navigation Styles with Micro-animations */
  .nav-item {
  @apply flex items-center px-3 py-2.5 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white gap-x-3
  hover:bg-white/50 dark:hover:bg-gray-800/50 rounded-xl transition-all duration-200;
}

.nav-item .icon-wrapper {
  @apply flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100/50 dark:bg-gray-800/50 group-hover:bg-gray-200/50 dark:group-hover:bg-gray-700/50 transition-colors;
}

.nav-item .badge {
  @apply ml-auto bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300 py-1 px-2.5 rounded-full text-xs font-medium hover:animate-pulse;
}
  
  .nav-item-active {
    @apply flex items-center space-x-3 px-4 py-3 rounded-xl bg-primary-50 dark:bg-primary-950 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 scale-[1.02] shadow-soft transition-all duration-200 ease-out;
    animation: bounceIn 0.4s ease-out;
  }
  
  /* Dropdown Styles */
  .dropdown {
    @apply absolute right-0 top-full mt-2 bg-white dark:bg-secondary-800 border border-secondary-200 dark:border-secondary-700 rounded-xl shadow-lg py-2 min-w-48 animate-fade-in-up;
    z-index: 99999;
  }

  /* Enhanced Picker Styles with Better Mobile Support */
  .picker-backdrop {
    z-index: 9998;
    transition: opacity 0.2s ease-out;
  }

  .picker-container {
    z-index: 9999;
    isolation: isolate;
    position: fixed;
    transform-origin: top right;
    animation: scaleIn 0.15s ease-out;
  }
  
  @media (max-width: 768px) {
    .picker-container {
      inset: 1rem;
      transform-origin: center;
    }
  }

  /* Job Card Z-Index Management */
  .job-card-active {
    z-index: 40;
    isolation: isolate;
  }
  
  /* Improved Focus Ring */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-secondary-900 transition-shadow duration-200;
  }
  
  /* Enhanced Schedule Section Styles */
  .schedule-section-button {
    @apply relative overflow-hidden;
  }
  
  .schedule-section-button:active {
    @apply scale-[0.98] transition-transform duration-100;
  }
  
  /* Touch-Friendly Mobile Styles */
  @media (max-width: 768px) {
    .card-interactive {
      @apply p-4;
    }
    
    .min-touch-target {
      min-height: 44px;
      min-width: 44px;
    }
  }

  /* Job Card Layout Improvements */
  .job-card-content {
    @apply min-w-0 w-full;
  }

  .job-card-text {
    @apply break-words overflow-wrap-anywhere;
  }
  
  .dropdown-item {
    @apply block w-full px-4 py-2.5 text-left text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-700 hover:text-secondary-900 dark:hover:text-secondary-100 transition-colors duration-150 ease-out;
  }
  
  /* Loading & Skeleton Styles */
  .skeleton {
    @apply animate-pulse bg-secondary-200 dark:bg-secondary-700 rounded;
  }
  
  .loading-dots {
    @apply inline-flex space-x-1;
  }
  
  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-bounce;
    animation-delay: calc(var(--i) * 0.1s);
  }
  
  /* Utility Classes */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }

  /* Layout Container Optimization */
  .main-content-container {
    @apply px-6 py-6 max-w-none;
    /* Minimal gaps for modern, clean layout */
  }

  /* Mobile-optimized spacing for content areas */
  @media (max-width: 640px) {
    .main-content-container {
      @apply px-4 py-4;
      /* Maintain comfortable thumb margins on mobile */
    }
  }
  
  .border-gradient {
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #667eea 0%, #764ba2 100%) border-box;
    border: 2px solid transparent;
  }
  
  .shadow-colored {
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
  }
  
  .backdrop-blur-glass {
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
  }
  
  /* Focus ring improvements */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900;
  }
  
  /* Enhanced Hover and Interaction Utilities */
  .hover-scale {
    @apply transform transition-transform duration-200 ease-out hover:scale-105 active:scale-95;
  }
  
  .hover-lift {
    @apply transform transition-all duration-200 ease-out hover:-translate-y-1 hover:shadow-lg active:translate-y-0 active:shadow-md;
  }
  
  .hover-glow {
    @apply transition-all duration-200 ease-out hover:shadow-colored hover:shadow-lg;
  }
  
  .hover-rotate {
    @apply transform transition-transform duration-200 ease-out hover:rotate-3;
  }
  
  .hover-bounce {
    @apply transition-transform duration-200 ease-out hover:animate-bounce-subtle;
  }
  
  .hover-wiggle {
    @apply transition-transform duration-200 ease-out hover:animate-wiggle;
  }
  
  .group-hover-lift {
    @apply transform transition-all duration-200 ease-out group-hover:-translate-y-1 group-hover:scale-105;
  }
  
  .interactive-element {
    @apply transform transition-all duration-200 ease-out hover:scale-[1.02] hover:-translate-y-0.5 hover:shadow-md active:scale-[0.98] active:translate-y-0;
  }
}

@layer utilities {
  /* Custom utilities for modern effects */
  .blur-backdrop {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }
  
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
} 