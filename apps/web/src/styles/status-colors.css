/**
 * Enterprise Status Color System
 * Standardized status colors across all modules for consistency
 * Based on enterprise design audit recommendations
 */

/* Universal Status Colors */
.status-success {
  @apply bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400;
}

.status-warning {
  @apply bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400;
}

.status-error {
  @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
}

.status-info {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
}

.status-neutral {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400;
}

/* Business-Specific Status Colors */
.status-paid {
  @apply bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400;
}

.status-pending {
  @apply bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400;
}

.status-overdue {
  @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
}

.status-draft {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400;
}

/* Job Status Colors */
.status-new {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
}

.status-quoted {
  @apply bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400;
}

.status-in-progress {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
}

.status-on-hold {
  @apply bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400;
}

.status-completed {
  @apply bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400;
}

.status-cancelled {
  @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
}

.status-scheduled {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400;
}

/* Client Status Colors */
.status-active {
  @apply bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400;
}

.status-inactive {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400;
}

.status-potential {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
}

/* Team Member Status Colors */
.status-available {
  @apply bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400;
}

.status-busy {
  @apply bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400;
}

.status-offline {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400;
}

/* Status Badge Base Styling */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

/* Combined Status Badge Classes */
.status-badge-new { @apply status-badge status-new; }
.status-badge-quoted { @apply status-badge status-quoted; }
.status-badge-in-progress { @apply status-badge status-in-progress; }
.status-badge-on-hold { @apply status-badge status-on-hold; }
.status-badge-completed { @apply status-badge status-completed; }
.status-badge-cancelled { @apply status-badge status-cancelled; }
.status-badge-scheduled { @apply status-badge status-scheduled; }

.status-badge-active { @apply status-badge status-active; }
.status-badge-inactive { @apply status-badge status-inactive; }
.status-badge-potential { @apply status-badge status-potential; }

.status-badge-available { @apply status-badge status-available; }
.status-badge-busy { @apply status-badge status-busy; }
.status-badge-offline { @apply status-badge status-offline; }

.status-badge-paid { @apply status-badge status-paid; }
.status-badge-pending { @apply status-badge status-pending; }
.status-badge-overdue { @apply status-badge status-overdue; }
.status-badge-draft { @apply status-badge status-draft; }

.status-badge-success { @apply status-badge status-success; }
.status-badge-warning { @apply status-badge status-warning; }
.status-badge-error { @apply status-badge status-error; }
.status-badge-info { @apply status-badge status-info; }
.status-badge-neutral { @apply status-badge status-neutral; }