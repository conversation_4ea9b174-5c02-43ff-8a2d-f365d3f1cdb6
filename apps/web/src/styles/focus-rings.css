/**
 * Unified Focus Ring System - Enterprise Design System
 * DeskBelt3 Business Management Platform
 * 
 * Standardized focus rings for accessibility compliance (WCAG 2.1 AA)
 * Ensures consistent focus indication across all interactive elements
 */

/* === BASE FOCUS RING SYSTEM === */

/* Default focus ring - Primary brand color */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
  @apply transition-all duration-200;
}

/* Dark mode focus ring adjustments */
.dark .focus-ring {
  @apply focus:ring-offset-neutral-900;
}

/* === SEMANTIC FOCUS RINGS === */

/* Success focus ring - For positive actions */
.focus-ring-success {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-success-500;
  @apply transition-all duration-200;
}

/* Warning focus ring - For attention-required actions */
.focus-ring-warning {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-warning-500;
  @apply transition-all duration-200;
}

/* Error/Danger focus ring - For destructive actions */
.focus-ring-error {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-error-500;
  @apply transition-all duration-200;
}

/* Neutral focus ring - For secondary actions */
.focus-ring-neutral {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-neutral-500;
  @apply transition-all duration-200;
}

/* === MODULE-SPECIFIC FOCUS RINGS === */

/* Jobs module focus ring */
.focus-ring-jobs {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
  @apply transition-all duration-200;
}

/* Clients module focus ring */
.focus-ring-clients {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500;
  @apply transition-all duration-200;
}

/* Schedule module focus ring */
.focus-ring-schedule {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500;
  @apply transition-all duration-200;
}

/* Invoicing module focus ring */
.focus-ring-invoicing {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500;
  @apply transition-all duration-200;
}

/* Analytics module focus ring */
.focus-ring-analytics {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500;
  @apply transition-all duration-200;
}

/* Team module focus ring */
.focus-ring-team {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500;
  @apply transition-all duration-200;
}

/* === FOCUS RING VARIANTS === */

/* Thin focus ring - For compact elements */
.focus-ring-thin {
  @apply focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-primary-500;
  @apply transition-all duration-200;
}

/* Thick focus ring - For high importance elements */
.focus-ring-thick {
  @apply focus:outline-none focus:ring-4 focus:ring-offset-2 focus:ring-primary-500;
  @apply transition-all duration-200;
}

/* Inset focus ring - For elements with borders */
.focus-ring-inset {
  @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500;
  @apply transition-all duration-200;
}

/* No offset focus ring - For tight layouts */
.focus-ring-no-offset {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500;
  @apply transition-all duration-200;
}

/* === COMPONENT-SPECIFIC FOCUS RINGS === */

/* Button focus ring */
.btn-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
  @apply transition-all duration-200;
}

/* Input focus ring */
.input-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  @apply transition-all duration-200;
}

/* Card focus ring - For interactive cards */
.card-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-4 focus:ring-primary-500;
  @apply transition-all duration-200;
}

/* Link focus ring */
.link-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-primary-500 focus:rounded-sm;
  @apply transition-all duration-200;
}

/* Tab focus ring */
.tab-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:rounded-lg;
  @apply transition-all duration-200;
}

/* Icon button focus ring */
.icon-btn-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:rounded-full;
  @apply transition-all duration-200;
}

/* Dropdown focus ring */
.dropdown-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  @apply transition-all duration-200;
}

/* === ACCESSIBILITY ENHANCEMENTS === */

/* High contrast focus ring for accessibility */
.focus-ring-high-contrast {
  @apply focus:outline-none focus:ring-4 focus:ring-offset-2 focus:ring-yellow-400;
  @apply transition-all duration-200;
}

/* Forced colors mode support */
@media (forced-colors: active) {
  .focus-ring,
  .focus-ring-success,
  .focus-ring-warning,
  .focus-ring-error,
  .focus-ring-neutral {
    @apply focus:ring-2 focus:ring-offset-2;
    focus-ring-color: Highlight;
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .focus-ring,
  .focus-ring-success,
  .focus-ring-warning,
  .focus-ring-error,
  .focus-ring-neutral,
  .focus-ring-thin,
  .focus-ring-thick,
  .focus-ring-inset,
  .focus-ring-no-offset,
  .btn-focus,
  .input-focus,
  .card-focus,
  .link-focus,
  .tab-focus,
  .icon-btn-focus,
  .dropdown-focus {
    @apply transition-none;
  }
}

/* === FOCUS VISIBLE SUPPORT === */

/* Modern focus-visible support */
.focus-visible-ring:focus-visible {
  @apply outline-none ring-2 ring-offset-2 ring-primary-500;
}

.focus-visible-ring:focus:not(:focus-visible) {
  @apply outline-none ring-0;
}

/* === SKIP LINKS === */

/* Skip to main content link */
.skip-link {
  @apply sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4;
  @apply bg-white dark:bg-neutral-900 text-neutral-900 dark:text-white;
  @apply px-4 py-2 rounded-lg shadow-lg border border-neutral-200 dark:border-neutral-700;
  @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  @apply transition-all duration-200 z-50;
}

/* === UTILITY CLASSES === */

/* Remove all focus styles (use carefully) */
.focus-none {
  @apply focus:outline-none focus:ring-0;
}

/* Apply focus ring on hover as well (for mouse users) */
.focus-ring-hover {
  @apply focus-ring hover:ring-2 hover:ring-offset-2 hover:ring-primary-500/50;
}

/* Custom focus ring with specific color */
.focus-ring-custom {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  /* Use with custom CSS to define ring color */
}

/* === PRINT STYLES === */

@media print {
  .focus-ring,
  .focus-ring-success,
  .focus-ring-warning,
  .focus-ring-error,
  .focus-ring-neutral,
  .focus-ring-thin,
  .focus-ring-thick,
  .focus-ring-inset,
  .focus-ring-no-offset,
  .btn-focus,
  .input-focus,
  .card-focus,
  .link-focus,
  .tab-focus,
  .icon-btn-focus,
  .dropdown-focus {
    @apply ring-0 ring-offset-0;
  }
}