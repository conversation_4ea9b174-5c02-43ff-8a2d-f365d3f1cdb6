/**
 * Touch-Optimized Interactions
 * Mobile-first interactive design with enhanced touch support
 * Based on Apple HIG and Google Material Design touch guidelines
 */

/* Touch Target Standards */
.touch-target {
  @apply min-h-[44px] min-w-[44px]; /* Apple HIG minimum */
}

.touch-target-large {
  @apply min-h-[48px] min-w-[48px]; /* Material Design recommendation */
}

.touch-target-xl {
  @apply min-h-[56px] min-w-[56px]; /* Enhanced accessibility */
}

/* Touch-Optimized Buttons */
.touch-button {
  @apply touch-target px-4 py-3 text-base font-medium;
  @apply rounded-xl transition-all duration-200 ease-out;
  @apply active:scale-95 active:transition-transform active:duration-100;
}

.touch-button-large {
  @apply touch-target-large px-6 py-4 text-lg font-medium;
  @apply rounded-xl transition-all duration-200 ease-out;
  @apply active:scale-95 active:transition-transform active:duration-100;
}

.touch-button-icon {
  @apply touch-target flex items-center justify-center;
  @apply rounded-xl transition-all duration-200 ease-out;
  @apply active:scale-90 active:transition-transform active:duration-100;
}

/* Touch-Friendly Cards */
.touch-card {
  @apply p-4 md:p-6 rounded-xl;
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700;
  @apply transition-all duration-200 ease-out;
  @apply active:scale-[0.98] active:transition-transform active:duration-100;
  @apply focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2;
}

.touch-card-interactive {
  @apply touch-card cursor-pointer;
  @apply hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600;
}

/* Touch-Optimized Form Inputs */
.touch-input {
  @apply block w-full px-4 py-4 text-base;
  @apply bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600;
  @apply rounded-xl shadow-sm;
  @apply focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none;
  @apply transition-all duration-200 ease-out;
}

.touch-textarea {
  @apply touch-input min-h-[120px] resize-y;
}

.touch-select {
  @apply touch-input appearance-none cursor-pointer;
  @apply bg-no-repeat bg-right bg-center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Touch Navigation */
.touch-nav-item {
  @apply flex items-center space-x-3 px-4 py-4 text-base font-medium;
  @apply text-gray-700 dark:text-gray-300;
  @apply rounded-xl transition-all duration-200 ease-out;
  @apply active:scale-95 active:bg-gray-100 dark:active:bg-gray-800;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.touch-nav-item-active {
  @apply touch-nav-item;
  @apply bg-primary-50 dark:bg-primary-950 text-primary-700 dark:text-primary-300;
  @apply border border-primary-200 dark:border-primary-800;
}

/* Touch-Optimized Status Badges */
.touch-status-badge {
  @apply inline-flex items-center px-3 py-2 text-sm font-medium;
  @apply rounded-xl transition-all duration-200 ease-out;
  @apply active:scale-95 active:transition-transform active:duration-100;
}

/* Touch-Friendly Dropdowns */
.touch-dropdown {
  @apply absolute top-full left-0 mt-2 w-full min-w-[200px];
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700;
  @apply rounded-xl shadow-lg z-50;
  @apply py-2;
}

.touch-dropdown-item {
  @apply block w-full px-4 py-3 text-left text-base;
  @apply text-gray-700 dark:text-gray-300;
  @apply transition-all duration-150 ease-out;
  @apply active:bg-primary-50 dark:active:bg-primary-950;
  @apply active:text-primary-700 dark:active:text-primary-300;
}

/* Touch Gestures Support */
.touch-swipe-container {
  @apply overflow-hidden relative;
  touch-action: pan-x;
}

.touch-scrollable {
  @apply overflow-auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
}

.touch-pullable {
  @apply relative;
  overscroll-behavior-y: contain;
}

/* Enhanced Touch Feedback */
.touch-ripple {
  @apply relative overflow-hidden;
}

.touch-ripple::before {
  content: '';
  @apply absolute inset-0 bg-current opacity-0 rounded-xl;
  transform: scale(0);
  transition: transform 0.3s ease-out, opacity 0.2s ease-out;
}

.touch-ripple:active::before {
  @apply opacity-10;
  transform: scale(1);
  transition: transform 0s, opacity 0s;
}

/* Touch-Optimized Tables */
.touch-table-container {
  @apply overflow-x-auto;
  -webkit-overflow-scrolling: touch;
}

.touch-table-row {
  @apply transition-all duration-150 ease-out;
  @apply active:bg-gray-50 dark:active:bg-gray-800/50;
}

.touch-table-cell {
  @apply px-4 py-4 text-base;
  @apply whitespace-nowrap;
}

/* Touch Modal Optimizations */
.touch-modal {
  @apply fixed inset-0 bg-black/50 z-50;
  @apply flex items-end justify-center md:items-center;
  @apply p-0 md:p-4;
}

.touch-modal-content {
  @apply bg-white dark:bg-gray-800 w-full md:max-w-md;
  @apply rounded-t-2xl md:rounded-2xl shadow-2xl;
  @apply max-h-[90vh] overflow-y-auto;
  @apply transform transition-all duration-300 ease-out;
}

.touch-modal-header {
  @apply sticky top-0 bg-white dark:bg-gray-800 z-10;
  @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  @apply rounded-t-2xl;
}

.touch-modal-body {
  @apply px-6 py-4;
}

.touch-modal-footer {
  @apply sticky bottom-0 bg-white dark:bg-gray-800 z-10;
  @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700;
  @apply flex space-x-3;
}

/* Touch Drawer Optimizations */
.touch-drawer {
  @apply fixed inset-0 z-50;
}

.touch-drawer-overlay {
  @apply absolute inset-0 bg-black/50;
}

.touch-drawer-content {
  @apply absolute right-0 top-0 h-full w-full max-w-sm;
  @apply bg-white dark:bg-gray-800 shadow-2xl;
  @apply transform transition-transform duration-300 ease-out;
}

.touch-drawer-header {
  @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  @apply flex items-center justify-between;
}

.touch-drawer-body {
  @apply px-6 py-4 overflow-y-auto;
  @apply flex-1;
}

/* Context-Specific Touch Optimizations */
.touch-job-card {
  @apply touch-card-interactive;
  @apply hover:border-blue-200 dark:hover:border-blue-800;
}

.touch-client-card {
  @apply touch-card-interactive;
  @apply hover:border-green-200 dark:hover:border-green-800;
}

.touch-invoice-card {
  @apply touch-card-interactive;
  @apply hover:border-amber-200 dark:hover:border-amber-800;
}

.touch-team-card {
  @apply touch-card-interactive;
  @apply hover:border-purple-200 dark:hover:border-purple-800;
}

/* Touch Accessibility Enhancements */
.touch-focus-visible:focus-visible {
  @apply ring-4 ring-primary-500 ring-offset-2 outline-none;
}

.touch-skip-link {
  @apply sr-only focus:not-sr-only;
  @apply fixed top-4 left-4 z-50;
  @apply bg-primary-600 text-white px-4 py-2 rounded-lg;
  @apply focus:ring-4 focus:ring-primary-500 focus:ring-offset-2;
}

/* Responsive Touch Adjustments */
@media (max-width: 640px) {
  /* Increase spacing on very small screens */
  .touch-card {
    @apply p-3;
  }
  
  .touch-button {
    @apply px-3 py-3 text-sm;
  }
  
  .touch-input {
    @apply px-3 py-3 text-base;
  }
  
  .touch-nav-item {
    @apply px-3 py-3 text-sm;
  }
  
  /* Stack buttons vertically on small screens */
  .touch-button-group {
    @apply flex flex-col space-y-2 space-x-0;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  /* Tablet optimizations */
  .touch-table-cell {
    @apply px-3 py-3 text-sm;
  }
  
  .touch-modal-content {
    @apply max-w-lg;
  }
}

@media (min-width: 769px) {
  /* Desktop: Reduce touch optimizations */
  .touch-button {
    @apply px-4 py-2 text-sm;
  }
  
  .touch-input {
    @apply px-3 py-3 text-sm;
  }
  
  .touch-nav-item {
    @apply px-3 py-2 text-sm;
  }
  
  /* Horizontal button groups on desktop */
  .touch-button-group {
    @apply flex flex-row space-x-2 space-y-0;
  }
}

/* Touch Performance Optimizations */
.touch-optimized {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.touch-text-selectable {
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Touch State Indicators */
.touch-pressed {
  @apply scale-95 opacity-80;
  transition: transform 0.1s ease-out, opacity 0.1s ease-out;
}

.touch-disabled {
  @apply opacity-50 cursor-not-allowed;
  pointer-events: none;
}

/* Loading States for Touch */
.touch-loading {
  @apply relative overflow-hidden;
}

.touch-loading::after {
  content: '';
  @apply absolute inset-0 bg-white/50 dark:bg-gray-900/50;
  @apply flex items-center justify-center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3e%3ccircle cx='12' cy='12' r='10' stroke='currentColor' stroke-width='4' class='opacity-25'/%3e%3cpath fill='currentColor' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z' class='opacity-75'/%3e%3c/svg%3e");
  background-size: 1.5rem 1.5rem;
  background-repeat: no-repeat;
  background-position: center;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}