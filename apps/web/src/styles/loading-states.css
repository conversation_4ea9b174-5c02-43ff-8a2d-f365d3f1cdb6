/**
 * Professional Loading States System
 * Enterprise-grade loading animations and skeleton components
 * Based on enterprise design audit recommendations
 */

/* Enterprise Loading Skeleton */
.loading-skeleton {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg;
}

.loading-skeleton-text {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-4;
}

.loading-skeleton-title {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-6;
}

.loading-skeleton-avatar {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded-full;
}

.loading-skeleton-card {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded-xl;
}

/* Enterprise Spinner */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
}

.loading-spinner-sm {
  @apply loading-spinner w-4 h-4;
}

.loading-spinner-md {
  @apply loading-spinner w-6 h-6;
}

.loading-spinner-lg {
  @apply loading-spinner w-8 h-8;
}

.loading-spinner-xl {
  @apply loading-spinner w-12 h-12;
}

/* Branded Loading Dots */
.loading-dots {
  @apply flex space-x-1 items-center justify-center;
}

.loading-dot {
  @apply w-2 h-2 bg-primary-600 rounded-full animate-bounce;
}

.loading-dot:nth-child(1) { animation-delay: 0s; }
.loading-dot:nth-child(2) { animation-delay: 0.1s; }
.loading-dot:nth-child(3) { animation-delay: 0.2s; }

/* Professional Loading Pulse */
.loading-pulse {
  @apply animate-pulse;
  animation-duration: 1.5s;
}

/* Loading Overlay */
.loading-overlay {
  @apply fixed inset-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm z-50;
  @apply flex items-center justify-center;
}

.loading-overlay-content {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 shadow-xl;
  @apply flex flex-col items-center space-y-4;
}

/* Module-Specific Loading States */
.loading-card-job {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6;
  @apply animate-pulse;
}

.loading-card-client {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6;
  @apply animate-pulse;
}

/* Progressive Loading Animation */
.loading-progressive {
  animation: progressive-load 2s ease-in-out infinite;
}

@keyframes progressive-load {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Loading Button States */
.loading-button {
  @apply relative overflow-hidden;
}

.loading-button::after {
  content: '';
  @apply absolute inset-0 bg-white/20 -translate-x-full;
  animation: button-shimmer 1.5s infinite;
}

@keyframes button-shimmer {
  100% { transform: translateX(100%); }
}

/* Professional Skeleton Layouts */
.skeleton-job-card {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6;
  @apply animate-pulse space-y-4;
}

.skeleton-client-card {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6;
  @apply animate-pulse space-y-4;
}

.skeleton-stats-card {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6;
  @apply animate-pulse space-y-3;
}

/* Table Loading States */
.skeleton-table-row {
  @apply animate-pulse;
}

.skeleton-table-cell {
  @apply px-6 py-4;
}

.skeleton-table-content {
  @apply bg-gray-200 dark:bg-gray-700 rounded h-4;
}

/* Form Loading States */
.skeleton-form-field {
  @apply space-y-2;
}

.skeleton-form-label {
  @apply bg-gray-200 dark:bg-gray-700 rounded h-4 w-24;
}

.skeleton-form-input {
  @apply bg-gray-200 dark:bg-gray-700 rounded-lg h-12 w-full;
}

/* Charts Loading State */
.skeleton-chart {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6;
  @apply animate-pulse;
}

.skeleton-chart-content {
  @apply bg-gray-200 dark:bg-gray-700 rounded-lg h-64 w-full;
}

/* Mobile-Optimized Loading */
@media (max-width: 768px) {
  .loading-skeleton-card {
    @apply p-4;
  }
  
  .loading-overlay-content {
    @apply mx-4 p-4;
  }
  
  .skeleton-job-card,
  .skeleton-client-card,
  .skeleton-stats-card {
    @apply p-4;
  }
}