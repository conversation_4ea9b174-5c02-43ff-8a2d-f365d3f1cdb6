@tailwind base;
@tailwind components;
@tailwind utilities;


@layer components {
  /* Enhanced <PERSON><PERSON> */
  .btn-primary {
    @apply bg-gradient-to-b from-primary-500 to-primary-600 
           hover:from-primary-600 hover:to-primary-700
           shadow-lg shadow-primary-500/20
           text-white font-medium py-2 px-4 rounded-lg 
           transition-all duration-200
           hover:scale-[1.02];
  }
  
  .btn-secondary {
    @apply bg-gradient-to-b from-gray-50 to-gray-100 
           hover:from-gray-100 hover:to-gray-200 
           shadow-md shadow-gray-400/10
           text-gray-700 font-medium py-2 px-4 rounded-lg 
           transition-all duration-200
           hover:scale-[1.02];
  }
  
  /* Enhanced Card */
  .card {
    @apply bg-white/90 dark:bg-gray-800/90
           backdrop-blur-sm
           border border-gray-200 dark:border-gray-700
           shadow-xl shadow-gray-200/20 dark:shadow-gray-900/30
           rounded-xl p-6
           hover:shadow-2xl hover:scale-[1.02]
           transition-all duration-200;
  }

  /* Glass Effect */
  .glass-effect {
    @apply bg-white/80 dark:bg-gray-800/80 
           backdrop-blur-lg 
           border border-white/20 dark:border-gray-700/20
           shadow-soft-xl;
  }

  /* Navigation Item */
  .nav-item {
    @apply flex items-center space-x-3 px-3 py-2 rounded-xl 
           text-gray-700 dark:text-gray-300 
           hover:bg-gray-100 dark:hover:bg-gray-800 
           hover:text-gray-900 dark:hover:text-white 
           transition-all duration-200
           hover:scale-[1.02];
  }

  /* Input Fields */
  .input {
    @apply block w-full px-4 py-2 
           bg-white dark:bg-gray-800 
           border border-gray-200 dark:border-gray-700 
           rounded-lg 
           focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 
           dark:focus:ring-primary-400/20 dark:focus:border-primary-400
           transition-all duration-200;
  }

  /* Loading Effects */
  .loading-shimmer {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700
           relative overflow-hidden;
  }

  .loading-shimmer::after {
    @apply absolute inset-0 
           -translate-x-full
           animate-[shimmer_2s_infinite]
           bg-gradient-to-r from-transparent via-white/10 to-transparent;
    content: '';
  }
}

/* Global Transitions */
* {
  @apply transition-colors duration-200;
}
