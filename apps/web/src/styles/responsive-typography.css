/**
 * Responsive Typography Scale
 * Mobile-optimized typography with fluid scaling and accessibility support
 * Based on enterprise design audit recommendations
 */

/* Responsive Display Typography */
.responsive-display {
  @apply text-2xl md:text-3xl lg:text-4xl xl:text-5xl;
  @apply font-bold tracking-tight leading-tight;
  @apply text-gray-900 dark:text-white;
}

.responsive-headline {
  @apply text-xl md:text-2xl lg:text-3xl xl:text-4xl;
  @apply font-semibold tracking-tight leading-tight;
  @apply text-gray-900 dark:text-white;
}

.responsive-title {
  @apply text-lg md:text-xl lg:text-2xl;
  @apply font-semibold tracking-tight leading-snug;
  @apply text-gray-900 dark:text-white;
}

.responsive-subtitle {
  @apply text-base md:text-lg lg:text-xl;
  @apply font-medium tracking-tight leading-snug;
  @apply text-gray-800 dark:text-gray-100;
}

.responsive-body {
  @apply text-sm md:text-base lg:text-lg;
  @apply font-normal leading-relaxed;
  @apply text-gray-700 dark:text-gray-300;
}

.responsive-caption {
  @apply text-xs md:text-sm;
  @apply font-medium uppercase tracking-wider;
  @apply text-gray-500 dark:text-gray-400;
}

/* Mobile-First Typography Scale */
.mobile-display {
  @apply text-2xl sm:text-3xl md:text-4xl;
  @apply font-bold tracking-tight leading-tight;
  @apply text-gray-900 dark:text-white;
}

.mobile-headline {
  @apply text-xl sm:text-2xl md:text-3xl;
  @apply font-semibold tracking-tight leading-tight;
  @apply text-gray-900 dark:text-white;
}

.mobile-title {
  @apply text-lg sm:text-xl md:text-2xl;
  @apply font-semibold tracking-tight leading-snug;
  @apply text-gray-900 dark:text-white;
}

.mobile-body {
  @apply text-base sm:text-lg;
  @apply font-normal leading-relaxed;
  @apply text-gray-700 dark:text-gray-300;
}

.mobile-small {
  @apply text-sm sm:text-base;
  @apply font-normal leading-relaxed;
  @apply text-gray-600 dark:text-gray-400;
}

/* Card Typography Scale */
.card-title {
  @apply text-base md:text-lg;
  @apply font-semibold leading-snug;
  @apply text-gray-900 dark:text-white;
}

.card-subtitle {
  @apply text-sm md:text-base;
  @apply font-medium leading-snug;
  @apply text-gray-700 dark:text-gray-300;
}

.card-body {
  @apply text-sm;
  @apply font-normal leading-relaxed;
  @apply text-gray-600 dark:text-gray-400;
}

.card-caption {
  @apply text-xs md:text-sm;
  @apply font-medium uppercase tracking-wider;
  @apply text-gray-500 dark:text-gray-500;
}

/* Button Typography Scale */
.btn-text-lg {
  @apply text-base md:text-lg;
  @apply font-semibold leading-tight;
}

.btn-text-md {
  @apply text-sm md:text-base;
  @apply font-medium leading-tight;
}

.btn-text-sm {
  @apply text-xs md:text-sm;
  @apply font-medium leading-tight;
}

/* Form Typography Scale */
.form-label {
  @apply text-sm md:text-base;
  @apply font-medium leading-snug;
  @apply text-gray-700 dark:text-gray-300;
}

.form-input-text {
  @apply text-sm md:text-base;
  @apply font-normal leading-normal;
  @apply text-gray-900 dark:text-white;
}

.form-help-text {
  @apply text-xs md:text-sm;
  @apply font-normal leading-relaxed;
  @apply text-gray-500 dark:text-gray-400;
}

.form-error-text {
  @apply text-xs md:text-sm;
  @apply font-medium leading-relaxed;
  @apply text-red-600 dark:text-red-400;
}

/* Navigation Typography Scale */
.nav-text {
  @apply text-sm md:text-base;
  @apply font-medium leading-tight;
  @apply text-gray-700 dark:text-gray-300;
}

.nav-text-active {
  @apply text-sm md:text-base;
  @apply font-semibold leading-tight;
  @apply text-primary-700 dark:text-primary-300;
}

.nav-breadcrumb {
  @apply text-xs md:text-sm;
  @apply font-medium leading-tight;
  @apply text-gray-500 dark:text-gray-400;
}

/* Table Typography Scale */
.table-header {
  @apply text-xs md:text-sm;
  @apply font-semibold uppercase tracking-wider;
  @apply text-gray-500 dark:text-gray-400;
}

.table-cell {
  @apply text-sm md:text-base;
  @apply font-normal leading-tight;
  @apply text-gray-900 dark:text-white;
}

.table-cell-secondary {
  @apply text-xs md:text-sm;
  @apply font-normal leading-tight;
  @apply text-gray-500 dark:text-gray-400;
}

/* Status Typography Scale */
.status-text {
  @apply text-xs md:text-sm;
  @apply font-medium leading-tight;
}

.badge-text {
  @apply text-xs;
  @apply font-medium leading-tight;
}

/* Dashboard Typography Scale */
.dashboard-stat-value {
  @apply text-xl md:text-2xl lg:text-3xl;
  @apply font-bold tracking-tight leading-tight;
  @apply text-gray-900 dark:text-white;
}

.dashboard-stat-label {
  @apply text-sm md:text-base;
  @apply font-medium leading-snug;
  @apply text-gray-600 dark:text-gray-400;
}

.dashboard-chart-title {
  @apply text-base md:text-lg;
  @apply font-semibold leading-snug;
  @apply text-gray-900 dark:text-white;
}

/* Notification Typography Scale */
.notification-title {
  @apply text-sm md:text-base;
  @apply font-semibold leading-snug;
  @apply text-gray-900 dark:text-white;
}

.notification-body {
  @apply text-xs md:text-sm;
  @apply font-normal leading-relaxed;
  @apply text-gray-600 dark:text-gray-400;
}

/* Loading Typography Scale */
.loading-text {
  @apply text-sm md:text-base;
  @apply font-medium leading-tight;
  @apply text-gray-600 dark:text-gray-400;
}

/* Responsive Line Heights */
.responsive-leading-tight {
  @apply leading-tight md:leading-snug;
}

.responsive-leading-normal {
  @apply leading-normal md:leading-relaxed;
}

.responsive-leading-relaxed {
  @apply leading-relaxed md:leading-loose;
}

/* Context-Aware Typography */
.text-jobs {
  @apply text-blue-700 dark:text-blue-300;
}

.text-clients {
  @apply text-green-700 dark:text-green-300;
}

.text-invoices {
  @apply text-amber-700 dark:text-amber-300;
}

.text-teams {
  @apply text-purple-700 dark:text-purple-300;
}

/* Semantic Typography Colors */
.text-success {
  @apply text-emerald-700 dark:text-emerald-300;
}

.text-warning {
  @apply text-amber-700 dark:text-amber-300;
}

.text-error {
  @apply text-red-700 dark:text-red-300;
}

.text-info {
  @apply text-blue-700 dark:text-blue-300;
}

.text-muted {
  @apply text-gray-500 dark:text-gray-400;
}

.text-subtle {
  @apply text-gray-400 dark:text-gray-500;
}

/* Typography States */
.text-hover {
  @apply hover:text-gray-900 dark:hover:text-white;
  @apply transition-colors duration-200;
}

.text-focus {
  @apply focus:text-gray-900 dark:focus:text-white;
  @apply transition-colors duration-200;
}

.text-active {
  @apply text-primary-700 dark:text-primary-300;
}

/* Accessibility Enhancements */
.text-high-contrast {
  @apply text-black dark:text-white;
}

.text-screen-reader-only {
  @apply sr-only;
}

/* Touch-Optimized Typography */
@media (max-width: 768px) {
  .touch-text {
    @apply text-base leading-relaxed;
  }
  
  .touch-caption {
    @apply text-sm leading-relaxed;
  }
  
  /* Ensure minimum font sizes on mobile */
  .responsive-body {
    @apply text-base;
  }
  
  .form-input-text {
    @apply text-base;
  }
  
  .btn-text-sm {
    @apply text-sm;
  }
}

/* Print Typography */
@media print {
  .responsive-display,
  .responsive-headline,
  .responsive-title,
  .mobile-display,
  .mobile-headline,
  .mobile-title {
    @apply text-black;
  }
  
  .responsive-body,
  .mobile-body,
  .card-body {
    @apply text-gray-800;
  }
  
  .text-muted,
  .text-subtle {
    @apply text-gray-600;
  }
}

/* Responsive Font Weights */
.responsive-font-light {
  @apply font-light md:font-normal;
}

.responsive-font-normal {
  @apply font-normal md:font-medium;
}

.responsive-font-medium {
  @apply font-medium md:font-semibold;
}

.responsive-font-semibold {
  @apply font-semibold md:font-bold;
}

/* Fluid Typography Scale (Advanced) */
.fluid-display {
  font-size: clamp(1.5rem, 4vw, 3rem);
  @apply font-bold tracking-tight leading-tight;
  @apply text-gray-900 dark:text-white;
}

.fluid-headline {
  font-size: clamp(1.25rem, 3vw, 2.25rem);
  @apply font-semibold tracking-tight leading-tight;
  @apply text-gray-900 dark:text-white;
}

.fluid-title {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  @apply font-semibold tracking-tight leading-snug;
  @apply text-gray-900 dark:text-white;
}

.fluid-body {
  font-size: clamp(0.875rem, 2vw, 1.125rem);
  @apply font-normal leading-relaxed;
  @apply text-gray-700 dark:text-gray-300;
}