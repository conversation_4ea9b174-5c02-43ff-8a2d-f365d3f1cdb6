/**
 * Professional Typography Scale - Enterprise Design System
 * DeskBelt3 Business Management Platform
 * 
 * Semantic typography classes based on design tokens
 * Ensures consistent text styling across the application
 */

/* === SEMANTIC TYPOGRAPHY CLASSES === */

/* Display - Large page titles and hero text */
.text-display {
  @apply text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-neutral-900 dark:text-white;
  line-height: 1.1;
  letter-spacing: -0.025em;
}

/* Headline - Page titles and major section headers */
.text-headline {
  @apply text-2xl md:text-3xl lg:text-4xl font-semibold tracking-tight text-neutral-900 dark:text-white;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

/* Title - Section titles and card headers */
.text-title {
  @apply text-xl md:text-2xl font-semibold text-neutral-900 dark:text-white;
  line-height: 1.3;
}

/* Subtitle - Secondary headings */
.text-subtitle {
  @apply text-lg md:text-xl font-medium text-neutral-900 dark:text-white;
  line-height: 1.4;
}

/* Body - Standard text content */
.text-body {
  @apply text-base font-normal text-neutral-700 dark:text-neutral-300;
  line-height: 1.5;
}

/* Body Medium - Emphasized body text */
.text-body-medium {
  @apply text-base font-medium text-neutral-900 dark:text-neutral-100;
  line-height: 1.5;
}

/* Label - Form labels and UI labels */
.text-label {
  @apply text-sm font-medium text-neutral-600 dark:text-neutral-400;
  line-height: 1.4;
}

/* Caption - Small descriptive text and metadata */
.text-caption {
  @apply text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide;
  line-height: 1.3;
  letter-spacing: 0.025em;
}

/* Small - Fine print and secondary information */
.text-small {
  @apply text-xs font-normal text-neutral-500 dark:text-neutral-400;
  line-height: 1.4;
}

/* === RESPONSIVE TYPOGRAPHY === */

/* Responsive Display - Scales down on mobile */
.text-display-responsive {
  @apply text-2xl font-bold tracking-tight text-neutral-900 dark:text-white;
  @apply md:text-3xl lg:text-4xl;
  line-height: 1.1;
  letter-spacing: -0.025em;
}

/* Responsive Headline - Scales down on mobile */
.text-headline-responsive {
  @apply text-xl font-semibold tracking-tight text-neutral-900 dark:text-white;
  @apply md:text-2xl lg:text-3xl;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

/* Responsive Title - Scales down on mobile */
.text-title-responsive {
  @apply text-lg font-semibold text-neutral-900 dark:text-white;
  @apply md:text-xl;
  line-height: 1.3;
}

/* === UTILITY TYPOGRAPHY CLASSES === */

/* Link styles */
.text-link {
  @apply text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300;
  @apply underline decoration-primary-600/30 hover:decoration-primary-700/50;
  @apply transition-all duration-200;
}

/* Muted text */
.text-muted {
  @apply text-neutral-500 dark:text-neutral-400;
}

/* Error text */
.text-error {
  @apply text-error-600 dark:text-error-400;
}

/* Success text */
.text-success {
  @apply text-success-600 dark:text-success-400;
}

/* Warning text */
.text-warning {
  @apply text-warning-600 dark:text-warning-400;
}

/* === TYPOGRAPHY COMPONENT PATTERNS === */

/* Page header pattern */
.page-header {
  @apply space-y-2;
}

.page-header .page-title {
  @apply text-headline-responsive;
}

.page-header .page-description {
  @apply text-body text-muted;
}

/* Card header pattern */
.card-header {
  @apply space-y-1;
}

.card-header .card-title {
  @apply text-title;
}

.card-header .card-description {
  @apply text-body text-muted;
}

/* Section header pattern */
.section-header {
  @apply space-y-1 mb-4;
}

.section-header .section-title {
  @apply text-caption;
}

/* === TYPOGRAPHY WITH SEMANTIC COLORS === */

/* Primary text emphasis */
.text-primary-emphasis {
  @apply text-primary-700 dark:text-primary-300 font-medium;
}

/* Success emphasis */
.text-success-emphasis {
  @apply text-success-700 dark:text-success-300 font-medium;
}

/* Warning emphasis */
.text-warning-emphasis {
  @apply text-warning-700 dark:text-warning-300 font-medium;
}

/* Error emphasis */
.text-error-emphasis {
  @apply text-error-700 dark:text-error-300 font-medium;
}

/* === MODULE-SPECIFIC TYPOGRAPHY === */

/* Jobs module text */
.text-jobs {
  @apply text-blue-700 dark:text-blue-300;
}

/* Clients module text */
.text-clients {
  @apply text-green-700 dark:text-green-300;
}

/* Schedule module text */
.text-schedule {
  @apply text-purple-700 dark:text-purple-300;
}

/* Invoicing module text */
.text-invoicing {
  @apply text-amber-700 dark:text-amber-300;
}

/* Analytics module text */
.text-analytics {
  @apply text-cyan-700 dark:text-cyan-300;
}

/* Team module text */
.text-team {
  @apply text-pink-700 dark:text-pink-300;
}

/* === ACCESSIBILITY ENHANCEMENTS === */

/* High contrast text for accessibility */
.text-high-contrast {
  @apply text-neutral-900 dark:text-white font-medium;
}

/* Screen reader only text */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible text enhancement */
.focus-text:focus-visible {
  @apply outline-2 outline-offset-2 outline-primary-500;
}

/* === TEXT TRUNCATION UTILITIES === */

/* Single line truncation */
.text-truncate {
  @apply truncate;
}

/* Multi-line truncation (2 lines) */
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Multi-line truncation (3 lines) */
.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* === TYPOGRAPHY ANIMATIONS === */

/* Fade in text animation */
.text-fade-in {
  @apply animate-fade-in-up;
}

/* Text hover scale */
.text-hover-scale {
  @apply transition-transform duration-200 hover:scale-105;
}

/* === FONT FEATURE SETTINGS === */

/* Enable advanced typography features */
.text-optimized {
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1, 'pnum' 1, 'tnum' 0, 'onum' 1, 'lnum' 0, 'dlig' 0;
}

/* Tabular numbers for data display */
.text-tabular {
  font-feature-settings: 'tnum' 1, 'lnum' 1;
  font-variant-numeric: tabular-nums;
}

/* === PRINT STYLES === */

@media print {
  .text-display,
  .text-headline,
  .text-title {
    @apply text-black;
  }
  
  .text-body,
  .text-label {
    @apply text-gray-800;
  }
  
  .text-muted {
    @apply text-gray-600;
  }
}