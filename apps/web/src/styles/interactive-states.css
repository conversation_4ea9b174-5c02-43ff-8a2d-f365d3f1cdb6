/**
 * Enhanced Interactive States System
 * Professional hover, focus, and active patterns for enterprise applications
 * Based on enterprise design audit recommendations
 */

/* Base Interactive Element */
.interactive-base {
  @apply transition-all duration-200 ease-out;
}

/* Professional Hover States */
.interactive-hover {
  @apply hover:scale-[1.02] hover:shadow-lg;
}

.interactive-hover-soft {
  @apply hover:scale-[1.01] hover:shadow-md;
}

.interactive-hover-lift {
  @apply hover:-translate-y-1 hover:shadow-xl;
}

.interactive-hover-glow {
  @apply hover:shadow-lg;
}

.interactive-hover-glow:hover {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

/* Enhanced Focus States */
.interactive-focus {
  @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:outline-none;
}

.interactive-focus-strong {
  @apply focus:ring-4 focus:ring-primary-500/30 focus:ring-offset-2 focus:outline-none;
}

.interactive-focus-subtle {
  @apply focus:ring-1 focus:ring-primary-400 focus:ring-offset-1 focus:outline-none;
}

/* Professional Active States */
.interactive-active {
  @apply active:scale-[0.98] active:shadow-sm;
}

.interactive-active-strong {
  @apply active:scale-[0.95] active:shadow-sm;
}

/* Complete Interactive Element Compositions */
.interactive {
  @apply interactive-base interactive-hover interactive-focus interactive-active;
}

.interactive-soft {
  @apply interactive-base interactive-hover-soft interactive-focus interactive-active;
}

.interactive-strong {
  @apply interactive-base interactive-hover-lift interactive-focus-strong interactive-active-strong;
}

/* Button Interactive States */
.btn-interactive {
  @apply transform transition-all duration-200 ease-out;
  @apply hover:scale-[1.02] hover:-translate-y-0.5 hover:shadow-lg;
  @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:outline-none;
  @apply active:scale-[0.98] active:translate-y-0 active:shadow-md;
}

.btn-interactive-soft {
  @apply transform transition-all duration-200 ease-out;
  @apply hover:scale-[1.01] hover:shadow-md;
  @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 focus:outline-none;
  @apply active:scale-[0.99] active:shadow-sm;
}

/* Card Interactive States */
.card-interactive-enhanced {
  @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700;
  @apply transform transition-all duration-300 ease-out cursor-pointer;
  @apply hover:scale-[1.01] hover:-translate-y-1 hover:shadow-xl hover:border-gray-300 dark:hover:border-gray-600;
  @apply focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2;
  @apply active:scale-[0.99] active:translate-y-0 active:shadow-lg;
}

.card-interactive-subtle {
  @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700;
  @apply transform transition-all duration-200 ease-out cursor-pointer;
  @apply hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600;
  @apply focus-within:ring-1 focus-within:ring-primary-400 focus-within:ring-offset-1;
}

/* Input Interactive States */
.input-interactive {
  @apply transition-all duration-200 ease-out;
  @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:outline-none;
  @apply focus:border-primary-500 focus:shadow-lg focus:scale-[1.01];
  @apply hover:border-gray-400 dark:hover:border-gray-500;
}

.input-interactive-floating {
  @apply transition-all duration-200 ease-out;
  @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:outline-none;
  @apply focus:border-primary-500 focus:shadow-lg focus:scale-[1.01];
  @apply hover:border-gray-400 dark:hover:border-gray-500;
}

/* Link Interactive States */
.link-interactive {
  @apply transition-all duration-200 ease-out;
  @apply hover:text-primary-700 dark:hover:text-primary-300;
  @apply focus:text-primary-700 dark:focus:text-primary-300;
  @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 focus:outline-none focus:rounded;
  @apply active:text-primary-800 dark:active:text-primary-200;
}

.link-interactive-underline {
  @apply link-interactive relative;
  @apply after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-0;
  @apply after:bg-primary-600 after:transition-all after:duration-200;
  @apply hover:after:w-full focus:after:w-full;
}

/* Icon Interactive States */
.icon-interactive {
  @apply transition-all duration-200 ease-out;
  @apply hover:scale-110 hover:text-primary-600 dark:hover:text-primary-400;
  @apply focus:scale-110 focus:text-primary-600 dark:focus:text-primary-400;
  @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 focus:outline-none focus:rounded;
  @apply active:scale-95;
}

.icon-interactive-bg {
  @apply icon-interactive;
  @apply hover:bg-primary-50 dark:hover:bg-primary-950/50;
  @apply focus:bg-primary-50 dark:focus:bg-primary-950/50;
  @apply active:bg-primary-100 dark:active:bg-primary-900/50;
  @apply rounded-lg p-2;
}

/* Dropdown Interactive States */
.dropdown-interactive {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700;
  @apply rounded-xl shadow-lg backdrop-blur-sm;
  @apply transform transition-all duration-200 ease-out;
  @apply origin-top-right scale-95 opacity-0;
  @apply focus-within:scale-100 focus-within:opacity-100;
}

.dropdown-interactive.open {
  @apply scale-100 opacity-100;
}

.dropdown-item-interactive {
  @apply block w-full px-4 py-2.5 text-left text-sm;
  @apply text-gray-700 dark:text-gray-300;
  @apply transition-all duration-150 ease-out;
  @apply hover:bg-primary-50 dark:hover:bg-primary-950/50;
  @apply hover:text-primary-700 dark:hover:text-primary-300;
  @apply focus:bg-primary-50 dark:focus:bg-primary-950/50;
  @apply focus:text-primary-700 dark:focus:text-primary-300;
  @apply focus:outline-none;
  @apply active:bg-primary-100 dark:active:bg-primary-900/50;
}

/* Status Badge Interactive States */
.status-badge-interactive {
  @apply transition-all duration-200 ease-out cursor-pointer;
  @apply hover:scale-105 hover:shadow-md;
  @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 focus:outline-none;
  @apply active:scale-95;
}

/* Navigation Interactive States */
.nav-item-interactive {
  @apply flex items-center space-x-3 px-3 py-2 rounded-lg;
  @apply text-gray-700 dark:text-gray-300;
  @apply transition-all duration-200 ease-out;
  @apply hover:bg-gray-100 dark:hover:bg-gray-800;
  @apply hover:text-gray-900 dark:hover:text-gray-100;
  @apply hover:scale-[1.02];
  @apply focus:bg-gray-100 dark:focus:bg-gray-800;
  @apply focus:text-gray-900 dark:focus:text-gray-100;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1;
  @apply active:scale-[0.98] active:bg-gray-200 dark:active:bg-gray-700;
}

.nav-item-active {
  @apply flex items-center space-x-3 px-4 py-3 rounded-xl;
  @apply bg-primary-50 dark:bg-primary-950 text-primary-700 dark:text-primary-300;
  @apply border border-primary-200 dark:border-primary-800;
  @apply shadow-sm scale-[1.02];
  @apply transition-all duration-200 ease-out;
}

/* Table Row Interactive States */
.table-row-interactive {
  @apply transition-all duration-150 ease-out cursor-pointer;
  @apply hover:bg-gray-50 dark:hover:bg-gray-800/50;
  @apply hover:shadow-sm;
  @apply focus-within:bg-gray-50 dark:focus-within:bg-gray-800/50;
  @apply focus-within:ring-1 focus-within:ring-primary-400 focus-within:ring-inset;
  @apply active:bg-gray-100 dark:active:bg-gray-700/50;
}

/* Modal Interactive States */
.modal-interactive {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50;
  @apply flex items-center justify-center p-4;
  @apply transition-all duration-300 ease-out;
  @apply opacity-0 scale-95;
}

.modal-interactive.open {
  @apply opacity-100 scale-100;
}

.modal-content-interactive {
  @apply bg-white dark:bg-gray-800 rounded-2xl shadow-2xl;
  @apply max-w-md w-full max-h-[90vh] overflow-y-auto;
  @apply transform transition-all duration-300 ease-out;
  @apply scale-95 opacity-0;
}

.modal-content-interactive.open {
  @apply scale-100 opacity-100;
}

/* Tab Interactive States */
.tab-interactive {
  @apply px-4 py-2 text-sm font-medium rounded-lg;
  @apply text-gray-600 dark:text-gray-400;
  @apply transition-all duration-200 ease-out;
  @apply hover:text-gray-900 dark:hover:text-gray-100;
  @apply hover:bg-gray-100 dark:hover:bg-gray-800;
  @apply focus:text-gray-900 dark:focus:text-gray-100;
  @apply focus:bg-gray-100 dark:focus:bg-gray-800;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1;
}

.tab-active {
  @apply px-4 py-2 text-sm font-medium rounded-lg;
  @apply bg-primary-600 text-white;
  @apply shadow-sm;
  @apply transition-all duration-200 ease-out;
}

/* Context-Aware Interactive States */
.interactive-jobs {
  @apply hover:border-blue-200 dark:hover:border-blue-800;
  @apply focus:ring-blue-500 focus:border-blue-500;
}

.interactive-clients {
  @apply hover:border-green-200 dark:hover:border-green-800;
  @apply focus:ring-green-500 focus:border-green-500;
}

.interactive-invoices {
  @apply hover:border-amber-200 dark:hover:border-amber-800;
  @apply focus:ring-amber-500 focus:border-amber-500;
}

.interactive-teams {
  @apply hover:border-purple-200 dark:hover:border-purple-800;
  @apply focus:ring-purple-500 focus:border-purple-500;
}

/* Mobile-Optimized Interactive States */
@media (max-width: 768px) {
  .interactive-hover {
    @apply hover:scale-100 hover:shadow-md;
  }
  
  .interactive-hover-lift {
    @apply hover:-translate-y-0.5 hover:shadow-lg;
  }
  
  .card-interactive-enhanced {
    @apply hover:scale-100 hover:-translate-y-0.5 hover:shadow-lg;
  }
  
  .btn-interactive {
    @apply hover:scale-100 hover:-translate-y-0 hover:shadow-md;
  }
  
  /* Increase touch targets for mobile */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
  
  .touch-button {
    @apply touch-target px-4 py-3 text-base;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: more) {
  .interactive-focus {
    @apply focus:ring-4 focus:ring-black dark:focus:ring-white;
  }
  
  .interactive-focus-strong {
    @apply focus:ring-4 focus:ring-black dark:focus:ring-white;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .interactive-base,
  .btn-interactive,
  .card-interactive-enhanced,
  .input-interactive,
  .link-interactive,
  .icon-interactive,
  .dropdown-interactive,
  .status-badge-interactive,
  .nav-item-interactive,
  .table-row-interactive,
  .modal-interactive,
  .tab-interactive {
    @apply transition-none;
  }
  
  .interactive-hover,
  .interactive-hover-soft,
  .interactive-hover-lift,
  .btn-interactive,
  .card-interactive-enhanced,
  .icon-interactive {
    @apply hover:scale-100 hover:transform-none;
  }
}