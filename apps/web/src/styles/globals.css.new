@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background-start: hsl(0 0% 100%);
    --background-end: hsl(0 0% 98%);
    --foreground: hsl(222.2 84% 4.9%);
    --card: hsl(0 0% 100%);
    --card-foreground: hsl(222.2 84% 4.9%);
    --popover: hsl(0 0% 100%);
    --popover-foreground: hsl(222.2 84% 4.9%);
    --primary: hsl(221.2 83.2% 53.3%);
    --primary-foreground: hsl(210 40% 98%);
    --secondary: hsl(210 40% 96.1%);
    --secondary-foreground: hsl(222.2 47.4% 11.2%);
    --muted: hsl(210 40% 96.1%);
    --muted-foreground: hsl(215.4 16.3% 46.9%);
    --accent: hsl(210 40% 96.1%);
    --accent-foreground: hsl(222.2 47.4% 11.2%);
    --destructive: hsl(0 84.2% 60.2%);
    --destructive-foreground: hsl(210 40% 98%);
    --border: hsl(214.3 31.8% 91.4%);
    --input: hsl(214.3 31.8% 91.4%);
    --ring: hsl(221.2 83.2% 53.3%);
    --radius: 0.5rem;
    --animate-duration: 200ms;
    --animate-timing: cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'cv02' 1, 'cv03' 1, 'cv04' 1, 'cv11' 1;
  }

  .dark {
    --background-start: hsl(222.2 84% 4.9%);
    --background-end: hsl(222.2 84% 3.7%);
    --foreground: hsl(210 40% 98%);
    --card: hsl(222.2 84% 4.9%);
    --card-foreground: hsl(210 40% 98%);
    --popover: hsl(222.2 84% 4.9%);
    --popover-foreground: hsl(210 40% 98%);
    --primary: hsl(217.2 91.2% 59.8%);
    --primary-foreground: hsl(222.2 47.4% 11.2%);
    --secondary: hsl(217.2 32.6% 17.5%);
    --secondary-foreground: hsl(210 40% 98%);
    --muted: hsl(217.2 32.6% 17.5%);
    --muted-foreground: hsl(215 20.2% 65.1%);
    --accent: hsl(217.2 32.6% 17.5%);
    --accent-foreground: hsl(210 40% 98%);
    --destructive: hsl(0 62.8% 30.6%);
    --destructive-foreground: hsl(210 40% 98%);
    --border: hsl(217.2 32.6% 17.5%);
    --input: hsl(217.2 32.6% 17.5%);
    --ring: hsl(224.3 76.3% 48%);
  }
}

@layer base {
  body {
    background: linear-gradient(
      to bottom,
      transparent,
      var(--background-end)
    ) var(--background-start);
    color: var(--foreground);
  }
}

@layer components {
  .btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25rem;
    border-radius: 0.5rem;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
  }

  .btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--background-start), 0 0 0 4px var(--ring);
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
    box-shadow: 0 10px 15px -3px hsl(221.2 83.2% 53.3% / 0.2);
  }

  .btn-primary:hover {
    background-color: hsl(221.2 83.2% 48.3%);
    transform: scale(1.02);
  }

  .btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
    border: 1px solid var(--border);
  }

  .btn-secondary:hover {
    background-color: var(--muted);
    transform: scale(1.02);
  }

  .card {
    background-color: var(--card);
    color: var(--card-foreground);
    border: 1px solid var(--border);
    border-radius: calc(var(--radius) * 2);
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card:hover {
    box-shadow: 0 25px 30px -12px rgb(0 0 0 / 0.25);
    transform: scale(1.02);
  }

  .glass {
    background-color: hsl(0 0% 100% / 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid hsl(0 0% 100% / 0.2);
    box-shadow: 0 8px 32px 0 rgb(0 0 0 / 0.1);
  }

  .dark .glass {
    background-color: hsl(222.2 84% 4.9% / 0.8);
    border-color: hsl(217.2 32.6% 17.5% / 0.2);
  }

  .nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    color: var(--foreground);
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .nav-item:hover {
    background-color: var(--muted);
  }

  .input {
    display: block;
    width: 100%;
    padding: 0.625rem 1rem;
    background-color: var(--background-start);
    color: var(--foreground);
    border: 1px solid var(--border);
    border-radius: var(--radius);
  }

  .input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px var(--ring);
  }
}

@layer utilities {
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.2s ease-out;
  }

  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::after {
    position: absolute;
    inset: 0;
    content: '';
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    transform: translateX(-100%);
    animation: shimmer 1.5s infinite;
  }
}

/* Keyframes */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}
