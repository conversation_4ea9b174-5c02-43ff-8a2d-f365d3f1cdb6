@tailwind base;
@tailwind components;
@tailwind utilities;

/**
 * Enterprise Shadow Elevation System - Enterprise Design System
 * DeskBelt3 Business Management Platform
 * 
 * Systematic elevation system for consistent depth hierarchy
 * Based on Material Design elevation principles with enterprise refinements
 */

/* === ELEVATION SCALE === */

/* Elevation 1 - Resting state cards and surfaces */
.elevation-1 {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04);
}

/* Elevation 2 - Hover state cards and raised surfaces */
.elevation-2 {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
}

/* Elevation 3 - Active states and modals */
.elevation-3 {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.08), 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Elevation 4 - Floating action buttons and high emphasis */
.elevation-4 {
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
}

/* Elevation 5 - Tooltips and popovers */
.elevation-5 {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(0, 0, 0, 0.08);
}

/* === SEMANTIC SHADOW USAGE === */

/* Card shadows */
.shadow-card {
  @apply shadow-sm;
}

.shadow-card-hover {
  @apply shadow-md;
}

.shadow-card-active {
  @apply shadow-lg;
}

/* Modal shadows */
.shadow-modal {
  @apply shadow-lg;
}

/* Dropdown shadows */
.shadow-dropdown {
  @apply shadow-md;
}

/* Floating element shadows */
.shadow-floating {
  @apply shadow-xl;
}

/* Tooltip shadows */
.shadow-tooltip {
  @apply shadow-2xl;
}

/* === INTERACTIVE SHADOWS === */

/* Card with hover elevation transition */
.card-elevation {
  @apply shadow-sm transition-all duration-200;
}

.card-elevation:hover {
  @apply shadow-md;
  transform: translateY(-1px);
}

.card-elevation:active {
  @apply shadow-sm;
  transform: translateY(0);
}

/* Button elevation */
.btn-elevation {
  @apply shadow-sm transition-all duration-200;
}

.btn-elevation:hover {
  @apply shadow-md;
  transform: translateY(-0.5px);
}

.btn-elevation:active {
  @apply shadow-sm;
  transform: translateY(0);
}

/* === COLORED SHADOWS === */

/* Primary colored shadow */
.shadow-primary {
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.15), 0 2px 4px rgba(59, 130, 246, 0.06);
}

/* Success colored shadow */
.shadow-success {
  box-shadow: 0 4px 6px rgba(34, 197, 94, 0.15), 0 2px 4px rgba(34, 197, 94, 0.06);
}

/* Warning colored shadow */
.shadow-warning {
  box-shadow: 0 4px 6px rgba(245, 158, 11, 0.15), 0 2px 4px rgba(245, 158, 11, 0.06);
}

/* Error colored shadow */
.shadow-error {
  box-shadow: 0 4px 6px rgba(239, 68, 68, 0.15), 0 2px 4px rgba(239, 68, 68, 0.06);
}

/* === MODULE-SPECIFIC COLORED SHADOWS === */

/* Jobs module shadow */
.shadow-jobs {
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.12), 0 2px 4px rgba(59, 130, 246, 0.04);
}

/* Clients module shadow */
.shadow-clients {
  box-shadow: 0 4px 6px rgba(34, 197, 94, 0.12), 0 2px 4px rgba(34, 197, 94, 0.04);
}

/* Schedule module shadow */
.shadow-schedule {
  box-shadow: 0 4px 6px rgba(139, 92, 246, 0.12), 0 2px 4px rgba(139, 92, 246, 0.04);
}

/* Invoicing module shadow */
.shadow-invoicing {
  box-shadow: 0 4px 6px rgba(245, 158, 11, 0.12), 0 2px 4px rgba(245, 158, 11, 0.04);
}

/* Analytics module shadow */
.shadow-analytics {
  box-shadow: 0 4px 6px rgba(6, 182, 212, 0.12), 0 2px 4px rgba(6, 182, 212, 0.04);
}

/* Team module shadow */
.shadow-team {
  box-shadow: 0 4px 6px rgba(236, 72, 153, 0.12), 0 2px 4px rgba(236, 72, 153, 0.04);
}

/* === DARK MODE SHADOWS === */

.dark .elevation-1 {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark .elevation-2 {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .elevation-3 {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2), 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dark .elevation-4 {
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.1);
}

.dark .elevation-5 {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3), 0 12px 24px rgba(0, 0, 0, 0.15);
}

/* === SHADOW UTILITIES === */

/* Remove all shadows */
.shadow-none {
  box-shadow: none !important;
}

/* Inner shadow for pressed states */
.shadow-inner {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.dark .shadow-inner {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Glow effect for special emphasis */
.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
}

.dark .shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.25);
}

/* === TRANSITION UTILITIES === */

/* Smooth shadow transitions */
.shadow-transition {
  transition: box-shadow 200ms ease-out, transform 200ms ease-out;
}

/* Fast shadow transitions */
.shadow-transition-fast {
  transition: box-shadow 150ms ease-out, transform 150ms ease-out;
}

/* Slow shadow transitions */
.shadow-transition-slow {
  transition: box-shadow 300ms ease-out, transform 300ms ease-out;
}

/* === COMPONENT PATTERNS === */

/* Interactive card pattern */
.interactive-card {
  @apply shadow-sm shadow-transition rounded-xl;
  @apply hover:shadow-md hover:-translate-y-0.5;
  @apply active:shadow-sm active:translate-y-0;
}

/* Floating action button pattern */
.floating-action {
  @apply shadow-xl shadow-transition rounded-full;
  @apply hover:shadow-2xl;
  @apply active:shadow-lg;
}

/* Modal backdrop with shadow */
.modal-backdrop {
  @apply shadow-lg;
  backdrop-filter: blur(4px);
}

/* Popover with arrow shadow */
.popover-shadow {
  @apply shadow-md;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* === ACCESSIBILITY CONSIDERATIONS === */

/* High contrast shadow for accessibility */
.shadow-high-contrast {
  box-shadow: 0 0 0 2px currentColor;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .shadow-transition,
  .shadow-transition-fast,
  .shadow-transition-slow,
  .card-elevation,
  .btn-elevation,
  .interactive-card,
  .floating-action {
    transition: none;
  }
  
  .card-elevation:hover,
  .btn-elevation:hover,
  .interactive-card:hover {
    transform: none;
  }
}

/* === PRINT STYLES === */

@media print {
  .elevation-1,
  .elevation-2,
  .elevation-3,
  .elevation-4,
  .elevation-5,
  .shadow-card,
  .shadow-card-hover,
  .shadow-card-active,
  .shadow-modal,
  .shadow-dropdown,
  .shadow-floating,
  .shadow-tooltip,
  .shadow-primary,
  .shadow-success,
  .shadow-warning,
  .shadow-error,
  .shadow-jobs,
  .shadow-clients,
  .shadow-schedule,
  .shadow-invoicing,
  .shadow-analytics,
  .shadow-team,
  .shadow-glow {
    box-shadow: none !important;
  }
  
  /* Add subtle border for definition in print */
  .elevation-1,
  .elevation-2,
  .elevation-3 {
    border: 1px solid #e5e7eb;
  }
}