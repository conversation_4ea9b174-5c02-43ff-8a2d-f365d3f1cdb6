/* Accessibility & Quality Improvements - Focus Management System */

/* Enhanced Focus Ring System */
.focus-ring-primary {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900 focus:animate-focus-ring-in;
}

.focus-ring-jobs {
  @apply focus:outline-none focus:ring-2 focus:ring-jobs-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900 focus:animate-focus-ring-in;
}

.focus-ring-clients {
  @apply focus:outline-none focus:ring-2 focus:ring-clients-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900 focus:animate-focus-ring-in;
}

.focus-ring-ai {
  @apply focus:outline-none focus:ring-2 focus:ring-ai-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900 focus:animate-focus-ring-in;
}

.focus-ring-error {
  @apply focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900 focus:animate-focus-ring-in;
}

.focus-ring-success {
  @apply focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900 focus:animate-focus-ring-in;
}

.focus-ring-warning {
  @apply focus:outline-none focus:ring-2 focus:ring-warning-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900 focus:animate-focus-ring-in;
}

/* Enhanced Focus States for Interactive Elements */
.focus-visible-enhanced {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:animate-focus-ring-in;
}

.focus-card {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900 focus:scale-[1.02] focus:shadow-lg;
}

/* Skip Navigation Links */
.skip-link {
  @apply fixed top-4 left-4 z-50 px-4 py-2 bg-primary-600 text-white rounded-md text-sm font-medium;
  @apply transform -translate-y-16 opacity-0 transition-all duration-fast;
  @apply focus:translate-y-0 focus:opacity-100 focus:animate-skip-link-slide;
}

/* Screen Reader Only Content */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
  clip: rect(0, 0, 0, 0);
}

.sr-only-focusable {
  @apply sr-only;
}

.sr-only-focusable:focus {
  @apply static w-auto h-auto p-0 m-0 overflow-visible whitespace-normal;
  clip: auto;
}

/* Landmark Regions */
.landmark-header {
  @apply relative;
}

.landmark-main {
  @apply relative min-h-screen;
}

.landmark-aside {
  @apply relative;
}

.landmark-footer {
  @apply relative;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .focus-ring-primary {
    @apply focus:ring-4 focus:ring-black dark:focus:ring-white;
  }
  
  .focus-ring-error {
    @apply focus:ring-4 focus:ring-red-900 dark:focus:ring-red-100;
  }
  
  .focus-ring-success {
    @apply focus:ring-4 focus:ring-green-900 dark:focus:ring-green-100;
  }
  
  /* Enhanced high contrast mode utilities */
  .high-contrast-auto {
    filter: contrast(1.5) brightness(1.1);
    border-width: 2px;
    animation: high-contrast-fade-in 200ms ease-out;
  }
  
  .high-contrast-text {
    color: CanvasText;
    background: Canvas;
  }
  
  .high-contrast-border {
    border-color: ButtonText;
  }
  
  .high-contrast-button {
    background: ButtonFace;
    color: ButtonText;
    border: 2px solid ButtonText;
  }
  
  .high-contrast-focus {
    outline: 3px solid Highlight;
    outline-offset: 2px;
  }
}

/* Color Contrast Validation Classes */
.contrast-aa {
  /* WCAG AA: 4.5:1 for normal text, 3:1 for large text */
  --contrast-ratio: 4.5;
}

.contrast-aaa {
  /* WCAG AAA: 7:1 for normal text, 4.5:1 for large text */
  --contrast-ratio: 7;
}

.contrast-large-aa {
  /* WCAG AA Large Text: 3:1 for 18pt+ or 14pt+ bold */
  --contrast-ratio: 3;
}

.contrast-large-aaa {
  /* WCAG AAA Large Text: 4.5:1 for 18pt+ or 14pt+ bold */
  --contrast-ratio: 4.5;
}

/* Color Contrast Validation Indicators */
.contrast-check::before {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: contrast-check 300ms ease-in-out;
}

.contrast-pass::before {
  background-color: #22c55e; /* Green for passing */
}

.contrast-fail::before {
  background-color: #ef4444; /* Red for failing */
}

.contrast-warning::before {
  background-color: #f59e0b; /* Orange for warnings */
}

/* Contrast Enhancement Utilities */
.enhance-contrast-subtle {
  filter: contrast(1.1);
}

.enhance-contrast-moderate {
  filter: contrast(1.3);
}

.enhance-contrast-strong {
  filter: contrast(1.5);
}

/* Text Shadow for Better Readability */
.text-shadow-contrast {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.text-shadow-strong-contrast {
  text-shadow: 
    0 1px 2px rgba(0, 0, 0, 0.8),
    0 0 4px rgba(0, 0, 0, 0.3);
}

/* Background Contrast Utilities */
.bg-contrast-overlay {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

.bg-high-contrast {
  background-color: Canvas;
  color: CanvasText;
  border: 1px solid ButtonBorder;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .focus-ring-primary,
  .focus-ring-jobs,
  .focus-ring-clients,
  .focus-ring-ai,
  .focus-ring-error,
  .focus-ring-success,
  .focus-ring-warning {
    @apply focus:animate-none;
  }
  
  .skip-link {
    @apply focus:animate-none;
  }
  
  .focus-card {
    @apply focus:scale-100;
  }
}

/* Keyboard Navigation Indicators */
.keyboard-nav-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Focus Trap Management */
.focus-trap-active {
  @apply relative;
}

.focus-trap-active::before,
.focus-trap-active::after {
  content: '';
  @apply absolute top-0 w-0 h-0;
  /* Invisible focus trap boundaries */
}

/* Color Contrast Enhancements */
.contrast-enhanced {
  @apply text-secondary-900 dark:text-secondary-100;
}

.contrast-enhanced-muted {
  @apply text-secondary-700 dark:text-secondary-300;
}

.contrast-enhanced-subtle {
  @apply text-secondary-600 dark:text-secondary-400;
}

/* Interactive Element States */
.interactive-accessible {
  @apply transition-all duration-fast ease-out;
  @apply hover:bg-secondary-50 dark:hover:bg-secondary-800;
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2;
  @apply active:bg-secondary-100 dark:active:bg-secondary-700;
}

/* Form Field Accessibility */
.form-field-accessible {
  @apply relative;
}

.form-field-accessible label {
  @apply block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2;
}

.form-field-accessible input,
.form-field-accessible textarea,
.form-field-accessible select {
  @apply focus-ring-primary;
}

.form-field-accessible .field-error {
  @apply focus-ring-error;
}

.form-field-accessible .field-success {
  @apply focus-ring-success;
}

.form-field-accessible .field-warning {
  @apply focus-ring-warning;
}

/* Error State Announcements */
.error-announcement {
  @apply text-error-600 dark:text-error-400 text-sm mt-1;
  @apply flex items-center gap-1;
}

.success-announcement {
  @apply text-success-600 dark:text-success-400 text-sm mt-1;
  @apply flex items-center gap-1;
}

.warning-announcement {
  @apply text-warning-600 dark:text-warning-400 text-sm mt-1;
  @apply flex items-center gap-1;
}

/* Live Region for Screen Reader Announcements */
.live-region {
  @apply sr-only;
}

.live-region[aria-live="polite"] {
  /* Polite announcements don't interrupt screen readers */
}

.live-region[aria-live="assertive"] {
  /* Assertive announcements interrupt screen readers */
}

/* Button Group Accessibility */
.button-group-accessible {
  @apply flex;
  role: group;
}

.button-group-accessible button:first-child {
  @apply rounded-r-none;
}

.button-group-accessible button:last-child {
  @apply rounded-l-none;
}

.button-group-accessible button:not(:first-child):not(:last-child) {
  @apply rounded-none;
}

.button-group-accessible button:focus {
  @apply z-10 relative;
}

/* Table Accessibility */
.table-accessible {
  @apply w-full border-collapse;
}

.table-accessible th {
  @apply text-left font-semibold bg-secondary-50 dark:bg-secondary-800 px-4 py-3;
  @apply border-b border-secondary-200 dark:border-secondary-700;
}

.table-accessible td {
  @apply px-4 py-3 border-b border-secondary-100 dark:border-secondary-800;
}

.table-accessible tr:hover {
  @apply bg-secondary-25 dark:bg-secondary-900;
}

.table-accessible tr:focus-within {
  @apply bg-secondary-50 dark:bg-secondary-800 outline-none ring-2 ring-inset ring-primary-500;
}

/* Modal/Dialog Accessibility */
.modal-accessible {
  @apply fixed inset-0 z-50 flex items-center justify-center;
  @apply bg-black bg-opacity-50 backdrop-blur-sm;
}

.modal-content-accessible {
  @apply bg-white dark:bg-secondary-800 rounded-xl shadow-xl max-w-lg w-full mx-4;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500;
}

/* Tooltip Accessibility */
.tooltip-accessible {
  @apply absolute z-50 px-2 py-1 text-xs font-medium text-white bg-secondary-900 rounded;
  @apply opacity-0 pointer-events-none transition-opacity duration-fast;
}

.tooltip-accessible[data-show] {
  @apply opacity-100 pointer-events-auto;
}

/* Progress Indicators */
.progress-accessible {
  @apply w-full bg-secondary-200 dark:bg-secondary-700 rounded-full h-2;
}

.progress-bar-accessible {
  @apply h-2 bg-primary-600 rounded-full transition-all duration-300 ease-out;
}

/* Status Indicators with Icons */
.status-accessible {
  @apply inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium;
}

.status-accessible .status-icon {
  @apply w-3 h-3 flex-shrink-0;
}

.status-success {
  @apply bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400;
}

.status-error {
  @apply bg-error-100 text-error-800 dark:bg-error-900/20 dark:text-error-400;
}

.status-warning {
  @apply bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-400;
}

.status-info {
  @apply bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400;
}