/**
 * Design Tokens - Enterprise Design System Foundation
 * DeskBelt3 Business Management Platform
 * 
 * Semantic design tokens for consistent enterprise-grade UI
 * Based on enterprise design audit recommendations
 */

export const designTokens = {
  // === SEMANTIC COLORS ===
  // Primary brand colors (unified across all modules)
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe', 
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',   // Primary brand blue
      600: '#2563eb',   // Primary action color
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554'
    },

    // Context-specific accent colors (subtle module recognition)
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0', 
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',   // Success/positive actions
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16'
    },

    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d', 
      400: '#fbbf24',
      500: '#f59e0b',   // Warning/attention states
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
      950: '#451a03'
    },

    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',   // Error/danger states
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a'
    },

    neutral: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',   // Secondary text and borders
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617'
    },

    // Module recognition colors (for subtle context awareness)
    module: {
      jobs: '#3b82f6',      // Blue - maintain current association
      clients: '#22c55e',   // Green - maintain current association  
      schedule: '#8b5cf6',  // Purple - for distinction
      invoicing: '#f59e0b', // Amber - business/financial
      analytics: '#06b6d4', // Cyan - data visualization
      team: '#ec4899'       // Pink - collaboration
    }
  },

  // === TYPOGRAPHY SCALE ===
  typography: {
    // Font families
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Monaco', 'Cascadia Code', 'monospace']
    },

    // Semantic typography scale
    display: {
      fontSize: '2.25rem',     // 36px
      lineHeight: '2.5rem',    // 40px
      fontWeight: '700',       // bold
      letterSpacing: '-0.025em'
    },

    headline: {
      fontSize: '1.875rem',    // 30px
      lineHeight: '2.25rem',   // 36px
      fontWeight: '600',       // semibold
      letterSpacing: '-0.025em'
    },

    title: {
      fontSize: '1.5rem',      // 24px
      lineHeight: '2rem',      // 32px
      fontWeight: '600',       // semibold
      letterSpacing: '0'
    },

    subtitle: {
      fontSize: '1.25rem',     // 20px
      lineHeight: '1.75rem',   // 28px
      fontWeight: '500',       // medium
      letterSpacing: '0'
    },

    body: {
      fontSize: '1rem',        // 16px
      lineHeight: '1.5rem',    // 24px
      fontWeight: '400',       // normal
      letterSpacing: '0'
    },

    bodyMedium: {
      fontSize: '1rem',        // 16px
      lineHeight: '1.5rem',    // 24px
      fontWeight: '500',       // medium
      letterSpacing: '0'
    },

    caption: {
      fontSize: '0.875rem',    // 14px
      lineHeight: '1.25rem',   // 20px
      fontWeight: '500',       // medium
      letterSpacing: '0.025em',
      textTransform: 'uppercase' as const
    },

    label: {
      fontSize: '0.875rem',    // 14px
      lineHeight: '1.25rem',   // 20px
      fontWeight: '500',       // medium
      letterSpacing: '0'
    },

    small: {
      fontSize: '0.75rem',     // 12px
      lineHeight: '1rem',      // 16px
      fontWeight: '400',       // normal
      letterSpacing: '0'
    }
  },

  // === SPACING SYSTEM ===
  spacing: {
    xs: '0.5rem',    // 8px - tight elements
    sm: '0.75rem',   // 12px - related elements
    md: '1rem',      // 16px - standard spacing
    lg: '1.5rem',    // 24px - section spacing
    xl: '2rem',      // 32px - major sections
    '2xl': '3rem',   // 48px - page sections
    '3xl': '4rem',   // 64px - major page divisions
  },

  // === BORDER RADIUS ===
  borderRadius: {
    none: '0',
    sm: '0.375rem',   // 6px
    md: '0.5rem',     // 8px
    lg: '0.75rem',    // 12px
    xl: '1rem',       // 16px
    '2xl': '1.5rem',  // 24px
    full: '9999px'
  },

  // === SHADOWS ===
  shadows: {
    // Enterprise shadow elevation system
    elevation1: '0 1px 3px rgba(0,0,0,0.08), 0 1px 2px rgba(0,0,0,0.04)',
    elevation2: '0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06)', 
    elevation3: '0 10px 15px rgba(0,0,0,0.08), 0 4px 6px rgba(0,0,0,0.05)',
    elevation4: '0 20px 25px rgba(0,0,0,0.1), 0 10px 10px rgba(0,0,0,0.04)',
    
    // Semantic shadow usage
    card: '0 1px 3px rgba(0,0,0,0.08), 0 1px 2px rgba(0,0,0,0.04)',
    cardHover: '0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06)',
    modal: '0 10px 15px rgba(0,0,0,0.08), 0 4px 6px rgba(0,0,0,0.05)',
    dropdown: '0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06)',
    floating: '0 20px 25px rgba(0,0,0,0.1), 0 10px 10px rgba(0,0,0,0.04)'
  },

  // === FOCUS RINGS ===
  focusRing: {
    // Unified focus ring system for accessibility
    default: {
      ring: '2px solid rgb(59 130 246 / 0.5)',  // primary-500 with opacity
      offset: '2px'
    },
    
    error: {
      ring: '2px solid rgb(239 68 68 / 0.5)',   // error-500 with opacity
      offset: '2px'
    },

    success: {
      ring: '2px solid rgb(34 197 94 / 0.5)',   // success-500 with opacity  
      offset: '2px'
    }
  },

  // === TRANSITIONS ===
  transitions: {
    // Standard transition durations and easings
    fast: '150ms ease-out',
    normal: '200ms ease-out', 
    slow: '300ms ease-out',
    
    // Specific transition types
    colors: '200ms ease-out',
    transform: '200ms ease-out',
    shadow: '200ms ease-out',
    all: '200ms ease-out'
  },

  // === COMPONENT VARIANTS ===
  components: {
    // Button component variants
    button: {
      size: {
        sm: {
          padding: '0.5rem 0.75rem',   // px-3 py-2
          fontSize: '0.875rem',        // text-sm
          lineHeight: '1.25rem'
        },
        md: {
          padding: '0.625rem 1rem',    // px-4 py-2.5
          fontSize: '0.875rem',        // text-sm
          lineHeight: '1.25rem'
        },
        lg: {
          padding: '0.75rem 1.5rem',   // px-6 py-3
          fontSize: '1rem',            // text-base
          lineHeight: '1.5rem'
        }
      },

      variant: {
        primary: {
          background: 'rgb(37 99 235)',      // primary-600
          backgroundHover: 'rgb(29 78 216)', // primary-700
          color: 'white',
          border: 'rgb(37 99 235)'           // primary-600
        },
        secondary: {
          background: 'rgb(241 245 249)',    // neutral-100
          backgroundHover: 'rgb(226 232 240)', // neutral-200
          color: 'rgb(15 23 42)',            // neutral-900
          border: 'rgb(203 213 225)'         // neutral-300
        },
        outline: {
          background: 'transparent',
          backgroundHover: 'rgb(239 246 255)', // primary-50
          color: 'rgb(29 78 216)',           // primary-700
          border: 'rgb(191 219 254)'         // primary-200
        },
        ghost: {
          background: 'transparent',
          backgroundHover: 'rgb(241 245 249)', // neutral-100
          color: 'rgb(51 65 85)',            // neutral-700
          border: 'transparent'
        }
      }
    },

    // Card component variants
    card: {
      base: {
        background: 'white',
        border: '1px solid rgb(226 232 240)', // neutral-200
        borderRadius: '1rem',                 // xl
        padding: '1.5rem',                    // 6
        shadow: '0 1px 3px rgba(0,0,0,0.08), 0 1px 2px rgba(0,0,0,0.04)'
      },
      
      interactive: {
        cursor: 'pointer',
        shadowHover: '0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06)',
        borderHover: '1px solid rgb(203 213 225)', // neutral-300
        transform: 'translateY(-1px)'
      }
    }
  }
} as const;

// === TYPE DEFINITIONS ===
export type DesignTokens = typeof designTokens;
export type ColorScale = typeof designTokens.colors.primary;
export type ModuleColor = keyof typeof designTokens.colors.module;
export type TypographyVariant = keyof typeof designTokens.typography;
export type SpacingSize = keyof typeof designTokens.spacing;
export type ShadowVariant = keyof typeof designTokens.shadows;

// === UTILITY FUNCTIONS ===

/**
 * Get color value from design tokens
 * @param path - Color path (e.g., 'primary.500', 'module.jobs')
 * @returns Color value or undefined if not found
 */
export function getColor(path: string): string | undefined {
  const parts = path.split('.');
  let current: any = designTokens.colors;
  
  for (const part of parts) {
    current = current?.[part];
  }
  
  return typeof current === 'string' ? current : undefined;
}

/**
 * Get module-specific color
 * @param module - Module name
 * @returns Module color value
 */
export function getModuleColor(module: ModuleColor): string {
  return designTokens.colors.module[module];
}

/**
 * Generate CSS custom properties from design tokens
 * @returns CSS custom properties string
 */
export function generateCSSVars(): string {
  const cssVars: string[] = [];
  
  // Generate color variables
  Object.entries(designTokens.colors).forEach(([category, colors]) => {
    if (typeof colors === 'object') {
      Object.entries(colors).forEach(([shade, value]) => {
        cssVars.push(`--color-${category}-${shade}: ${value};`);
      });
    }
  });
  
  // Generate spacing variables
  Object.entries(designTokens.spacing).forEach(([size, value]) => {
    cssVars.push(`--spacing-${size}: ${value};`);
  });
  
  // Generate shadow variables
  Object.entries(designTokens.shadows).forEach(([name, value]) => {
    cssVars.push(`--shadow-${name}: ${value};`);
  });
  
  return `:root {\n  ${cssVars.join('\n  ')}\n}`;
}

export default designTokens;