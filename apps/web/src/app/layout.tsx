import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import '../styles/globals.css'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { AuthProvider } from '@/contexts/AuthContext'
import { DrawerProvider } from '@/contexts/DrawerContext'
import { ToastProvider } from '@/contexts/ToastContext'
import { GlobalDrawers } from '@/components/GlobalDrawers'
import { Suspense } from 'react'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'DeskBelt - Trade Management Platform',
  description: 'Professional job and client management for UK tradespeople',
}

function LoadingFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Loading DeskBelt...</p>
      </div>
    </div>
  )
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${inter.className}`} suppressHydrationWarning>
        <Suspense fallback={<LoadingFallback />}>
          <ThemeProvider>
            <AuthProvider>
              <DrawerProvider>
                <ToastProvider>
                  {children}
                  <GlobalDrawers />
                </ToastProvider>
              </DrawerProvider>
            </AuthProvider>
          </ThemeProvider>
        </Suspense>
      </body>
    </html>
  )
} 