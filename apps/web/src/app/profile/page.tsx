'use client';

import React, { useState } from 'react';
import Layout from '@/components/Layout';
import { useRequireAuth } from '@/hooks/useRequireAuth';
import { useProfile } from '@/hooks/useProfile';
import { useWorkforce } from '@/hooks/useWorkforce';
import { useTeamMembers } from '@/hooks/useTeamMembers';
import { 
  UserIcon,
  EyeIcon,
  EyeSlashIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  BuildingOfficeIcon,
  PhoneIcon,
  GlobeAltIcon,
  MapPinIcon,
  IdentificationIcon,
  UserGroupIcon,
  StarIcon,
  ShieldCheckIcon,
  CogIcon
} from '@heroicons/react/24/outline';

export default function ProfilePage() {
  const { user, loading, isAuthenticated } = useRequireAuth();
  const { data: profile, isLoading: profileLoading, updateProfile, changePassword } = useProfile();
  
  // Team/Workforce integration
  const { workforce, isLoading: workforceLoading, error: workforceError } = useWorkforce();
  const { 
    members: teamMembers, 
    isLoading: membersLoading, 
    getPermissionSummary,
    getMemberDisplayName,
    getMemberInitials 
  } = useTeamMembers(workforce?.id || null);

  // Form states
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editData, setEditData] = useState({
    full_name: '',
    company_name: '',
    phone: '',
    website: '',
    address: '',
    country: 'United Kingdom',
    vat_number: ''
  });

  // Password change states
  const [showPasswordSection, setShowPasswordSection] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState(false);

  // OAuth states
  const [oauthConnections, setOauthConnections] = useState({
    google: false,
    facebook: false
  });

  // Initialize edit data when profile loads
  React.useEffect(() => {
    if (profile) {
      setEditData({
        full_name: profile.full_name || '',
        company_name: profile.company_name || '',
        phone: profile.phone || '',
        website: profile.website || '',
        address: profile.address || '',
        country: profile.country || 'United Kingdom',
        vat_number: profile.vat_number || ''
      });
    }
  }, [profile]);

  // Show loading spinner while checking authentication
  if (loading || profileLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </Layout>
    );
  }

  // Don't render the page content if not authenticated
  if (!isAuthenticated || !profile) {
    return null;
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n.charAt(0))
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset form data
      setEditData({
        full_name: profile?.full_name || '',
        company_name: profile?.company_name || '',
        phone: profile?.phone || '',
        website: profile?.website || '',
        address: profile?.address || '',
        country: profile?.country || 'United Kingdom',
        vat_number: profile?.vat_number || ''
      });
    }
    setIsEditing(!isEditing);
  };

  const handleSaveProfile = async () => {
    setIsSaving(true);
    try {
      await updateProfile(editData);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    if (strength <= 2) return { level: 'weak', color: 'red', text: 'Weak' };
    if (strength <= 3) return { level: 'medium', color: 'yellow', text: 'Medium' };
    return { level: 'strong', color: 'green', text: 'Strong' };
  };

  const handlePasswordChange = async () => {
    setPasswordError('');
    
    // Validation
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      setPasswordError('All password fields are required');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setPasswordError('New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      setPasswordError('New password must be at least 8 characters');
      return;
    }

    setIsChangingPassword(true);
    try {
      await changePassword(passwordData.currentPassword, passwordData.newPassword);
      setPasswordSuccess(true);
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
      setTimeout(() => {
        setPasswordSuccess(false);
        setShowPasswordSection(false);
      }, 3000);
    } catch (error) {
      setPasswordError('Failed to change password. Please check your current password.');
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleOAuthConnect = (provider: 'google' | 'facebook') => {
    // Placeholder for OAuth integration
    console.log(`Connecting to ${provider}...`);
    // This would typically redirect to OAuth provider
    setOauthConnections(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }));
  };

  const passwordStrength = getPasswordStrength(passwordData.newPassword);

  return (
    <Layout>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Your Profile</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage your personal information and account settings</p>
        </div>

        {/* Profile Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Profile Information</h3>
            <button
              onClick={handleEditToggle}
              className="px-4 py-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors"
            >
              {isEditing ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {/* Profile Picture and Basic Info */}
          <div className="flex items-start space-x-6 mb-8">
            <div className="w-20 h-20 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center text-blue-700 dark:text-blue-300 text-2xl font-bold">
              {getInitials(profile.full_name || 'User')}
            </div>
            
            <div className="flex-1">
              {isEditing ? (
                <div className="space-y-3">
                  <input
                    type="text"
                    value={editData.full_name}
                    onChange={(e) => setEditData(prev => ({ ...prev, full_name: e.target.value }))}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Your full name"
                  />
                  <input
                    type="text"
                    value={editData.company_name}
                    onChange={(e) => setEditData(prev => ({ ...prev, company_name: e.target.value }))}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Company name"
                  />
                </div>
              ) : (
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                    {profile.full_name || 'Your Name'}
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400 text-lg">
                    {profile.company_name || 'Your Company'}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Contact Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Email (Read-only) */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <UserIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              </div>
              <div className="flex-1">
                <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Email</div>
                <div className="text-gray-900 dark:text-white">
                  {user?.email || 'Not set'}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Read-only</div>
              </div>
            </div>

            {/* Phone */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <PhoneIcon className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div className="flex-1">
                {isEditing ? (
                  <input
                    type="tel"
                    value={editData.phone}
                    onChange={(e) => setEditData(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    placeholder="+44 7700 900000"
                  />
                ) : (
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Phone</div>
                    <div className="text-gray-900 dark:text-white">
                      {profile.phone || 'Not set'}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Website */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <GlobeAltIcon className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="flex-1">
                {isEditing ? (
                  <input
                    type="url"
                    value={editData.website}
                    onChange={(e) => setEditData(prev => ({ ...prev, website: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    placeholder="https://yourwebsite.com"
                  />
                ) : (
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Website</div>
                    <div className="text-gray-900 dark:text-white">
                      {profile.website || 'Not set'}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Address */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <MapPinIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                {isEditing ? (
                  <textarea
                    value={editData.address}
                    onChange={(e) => setEditData(prev => ({ ...prev, address: e.target.value }))}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm resize-none"
                    placeholder="Your business address"
                  />
                ) : (
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Address</div>
                    <div className="text-gray-900 dark:text-white">
                      {profile.address || 'Not set'}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Country */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <BuildingOfficeIcon className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="flex-1">
                {isEditing ? (
                  <select
                    value={editData.country}
                    onChange={(e) => setEditData(prev => ({ ...prev, country: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  >
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="Ireland">Ireland</option>
                    <option value="United States">United States</option>
                    <option value="Canada">Canada</option>
                    <option value="Australia">Australia</option>
                  </select>
                ) : (
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Country</div>
                    <div className="text-gray-900 dark:text-white">
                      {profile.country || 'Not set'}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* VAT Number */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg flex items-center justify-center">
                <IdentificationIcon className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div className="flex-1">
                {isEditing ? (
                  <input
                    type="text"
                    value={editData.vat_number}
                    onChange={(e) => setEditData(prev => ({ ...prev, vat_number: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    placeholder="GB123456789"
                  />
                ) : (
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">VAT Number</div>
                    <div className="text-gray-900 dark:text-white">
                      {profile.vat_number || 'Not set'}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Save/Cancel Buttons */}
          {isEditing && (
            <div className="flex space-x-3 justify-end mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleEditToggle}
                disabled={isSaving}
                className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveProfile}
                disabled={isSaving}
                className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <CheckIcon className="w-4 h-4 mr-2" />
                {isSaving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          )}
        </div>

        {/* Password Change Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Password & Security</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Change your password to keep your account secure</p>
            </div>
            <button
              onClick={() => setShowPasswordSection(!showPasswordSection)}
              className="px-4 py-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors"
            >
              {showPasswordSection ? 'Cancel' : 'Change Password'}
            </button>
          </div>

          {showPasswordSection && (
            <div className="space-y-4">
              {passwordError && (
                <div className="flex items-center space-x-2 text-red-600 dark:text-red-400 text-sm">
                  <ExclamationTriangleIcon className="w-4 h-4" />
                  <span>{passwordError}</span>
                </div>
              )}

              {passwordSuccess && (
                <div className="flex items-center space-x-2 text-green-600 dark:text-green-400 text-sm">
                  <CheckIcon className="w-4 h-4" />
                  <span>Password changed successfully!</span>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Current Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
                    Current Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.current ? 'text' : 'password'}
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter current password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords(prev => ({ ...prev, current: !prev.current }))}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      {showPasswords.current ? <EyeSlashIcon className="w-4 h-4" /> : <EyeIcon className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {/* New Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
                    New Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.new ? 'text' : 'password'}
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter new password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      {showPasswords.new ? <EyeSlashIcon className="w-4 h-4" /> : <EyeIcon className="w-4 h-4" />}
                    </button>
                  </div>
                  {passwordData.newPassword && (
                    <div className="mt-1">
                      <div className={`text-xs font-medium text-${passwordStrength.color}-600 dark:text-${passwordStrength.color}-400`}>
                        {passwordStrength.text}
                      </div>
                      <div className="flex space-x-1 mt-1">
                        {[1, 2, 3, 4, 5].map((level) => (
                          <div
                            key={level}
                            className={`h-1 w-full rounded ${
                              level <= (passwordStrength.level === 'weak' ? 1 : passwordStrength.level === 'medium' ? 3 : 5)
                                ? `bg-${passwordStrength.color}-500`
                                : 'bg-gray-200 dark:bg-gray-600'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Confirm Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
                    Confirm Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.confirm ? 'text' : 'password'}
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Confirm new password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      {showPasswords.confirm ? <EyeSlashIcon className="w-4 h-4" /> : <EyeIcon className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={handlePasswordChange}
                  disabled={isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                  className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <CheckIcon className="w-4 h-4 mr-2" />
                  {isChangingPassword ? 'Changing...' : 'Change Password'}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Team Membership Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center">
                <UserGroupIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Team Membership</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Your team roles and collaboration settings</p>
              </div>
            </div>
            <a
              href="/dashboard/teams"
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-lg transition-colors"
            >
              <CogIcon className="w-4 h-4 mr-1.5" />
              Manage Teams
            </a>
          </div>

          {/* Loading State */}
          {(workforceLoading || membersLoading) && (
            <div className="flex items-center justify-center py-8">
              <div className="w-6 h-6 border-2 border-indigo-200 dark:border-indigo-800 border-t-indigo-600 dark:border-t-indigo-400 rounded-full animate-spin" />
              <span className="ml-3 text-sm text-gray-600 dark:text-gray-400">Loading team information...</span>
            </div>
          )}

          {/* Error State */}
          {workforceError && (
            <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  Unable to load team information. <a href="/dashboard/teams" className="underline hover:no-underline">Go to Teams page</a>
                </p>
              </div>
            </div>
          )}

          {/* Team Information */}
          {!workforceLoading && !workforceError && workforce && (
            <div className="space-y-6">
              {/* Current Team */}
              <div className="p-4 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 border border-indigo-200 dark:border-indigo-800 rounded-xl">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                      <span className="text-white text-sm font-bold">
                        {workforce.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-indigo-900 dark:text-indigo-100">
                        {workforce.name}
                      </h4>
                      <p className="text-xs text-indigo-700 dark:text-indigo-300">
                        Your primary team
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {workforce.owner_id === user?.id ? (
                      <span className="inline-flex items-center px-2.5 py-1 rounded-lg text-xs font-semibold bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 border border-yellow-200 dark:border-yellow-700">
                        <StarIcon className="w-3 h-3 mr-1" />
                        Owner
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-1 rounded-lg text-xs font-semibold bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-700">
                        <ShieldCheckIcon className="w-3 h-3 mr-1" />
                        Member
                      </span>
                    )}
                  </div>
                </div>
                
                {/* Team Stats */}
                {teamMembers.length > 0 && (
                  <div className="grid grid-cols-3 gap-4 pt-3 border-t border-indigo-200 dark:border-indigo-700">
                    <div className="text-center">
                      <div className="text-lg font-bold text-indigo-900 dark:text-indigo-100">
                        {teamMembers.length}
                      </div>
                      <div className="text-xs text-indigo-600 dark:text-indigo-400">
                        Team Members
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-indigo-900 dark:text-indigo-100">
                        {teamMembers.filter(m => m.role === 'owner').length}
                      </div>
                      <div className="text-xs text-indigo-600 dark:text-indigo-400">
                        Owners
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-indigo-900 dark:text-indigo-100">
                        {teamMembers.filter(m => m.role === 'member').length}
                      </div>
                      <div className="text-xs text-indigo-600 dark:text-indigo-400">
                        Members
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* User's Permissions Summary */}
              {teamMembers.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Your Permissions</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {(() => {
                      const currentMember = teamMembers.find(m => m.user_id === user?.id);
                      if (!currentMember) return null;
                      
                      const permissions = [
                        { key: 'can_manage_jobs', label: 'Manage Jobs', enabled: currentMember.can_manage_jobs },
                        { key: 'can_manage_clients', label: 'Manage Clients', enabled: currentMember.can_manage_clients },
                        { key: 'can_manage_invoices', label: 'Manage Invoices', enabled: currentMember.can_manage_invoices },
                        { key: 'can_manage_quotes', label: 'Manage Quotes', enabled: currentMember.can_manage_quotes },
                        { key: 'can_view_reports', label: 'View Reports', enabled: currentMember.can_view_reports },
                        { key: 'can_manage_team', label: 'Manage Team', enabled: currentMember.can_manage_team },
                      ];
                      
                      return permissions.map((permission) => (
                        <div
                          key={permission.key}
                          className={`flex items-center justify-between p-3 rounded-lg border ${
                            permission.enabled
                              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                              : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                          }`}
                        >
                          <span className={`text-sm font-medium ${
                            permission.enabled
                              ? 'text-green-900 dark:text-green-100'
                              : 'text-gray-600 dark:text-gray-400'
                          }`}>
                            {permission.label}
                          </span>
                          <div className={`w-2 h-2 rounded-full ${
                            permission.enabled
                              ? 'bg-green-500'
                              : 'bg-gray-300 dark:bg-gray-600'
                          }`} />
                        </div>
                      ));
                    })()}
                  </div>
                  
                  {/* Overall Permission Summary */}
                  {(() => {
                    const currentMember = teamMembers.find(m => m.user_id === user?.id);
                    if (!currentMember) return null;
                    
                    return (
                      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-blue-900 dark:text-blue-100">
                            <strong>Access Level:</strong> {getPermissionSummary(currentMember)}
                          </span>
                          <a
                            href="/dashboard/teams"
                            className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium underline"
                          >
                            Request Changes
                          </a>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* Quick Actions */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex flex-wrap gap-3">
                  <a
                    href="/dashboard/teams"
                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                  >
                    <UserGroupIcon className="w-4 h-4 mr-1.5" />
                    View All Members
                  </a>
                  {workforce.owner_id === user?.id && (
                    <a
                      href="/dashboard/teams"
                      className="inline-flex items-center px-3 py-2 text-sm font-medium text-indigo-700 dark:text-indigo-300 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 rounded-lg hover:bg-indigo-100 dark:hover:bg-indigo-900/30 transition-colors"
                    >
                      <CogIcon className="w-4 h-4 mr-1.5" />
                      Team Settings
                    </a>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* No Team State */}
          {!workforceLoading && !workforceError && !workforce && (
            <div className="text-center py-8">
              <UserGroupIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                No Team Membership
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                You're not currently part of any team. Create or join a team to start collaborating.
              </p>
              <a
                href="/dashboard/teams"
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors"
              >
                <UserGroupIcon className="w-4 h-4 mr-2" />
                Get Started with Teams
              </a>
            </div>
          )}
        </div>

        {/* Connected Accounts Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Connected Accounts</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Connect your social accounts for easier sign-in</p>
          </div>

          <div className="space-y-4">
            {/* Google */}
            <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Google</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {oauthConnections.google ? 'Connected' : 'Not connected'}
                  </div>
                </div>
              </div>
              <button
                onClick={() => handleOAuthConnect('google')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  oauthConnections.google
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {oauthConnections.google ? 'Disconnect' : 'Connect'}
              </button>
            </div>

            {/* Facebook */}
            <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5" fill="#1877F2" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Facebook</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {oauthConnections.facebook ? 'Connected' : 'Not connected'}
                  </div>
                </div>
              </div>
              <button
                onClick={() => handleOAuthConnect('facebook')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  oauthConnections.facebook
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {oauthConnections.facebook ? 'Disconnect' : 'Connect'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
} 