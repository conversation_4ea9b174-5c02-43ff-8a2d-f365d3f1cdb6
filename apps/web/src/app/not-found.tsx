'use client';

import Link from 'next/link';

// Simple inline Button component
const Button = ({ 
  theme = 'default', 
  variant = 'primary', 
  size = 'md', 
  children, 
  className = '', 
  ...props 
}: {
  theme?: 'default' | 'jobs' | 'clients' | 'ai';
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
  [key: string]: any;
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  const themeClasses = {
    default: variant === 'primary' ? 'bg-gray-900 text-white hover:bg-gray-800 focus:ring-gray-500' : 'border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-gray-500',
    jobs: variant === 'primary' ? 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500' : 'border border-blue-300 text-blue-700 bg-white hover:bg-blue-50 focus:ring-blue-500',
    clients: variant === 'primary' ? 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500' : 'border border-green-300 text-green-700 bg-white hover:bg-green-50 focus:ring-green-500',
    ai: variant === 'primary' ? 'bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500' : 'border border-orange-300 text-orange-700 bg-white hover:bg-orange-50 focus:ring-orange-500',
  };
  
  return (
    <button 
      className={`${baseClasses} ${sizeClasses[size]} ${themeClasses[theme]} ${className}`} 
      {...props}
    >
      {children}
    </button>
  );
};

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <div className="text-center">
        <div className="text-8xl mb-8">🔧</div>
        <h1 className="text-6xl font-bold text-gray-900 dark:text-white mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-6">
          Page Not Found
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
          Looks like this page went missing! Let's get you back to managing your jobs and clients.
        </p>
        <div className="space-x-4">
          <Link href="/dashboard">
            <Button theme="jobs" size="lg">
              Go to Dashboard
            </Button>
          </Link>
          <Link href="/">
            <Button variant="outline" size="lg">
              Go Home
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
} 