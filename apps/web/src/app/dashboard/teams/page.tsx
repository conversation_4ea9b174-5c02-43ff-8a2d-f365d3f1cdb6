'use client';

import React, { useState } from 'react';
import { 
  MagnifyingGlassIcon,
  PlusIcon, 
  UsersIcon, 
  CogIcon,
  UserGroupIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ChevronDownIcon,
  PhoneIcon,
  EnvelopeIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { useWorkforce } from '@/hooks/useWorkforce';
import { usePendingInvitations } from '@/hooks/usePendingInvitations';
import { useTeamMembers } from '@/hooks/useTeamMembers';
import { useAuth } from '@/contexts/AuthContext';
import CreateTeamDialog from '@/components/CreateTeamDialog';
import WorkforceDrawer from '@/components/WorkforceDrawer';
import { Button } from '@deskbelt/ui';

import { TeamMember } from '@/hooks/useTeamMembers';

// Transform team member data to display format
interface DisplayMember {
  id: string;
  name: string;
  role: string;
  email: string;
  phone: string | null;
  avatar: null;
  status: 'available' | 'busy' | 'offline';
  specialty: string;
  rating: number;
  jobsCompleted: number;
  revenue: number;
  skills: string[];
}

const transformTeamMember = (member: TeamMember): DisplayMember => {
  // Extract display name from user data
  const name = member.users.full_name || member.users.email.split('@')[0];
  
  // Determine role display based on permissions
  let roleDisplay = member.role === 'owner' ? 'Team Owner' : 'Team Member';
  
  // Determine specialty based on role or permissions
  let specialty = 'General';
  if (member.can_manage_team) specialty = 'Team Management';
  else if (member.can_manage_jobs && member.can_manage_clients) specialty = 'Operations';
  else if (member.can_manage_jobs) specialty = 'Job Management';
  else if (member.can_manage_clients) specialty = 'Client Management';
  
  // Create skills array based on permissions
  const skills: string[] = [];
  if (member.can_manage_jobs) skills.push('Jobs');
  if (member.can_manage_clients) skills.push('Clients');
  if (member.can_manage_invoices) skills.push('Invoices');
  if (member.can_manage_quotes) skills.push('Quotes');
  if (member.can_view_reports) skills.push('Reports');
  if (member.can_manage_team) skills.push('Team Management');
  
  // For display purposes, show first 2 skills and count if more
  const displaySkills = skills.length > 2 
    ? [...skills.slice(0, 2), `+${skills.length - 2}`]
    : skills;
  
  return {
    id: member.user_id,
    name,
    role: roleDisplay,
    email: member.users.email,
    phone: member.users.phone,
    avatar: null,
    status: 'available', // Default status - could be enhanced with real status tracking
    specialty,
    rating: 4.5, // Default rating - could be enhanced with real rating system
    jobsCompleted: 0, // Could be enhanced with real job completion tracking
    revenue: 0, // Could be enhanced with real revenue tracking
    skills: displaySkills.length > 0 ? displaySkills : ['Basic Access']
  };
};


// Stats Card Component
const StatsCard = ({ title, value, description, icon, color }: {
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactNode;
  color: string;
}) => (
  <div className="card elevation-1 hover:elevation-2 p-6 shadow-transition">
    <div className="flex items-center">
      <div className={`flex-shrink-0 p-3 rounded-lg ${color}`}>
        {icon}
      </div>
      <div className="ml-4 flex-1">
        <p className="text-sm font-medium text-secondary-600 dark:text-secondary-400">
          {title}
        </p>
        <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-50">
          {value}
        </p>
        <p className="text-sm text-secondary-500 dark:text-secondary-400">
          {description}
        </p>
      </div>
    </div>
  </div>
);

// Team Member Card Component (Grid View)
const TeamMemberCard = ({ member, onView, onEdit, onDelete }: {
  member: DisplayMember;
  onView: (member: any) => void;
  onEdit: (member: any) => void;
  onDelete: (member: any) => void;
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'status-badge-available';
      case 'busy': return 'status-badge-busy';
      case 'offline': return 'status-badge-offline';
      default: return 'status-badge-neutral';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <div className="card-interactive elevation-1 hover:elevation-2 p-6 space-y-4 shadow-transition">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
            {getInitials(member.name)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50 truncate">
                {member.name}
              </h3>
              <span className={getStatusColor(member.status)}>
                {member.status}
              </span>
            </div>
            <p className="text-sm text-secondary-600 dark:text-secondary-400 mb-1">
              {member.role}
            </p>
            <p className="text-sm text-secondary-500 dark:text-secondary-500">
              {member.specialty}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <p className="text-secondary-500 dark:text-secondary-400">Rating</p>
          <p className="font-semibold text-secondary-900 dark:text-secondary-50">
            ⭐ {member.rating} ({member.jobsCompleted} jobs)
          </p>
        </div>
        <div>
          <p className="text-secondary-500 dark:text-secondary-400">Revenue</p>
          <p className="font-semibold text-secondary-900 dark:text-secondary-50">
            £{member.revenue.toLocaleString()}
          </p>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <EnvelopeIcon className="w-4 h-4 text-secondary-400" />
        <span className="text-sm text-secondary-600 dark:text-secondary-400 truncate">
          {member.email}
        </span>
      </div>

      <div className="flex items-center space-x-2">
        <PhoneIcon className="w-4 h-4 text-secondary-400" />
        <span className="text-sm text-secondary-600 dark:text-secondary-400">
          {member.phone}
        </span>
      </div>

      <div className="flex flex-wrap gap-1">
        {member.skills.map((skill, index) => (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"
          >
            {skill}
          </span>
        ))}
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-secondary-200 dark:border-secondary-700">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onView(member)}
            className="p-2 text-secondary-500 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-200 hover:bg-secondary-100 dark:hover:bg-secondary-800 rounded-lg transition-colors"
            title="View Profile"
          >
            <EyeIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => onEdit(member)}
            className="p-2 text-secondary-500 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-200 hover:bg-secondary-100 dark:hover:bg-secondary-800 rounded-lg transition-colors"
            title="Edit Member"
          >
            <PencilIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => onDelete(member)}
            className="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
            title="Remove Member"
          >
            <TrashIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Team Member Table Row Component (List View)
const TeamMemberTableRow = ({ member, onView, onEdit, onDelete }: {
  member: DisplayMember;
  onView: (member: any) => void;
  onEdit: (member: any) => void;
  onDelete: (member: any) => void;
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'status-badge-available';
      case 'busy': return 'status-badge-busy';
      case 'offline': return 'status-badge-offline';
      default: return 'status-badge-neutral';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <tr className="hover:bg-secondary-50 dark:hover:bg-secondary-800/50 transition-colors">
      <td className="px-6 py-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
            {getInitials(member.name)}
          </div>
          <div>
            <div className="text-sm font-medium text-secondary-900 dark:text-secondary-50">
              {member.name}
            </div>
            <div className="text-sm text-secondary-500 dark:text-secondary-400">
              {member.role}
            </div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-secondary-900 dark:text-secondary-50">
          {member.specialty}
        </div>
      </td>
      <td className="px-6 py-4">
        <span className={getStatusColor(member.status)}>
          {member.status}
        </span>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-secondary-900 dark:text-secondary-50">
          ⭐ {member.rating}
        </div>
        <div className="text-sm text-secondary-500 dark:text-secondary-400">
          {member.jobsCompleted} jobs
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm font-medium text-secondary-900 dark:text-secondary-50">
          £{member.revenue.toLocaleString()}
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-secondary-900 dark:text-secondary-50">
          {member.email}
        </div>
        <div className="text-sm text-secondary-500 dark:text-secondary-400">
          {member.phone}
        </div>
      </td>
      <td className="px-6 py-4 text-right">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onView(member)}
            className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"
            title="View Profile"
          >
            <EyeIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => onEdit(member)}
            className="text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-secondary-300"
            title="Edit Member"
          >
            <PencilIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => onDelete(member)}
            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
            title="Remove Member"
          >
            <TrashIcon className="w-4 h-4" />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default function TeamsPage() {
  const { workforce, isLoading, error, refetch } = useWorkforce();
  const { stats: invitationStats } = usePendingInvitations(workforce?.id || null);
  const { members: rawTeamMembers, isLoading: membersLoading, error: membersError, refetch: refetchMembers } = useTeamMembers(workforce?.id || null);
  const { user } = useAuth();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isWorkforceDrawerOpen, setIsWorkforceDrawerOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatusFilters, setSelectedStatusFilters] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Transform team members and ensure owner appears first
  const teamMembers: DisplayMember[] = React.useMemo(() => {
    if (!rawTeamMembers || rawTeamMembers.length === 0) {
      // If no team members exist and we have a user, create a display entry for the current user
      if (user && workforce) {
        return [{
          id: user.id,
          name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Team Owner',
          role: 'Team Owner',
          email: user.email || '',
          phone: user.user_metadata?.phone || null,
          avatar: null,
          status: 'available' as const,
          specialty: 'Team Management',
          rating: 5.0,
          jobsCompleted: 0,
          revenue: 0,
          skills: ['Team Management', 'All Access']
        }];
      }
      return [];
    }

    return rawTeamMembers
      .map(transformTeamMember)
      .sort((a, b) => {
        // Ensure team owners appear first
        if (a.role.includes('Owner') && !b.role.includes('Owner')) return -1;
        if (b.role.includes('Owner') && !a.role.includes('Owner')) return 1;
        return a.name.localeCompare(b.name);
      });
  }, [rawTeamMembers, user, workforce]);

  // Calculate real stats based on actual team members
  const teamStats = React.useMemo(() => {
    const availableMembers = teamMembers.filter(m => m.status === 'available').length;
    const busyMembers = teamMembers.filter(m => m.status === 'busy').length;
    
    return {
      totalMembers: teamMembers.length,
      availableMembers,
      busyMembers,
      pendingInvites: invitationStats.count || 0
    };
  }, [teamMembers, invitationStats.count]);

  const statusOptions = [
    { value: 'available', label: 'Available', color: 'bg-green-500' },
    { value: 'busy', label: 'Busy', color: 'bg-orange-500' },
    { value: 'offline', label: 'Offline', color: 'bg-gray-500' },
  ];

  const handleStatusFilter = (status: string) => {
    setSelectedStatusFilters(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const handleInviteMember = () => {
    setIsWorkforceDrawerOpen(true);
    console.log('Open invite member drawer');
  };

  const handleViewMember = (member: any) => {
    console.log('View member:', member.id);
  };

  const handleEditMember = (member: any) => {
    console.log('Edit member:', member.id);
  };

  const handleDeleteMember = (member: any) => {
    console.log('Delete member:', member.id);
  };

  // Filter members based on search and status
  const filteredMembers = teamMembers.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatusFilters.length === 0 || selectedStatusFilters.includes(member.status);
    return matchesSearch && matchesStatus;
  });

  if (isLoading || membersLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error || membersError) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-red-100 dark:bg-red-900 rounded-full">
          <UsersIcon className="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Failed to load team data
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          {error || membersError}
        </p>
        <button
          onClick={() => {
            refetch();
            refetchMembers();
          }}
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900 dark:text-secondary-50">
            Team Management
          </h1>
          <p className="text-secondary-600 dark:text-secondary-400 mt-1">
            Manage your workforce and team invitations
          </p>
        </div>
        <Button 
          variant="primary"
          context="team"
          onClick={handleInviteMember}
          icon={<UserGroupIcon className="w-5 h-5" />}
        >
          Invite Member
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Members"
          value={teamStats.totalMembers}
          description="Active team members"
          icon={<UsersIcon className="w-6 h-6 text-white" />}
          color="bg-purple-500"
        />
        <StatsCard
          title="Available"
          value={teamStats.availableMembers}
          description="Ready for assignments"
          icon={<UserGroupIcon className="w-6 h-6 text-white" />}
          color="bg-green-500"
        />
        <StatsCard
          title="Busy"
          value={teamStats.busyMembers}
          description="Currently assigned"
          icon={<CogIcon className="w-6 h-6 text-white" />}
          color="bg-orange-500"
        />
        <StatsCard
          title="Pending Invites"
          value={teamStats.pendingInvites}
          description="Awaiting response"
          icon={<EnvelopeIcon className="w-6 h-6 text-white" />}
          color="bg-blue-500"
        />
      </div>

      {/* Search and Filter Bar */}
      <div className="card elevation-1 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          {/* Search Bar */}
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search members..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus-ring"
            />
          </div>

          {/* Status Filter Dropdown */}
          <div className="relative">
            <select
              value=""
              onChange={(e) => {
                if (e.target.value) {
                  handleStatusFilter(e.target.value);
                }
              }}
              className="appearance-none bg-white dark:bg-secondary-700 border border-secondary-300 dark:border-secondary-600 rounded-xl px-4 py-3 pr-8 text-secondary-900 dark:text-secondary-50 focus-ring min-w-[140px] transition-all duration-200"
            >
              <option value="">All Status</option>
              {statusOptions.map((status) => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            </div>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors focus-ring ${
                viewMode === 'grid'
                  ? 'bg-white dark:bg-gray-700 text-purple-600 dark:text-purple-400 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
              title="Grid View"
            >
              <Squares2X2Icon className="w-5 h-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors focus-ring ${
                viewMode === 'list'
                  ? 'bg-white dark:bg-gray-700 text-purple-600 dark:text-purple-400 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
              title="List View"
            >
              <ListBulletIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Active Filters */}
        {selectedStatusFilters.length > 0 && (
          <div className="mt-4 flex items-center space-x-2">
            <span className="text-sm text-secondary-600 dark:text-secondary-400">Active filters:</span>
            {selectedStatusFilters.map((status) => {
              const statusOption = statusOptions.find(s => s.value === status);
              return (
                <span
                  key={status}
                  className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"
                >
                  {statusOption?.label}
                  <button
                    onClick={() => handleStatusFilter(status)}
                    className="ml-2 text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300"
                  >
                    ×
                  </button>
                </span>
              );
            })}
            <button
              onClick={() => setSelectedStatusFilters([])}
              className="text-sm text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300"
            >
              Clear all
            </button>
          </div>
        )}
      </div>

      {/* Team Members Content */}
      <div className="space-y-4">
        {/* Team Members Grid/List */}
        {filteredMembers.length > 0 ? (
          <>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr">
                {filteredMembers.map((member) => (
                  <TeamMemberCard
                    key={member.id}
                    member={member}
                    onView={handleViewMember}
                    onEdit={handleEditMember}
                    onDelete={handleDeleteMember}
                  />
                ))}
              </div>
            ) : (
              <div className="card elevation-1 p-0 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-secondary-200 dark:divide-secondary-700">
                    <thead className="bg-secondary-50 dark:bg-secondary-800">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Member
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Specialty
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Rating
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Revenue
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Contact
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-secondary-800 divide-y divide-secondary-200 dark:divide-secondary-700">
                      {filteredMembers.map((member) => (
                        <TeamMemberTableRow
                          key={member.id}
                          member={member}
                          onView={handleViewMember}
                          onEdit={handleEditMember}
                          onDelete={handleDeleteMember}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {searchTerm || selectedStatusFilters.length > 0 ? 'No members found' : 'No team members yet'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              {searchTerm || selectedStatusFilters.length > 0
                ? 'Try adjusting your search or filters'
                : 'Invite your first team member to get started'
              }
            </p>
            {!(searchTerm || selectedStatusFilters.length > 0) && (
              <Button
                variant="primary"
                context="team"
                onClick={handleInviteMember}
                icon={<UserGroupIcon className="w-5 h-5" />}
              >
                Invite Your First Member
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Create Team Dialog */}
      <CreateTeamDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onTeamCreated={(newTeam) => {
          setIsCreateDialogOpen(false);
          refetch();
          console.log('Team created:', newTeam);
        }}
      />

      {/* Workforce Management Drawer */}
      <WorkforceDrawer
        isOpen={isWorkforceDrawerOpen}
        onClose={() => setIsWorkforceDrawerOpen(false)}
      />
    </div>
  );
}