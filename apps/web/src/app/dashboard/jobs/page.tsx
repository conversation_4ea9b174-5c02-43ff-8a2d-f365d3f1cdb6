'use client';

import { useState, useEffect, useMemo } from 'react';
import JobCard from '@/components/JobCard';
import { JobDrawer } from '@/components/JobDrawer';
import { ClientDrawer } from '@/components/ClientDrawer';
import { ClientNotesDrawer } from '@/components/ClientNotesDrawer';
import { JobLogDrawer } from '@/components/JobLogDrawer';
import JobSchedulingModal from '@/components/JobSchedulingModal';
import ConfirmDeleteJobModal from '@/components/ConfirmDeleteJobModal';
import { EditJobDrawer } from '@/components/EditJobDrawer';
import { useRequireAuth } from '@/hooks/useRequireAuth';
import { useJobs } from '@/hooks/useJobs';
import { JobStatus, CreateJobData } from '@/types/Job';
import {
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ChevronDownIcon,
  EyeIcon,
  PhoneIcon,
  ChatBubbleLeftIcon,
  EnvelopeIcon,
  CalendarIcon,
  PencilIcon,
  TrashIcon,
  UserIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { useDrawer } from '@/contexts/DrawerContext';
import React from 'react';
import { getApiUrl } from '@/lib/api';
import { Input } from '@deskbelt/ui'; // Assuming path
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/contexts/ToastContext';
import { Button } from '@deskbelt/ui';

// Job Table Row Component (List View)
const JobTableRow = ({ job, onJobClick, onClientClick, onNotesClick, onCallClick, onMessageClick, onEmailClick, onSchedulingClick, onEditClick, onDeleteClick }: {
  job: any;
  onJobClick: (jobId: string) => void;
  onClientClick: (clientId: string) => void;
  onNotesClick: (jobId: string) => void;
  onCallClick: (phone: string) => void;
  onMessageClick: (clientId: string) => void;
  onEmailClick: (email: string, job: any) => void;
  onSchedulingClick: (jobId: string) => void;
  onEditClick: (jobId: string) => void;
  onDeleteClick: (jobId: string) => void;
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', { 
      day: '2-digit', 
      month: 'short', 
      year: 'numeric' 
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'quoted': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      case 'in_progress': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'on_hold': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'completed': return 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'new': return 'New';
      case 'quoted': return 'Quoted';
      case 'in_progress': return 'In Progress';
      case 'on_hold': return 'On Hold';
      case 'completed': return 'Completed';
      default: return status;
    }
  };

  return (
    <tr className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50 transition-colors cursor-pointer" onClick={() => onJobClick(job.id)}>
      <td className="px-6 py-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
            {job.title.charAt(0).toUpperCase()}
          </div>
          <div>
            <div className="text-sm font-medium text-neutral-900 dark:text-neutral-50">
              {job.title}
            </div>
            <div className="text-sm text-neutral-500 dark:text-neutral-400">
              #{job.id.slice(-6).toUpperCase()}
            </div>
            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(job.status)} mt-1`}>
              {getStatusLabel(job.status)}
            </span>
          </div>
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-neutral-900 dark:text-neutral-50">
          {job.client.name}
        </div>
        {job.client.business_name && (
          <div className="text-sm text-neutral-500 dark:text-neutral-400">
            {job.client.business_name}
          </div>
        )}
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-neutral-900 dark:text-neutral-50">
          {job.scheduled_at ? formatDate(job.scheduled_at) : 'Not scheduled'}
        </div>
        {job.assigned_to && (
          <div className="text-sm text-neutral-500 dark:text-neutral-400">
            Assigned to {job.assigned_to.full_name}
          </div>
        )}
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-neutral-900 dark:text-neutral-50">
          {formatDate(job.created_at)}
        </div>
      </td>
      <td className="px-6 py-4 text-right">
        <div className="flex items-center justify-end space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onNotesClick(job.id);
            }}
            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 p-1 rounded transition-colors"
            title="View Job Log"
          >
            <DocumentTextIcon className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (job.client.phone) onCallClick(job.client.phone);
            }}
            disabled={!job.client.phone}
            className={`p-1 rounded transition-colors ${
              job.client.phone
                ? 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300'
                : 'text-neutral-300 dark:text-neutral-600 cursor-not-allowed'
            }`}
            title={job.client.phone ? `Call ${job.client.phone}` : 'No phone number'}
          >
            <PhoneIcon className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (job.client.email) onEmailClick(job.client.email, job);
            }}
            disabled={!job.client.email}
            className={`p-1 rounded transition-colors ${
              job.client.email
                ? 'text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300'
                : 'text-neutral-300 dark:text-neutral-600 cursor-not-allowed'
            }`}
            title={job.client.email ? `Email ${job.client.email}` : 'No email address'}
          >
            <EnvelopeIcon className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onSchedulingClick(job.id);
            }}
            className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300 p-1 rounded transition-colors"
            title="Schedule Job"
          >
            <CalendarIcon className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEditClick(job.id);
            }}
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-neutral-300 p-1 rounded transition-colors"
            title="Edit Job"
          >
            <PencilIcon className="w-4 h-4" />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default function JobsPage() {
  const { user, loading: authLoading, isAuthenticated } = useRequireAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatusFilter, setSelectedStatusFilter] = useState<JobStatus | ''>('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [selectedClientData, setSelectedClientData] = useState<any>(null);
  const [selectedClientNotesId, setSelectedClientNotesId] = useState<string | null>(null);
  const [selectedJobLogId, setSelectedJobLogId] = useState<string | null>(null);
  const [openDropdowns, setOpenDropdowns] = useState<{[jobId: string]: {status: boolean, date: boolean, time: boolean}}>({});
  const [schedulingJobId, setSchedulingJobId] = useState<string | null>(null);
  const [deleteJobId, setDeleteJobId] = useState<string | null>(null);
  const [editJobId, setEditJobId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const { authenticatedGet, authenticatedFetch } = useAuthenticatedFetch();
  const { showSuccess, showError } = useToast();

  // Memoize the status array to prevent infinite re-renders
  const statusArray = useMemo(() => {
    return selectedStatusFilter ? [selectedStatusFilter] : [];
  }, [selectedStatusFilter]);

  const { data: jobs, isLoading, error, refetch, hasMore, loadMore } = useJobs({
    search: searchTerm,
    status: statusArray,
  });

  const { openNewJobDrawer, setOnJobCreated, openSendEmailModal, openScheduleJobDrawer } = useDrawer();

  // keep ref to refetch
  const refetchRef = React.useRef(refetch);
  React.useEffect(() => (refetchRef.current = refetch), [refetch]);
  React.useEffect(() => {
    setOnJobCreated(() => {
      refetchRef.current();
    });
  }, [setOnJobCreated]);

  // TODO: Add click-outside handler for dropdowns (disabled for debugging)

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobs-500"></div>
        </div>
      </>
    );
  }

  // Don't render the page content if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  const statusOptions: { value: JobStatus; label: string; color: string }[] = [
    { value: 'new', label: 'New', color: 'bg-blue-500' },
    { value: 'quoted', label: 'Quoted', color: 'bg-indigo-500' },
    { value: 'in_progress', label: 'In Progress', color: 'bg-green-500' },
    { value: 'on_hold', label: 'On Hold', color: 'bg-orange-500' },
    { value: 'completed', label: 'Completed', color: 'bg-teal-500' },
  ];

  const handleStatusFilter = (status: JobStatus | '') => {
    setSelectedStatusFilter(status);
  };

  const handleJobClick = (jobId: string) => {
    setSelectedJobId(jobId);
  };

  const handleClientClick = async (clientId: string) => {
    setSelectedClientId(clientId);
    
    // Fetch full client data including rating
    try {
      const response = await authenticatedGet(getApiUrl(`/api/clients/${clientId}`));
      if (response.ok) {
        const clientData = await response.json();
        setSelectedClientData(clientData);
      } else {
        console.error('Failed to fetch client data');
        setSelectedClientData(null);
      }
    } catch (error) {
      console.error('Error fetching client data:', error);
      setSelectedClientData(null);
    }
  };

  const handleNotesClick = (jobId: string) => {
    setSelectedJobLogId(jobId);
  };

  const handleCallClick = (phone: string) => {
    window.open(`tel:${phone}`, '_self');
  };

  const handleMessageClick = (clientId: string) => {
    // TODO: Open messaging interface
    console.log('Send message to client:', clientId);
  };

  const handleEmailClick = (email: string, job: any) => {
    // Open the send email modal with the job data
    openSendEmailModal(job);
  };

  const handleStatusChange = async (jobId: string, status: JobStatus) => {
    try {
      const response = await authenticatedFetch(getApiUrl(`/api/jobs/${jobId}`), {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update job status: ${response.status}`);
      }
      
      console.log('✅ Job status updated successfully');
    } catch (error) {
      console.error('❌ Failed to update job status:', error);
    }
    
    // Close dropdown
    setOpenDropdowns(prev => ({
      ...prev,
      [jobId]: { ...prev[jobId], status: false }
    }));
    // Refresh the jobs list
    refetch();
  };

  const handleDateChange = async (jobId: string, date: string) => {
    try {
      const response = await authenticatedFetch(getApiUrl(`/api/jobs/${jobId}`), {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ scheduled_at: date })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update job date: ${response.status}`);
      }
      
      console.log('✅ Job date updated successfully');
    } catch (error) {
      console.error('❌ Failed to update job date:', error);
    }
    
    // Close dropdown  
    setOpenDropdowns(prev => ({
      ...prev,
      [jobId]: { ...prev[jobId], date: false }
    }));
    // Refresh the jobs list
    refetch();
  };

  const handleScheduleChange = async (jobId: string, schedule: {
    date: string;
    startTime?: string | null;
    endTime?: string | null;
    duration?: string | null;
    notes?: string | null;
  }) => {
    try {
      const response = await authenticatedFetch(getApiUrl(`/api/jobs/${jobId}`), {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          scheduled_at: schedule.date,
          scheduled_start_time: schedule.startTime,
          scheduled_end_time: schedule.endTime,
          estimated_duration: schedule.duration,
          scheduling_notes: schedule.notes
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update job schedule: ${response.status}`);
      }
      
      console.log('✅ Job schedule updated successfully');
    } catch (error) {
      console.error('❌ Failed to update job schedule:', error);
    }
    
    // Close dropdown  
    setOpenDropdowns(prev => ({
      ...prev,
      [jobId]: { ...prev[jobId], time: false }
    }));
    // Refresh the jobs list
    refetch();
  };

  const handleStatusDropdownToggle = (jobId: string) => {
    console.log('Status dropdown toggle for job:', jobId, 'Current state:', openDropdowns[jobId]?.status);
    setOpenDropdowns(prev => ({
      ...prev,
      [jobId]: { 
        status: !(prev[jobId]?.status || false),
        date: false, // Close date picker when opening status
        time: false  // Close time picker when opening status
      }
    }));
  };

  // Enhanced scheduling handler - uses global drawer
  const handleSchedulingClick = (jobId: string) => {
    // Close any open dropdowns first
    setOpenDropdowns({});
    // Use global schedule job drawer for consistency
    openScheduleJobDrawer(jobId);
  };

  const handleSchedulingModalClose = () => {
    setSchedulingJobId(null);
  };

  // Legacy handlers for backward compatibility (if needed)
  const handleDateDropdownToggle = (jobId: string) => {
    // Redirect to new scheduling flow
    handleSchedulingClick(jobId);
  };

  const handleTimePickerToggle = (jobId: string) => {
    // Redirect to new scheduling flow
    handleSchedulingClick(jobId);
  };

  // Client-specific handlers for ClientDrawer functionality
  const handleClientCreateJobClick = (clientId: string) => {
    // TODO: Navigate to job creation with pre-selected client
    //console.log('Create job for client:', clientId);
  };

  const handleClientNotesClick = (clientId: string) => {
    setSelectedClientNotesId(clientId);
  };

  const handleClientRatingChange = async (clientId: string, rating: number) => {
    // Optimistically update the local client data immediately
    if (selectedClientData && selectedClientData.id === clientId) {
      setSelectedClientData({ ...selectedClientData, rating });
    }
    
    // Make API call to update client rating
    try {
      const response = await authenticatedFetch(getApiUrl(`/api/clients/${clientId}/rating`), {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rating })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update rating: ${response.status} ${response.statusText}`);
      }
      
      console.log('✅ Client rating updated successfully');
      
      // Refresh jobs to get updated client data
      refetch();
    } catch (error) {
      console.error('❌ Failed to update client rating:', error);
      // Revert the optimistic update on error by refetching client data
      if (selectedClientData && selectedClientData.id === clientId) {
        try {
          const response = await authenticatedGet(getApiUrl(`/api/clients/${clientId}`));
          if (response.ok) {
            const clientData = await response.json();
            setSelectedClientData(clientData);
          }
        } catch (fetchError) {
          console.error('Failed to revert client data:', fetchError);
        }
      }
    }
  };

  // Edit and Delete handlers
  const handleEditClick = (job: any) => {
    setEditJobId(job.id);
  };

  const handleDeleteClick = (job: any) => {
    setDeleteJobId(job.id);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteJobId) return;
    
    setIsDeleting(true);
    
    try {
      const response = await authenticatedFetch(getApiUrl(`/api/jobs/${deleteJobId}`), {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(errorData.message || `Failed to delete job: ${response.status}`);
      }
      
      // Success - show toast and refresh list
      showSuccess('Job Deleted', 'The job has been successfully deleted.');
      refetch();
      
    } catch (error) {
      console.error('❌ Failed to delete job:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      showError('Delete Failed', errorMessage);
    } finally {
      setIsDeleting(false);
      setDeleteJobId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteJobId(null);
  };

  const handleEditJobUpdated = (updatedJob: any) => {
    // Show success message
    showSuccess('Job Updated', 'The job has been successfully updated.');
    // Refresh the jobs list to show updated data
    refetch();
    // Close the edit drawer
    setEditJobId(null);
  };

  const handleEditClose = () => {
    setEditJobId(null);
  };

  const JobSkeleton = () => (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 animate-pulse">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
        </div>
        <div className="w-20 h-6 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
      </div>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded mr-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded mr-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
        </div>
      </div>
      <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
        </div>
        <div className="w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    </div>
  );

  return (
    <>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-headline">
              Job Management
            </h1>
            <p className="text-body text-muted mt-1">
              Manage all your electrical jobs and track progress
            </p>
          </div>
          <Button 
            variant="primary"
            context="jobs" 
            onClick={() => openNewJobDrawer()}
            icon={<PlusIcon className="w-5 h-5" />}
          >
            Create Job
          </Button>
        </div>

        {/* Search and Filter Bar */}
        <div className="card elevation-1 p-6">
          <div className="flex items-center gap-4">
            {/* Search Bar - Expanded */}
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="text"
                placeholder="Search jobs, descriptions, or clients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 focus-ring" // Add padding for the icon and focus ring
              />
            </div>

            {/* Status Filter Dropdown */}
            <div className="relative">
              <select
                value={selectedStatusFilter}
                onChange={(e) => handleStatusFilter(e.target.value as JobStatus | '')}
                className="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 pr-8 text-gray-900 dark:text-white focus-ring min-w-[140px] transition-all duration-200"
              >
                <option value="">All Statuses</option>
                {statusOptions.map((status) => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors focus-ring ${
                  viewMode === 'grid'
                    ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
                title="Grid View"
              >
                <Squares2X2Icon className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors focus-ring ${
                  viewMode === 'list'
                    ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
                title="List View"
              >
                <ListBulletIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Active Filter Display */}
        {selectedStatusFilter && (
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Active Filter:
                </span>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                  {statusOptions.find(opt => opt.value === selectedStatusFilter)?.label || selectedStatusFilter}
                  <button
                    onClick={() => handleStatusFilter('')}
                    className="ml-2 hover:bg-blue-200 dark:hover:bg-blue-800/50 rounded-full p-0.5"
                    title="Remove filter"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              </div>
              <button
                onClick={() => setSelectedStatusFilter('')}
                className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
              >
                Clear filter
              </button>
            </div>
          </div>
        )}

        {/* Unscheduled Jobs Alert */}
        {jobs.length > 0 && (() => {
          const unscheduledJobs = jobs.filter(job => !job.scheduled_at);
          if (unscheduledJobs.length > 0) {
            return (
              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="bg-amber-500 rounded-full p-2">
                      <CalendarIcon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-amber-900 dark:text-amber-200 mb-1">
                        {unscheduledJobs.length} Job{unscheduledJobs.length === 1 ? '' : 's'} Need Scheduling
                      </h3>
                      <p className="text-amber-700 dark:text-amber-300 text-sm mb-3">
                        These jobs don't have scheduled dates yet. Click "Schedule" to assign dates and times.
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {unscheduledJobs.slice(0, 3).map((job) => (
                          <button
                            key={job.id}
                            onClick={() => handleSchedulingClick(job.id)}
                            className="inline-flex items-center px-3 py-1.5 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200 rounded-lg text-sm font-medium hover:bg-amber-200 dark:hover:bg-amber-900/50 transition-colors"
                          >
                            <CalendarIcon className="w-4 h-4 mr-1.5" />
                            {job.title}
                          </button>
                        ))}
                        {unscheduledJobs.length > 3 && (
                          <span className="inline-flex items-center px-3 py-1.5 text-amber-700 dark:text-amber-300 text-sm">
                            +{unscheduledJobs.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      // Open the first unscheduled job for scheduling
                      if (unscheduledJobs.length > 0) {
                        handleSchedulingClick(unscheduledJobs[0].id);
                      }
                    }}
                    className="inline-flex items-center px-4 py-2 bg-amber-600 text-white rounded-lg font-medium hover:bg-amber-700 transition-colors"
                  >
                    <CalendarIcon className="w-4 h-4 mr-2" />
                    Schedule Now
                  </button>
                </div>
              </div>
            );
          }
          return null;
        })()}

        {/* Jobs List */}
        <div className="space-y-4">
          {/* Error State */}
          {error && jobs.length > 0 && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 text-center">
              <div className="text-red-600 dark:text-red-400 mb-2">
                <ExclamationTriangleIcon className="w-8 h-8 mx-auto mb-2" />
                <p className="font-medium">Failed to load jobs</p>
              </div>
              <p className="text-sm text-red-500 dark:text-red-400 mb-4">{error}</p>
              <button
                onClick={refetch}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Friendly empty state even if error but no jobs */}
          {error && jobs.length === 0 && (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">📋</div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No jobs to display
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-8">
                It looks like there aren't any jobs right now. Click <span className="font-medium">+ New Job</span> to add one.
              </p>
            </div>
          )}

          {isLoading && jobs.length === 0 && (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {Array.from({ length: 6 }, (_, i) => (
                <JobSkeleton key={i} />
              ))}
            </div>
          )}

          {!isLoading && jobs.length === 0 && !error && (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">📋</div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {searchTerm || selectedStatusFilter ? 'No jobs found' : 'No jobs yet'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-8">
                {searchTerm || selectedStatusFilter
                  ? 'Try adjusting your search or filters'
                  : 'Create a new job to get started'
                }
              </p>
              {!(searchTerm || selectedStatusFilter) && (
                <button
                  onClick={() => openNewJobDrawer()}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="w-5 h-5 mr-2" />
                  Create a New Job
                </button>
              )}
            </div>
          )}

          {jobs.length > 0 && (
            <>
              {viewMode === 'grid' ? (
                <div className="space-y-6">
                  {/* Unscheduled Jobs Section */}
                  {(() => {
                    const unscheduledJobs = jobs.filter(job => !job.scheduled_at);
                    const scheduledJobs = jobs.filter(job => job.scheduled_at);

                    return (
                      <>
                        {unscheduledJobs.length > 0 && (
                          <div>
                            <div className="flex items-center justify-between mb-4">
                              <h2 className="text-lg font-semibold text-amber-900 dark:text-amber-200 flex items-center">
                                <CalendarIcon className="w-5 h-5 mr-2 text-amber-600" />
                                Unscheduled Jobs ({unscheduledJobs.length})
                              </h2>
                              <button
                                onClick={() => {
                                  if (unscheduledJobs.length > 0) {
                                    handleSchedulingClick(unscheduledJobs[0].id);
                                  }
                                }}
                                className="text-sm text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 font-medium"
                              >
                                Schedule All →
                              </button>
                            </div>
                            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr mb-8">
                              {unscheduledJobs.map((job) => (
                                <div key={job.id} className="relative">
                                  <div className="absolute -top-2 -right-2 z-10">
                                    <div className="bg-amber-500 text-white rounded-full p-1.5 shadow-lg">
                                      <CalendarIcon className="w-4 h-4" />
                                    </div>
                                  </div>
                                  <JobCard
                                    job={job}
                                    onJobClick={handleJobClick}
                                    onClientClick={handleClientClick}
                                    onNotesClick={handleNotesClick}
                                    onCallClick={handleCallClick}
                                    onMessageClick={handleMessageClick}
                                    onEmailClick={handleEmailClick}
                                    onStatusChange={handleStatusChange}
                                    onDateChange={handleDateChange}
                                    onScheduleChange={handleScheduleChange}
                                    showStatusDropdown={openDropdowns[job.id]?.status || false}
                                    showDatePicker={openDropdowns[job.id]?.date || false}
                                    showTimePicker={openDropdowns[job.id]?.time || false}
                                    onStatusDropdownToggle={() => handleStatusDropdownToggle(job.id)}
                                    onDateDropdownToggle={() => handleDateDropdownToggle(job.id)}
                                    onTimePickerToggle={() => handleTimePickerToggle(job.id)}
                                    onSchedulingClick={handleSchedulingClick}
                                    onEditClick={() => handleEditClick(job)}
                                    onDeleteClick={() => handleDeleteClick(job)}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Scheduled Jobs Section */}
                        {scheduledJobs.length > 0 && (
                          <div>
                            {unscheduledJobs.length > 0 && (
                              <div className="flex items-center mb-4">
                                <h2 className="text-lg font-semibold text-secondary-900 dark:text-secondary-100 flex items-center">
                                  <CalendarIcon className="w-5 h-5 mr-2 text-green-600" />
                                  Scheduled Jobs ({scheduledJobs.length})
                                </h2>
                              </div>
                            )}
                            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr">
                              {scheduledJobs.map((job) => (
                                <JobCard
                                  key={job.id}
                                  job={job}
                                  onJobClick={handleJobClick}
                                  onClientClick={handleClientClick}
                                  onNotesClick={handleNotesClick}
                                  onCallClick={handleCallClick}
                                  onMessageClick={handleMessageClick}
                                  onEmailClick={handleEmailClick}
                                  onStatusChange={handleStatusChange}
                                  onDateChange={handleDateChange}
                                  onScheduleChange={handleScheduleChange}
                                  showStatusDropdown={openDropdowns[job.id]?.status || false}
                                  showDatePicker={openDropdowns[job.id]?.date || false}
                                  showTimePicker={openDropdowns[job.id]?.time || false}
                                  onStatusDropdownToggle={() => handleStatusDropdownToggle(job.id)}
                                  onDateDropdownToggle={() => handleDateDropdownToggle(job.id)}
                                  onTimePickerToggle={() => handleTimePickerToggle(job.id)}
                                  onSchedulingClick={handleSchedulingClick}
                                  onEditClick={() => handleEditClick(job)}
                                  onDeleteClick={() => handleDeleteClick(job)}
                                />
                              ))}
                            </div>
                          </div>
                        )}

                        {/* If all jobs are scheduled, show them normally */}
                        {unscheduledJobs.length === 0 && scheduledJobs.length === 0 && (
                          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr">
                            {jobs.map((job) => (
                              <JobCard
                                key={job.id}
                                job={job}
                                onJobClick={handleJobClick}
                                onClientClick={handleClientClick}
                                onNotesClick={handleNotesClick}
                                onCallClick={handleCallClick}
                                onMessageClick={handleMessageClick}
                                onEmailClick={handleEmailClick}
                                onStatusChange={handleStatusChange}
                                onDateChange={handleDateChange}
                                onScheduleChange={handleScheduleChange}
                                showStatusDropdown={openDropdowns[job.id]?.status || false}
                                showDatePicker={openDropdowns[job.id]?.date || false}
                                showTimePicker={openDropdowns[job.id]?.time || false}
                                onStatusDropdownToggle={() => handleStatusDropdownToggle(job.id)}
                                onDateDropdownToggle={() => handleDateDropdownToggle(job.id)}
                                onTimePickerToggle={() => handleTimePickerToggle(job.id)}
                                onSchedulingClick={handleSchedulingClick}
                                onEditClick={() => handleEditClick(job)}
                                onDeleteClick={() => handleDeleteClick(job)}
                              />
                            ))}
                          </div>
                        )}
                      </>
                    );
                  })()}
                </div>
              ) : (
                <div className="card elevation-1 p-0 overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
                      <thead className="bg-neutral-50 dark:bg-neutral-800">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Job
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Client
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Schedule
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Created
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-secondary-800 divide-y divide-secondary-200 dark:divide-secondary-700">
                        {jobs.map((job) => {
                          const isUnscheduled = !job.scheduled_at;
                          return (
                            <tr
                              key={job.id}
                              className={`hover:bg-neutral-50 dark:hover:bg-neutral-800/50 transition-colors cursor-pointer ${
                                isUnscheduled ? 'bg-amber-50/50 dark:bg-amber-900/10 border-l-4 border-amber-500' : ''
                              }`}
                              onClick={() => handleJobClick(job.id)}
                            >
                              <td className="px-6 py-4">
                                <div className="flex items-center space-x-3">
                                  <div className={`w-10 h-10 bg-gradient-to-br rounded-full flex items-center justify-center text-white font-semibold text-sm ${
                                    isUnscheduled ? 'from-amber-500 to-amber-600' : 'from-blue-500 to-blue-600'
                                  }`}>
                                    {job.title.charAt(0).toUpperCase()}
                                  </div>
                                  <div>
                                    <div className="flex items-center space-x-2">
                                      <div className="text-sm font-medium text-neutral-900 dark:text-neutral-50">
                                        {job.title}
                                      </div>
                                      {isUnscheduled && (
                                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200">
                                          <CalendarIcon className="w-3 h-3 mr-1" />
                                          Needs Scheduling
                                        </span>
                                      )}
                                    </div>
                                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                                      #{job.id.slice(-6).toUpperCase()}
                                    </div>
                                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1 ${
                                      job.status === 'new' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                                      job.status === 'quoted' ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400' :
                                      job.status === 'in_progress' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                                      job.status === 'on_hold' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' :
                                      job.status === 'completed' ? 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400' :
                                      'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                                    }`}>
                                      {job.status === 'new' ? 'New' :
                                       job.status === 'quoted' ? 'Quoted' :
                                       job.status === 'in_progress' ? 'In Progress' :
                                       job.status === 'on_hold' ? 'On Hold' :
                                       job.status === 'completed' ? 'Completed' :
                                       job.status}
                                    </span>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                <div className="text-sm text-neutral-900 dark:text-neutral-50">
                                  {job.client.name}
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                <div className="flex items-center space-x-2">
                                  <div className={`text-sm ${
                                    isUnscheduled
                                      ? 'text-amber-700 dark:text-amber-300 font-medium'
                                      : 'text-neutral-900 dark:text-neutral-50'
                                  }`}>
                                    {job.scheduled_at ? new Date(job.scheduled_at).toLocaleDateString('en-GB', {
                                      day: '2-digit',
                                      month: 'short',
                                      year: 'numeric'
                                    }) : 'Not scheduled'}
                                  </div>
                                  {isUnscheduled && (
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleSchedulingClick(job.id);
                                      }}
                                      className="inline-flex items-center px-2 py-1 bg-amber-600 text-white rounded text-xs font-medium hover:bg-amber-700 transition-colors"
                                    >
                                      <CalendarIcon className="w-3 h-3 mr-1" />
                                      Schedule
                                    </button>
                                  )}
                                </div>
                                {job.assigned_to && (
                                  <div className="text-sm text-neutral-500 dark:text-neutral-400">
                                    Assigned to {job.assigned_to.full_name}
                                  </div>
                                )}
                              </td>
                              <td className="px-6 py-4">
                                <div className="text-sm text-neutral-900 dark:text-neutral-50">
                                  {new Date(job.created_at).toLocaleDateString('en-GB', {
                                    day: '2-digit',
                                    month: 'short',
                                    year: 'numeric'
                                  })}
                                </div>
                              </td>
                              <td className="px-6 py-4 text-right">
                                <div className="flex items-center justify-end space-x-2">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleNotesClick(job.id);
                                    }}
                                    className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 p-1 rounded transition-colors"
                                    title="View Job Log"
                                  >
                                    <DocumentTextIcon className="w-4 h-4" />
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (job.client.phone) handleCallClick(job.client.phone);
                                    }}
                                    disabled={!job.client.phone}
                                    className={`p-1 rounded transition-colors ${
                                      job.client.phone
                                        ? 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300'
                                        : 'text-neutral-300 dark:text-neutral-600 cursor-not-allowed'
                                    }`}
                                    title={job.client.phone ? `Call ${job.client.phone}` : 'No phone number'}
                                  >
                                    <PhoneIcon className="w-4 h-4" />
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (job.client.email) handleEmailClick(job.client.email, job);
                                    }}
                                    disabled={!job.client.email}
                                    className={`p-1 rounded transition-colors ${
                                      job.client.email
                                        ? 'text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300'
                                        : 'text-neutral-300 dark:text-neutral-600 cursor-not-allowed'
                                    }`}
                                    title={job.client.email ? `Email ${job.client.email}` : 'No email address'}
                                  >
                                    <EnvelopeIcon className="w-4 h-4" />
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleSchedulingClick(job.id);
                                    }}
                                    className={`p-1 rounded transition-colors ${
                                      isUnscheduled
                                        ? 'text-amber-600 hover:text-amber-900 dark:text-amber-400 dark:hover:text-amber-300 bg-amber-100 dark:bg-amber-900/30'
                                        : 'text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300'
                                    }`}
                                    title={isUnscheduled ? 'Schedule Job (Priority)' : 'Reschedule Job'}
                                  >
                                    <CalendarIcon className="w-4 h-4" />
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditClick(job);
                                    }}
                                    className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-neutral-300 p-1 rounded transition-colors"
                                    title="Edit Job"
                                  >
                                    <PencilIcon className="w-4 h-4" />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Load More Button */}
              {hasMore && (
                <div className="text-center pt-6">
                  <button
                    onClick={loadMore}
                    disabled={isLoading}
                    className="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                        Loading...
                      </>
                    ) : (
                      'Load More Jobs'
                    )}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
      
      {/* Job Drawer */}
      <JobDrawer
        jobId={selectedJobId}
        onClose={() => setSelectedJobId(null)}
        onClientClick={handleClientClick}
        onJobUpdated={refetch}
      />
      
      {/* Client Drawer */}
      <ClientDrawer
        clientId={selectedClientId}
        onClose={() => {
          setSelectedClientId(null);
          setSelectedClientData(null);
        }}
        onCreateJobClick={handleClientCreateJobClick}
        onNotesClick={handleClientNotesClick}
        onRatingChange={handleClientRatingChange}
        clientData={selectedClientData}
      />

      {/* Client Notes Drawer */}
      <ClientNotesDrawer
        clientId={selectedClientNotesId}
        onClose={() => setSelectedClientNotesId(null)}
      />

      {/* Job Log Drawer */}
      <JobLogDrawer
        jobId={selectedJobLogId}
        jobTitle={selectedJobLogId ? jobs.find(job => job.id === selectedJobLogId)?.title : undefined}
        onClose={() => setSelectedJobLogId(null)}
      />

      {/* Legacy Job Scheduling Modal - removed in favor of global drawer */}

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteJobModal
        job={deleteJobId ? jobs.find(job => job.id === deleteJobId) || null : null}
        isOpen={!!deleteJobId}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        isLoading={isDeleting}
      />

      {/* Edit Job Drawer */}
      <EditJobDrawer
        job={editJobId ? jobs.find(job => job.id === editJobId) || null : null}
        isOpen={!!editJobId}
        onClose={handleEditClose}
        onJobUpdated={handleEditJobUpdated}
      />
    </>
  );
} 