'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRequireAuth } from '@/hooks/useRequireAuth';
import { useInvoices, Invoice } from '@/hooks/useInvoices';
import { useDrawer } from '@/contexts/DrawerContext';
import { useToast } from '@/contexts/ToastContext';
import { 
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChevronDownIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { 
  CurrencyPoundIcon,
  ClockIcon,
  ExclamationCircleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/solid';
import { Button } from '@deskbelt/ui';
import { InvoiceCard } from '@/components/InvoiceCard';
import React from 'react';


// Stats Card Component
const StatsCard = ({ title, value, description, icon, color }: {
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactNode;
  color: string;
}) => (
  <div className="bg-white dark:bg-neutral-800 rounded-xl border border-neutral-200 dark:border-neutral-700 p-6 elevation-1 hover:elevation-2 shadow-transition">
    <div className="flex items-center">
      <div className={`flex-shrink-0 p-3 rounded-lg ${color}`}>
        {icon}
      </div>
      <div className="ml-4 flex-1">
        <p className="text-label text-muted">
          {title}
        </p>
        <p className="text-title">
          {typeof value === 'number' ? `£${value.toLocaleString('en-GB', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : value}
        </p>
        <p className="text-small text-muted">
          {description}
        </p>
      </div>
    </div>
  </div>
);

// Enhanced Invoice Card is now imported from components

// Invoice Table Row Component (List View)
const InvoiceTableRow = ({ invoice, onView, onEdit, onDelete }: {
  invoice: Invoice;
  onView: (invoice: Invoice) => void;
  onEdit: (invoice: Invoice) => void;
  onDelete: (invoice: Invoice) => void;
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'status-badge-paid';
      case 'sent': return 'status-badge-pending';
      case 'overdue': return 'status-badge-overdue';
      case 'draft': return 'status-badge-draft';
      default: return 'status-badge-neutral';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  return (
    <tr className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50 transition-colors">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm font-medium text-neutral-900 dark:text-neutral-50">
          INV-{invoice.id.slice(-3).toUpperCase()}
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-neutral-900 dark:text-neutral-50">
          {invoice.client?.name || 'Unknown Client'}
        </div>
        <div className="text-sm text-neutral-500 dark:text-neutral-400">
          {invoice.client?.email}
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-neutral-900 dark:text-neutral-50">
          {invoice.job?.title || 'No job title'}
        </div>
        <div className="text-sm text-neutral-500 dark:text-neutral-400">
          JOB-{invoice.job_id.slice(-3).toUpperCase()}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm font-medium text-neutral-900 dark:text-neutral-50">
          £{invoice.amount.toLocaleString('en-GB', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={getStatusColor(invoice.status)}>
          {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500 dark:text-neutral-400">
        {formatDate(invoice.created_at)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500 dark:text-neutral-400">
        {formatDate(invoice.due_date)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onView(invoice)}
            className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
            title="View Invoice"
          >
            <EyeIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => onEdit(invoice)}
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-neutral-300"
            title="Edit Invoice"
          >
            <PencilIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => onDelete(invoice)}
            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
            title="Delete Invoice"
          >
            <TrashIcon className="w-4 h-4" />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default function InvoicingPage() {
  const { user, loading: authLoading, isAuthenticated } = useRequireAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatusFilters, setSelectedStatusFilters] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const { showSuccess, showError } = useToast();

  const { 
    invoices, 
    stats, 
    isLoading, 
    error, 
    refetch, 
    hasMore, 
    loadMore 
  } = useInvoices({
    search: searchTerm,
    status: selectedStatusFilters
  });

  // Move all hooks before any early returns to comply with Rules of Hooks
  const { openCreateInvoiceDrawer, openInvoicePreviewModal, setOnJobCreated } = useDrawer();

  // Set up refresh callback for when invoices are deleted
  useEffect(() => {
    setOnJobCreated(() => {
      refetch();
    });
  }, [refetch, setOnJobCreated]);

  // Memoized filtered invoices to prevent infinite re-renders - MOVED BEFORE EARLY RETURNS
  const filteredInvoices = useMemo(() => {
    if (!invoices || invoices.length === 0) return [];
    
    return invoices.filter(invoice => {
      const matchesSearch = !searchTerm || 
        invoice.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (invoice.client?.name && invoice.client.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (invoice.job?.title && invoice.job.title.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesStatus = selectedStatusFilters.length === 0 || 
        selectedStatusFilters.includes(invoice.status);
      
      return matchesSearch && matchesStatus;
    });
  }, [invoices, searchTerm, selectedStatusFilters]);

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  // Don't render the page content if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  const statusOptions = [
    { value: 'draft', label: 'Draft', color: 'bg-gray-500' },
    { value: 'sent', label: 'Sent', color: 'bg-blue-500' },
    { value: 'paid', label: 'Paid', color: 'bg-green-500' },
    { value: 'overdue', label: 'Overdue', color: 'bg-red-500' },
  ];

  const handleStatusFilter = (status: string) => {
    setSelectedStatusFilters(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const handleCreateInvoice = () => {
    openCreateInvoiceDrawer();
  };

  const handleViewInvoice = (invoice: Invoice) => {
    openInvoicePreviewModal(invoice);
  };

  const handleEditInvoice = (invoice: Invoice) => {
    openCreateInvoiceDrawer(invoice);
  };

  const handleDeleteInvoice = (invoice: Invoice) => {
    // TODO: Open delete confirmation modal
    console.log('Delete invoice:', invoice.id);
  };

  const handleSendEmail = (invoice: Invoice) => {
    // TODO: Integrate with email system
    console.log('Send email for invoice:', invoice.id);
    showSuccess('Email functionality coming soon!');
  };

  const handlePrint = (invoice: Invoice) => {
    // TODO: Implement print functionality
    console.log('Print invoice:', invoice.id);
    showSuccess('Print functionality coming soon!');
  };

  const handleDuplicate = (invoice: Invoice) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate invoice:', invoice.id);
    showSuccess('Duplicate functionality coming soon!');
  };

  const handleMarkPaid = (invoice: Invoice) => {
    // TODO: Implement mark as paid functionality
    console.log('Mark as paid:', invoice.id);
    showSuccess('Mark as paid functionality coming soon!');
  };

  const InvoiceSkeleton = () => (
    <div className="card animate-pulse">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="h-6 bg-neutral-200 dark:bg-neutral-700 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/2"></div>
        </div>
        <div className="w-20 h-6 bg-neutral-200 dark:bg-neutral-700 rounded"></div>
      </div>
      <div className="flex items-center justify-between mb-4">
        <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/4"></div>
        <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/4"></div>
      </div>
      <div className="flex items-center justify-between pt-4 border-t border-neutral-200 dark:border-neutral-700">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-neutral-200 dark:bg-neutral-700 rounded-xl"></div>
          <div className="w-8 h-8 bg-neutral-200 dark:bg-neutral-700 rounded-xl"></div>
          <div className="w-8 h-8 bg-neutral-200 dark:bg-neutral-700 rounded-xl"></div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-headline">
            Invoices
          </h1>
          <p className="text-body text-muted">
            Create, manage, and track your invoices
          </p>
        </div>
        <Button 
          variant="primary"
          context="invoicing" 
          onClick={handleCreateInvoice}
          icon={<PlusIcon className="w-5 h-5" />}
        >
          Create Invoice
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Revenue"
          value={stats.totalRevenue}
          description="From paid invoices"
          icon={<CurrencyPoundIcon className="w-6 h-6 text-white" />}
          color="bg-green-500"
        />
        <StatsCard
          title="Pending Payment"
          value={stats.pendingAmount}
          description="Awaiting payment"
          icon={<ClockIcon className="w-6 h-6 text-white" />}
          color="bg-blue-500"
        />
        <StatsCard
          title="Overdue Amount"
          value={stats.overdueAmount}
          description="Past due date"
          icon={<ExclamationCircleIcon className="w-6 h-6 text-white" />}
          color="bg-red-500"
        />
        <StatsCard
          title="Draft Invoices"
          value={stats.draftCount}
          description="Unsent invoices"
          icon={<DocumentTextIcon className="w-6 h-6 text-white" />}
          color="bg-gray-500"
        />
      </div>

      {/* Search and Filter Bar */}
      <div className="card p-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          {/* Search Bar */}
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search invoices..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          {/* Status Filter Dropdown */}
          <div className="relative">
            <select
              value=""
              onChange={(e) => {
                if (e.target.value) {
                  handleStatusFilter(e.target.value);
                }
              }}
              className="appearance-none bg-white dark:bg-neutral-700 border border-neutral-300 dark:border-neutral-600 rounded-xl px-4 py-3 pr-8 text-neutral-900 dark:text-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-w-[140px] transition-all duration-200"
            >
              <option value="">All Statuses</option>
              {statusOptions.map((status) => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            </div>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
              title="Grid View"
            >
              <Squares2X2Icon className="w-5 h-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
              title="List View"
            >
              <ListBulletIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Active Filters */}
        {selectedStatusFilters.length > 0 && (
          <div className="mt-4 flex items-center space-x-2">
            <span className="text-sm text-neutral-600 dark:text-neutral-400">Active filters:</span>
            {selectedStatusFilters.map((status) => {
              const statusOption = statusOptions.find(s => s.value === status);
              return (
                <span
                  key={status}
                  className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400"
                >
                  {statusOption?.label}
                  <button
                    onClick={() => handleStatusFilter(status)}
                    className="ml-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300"
                  >
                    ×
                  </button>
                </span>
              );
            })}
            <button
              onClick={() => setSelectedStatusFilters([])}
              className="text-sm text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300"
            >
              Clear all
            </button>
          </div>
        )}
      </div>

      {/* Invoices Content */}
      <div className="space-y-4">
        {/* Error State */}
        {error && filteredInvoices.length > 0 && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 text-center">
            <div className="text-red-600 dark:text-red-400 mb-2">
              <ExclamationTriangleIcon className="w-8 h-8 mx-auto mb-2" />
              <p className="font-medium">Failed to load invoices</p>
            </div>
            <p className="text-sm text-red-500 dark:text-red-400 mb-4">{error}</p>
            <button
              onClick={refetch}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Loading State */}
        {isLoading && filteredInvoices.length === 0 && (
          <>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {Array.from({ length: 6 }, (_, i) => (
                  <InvoiceSkeleton key={i} />
                ))}
              </div>
            ) : (
              <div className="card p-0 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
                    <thead className="bg-neutral-50 dark:bg-neutral-800">
                      <tr>
                        {['Invoice #', 'Client', 'Job', 'Amount', 'Status', 'Issue Date', 'Due Date', 'Actions'].map((header) => (
                          <th key={header} className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {Array.from({ length: 6 }, (_, i) => (
                        <tr key={i}>
                          {Array.from({ length: 8 }, (_, j) => (
                            <td key={j} className="px-6 py-4 whitespace-nowrap">
                              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        )}

        {/* Empty State */}
        {!isLoading && filteredInvoices.length === 0 && !error && (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">💳</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {searchTerm || selectedStatusFilters.length > 0 ? 'No invoices found' : 'No invoices yet'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              {searchTerm || selectedStatusFilters.length > 0
                ? 'Try adjusting your search or filters'
                : 'Create your first invoice to get started'
              }
            </p>
            {!(searchTerm || selectedStatusFilters.length > 0) && (
              <Button
                variant="primary"
                context="invoicing"
                onClick={handleCreateInvoice}
                icon={<PlusIcon className="w-5 h-5" />}
              >
                Create Your First Invoice
              </Button>
            )}
          </div>
        )}

        {/* Invoices Grid/List */}
        {filteredInvoices.length > 0 && (
          <>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr">
                {filteredInvoices.map((invoice) => (
                  <InvoiceCard
                    key={invoice.id}
                    invoice={invoice}
                    onView={handleViewInvoice}
                    onEdit={handleEditInvoice}
                    onDelete={handleDeleteInvoice}
                    onSendEmail={handleSendEmail}
                    onPrint={handlePrint}
                    onDuplicate={handleDuplicate}
                    onMarkPaid={handleMarkPaid}
                  />
                ))}
              </div>
            ) : (
              <div className="card p-0 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
                    <thead className="bg-neutral-50 dark:bg-neutral-800">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                          Invoice #
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                          Client
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                          Job
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                          Issue Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                          Due Date
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
                      {filteredInvoices.map((invoice) => (
                        <InvoiceTableRow
                          key={invoice.id}
                          invoice={invoice}
                          onView={handleViewInvoice}
                          onEdit={handleEditInvoice}
                          onDelete={handleDeleteInvoice}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Load More Button */}
            {hasMore && (
              <div className="text-center pt-6">
                <button
                  onClick={loadMore}
                  disabled={isLoading}
                  className="inline-flex items-center px-6 py-3 border border-neutral-300 dark:border-neutral-600 text-base font-medium rounded-md text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-neutral-600 mr-2"></div>
                      Loading...
                    </>
                  ) : (
                    'Load More Invoices'
                  )}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}