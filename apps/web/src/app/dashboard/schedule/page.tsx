'use client';

import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import { 
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { useJobs } from '@/hooks/useJobs';
import { useDrawer } from '@/contexts/DrawerContext';
import { useRequireAuth } from '@/hooks/useRequireAuth';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/contexts/ToastContext';
import { Job, formatJobSchedule } from '@/types/Job';

// Utility function to format date without timezone conversion
const formatDateString = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Calendar utilities
const DAYS_OF_WEEK = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

// Utility functions moved outside component to prevent recreation on every render
const getDaysInMonth = (date: Date) => {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
};

const getFirstDayOfMonth = (date: Date) => {
  return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
};

// Type for calendar events (only job-related events)
interface CalendarEvent {
  id: string;
  title: string;
  type: 'job';
  client: string;
  date: string;
  time: string;
  location: string;
  status: string;
  color: string;
  originalJob?: Job; // Reference to the original job object
}

// Reusable Button component
const Button = ({ 
  theme = 'default', 
  variant = 'primary', 
  size = 'md', 
  children, 
  className = '', 
  ...props 
}: {
  theme?: 'default' | 'jobs' | 'clients' | 'ai' | 'schedule';
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
  [key: string]: any;
}) => {
  const getThemeColors = () => {
    const themes = {
      default: 'bg-blue-500/90 hover:bg-blue-600 border-blue-500/90 text-white shadow-sm hover:shadow-md',
      jobs: 'bg-blue-500/90 hover:bg-blue-600 border-blue-500/90 text-white shadow-sm hover:shadow-md',
      clients: 'bg-green-500/90 hover:bg-green-600 border-green-500/90 text-white shadow-sm hover:shadow-md',
      ai: 'bg-orange-500/90 hover:bg-orange-600 border-orange-500/90 text-white shadow-sm hover:shadow-md',
      schedule: 'bg-indigo-500/90 hover:bg-indigo-600 border-indigo-500/90 text-white shadow-sm hover:shadow-md'
    };
    return themes[theme];
  };

  const getVariantClasses = () => {
    if (variant === 'outline') {
      const themes = {
        default: 'border-gray-200 text-gray-600 hover:bg-gray-50 shadow-sm hover:shadow-md',
        jobs: 'border-blue-200 text-blue-600 hover:bg-blue-50 shadow-sm hover:shadow-md',
        clients: 'border-green-200 text-green-600 hover:bg-green-50 shadow-sm hover:shadow-md',
        ai: 'border-orange-200 text-orange-600 hover:bg-orange-50 shadow-sm hover:shadow-md',
        schedule: 'border-indigo-200 text-indigo-600 hover:bg-indigo-50 shadow-sm hover:shadow-md'
      };
      return `border ${themes[theme]} bg-white/80 dark:bg-gray-700/80`;
    }
    return getThemeColors();
  };

  const getSizeClasses = () => {
    const sizes = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base'
    };
    return sizes[size];
  };

  return (
    <button
      className={`
        inline-flex items-center justify-center font-medium rounded-lg
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-400
        transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed
        ${getVariantClasses()} ${getSizeClasses()} ${className}
      `}
      {...props}
    >
      {children}
    </button>
  );
};

// Calendar Event Component - Memoized for performance
const CalendarEvent = React.memo(({
  event,
  onEventClick,
  onDragStart,
  onDragEnd,
  isDragging = false
}: {
  event: CalendarEvent;
  onEventClick?: (event: CalendarEvent) => void;
  onDragStart?: (e: React.DragEvent, event: CalendarEvent) => void;
  onDragEnd?: (e: React.DragEvent) => void;
  isDragging?: boolean;
}) => {
  const getEventColors = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800',
      indigo: 'bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800',
      green: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800',
      orange: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',
      teal: 'bg-teal-100 text-teal-800 border-teal-200 dark:bg-teal-900/20 dark:text-teal-400 dark:border-teal-800',
      purple: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800',
      red: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
      gray: 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div
      draggable={true}
      className={`px-2 py-1 mb-1 rounded border text-xs cursor-move hover:shadow-sm transition-all select-none ${
        getEventColors(event.color)
      } ${isDragging ? 'opacity-50 scale-95' : 'hover:scale-105'}`}
      onClick={() => onEventClick?.(event)}
      onDragStart={(e) => onDragStart?.(e, event)}
      onDragEnd={onDragEnd}
      title={`${event.title}${event.time !== 'All day' ? ` (${event.time})` : ''} - Click to view details, drag to reschedule`}
    >
      <div className="font-medium truncate">{event.title}</div>
      <div className="text-xs opacity-75">{event.time}</div>
    </div>
  );
});

// Mini Calendar Component - Memoized for performance
const MiniCalendar = React.memo(({ currentDate, onDateChange }: {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}) => {
  const [miniDate, setMiniDate] = useState(new Date());

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const handlePrevMonth = () => {
    setMiniDate(new Date(miniDate.getFullYear(), miniDate.getMonth() - 1, 1));
  };

  const handleNextMonth = () => {
    setMiniDate(new Date(miniDate.getFullYear(), miniDate.getMonth() + 1, 1));
  };

  const handleDateClick = (day: number) => {
    const newDate = new Date(miniDate.getFullYear(), miniDate.getMonth(), day);
    onDateChange(newDate);
  };

  const daysInMonth = getDaysInMonth(miniDate);
  const firstDay = getFirstDayOfMonth(miniDate);
  const days = [];

  // Add empty cells for days before the first day of the month
  for (let i = 0; i < firstDay; i++) {
    days.push(<div key={`empty-${i}`} className="text-center py-1"></div>);
  }

  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    const isToday = day === new Date().getDate() && 
                   miniDate.getMonth() === new Date().getMonth() && 
                   miniDate.getFullYear() === new Date().getFullYear();
    const isSelected = day === currentDate.getDate() && 
                      miniDate.getMonth() === currentDate.getMonth() && 
                      miniDate.getFullYear() === currentDate.getFullYear();

    days.push(
      <button
        key={day}
        onClick={() => handleDateClick(day)}
        className={`text-center py-1 text-sm w-8 h-8 rounded transition-colors ${
          isToday ? 'bg-indigo-500 text-white font-semibold' :
          isSelected ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400' :
          'text-secondary-600 dark:text-secondary-400 hover:bg-secondary-100 dark:hover:bg-secondary-800'
        }`}
      >
        {day}
      </button>
    );
  }

  return (
    <div className="card p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-secondary-900 dark:text-secondary-50">
          Mini Calendar
        </h3>
      </div>
      
      <div className="mb-3">
        <div className="flex items-center justify-between mb-2">
          <button
            onClick={handlePrevMonth}
            className="p-1 text-secondary-500 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-200"
          >
            <ChevronLeftIcon className="w-4 h-4" />
          </button>
          <span className="text-sm font-medium text-secondary-900 dark:text-secondary-50">
            {MONTHS[miniDate.getMonth()]} {miniDate.getFullYear()}
          </span>
          <button
            onClick={handleNextMonth}
            className="p-1 text-secondary-500 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-200"
          >
            <ChevronRightIcon className="w-4 h-4" />
          </button>
        </div>

        <div className="grid grid-cols-7 gap-1 mb-2">
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
            <div key={`${day}-${index}`} className="text-center text-xs font-medium text-secondary-500 dark:text-secondary-400 py-1">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {days}
        </div>
      </div>

      <Button 
        theme="schedule" 
        size="sm" 
        className="w-full"
        onClick={() => onDateChange(new Date())}
      >
        Today
      </Button>
    </div>
  );
});

// Upcoming Events Component - Memoized for performance
const UpcomingEvents = React.memo(({ events }: { events: CalendarEvent[] }) => {
  const upcomingEvents = events
    .filter(event => new Date(event.date) >= new Date())
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 5);

  const formatEventDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-GB', { 
        weekday: 'long',
        day: 'numeric',
        month: 'short'
      });
    }
  };

  const getEventIcon = (type: string) => {
    // All events are jobs, so always return the job icon
    return '🔧';
  };

  return (
    <div className="card p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-secondary-900 dark:text-secondary-50">
          Upcoming Events
        </h3>
        <button className="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300">
          View All
        </button>
      </div>

      <div className="space-y-3">
        {upcomingEvents.map((event) => (
          <div key={event.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-secondary-50 dark:hover:bg-secondary-800/50 transition-colors cursor-pointer">
            <div className="text-lg">{getEventIcon(event.type)}</div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h4 className="text-sm font-medium text-secondary-900 dark:text-secondary-50 truncate">
                  {event.title}
                </h4>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  event.status === 'new' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                  event.status === 'quoted' ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400' :
                  event.status === 'in_progress' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                  event.status === 'on_hold' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' :
                  event.status === 'completed' ? 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                }`}>
                  {event.status.replace('_', ' ')}
                </span>
              </div>
              <p className="text-xs text-secondary-600 dark:text-secondary-400">
                {formatEventDate(event.date)} • {event.time}
              </p>
              <p className="text-xs text-secondary-500 dark:text-secondary-500 truncate">
                {event.location}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
});

// Week View Component - Memoized for performance
const WeekView = React.memo(({
  currentDate,
  events,
  onEventClick,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDragLeave,
  onDrop,
  draggedEvent,
  dragOverDate,
  dragOverTime
}: {
  currentDate: Date;
  events: CalendarEvent[];
  onEventClick?: (event: CalendarEvent) => void;
  onDragStart?: (e: React.DragEvent, event: CalendarEvent) => void;
  onDragEnd?: (e: React.DragEvent) => void;
  onDragOver?: (e: React.DragEvent, date: string, time?: string) => void;
  onDragLeave?: (e: React.DragEvent) => void;
  onDrop?: (e: React.DragEvent, targetDate: string, targetTime?: string) => void;
  draggedEvent?: CalendarEvent | null;
  dragOverDate?: string | null;
  dragOverTime?: string | null;
}) => {
  // Get the start of the week (Sunday)
  const getWeekStart = (date: Date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day;
    return new Date(d.setDate(diff));
  };

  // Generate week days
  const weekStart = getWeekStart(currentDate);
  const weekDays = Array.from({ length: 7 }, (_, i) => {
    const day = new Date(weekStart);
    day.setDate(weekStart.getDate() + i);
    return day;
  });

  // Time slots for the week view (8 AM to 8 PM)
  const timeSlots = Array.from({ length: 13 }, (_, i) => {
    const hour = i + 8; // Start from 8 AM
    return {
      time: hour < 12 ? `${hour}:00 AM` : hour === 12 ? '12:00 PM' : `${hour - 12}:00 PM`,
      hour: hour
    };
  });

  // Get events for a specific date and time slot
  const getEventsForSlot = (date: Date, hour: number) => {
    const dateString = formatDateString(date);
    return events.filter(event => {
      if (event.date !== dateString) return false;

      // Parse time if available
      if (event.time && event.time !== 'All day') {
        // Handle both single time and time range formats
        const timeStr = event.time.includes(' - ') ? event.time.split(' - ')[0] : event.time;

        // Parse time - handle both 12-hour (AM/PM) and 24-hour formats
        const parseTimeToHour = (timeString: string): number | null => {
          // Try AM/PM format first
          let timeMatch = timeString.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);
          if (timeMatch) {
            let hour = parseInt(timeMatch[1]);
            const isPM = timeMatch[3].toUpperCase() === 'PM';
            if (isPM && hour !== 12) hour += 12;
            if (!isPM && hour === 12) hour = 0;
            return hour;
          }

          // Try 24-hour format
          timeMatch = timeString.match(/(\d{1,2}):(\d{2})/);
          if (timeMatch) {
            return parseInt(timeMatch[1]);
          }

          return null;
        };

        const eventHour = parseTimeToHour(timeStr.trim());
        if (eventHour !== null) {
          return eventHour === hour;
        }
      }

      // For all-day events or events without specific time, show in first slot (8 AM)
      return hour === 8;
    });
  };

  return (
    <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
      {/* Week Header */}
      <div className="grid grid-cols-8 border-b border-gray-200 dark:border-gray-700">
        <div className="p-4 text-sm font-medium text-gray-500 dark:text-gray-400">
          Time
        </div>
        {weekDays.map((day, index) => {
          const isToday = day.toDateString() === new Date().toDateString();
          const isSelected = day.toDateString() === currentDate.toDateString();
          return (
            <div key={index} className={`p-4 text-center border-l border-gray-200 dark:border-gray-700 ${
              isSelected ? 'bg-indigo-100 dark:bg-indigo-900/20' : ''
            }`}>
              <div className={`text-sm font-medium ${
                isToday ? 'text-indigo-600 dark:text-indigo-400' : 'text-gray-900 dark:text-gray-100'
              }`}>
                {DAYS_OF_WEEK[index]}
              </div>
              <div className={`text-lg ${
                isToday
                  ? 'w-8 h-8 bg-indigo-500 text-white rounded-full flex items-center justify-center mx-auto mt-1'
                  : 'text-gray-600 dark:text-gray-400'
              }`}>
                {day.getDate()}
              </div>
            </div>
          );
        })}
      </div>

      {/* Time Grid */}
      <div className="max-h-[600px] overflow-y-auto">
        {timeSlots.map((slot, slotIndex) => (
          <div key={slotIndex} className="grid grid-cols-8 border-b border-gray-100 dark:border-gray-800 min-h-[80px]">
            {/* Time Label */}
            <div className="p-4 text-sm text-gray-500 dark:text-gray-400 border-r border-gray-200 dark:border-gray-700">
              {slot.time}
            </div>

            {/* Day Columns */}
            {weekDays.map((day, dayIndex) => {
              const slotEvents = getEventsForSlot(day, slot.hour);
              const dateString = formatDateString(day);
              const timeString = `${slot.hour}:00`;
              const isDragOver = dragOverDate === dateString && dragOverTime === timeString;
              const isSelected = day.toDateString() === currentDate.toDateString();

              return (
                <div
                  key={dayIndex}
                  className={`p-2 border-l border-gray-100 dark:border-gray-800 transition-colors ${
                    isDragOver ? 'bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-300 dark:ring-blue-600' :
                    isSelected ? 'bg-indigo-50 dark:bg-indigo-900/10' :
                    'hover:bg-gray-50 dark:hover:bg-gray-800/50'
                  }`}
                  onDragOver={(e) => onDragOver?.(e, dateString, timeString)}
                  onDragLeave={onDragLeave}
                  onDrop={(e) => onDrop?.(e, dateString, timeString)}
                >
                  {slotEvents.map((event) => (
                    <CalendarEvent
                      key={event.id}
                      event={event}
                      onEventClick={onEventClick}
                      onDragStart={onDragStart}
                      onDragEnd={onDragEnd}
                      isDragging={draggedEvent?.id === event.id}
                    />
                  ))}
                  {isDragOver && (
                    <div className="px-2 py-1 mb-1 rounded border-2 border-dashed border-blue-300 dark:border-blue-600 text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/10">
                      Drop here to reschedule
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );
});

// Day View Component - Memoized for performance
const DayView = React.memo(({
  currentDate,
  events,
  onEventClick,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDragLeave,
  onDrop,
  draggedEvent,
  dragOverDate,
  dragOverTime
}: {
  currentDate: Date;
  events: CalendarEvent[];
  onEventClick?: (event: CalendarEvent) => void;
  onDragStart?: (e: React.DragEvent, event: CalendarEvent) => void;
  onDragEnd?: (e: React.DragEvent) => void;
  onDragOver?: (e: React.DragEvent, date: string, time?: string) => void;
  onDragLeave?: (e: React.DragEvent) => void;
  onDrop?: (e: React.DragEvent, targetDate: string, targetTime?: string) => void;
  draggedEvent?: CalendarEvent | null;
  dragOverDate?: string | null;
  dragOverTime?: string | null;
}) => {
  // Time slots for the day view (6 AM to 10 PM with 30-minute intervals)
  const timeSlots = [];
  for (let hour = 6; hour <= 22; hour++) {
    timeSlots.push({
      time: hour < 12 ? `${hour}:00 AM` : hour === 12 ? '12:00 PM' : `${hour - 12}:00 PM`,
      hour: hour,
      minute: 0
    });
    if (hour < 22) { // Don't add 30-minute slot for the last hour
      timeSlots.push({
        time: hour < 12 ? `${hour}:30 AM` : hour === 12 ? '12:30 PM' : `${hour - 12}:30 PM`,
        hour: hour,
        minute: 30
      });
    }
  }

  // Get events for the current date
  const dateString = formatDateString(currentDate);
  const dayEvents = events.filter(event => event.date === dateString);

  // Get events for a specific time slot
  const getEventsForSlot = (hour: number, minute: number) => {
    return dayEvents.filter(event => {
      // Parse time if available
      if (event.time && event.time !== 'All day') {
        // Handle both single time and time range formats
        const timeStr = event.time.includes(' - ') ? event.time.split(' - ')[0] : event.time;

        // Parse time - handle both 12-hour (AM/PM) and 24-hour formats
        const parseTime = (timeString: string): { hour: number; minute: number } | null => {
          // Try AM/PM format first
          let timeMatch = timeString.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);
          if (timeMatch) {
            let hour = parseInt(timeMatch[1]);
            const minute = parseInt(timeMatch[2]);
            const isPM = timeMatch[3].toUpperCase() === 'PM';
            if (isPM && hour !== 12) hour += 12;
            if (!isPM && hour === 12) hour = 0;
            return { hour, minute };
          }

          // Try 24-hour format
          timeMatch = timeString.match(/(\d{1,2}):(\d{2})/);
          if (timeMatch) {
            return {
              hour: parseInt(timeMatch[1]),
              minute: parseInt(timeMatch[2])
            };
          }

          return null;
        };

        const eventTime = parseTime(timeStr.trim());
        if (eventTime) {
          // For 30-minute slots, match events that fall within the slot
          // e.g., 9:00 AM slot matches events from 9:00-9:29, 9:30 AM slot matches 9:30-9:59
          const slotStart = hour * 60 + minute; // Convert to minutes
          const slotEnd = slotStart + 30; // 30-minute slot
          const eventMinutes = eventTime.hour * 60 + eventTime.minute;

          const matches = eventMinutes >= slotStart && eventMinutes < slotEnd;
          return matches;
        }
      }

      // For all-day events or events without specific time, show in first slot (6:00 AM)
      return hour === 6 && minute === 0;
    });
  };

  const isToday = currentDate.toDateString() === new Date().toDateString();

  return (
    <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
      {/* Day Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <div className={`text-sm font-medium ${
            isToday ? 'text-indigo-600 dark:text-indigo-400' : 'text-gray-600 dark:text-gray-400'
          }`}>
            {currentDate.toLocaleDateString('en-GB', { weekday: 'long' })}
          </div>
          <div className={`text-2xl font-bold ${
            isToday
              ? 'w-12 h-12 bg-indigo-500 text-white rounded-full flex items-center justify-center mx-auto mt-2'
              : 'text-gray-900 dark:text-gray-100 mt-1'
          }`}>
            {currentDate.getDate()}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {currentDate.toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}
          </div>
        </div>
      </div>

      {/* Time Grid */}
      <div className="max-h-[700px] overflow-y-auto">
        {timeSlots.map((slot, slotIndex) => {
          const slotEvents = getEventsForSlot(slot.hour, slot.minute);
          const isCurrentHour = isToday &&
            new Date().getHours() === slot.hour &&
            (new Date().getMinutes() >= slot.minute && new Date().getMinutes() < slot.minute + 30);

          const dateString = formatDateString(currentDate);
          const timeString = `${slot.hour}:${slot.minute.toString().padStart(2, '0')}`;
          const isDragOver = dragOverDate === dateString && dragOverTime === timeString;

          return (
            <div
              key={slotIndex}
              className={`flex border-b border-gray-100 dark:border-gray-800 min-h-[60px] ${
                isCurrentHour ? 'bg-indigo-50 dark:bg-indigo-900/10' : ''
              }`}
            >
              {/* Time Label */}
              <div className="w-24 p-3 text-sm text-gray-500 dark:text-gray-400 border-r border-gray-200 dark:border-gray-700 flex-shrink-0">
                {slot.time}
              </div>

              {/* Event Area */}
              <div
                className={`flex-1 p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors ${
                  isDragOver ? 'bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-300 dark:ring-blue-600' : ''
                }`}
                onDragOver={(e) => onDragOver?.(e, dateString, timeString)}
                onDragLeave={onDragLeave}
                onDrop={(e) => onDrop?.(e, dateString, timeString)}
              >
                {slotEvents.map((event) => (
                  <CalendarEvent
                    key={event.id}
                    event={event}
                    onEventClick={onEventClick}
                    onDragStart={onDragStart}
                    onDragEnd={onDragEnd}
                    isDragging={draggedEvent?.id === event.id}
                  />
                ))}
                {isDragOver && (
                  <div className="px-3 py-2 mb-2 rounded-lg border-2 border-dashed border-blue-300 dark:border-blue-600 text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/10">
                    Drop here to reschedule
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
});

export default function SchedulePage() {
  const { user, loading: authLoading, isAuthenticated } = useRequireAuth();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month');
  const [activeFilters, setActiveFilters] = useState<string[]>(['new', 'quoted', 'in_progress', 'on_hold', 'completed']);
  const [isInitialized, setIsInitialized] = useState(false);
  const [stableJobs, setStableJobs] = useState<Job[] | null>(null);

  // Drag and drop state
  const [draggedEvent, setDraggedEvent] = useState<CalendarEvent | null>(null);
  const [dragOverDate, setDragOverDate] = useState<string | null>(null);
  const [dragOverTime, setDragOverTime] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  const { openScheduleJobDrawer, openJobDetailsDrawer, setOnJobCreated } = useDrawer();
  const { data: jobs, isLoading, error, isRetrying, retryCount, refetch } = useJobs();
  const { authenticatedFetch } = useAuthenticatedFetch();
  const { showSuccess, showError } = useToast();

  // Simplified debug logging - reduced frequency to avoid console spam
  const renderCount = useRef(0);
  useEffect(() => {
    renderCount.current++;
    // Only log every 20th render or significant changes to avoid console spam
    if (renderCount.current % 20 === 0) {
      console.log('🔄 SchedulePage render', { 
        renderCount: renderCount.current,
        jobsLength: jobs?.length, 
        isLoading,
        stableJobsLength: stableJobs?.length
      });
    }
  });

  // Stabilize the loading state to prevent flickering
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      setIsInitialized(true);
    }
  }, [authLoading, isAuthenticated]);

  // Register refetch function with drawer context for real-time updates
  useEffect(() => {
    setOnJobCreated(refetch);
  }, [setOnJobCreated, refetch]);

  // Enhanced stable jobs logic with better comparison and debouncing
  const lastJobsRef = useRef<Job[] | null>(null);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    // Set stableJobs immediately when jobs data is available, regardless of loading state
    if (jobs && Array.isArray(jobs)) {
      // Clear any pending timeout
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      
      // Debounce updates to prevent rapid changes
      updateTimeoutRef.current = setTimeout(() => {
        setStableJobs(prevJobs => {
          // If no previous jobs, use current jobs
          if (!prevJobs) {
            lastJobsRef.current = jobs;
            return jobs;
          }
          
          // Create a more robust comparison using job IDs and key data
          const createJobSignature = (jobList: Job[]) => {
            return jobList
              .map(job => `${job.id}-${job.status}-${job.scheduled_at || 'none'}`)
              .sort()
              .join('|');
          };
          
          const prevSignature = createJobSignature(prevJobs);
          const currentSignature = createJobSignature(jobs);
          
          // Only update if data has actually changed
          if (prevSignature === currentSignature) {
            return prevJobs; // Return previous reference to prevent updates
          }
          
          // Log only significant changes to avoid console spam
          if (Math.abs(prevJobs.length - jobs.length) > 0) {
            console.log('📅 Jobs data changed', {
              prevCount: prevJobs.length,
              newCount: jobs.length
            });
          }
          
          lastJobsRef.current = jobs;
          return jobs;
        });
      }, 50); // Reduced debounce to 50ms for faster response
    }
    
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, [jobs]); // Removed isLoading dependency

  const handleEventClick = (event: CalendarEvent) => {
    // Don't open details if we're in the middle of a drag operation
    if (isDragging) return;

    // Open schedule job drawer for the clicked job to view and edit scheduling details
    if (event.originalJob) {
      openScheduleJobDrawer(event.originalJob.id);
    }
  };

  // Drag and drop handlers
  const handleDragStart = useCallback((e: React.DragEvent, event: CalendarEvent) => {
    e.stopPropagation();
    setDraggedEvent(event);
    setIsDragging(true);

    // Set drag data for accessibility
    e.dataTransfer.setData('text/plain', event.id);
    e.dataTransfer.effectAllowed = 'move';

    // Add visual feedback
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.opacity = '0.5';
    }
  }, []);

  const handleDragEnd = useCallback((e: React.DragEvent) => {
    e.stopPropagation();
    setDraggedEvent(null);
    setDragOverDate(null);
    setDragOverTime(null);
    setIsDragging(false);

    // Reset visual feedback
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.opacity = '1';
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent, date: string, time?: string) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = 'move';

    setDragOverDate(date);
    setDragOverTime(time || null);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Only clear drag over state if we're leaving the drop zone entirely
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragOverDate(null);
      setDragOverTime(null);
    }
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent, targetDate: string, targetTime?: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!draggedEvent || !draggedEvent.originalJob) {
      setDraggedEvent(null);
      setDragOverDate(null);
      setDragOverTime(null);
      setIsDragging(false);
      return;
    }

    const job = draggedEvent.originalJob;
    const newDate = targetDate;

    // Don't update if dropping on the same date and time
    const currentDate = job.scheduled_at ? job.scheduled_at.split('T')[0] : '';
    const currentTime = job.scheduled_start_time || '';
    const newTime = targetTime || '';

    if (currentDate === newDate && currentTime === newTime) {
      setDraggedEvent(null);
      setDragOverDate(null);
      setDragOverTime(null);
      setIsDragging(false);
      return;
    }

    try {
      // Update job schedule via API
      const response = await authenticatedFetch(`/api/jobs/${job.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scheduled_at: newDate,
          scheduled_start_time: targetTime || null,
          // Keep existing end time if we have one, otherwise clear it
          scheduled_end_time: targetTime && job.scheduled_end_time ? job.scheduled_end_time : null,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reschedule job');
      }

      showSuccess('Job Rescheduled', `${job.title} has been moved to ${new Date(newDate).toLocaleDateString('en-GB')}${targetTime ? ` at ${targetTime}` : ''}`);

      // Immediately refresh the data to show the change
      refetch();

    } catch (error) {
      console.error('Error rescheduling job:', error);
      showError('Reschedule Failed', 'Failed to reschedule job. Please try again.');
    } finally {
      setDraggedEvent(null);
      setDragOverDate(null);
      setDragOverTime(null);
      setIsDragging(false);
    }
  }, [draggedEvent, authenticatedFetch, showSuccess, showError, refetch]);

  // Combine all calendar data into a single useMemo to reduce circular dependencies
  const calendarData = useMemo(() => {
    // Step 1: Create scheduled events from real jobs only
    const scheduledEvents: CalendarEvent[] = (stableJobs || [])
      .filter(job => job.scheduled_at)
      .map(job => {
        // Enhanced time formatting with better display
        const formatTime = (timeStr: string): string => {
          if (!timeStr) return '';

          // If it's already in 12-hour format, return as is
          if (timeStr.includes('AM') || timeStr.includes('PM')) {
            return timeStr;
          }

          // Convert 24-hour format to 12-hour format
          const [hours, minutes] = timeStr.split(':').map(Number);
          const period = hours >= 12 ? 'PM' : 'AM';
          const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;

          // Format minutes - show :00 for exact hours, otherwise show minutes
          const formattedMinutes = minutes === 0 ? ':00' : `:${minutes.toString().padStart(2, '0')}`;

          return `${displayHours}${formattedMinutes} ${period}`;
        };

        // Calculate duration for display
        const calculateDuration = (startTime: string, endTime: string): string => {
          try {
            const [startHours, startMinutes] = startTime.split(':').map(Number);
            const [endHours, endMinutes] = endTime.split(':').map(Number);

            const startTotalMinutes = startHours * 60 + startMinutes;
            const endTotalMinutes = endHours * 60 + endMinutes;
            const durationMinutes = endTotalMinutes - startTotalMinutes;

            if (durationMinutes <= 0) return '';

            const hours = Math.floor(durationMinutes / 60);
            const minutes = durationMinutes % 60;

            if (hours === 0) {
              return `(${minutes}m)`;
            } else if (minutes === 0) {
              return `(${hours}h)`;
            } else {
              return `(${hours}h ${minutes}m)`;
            }
          } catch {
            return '';
          }
        };

        const time = (() => {
          if (job.scheduled_start_time && job.scheduled_end_time) {
            // Both start and end times available
            const startTime = formatTime(job.scheduled_start_time);
            const endTime = formatTime(job.scheduled_end_time);
            const duration = calculateDuration(job.scheduled_start_time, job.scheduled_end_time);
            return `${startTime} - ${endTime} ${duration}`.trim();
          } else if (job.scheduled_start_time) {
            // Only start time available
            const startTime = formatTime(job.scheduled_start_time);
            return `${startTime}`;
          } else {
            // No specific time
            return 'All day';
          }
        })();

        // Map job status to color
        const getJobStatusColor = (status: string) => {
          switch (status) {
            case 'new': return 'blue';
            case 'quoted': return 'indigo';
            case 'in_progress': return 'green';
            case 'on_hold': return 'orange';
            case 'completed': return 'teal';
            case 'archived': return 'gray';
            default: return 'blue';
          }
        };

        return {
          id: job.id,
          title: job.title,
          type: 'job' as const,
          client: job.client?.name || 'Unknown Client',
          date: job.scheduled_at!.split('T')[0],
          time,
          location: job.client?.name || 'No location',
          status: job.status,
          color: getJobStatusColor(job.status),
          originalJob: job // Keep reference to original job for click handling
        };
      });

    // Step 2: Create filter options based on SCHEDULED job statuses only
    const statusCounts: Record<string, number> = {};

    // Count only scheduled jobs by their status
    scheduledEvents.forEach(event => {
      statusCounts[event.status] = (statusCounts[event.status] || 0) + 1;
    });
    
    const filterOptions = [
      { id: 'new', label: 'New', color: 'blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400', count: statusCounts['new'] || 0 },
      { id: 'quoted', label: 'Quoted', color: 'indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400', count: statusCounts['quoted'] || 0 },
      { id: 'in_progress', label: 'In Progress', color: 'green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400', count: statusCounts['in_progress'] || 0 },
      { id: 'on_hold', label: 'On Hold', color: 'orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400', count: statusCounts['on_hold'] || 0 },
      { id: 'completed', label: 'Completed', color: 'teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400', count: statusCounts['completed'] || 0 },
      { id: 'archived', label: 'Archived', color: 'gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400', count: statusCounts['archived'] || 0 }
    ];

    // Step 3: Filter events based on active filters (job statuses)
    const filteredEvents = scheduledEvents.filter(event => {
      // All events are jobs, filter by job status
      return activeFilters.includes(event.status);
    });

    // Step 4: Generate calendar days
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDay = getFirstDayOfMonth(currentDate);
    const calendarDays = [];

    // Helper function to get events for a specific date
    const getEventsForDate = (date: Date) => {
      const dateString = formatDateString(date);
      return filteredEvents.filter(event => event.date === dateString);
    };

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      const prevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 0);
      const day = prevMonth.getDate() - firstDay + i + 1;
      calendarDays.push({
        day,
        date: new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, day),
        isCurrentMonth: false,
        events: []
      });
    }

    // Add days of the current month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
      calendarDays.push({
        day,
        date,
        isCurrentMonth: true,
        events: getEventsForDate(date)
      });
    }

    // Add days from next month to fill the grid
    const remainingCells = 42 - calendarDays.length; // 6 rows × 7 days
    for (let day = 1; day <= remainingCells; day++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, day);
      calendarDays.push({
        day,
        date,
        isCurrentMonth: false,
        events: []
      });
    }

    return {
      scheduledEvents,
      filterOptions,
      filteredEvents,
      calendarDays
    };
  }, [stableJobs, activeFilters, currentDate]);

  // Extract data from combined calculation
  const { scheduledEvents, filterOptions, filteredEvents, calendarDays } = calendarData;

  const handlePrevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  const handleFilterToggle = (filterId: string) => {
    setActiveFilters(prev =>
      prev.includes(filterId)
        ? prev.filter(id => id !== filterId)
        : [...prev, filterId]
    );
  };

  // Show loading spinner while initializing
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  // Show error state if there's an error loading jobs
  if (error) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-red-100 dark:bg-red-900 rounded-full">
          <CalendarIcon className="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Failed to load schedule data
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          {error}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900 dark:text-secondary-50">
            Schedule
          </h1>
          <p className="text-secondary-600 dark:text-secondary-400 mt-1">
            Manage your appointments, meetings, and project deadlines
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* View Mode Toggle */}
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {['month', 'week', 'day'].map((mode) => (
              <button
                key={mode}
                onClick={() => setViewMode(mode as any)}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors capitalize ${
                  viewMode === mode
                    ? 'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400 shadow-sm'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                {mode}
              </button>
            ))}
          </div>

          <Button
            theme="schedule"
            onClick={() => openScheduleJobDrawer()}
            className="shadow-lg"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Schedule Job
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Calendar Area */}
        <div className="lg:col-span-3 space-y-6">
          {/* Jobs Loading Indicator - only show if no stable data yet */}
          {(isLoading && !stableJobs) || isRetrying ? (
            <div className="card p-4">
              <div className="flex items-center justify-center space-x-2 text-secondary-600 dark:text-secondary-400">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500"></div>
                <span className="text-sm">
                  {isRetrying
                    ? `Reconnecting to server... (attempt ${retryCount + 1}/4)`
                    : 'Loading scheduled jobs...'
                  }
                </span>
              </div>
              {isRetrying && (
                <div className="mt-2 text-center">
                  <div className="text-xs text-amber-600 dark:text-amber-400">
                    Connection issue detected - automatically retrying
                  </div>
                </div>
              )}
            </div>
          ) : null}
          {/* Event Filter Tabs */}
          <div className="card p-4">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setActiveFilters(filterOptions.map(f => f.id))}
                className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeFilters.length === filterOptions.length
                    ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400'
                    : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                All Events
                <span className="ml-2 px-1.5 py-0.5 bg-white dark:bg-gray-700 rounded text-xs">
                  {filteredEvents.length}
                </span>
              </button>
              
              {filterOptions.map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => handleFilterToggle(filter.id)}
                  className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeFilters.includes(filter.id)
                      ? `bg-${filter.color}`
                      : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                  }`}
                >
                  {filter.label}
                  <span className="ml-2 px-1.5 py-0.5 bg-white dark:bg-gray-700 rounded text-xs">
                    {filter.count}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Calendar Header */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handlePrevMonth}
                    className="p-2 text-secondary-500 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-200 hover:bg-secondary-100 dark:hover:bg-secondary-800 rounded-lg transition-colors"
                  >
                    <ChevronLeftIcon className="w-5 h-5" />
                  </button>
                  <h2 className="text-xl font-semibold text-secondary-900 dark:text-secondary-50">
                    {MONTHS[currentDate.getMonth()]} {currentDate.getFullYear()}
                  </h2>
                  <button
                    onClick={handleNextMonth}
                    className="p-2 text-secondary-500 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-200 hover:bg-secondary-100 dark:hover:bg-secondary-800 rounded-lg transition-colors"
                  >
                    <ChevronRightIcon className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <Button
                variant="outline"
                theme="schedule"
                onClick={() => setCurrentDate(new Date())}
              >
                Today
              </Button>
            </div>

            {/* Calendar Grid - Month/Week/Day Views */}
            {viewMode === 'month' ? (
              <div className="grid grid-cols-7 gap-px bg-secondary-200 dark:bg-secondary-700 border border-secondary-200 dark:border-secondary-700 rounded-lg overflow-hidden">
                {/* Day Headers */}
                {DAYS_OF_WEEK.map((day) => (
                  <div key={day} className="bg-secondary-50 dark:bg-secondary-800 px-3 py-4 text-center">
                    <span className="text-sm font-medium text-secondary-600 dark:text-secondary-400">
                      {day}
                    </span>
                  </div>
                ))}

                {/* Calendar Days */}
                {calendarDays.map((calendarDay, index) => {
                  const isToday = calendarDay.date.toDateString() === new Date().toDateString();
                  const isSelected = calendarDay.date.toDateString() === currentDate.toDateString();
                  const dateString = formatDateString(calendarDay.date);
                  const isDragOver = dragOverDate === dateString && !dragOverTime;

                  return (
                    <div
                      key={index}
                      className={`min-h-[120px] p-2 transition-all ${
                        !calendarDay.isCurrentMonth ? 'opacity-40' : ''
                      } ${
                        isDragOver ? 'bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-300 dark:ring-blue-600' :
                        isSelected ? 'bg-indigo-100 dark:bg-indigo-900/20' :
                        'bg-white dark:bg-secondary-900'
                      }`}
                      onDragOver={(e) => handleDragOver(e, dateString)}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, dateString)}
                    >
                      <div className={`text-sm font-medium mb-2 ${
                        isToday
                          ? 'w-6 h-6 bg-indigo-500 text-white rounded-full flex items-center justify-center'
                          : 'text-secondary-900 dark:text-secondary-50'
                      }`}>
                        {calendarDay.day}
                      </div>

                      <div className="space-y-1">
                        {calendarDay.events.map((event) => (
                          <CalendarEvent
                            key={event.id}
                            event={event}
                            onEventClick={handleEventClick}
                            onDragStart={handleDragStart}
                            onDragEnd={handleDragEnd}
                            isDragging={draggedEvent?.id === event.id}
                          />
                        ))}
                        {isDragOver && (
                          <div className="px-2 py-1 mb-1 rounded border-2 border-dashed border-blue-300 dark:border-blue-600 text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/10">
                            Drop here to reschedule
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : viewMode === 'week' ? (
              <WeekView
                currentDate={currentDate}
                events={filteredEvents}
                onEventClick={handleEventClick}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                draggedEvent={draggedEvent}
                dragOverDate={dragOverDate}
                dragOverTime={dragOverTime}
              />
            ) : (
              <DayView
                currentDate={currentDate}
                events={filteredEvents}
                onEventClick={handleEventClick}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                draggedEvent={draggedEvent}
                dragOverDate={dragOverDate}
                dragOverTime={dragOverTime}
              />
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <MiniCalendar currentDate={currentDate} onDateChange={setCurrentDate} />
          <UpcomingEvents events={scheduledEvents} />
        </div>
      </div>
    </div>
  );
}