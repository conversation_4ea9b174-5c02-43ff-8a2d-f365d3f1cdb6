'use client';

import React, { useState, useMemo } from 'react';
import { ClientCard } from '@/components/ClientCard';
import { ClientDrawer } from '@/components/ClientDrawer';
import { ClientNotesDrawer } from '@/components/ClientNotesDrawer';
import { EditClientDrawer } from '@/components/EditClientDrawer';
import { ConfirmationModal } from '@/components/ConfirmationModal';
import { useRequireAuth } from '@/hooks/useRequireAuth';
import { useClients } from '@/hooks/useClients';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/contexts/ToastContext';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  UserPlusIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ChevronDownIcon,
  FunnelIcon,
  DocumentTextIcon,
  BriefcaseIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { useDrawer } from '@/contexts/DrawerContext';
import { getApiUrl } from '@/lib/api';
import { Button } from '@deskbelt/ui';


// Client Table Row Component (List View)
const ClientTableRow = ({ client, onClientClick, onCreateJobClick, onRequestReviewClick, onCallClick, onMessageClick, onEditClick, onNotesClick, onDeleteClick, onRatingChange }: {
  client: any;
  onClientClick: (clientId: string) => void;
  onCreateJobClick: (clientId: string) => void;
  onRequestReviewClick: (clientId: string) => void;
  onCallClick: (phone: string) => void;
  onMessageClick: (clientId: string) => void;
  onEditClick: (clientId: string) => void;
  onNotesClick: (clientId: string) => void;
  onDeleteClick: (clientId: string) => void;
  onRatingChange: (clientId: string, rating: number) => void;
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', { 
      day: '2-digit', 
      month: 'short', 
      year: 'numeric'
    });
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getClientStatus = (client: any) => {
    return client.activeJobs > 0 ? 'active' : 'inactive';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case 'potential': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <span
          key={i}
          className={`text-xs ${
            i <= rating 
              ? 'text-yellow-400' 
              : 'text-gray-300 dark:text-gray-600'
          }`}
        >
          ★
        </span>
      );
    }
    return stars;
  };

  const status = getClientStatus(client);

  return (
    <tr className="hover:bg-secondary-50 dark:hover:bg-secondary-800/50 transition-colors cursor-pointer" onClick={() => onClientClick(client.id)}>
      <td className="px-6 py-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
            {getInitials(client.name)}
          </div>
          <div>
            <div className="text-sm font-medium text-secondary-900 dark:text-secondary-50">
              {client.name}
            </div>
            {client.business_name && (
              <div className="text-sm text-secondary-500 dark:text-secondary-400">
                {client.business_name}
              </div>
            )}
            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(status)} mt-1`}>
              {status}
            </span>
          </div>
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-secondary-900 dark:text-secondary-50">
          {client.email || 'No email'}
        </div>
        <div className="text-sm text-secondary-500 dark:text-secondary-400">
          {client.phone || 'No phone'}
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm font-medium text-secondary-900 dark:text-secondary-50">
          {client.totalJobs} total
        </div>
        <div className="text-sm text-secondary-500 dark:text-secondary-400">
          {client.activeJobs} active
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="flex items-center space-x-1">
          {renderStars(client.rating)}
          {client.rating > 0 && (
            <span className="text-sm text-secondary-500 dark:text-secondary-400 ml-2">
              ({client.rating}/5)
            </span>
          )}
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-secondary-900 dark:text-secondary-50">
          {formatDate(client.created_at)}
        </div>
      </td>
      <td className="px-6 py-4 text-right">
        <div className="flex items-center justify-end space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onNotesClick(client.id);
            }}
            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 p-1 rounded transition-colors"
            title="View Notes"
          >
            <DocumentTextIcon className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onCreateJobClick(client.id);
            }}
            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 p-1 rounded transition-colors"
            title="Create Job"
          >
            <BriefcaseIcon className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEditClick(client.id);
            }}
            className="text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-secondary-300 p-1 rounded transition-colors"
            title="Edit Client"
          >
            <PencilIcon className="w-4 h-4" />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default function ClientsPage() {
  const { user, loading: authLoading } = useRequireAuth();
  const { authenticatedFetch } = useAuthenticatedFetch();
  const { showSuccess, showError, showApiError } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [notesClientId, setNotesClientId] = useState<string | null>(null);
  const [editClientId, setEditClientId] = useState<string | null>(null);
  const [deleteClientId, setDeleteClientId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedStatusFilters, setSelectedStatusFilters] = useState<string[]>([]);

  const { data: clients, isLoading, error, hasMore, loadMore, refetch, totalCount, updateClient, removeClient } = useClients({
    search: searchTerm,
    limit: 20
  });

  const router = useRouter();
  const { openNewClientDrawer, setOnClientCreated, openNewJobDrawer, openRequestReviewDrawer } = useDrawer();

  // Status options for filtering (based on typical client lifecycle)
  const statusOptions = [
    { value: 'active', label: 'Active', color: 'bg-green-500' },
    { value: 'inactive', label: 'Inactive', color: 'bg-gray-500' },
    { value: 'potential', label: 'Potential', color: 'bg-blue-500' },
  ];

  // Keep a stable ref to the latest refetch function
  const refetchRef = React.useRef(refetch);

  // Update the ref whenever refetch changes
  React.useEffect(() => {
    refetchRef.current = refetch;
  }, [refetch]);

  // Register the callback only once (empty dependency array)
  React.useEffect(() => {
    setOnClientCreated(() => {
      // Call the latest refetch stored in the ref
      refetchRef.current();
      
      // Show success toast for client creation
      showSuccess(
        'Client Created',
        'New client has been successfully added to your system.'
      );
    });
  }, [setOnClientCreated, showSuccess]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleClientClick = (clientId: string) => {
    setSelectedClientId(clientId);
  };

  const handleCreateJobClick = (clientId: string) => {
    // Open NewJobDrawer with pre-selected client
    openNewJobDrawer(clientId);
  };

  const handleRequestReviewClick = (clientId: string) => {
    // Find the client data and open RequestReviewDrawer
    const client = clients.find(c => c.id === clientId);
    if (client) {
      openRequestReviewDrawer({
        id: client.id,
        name: client.name,
        email: client.email || undefined,
        phone: client.phone || undefined
      });
    }
  };

  const handleCallClick = (phone: string) => {
    window.open(`tel:${phone}`, '_self');
  };

  const handleMessageClick = (clientId: string) => {
    // TODO: Open client drawer or messaging interface
    console.log('Message client:', clientId);
  };

  const handleEditClick = (clientId: string) => {
    setEditClientId(clientId);
  };

  const handleNotesClick = (clientId: string) => {
    setNotesClientId(clientId);
  };

  const handleDeleteClick = (clientId: string) => {
    setDeleteClientId(clientId);
  };

  const handleConfirmDelete = async () => {
    if (!deleteClientId) return;
    
    setIsDeleting(true);
    
    try {
      const response = await authenticatedFetch(`/api/clients/${deleteClientId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log('✅ Client deleted successfully');
        
        // Get client name for toast message
        const clientName = clients.find(c => c.id === deleteClientId)?.name || 'Client';
        
        // Remove client from local state immediately for instant UI feedback
        removeClient(deleteClientId);
        
        // Close any open drawers for this client
        if (selectedClientId === deleteClientId) {
          setSelectedClientId(null);
        }
        if (notesClientId === deleteClientId) {
          setNotesClientId(null);
        }
        if (editClientId === deleteClientId) {
          setEditClientId(null);
        }
        
        // Close the confirmation modal
        setDeleteClientId(null);
        
        // Show success toast
        showSuccess(
          'Client Deleted',
          `${clientName} has been successfully deleted from your system.`
        );
      } else {
        const errorData = await response.json();
        console.error('Failed to delete client:', errorData);
        
        // Use showApiError to display comprehensive error information
        showApiError('Delete Failed', errorData);
      }
    } catch (error) {
      console.error('Error deleting client:', error);
      showError(
        'Connection Error',
        'Unable to connect to server. Please check your connection and try again.',
        7000
      );
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setDeleteClientId(null);
    setIsDeleting(false);
  };

  const handleRatingChange = async (clientId: string, rating: number) => {
    // Optimistically update the client in the local state immediately
    // This will sync both the dashboard card and drawer
    updateClient(clientId, { rating });
    
    // Make API call to persist rating to database
    try {
      const response = await authenticatedFetch(getApiUrl(`/api/clients/${clientId}/rating`), {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rating })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update rating: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      console.log('✅ Rating updated successfully:', result);
      
      // No need to trigger refresh - optimistic update handles UI sync
    } catch (error) {
      console.error('❌ Failed to update rating:', error);
      // Revert the optimistic update on error
      refetch();
    }
  };

  const handleClientUpdated = (updatedClient: any) => {
    console.log('Updating client with data:', updatedClient);
    
    // Update the client in local state
    updateClient(updatedClient.id, updatedClient);
    
    // Also refresh the data to ensure consistency
    refetch();
    
    // Show success toast
    showSuccess(
      'Client Updated',
      `${updatedClient.name}'s information has been successfully updated.`
    );
    
    // Close the edit drawer
    setEditClientId(null);
  };

  const handleStatusFilter = (status: string) => {
    setSelectedStatusFilters(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  // Memoize filtered clients to prevent recreation on every render
  const filteredClients = useMemo(() => {
    return clients.filter(client => {
      const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (client.email && client.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
                           (client.business_name && client.business_name.toLowerCase().includes(searchTerm.toLowerCase()));
      
      // For now, we'll consider clients with active jobs as 'active', otherwise 'inactive'
      // In a real app, you'd have a status field on the client
      const clientStatus = client.activeJobs > 0 ? 'active' : 'inactive';
      const matchesStatus = selectedStatusFilters.length === 0 || selectedStatusFilters.includes(clientStatus);
      
      return matchesSearch && matchesStatus;
    });
  }, [clients, searchTerm, selectedStatusFilters]);



  const ClientSkeleton = () => (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 animate-pulse">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
        </div>
      </div>
      <div className="space-y-2 mb-4">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
      </div>
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-4"></div>
      <div className="flex gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex-1 h-9 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
        <div className="flex-1 h-9 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
      </div>
    </div>
  );

  // Determine if we should show the red error alert (only when we already have some data to display)
  const showErrorAlert = error && clients.length > 0;

  if (authLoading || !user) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900 dark:text-secondary-50">
            Clients
          </h1>
          <p className="text-secondary-600 dark:text-secondary-400 mt-1">
            Manage your client relationships and project portfolios
          </p>
        </div>
        <Button 
          variant="primary"
          context="clients"
          onClick={openNewClientDrawer}
          leftIcon={<PlusIcon className="w-5 h-5" />}
        >
          Add Client
        </Button>
      </div>

      {/* Search and Filter Bar */}
      <div className="card elevation-1 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          {/* Search Bar */}
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search clients by name, email, or company..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus-ring"
            />
          </div>

          {/* Status Filter Dropdown */}
          <div className="relative">
            <select
              value=""
              onChange={(e) => {
                if (e.target.value) {
                  handleStatusFilter(e.target.value);
                }
              }}
              className="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 pr-8 text-gray-900 dark:text-white focus-ring min-w-[140px]"
            >
              <option value="">All Status</option>
              {statusOptions.map((status) => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            </div>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white dark:bg-gray-700 text-green-600 dark:text-green-400 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
              title="Grid View"
            >
              <Squares2X2Icon className="w-5 h-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-white dark:bg-gray-700 text-green-600 dark:text-green-400 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
              title="List View"
            >
              <ListBulletIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Active Filters */}
        {selectedStatusFilters.length > 0 && (
          <div className="mt-4 flex items-center space-x-2">
            <span className="text-sm text-secondary-600 dark:text-secondary-400">Active filters:</span>
            {selectedStatusFilters.map((status) => {
              const statusOption = statusOptions.find(s => s.value === status);
              return (
                <span
                  key={status}
                  className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                >
                  {statusOption?.label}
                  <button
                    onClick={() => handleStatusFilter(status)}
                    className="ml-2 text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                  >
                    ×
                  </button>
                </span>
              );
            })}
            <button
              onClick={() => setSelectedStatusFilters([])}
              className="text-sm text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300"
            >
              Clear all
            </button>
          </div>
        )}

        {/* Results Count */}
        {!isLoading && filteredClients.length > 0 && (
          <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            {searchTerm || selectedStatusFilters.length > 0 ? (
              <>Showing {filteredClients.length} of {totalCount} clients</>
            ) : (
              <>Showing {filteredClients.length} of {totalCount} clients</>
            )}
          </div>
        )}
      </div>

      {/* Content Area */}
      <div>
        {/* Loading State */}
        {isLoading && clients.length === 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <ClientSkeleton key={index} />
            ))}
          </div>
        )}

        {/* Error State (only if we already have data) */}
        {showErrorAlert && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex">
              <ExclamationTriangleIcon className="w-5 h-5 text-red-400 flex-shrink-0" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-400">
                  Unable to refresh clients
                </h3>
                <p className="mt-1 text-sm text-red-700 dark:text-red-300">
                  Please check your connection and try again.
                </p>
                <div className="mt-3">
                  <Button variant="outline" size="sm" onClick={refetch}>
                    Try again
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredClients.length === 0 && (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {searchTerm || selectedStatusFilters.length > 0 ? 'No clients found' : 'No clients yet'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              {searchTerm || selectedStatusFilters.length > 0
                ? 'Try adjusting your search or filters'
                : 'Add your first client to get started'
              }
            </p>
            {!(searchTerm || selectedStatusFilters.length > 0) && (
              <Button
                variant="primary"
                context="clients"
                onClick={openNewClientDrawer}
                icon={<PlusIcon className="w-5 h-5" />}
              >
                Add Your First Client
              </Button>
            )}
          </div>
        )}

        {/* Clients Content */}
        {!isLoading && !error && filteredClients.length > 0 && (
          <>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr">
                {filteredClients.map((client) => (
                  <ClientCard
                    key={client.id}
                    client={client}
                    onClientClick={handleClientClick}
                    onCreateJobClick={handleCreateJobClick}
                    onRequestReviewClick={handleRequestReviewClick}
                    onCallClick={handleCallClick}
                    onMessageClick={handleMessageClick}
                    onEditClick={handleEditClick}
                    onNotesClick={handleNotesClick}
                    onDeleteClick={handleDeleteClick}
                    onRatingChange={handleRatingChange}
                  />
                ))}
              </div>
            ) : (
              <div className="card elevation-1 p-0 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-secondary-200 dark:divide-secondary-700">
                    <thead className="bg-secondary-50 dark:bg-secondary-800">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Client
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Contact
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Jobs
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Rating
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Since
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-secondary-800 divide-y divide-secondary-200 dark:divide-secondary-700">
                      {filteredClients.map((client) => (
                        <ClientTableRow
                          key={client.id}
                          client={client}
                          onClientClick={handleClientClick}
                          onCreateJobClick={handleCreateJobClick}
                          onRequestReviewClick={handleRequestReviewClick}
                          onCallClick={handleCallClick}
                          onMessageClick={handleMessageClick}
                          onEditClick={handleEditClick}
                          onNotesClick={handleNotesClick}
                          onDeleteClick={handleDeleteClick}
                          onRatingChange={handleRatingChange}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Load More */}
            {hasMore && (
              <div className="mt-8 text-center">
                <Button 
                  variant="outline" 
                  onClick={loadMore}
                  disabled={isLoading}
                >
                  {isLoading ? 'Loading...' : 'Load more clients'}
                </Button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Client Drawer */}
      <ClientDrawer
        clientId={selectedClientId}
        onClose={() => setSelectedClientId(null)}
        onCreateJobClick={handleCreateJobClick}
        onNotesClick={handleNotesClick}
        onRatingChange={handleRatingChange}
        clientData={selectedClientId ? clients.find(c => c.id === selectedClientId) : null}
      />

      {/* Client Notes Drawer */}
      <ClientNotesDrawer
        clientId={notesClientId}
        onClose={() => setNotesClientId(null)}
      />

      {/* Edit Client Drawer */}
      <EditClientDrawer
        isOpen={!!editClientId}
        onClose={() => setEditClientId(null)}
        clientId={editClientId}
        clientData={editClientId ? {
          ...clients.find(c => c.id === editClientId)!,
          business_name: clients.find(c => c.id === editClientId)?.business_name || undefined,
          address: clients.find(c => c.id === editClientId)?.address || '',
          phone: clients.find(c => c.id === editClientId)?.phone || undefined,
          email: clients.find(c => c.id === editClientId)?.email || undefined,
        } : undefined}
        onClientUpdated={handleClientUpdated}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={!!deleteClientId}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Client"
        message={
          deleteClientId 
            ? `Are you sure you want to delete ${clients.find(c => c.id === deleteClientId)?.name || 'this client'}? This action cannot be undone and will permanently remove all client data, including notes and job history.`
            : ''
        }
        confirmText="Delete Client"
        cancelText="Cancel"
        variant="danger"
        isLoading={isDeleting}
      />
    </div>
  );
} 