'use client';

import {
  CurrencyPoundIcon,
  BriefcaseIcon,
  UsersIcon,
  ChartBarIcon,
  ClockIcon,
  CalendarDaysIcon,
  ArrowTrendingUpIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  BellIcon
} from '@heroicons/react/24/outline';
import StatsCard from '@/components/StatsCard';
import InvoiceValue<PERSON>hart from '@/components/RevenueChart';
import Interaction<PERSON>hart from '@/components/JobStatusChart';
import { DateRangeFilter, DateRange, getDefaultDateRange } from '@/components/DateRangeFilter';
import { useDashboardStats } from '@/hooks/useDashboardStats';
import { useJobs } from '@/hooks/useJobs';
import { useClients } from '@/hooks/useClients';
import { useNotifications } from '@/hooks/useNotifications';
import React, { useEffect, useState, useMemo } from 'react';
import {DashboardQuickActions} from "@/components/DashboardQuickActions";
import {DashboardTabs} from "@/components/DashboardTabs";
import Link from 'next/link';

export default function DashboardOverview() {
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange>(getDefaultDateRange());
  const { data: statsData, isLoading: statsLoading, error: statsError } = useDashboardStats(selectedDateRange);
  const { data: jobs, isLoading: jobsLoading } = useJobs();
  const { data: clients, isLoading: clientsLoading } = useClients();
  const { notifications, stats: notificationStats } = useNotifications({ includeRead: false, autoRefresh: true });
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // Check for dark mode
    const isDark = document.documentElement.classList.contains('dark');
    setIsDarkMode(isDark);

    // Listen for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const isDark = document.documentElement.classList.contains('dark');
          setIsDarkMode(isDark);
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  const handleDateRangeChange = (newRange: DateRange) => {
    setSelectedDateRange(newRange);
  };

  // Enhanced data processing with real calculations - MOVED BEFORE EARLY RETURNS
  const stats = statsData?.stats || {
    totalInvoiceValue: 0,
    totalJobs: 0,
    totalClients: 0,
    invoiceValueChange: 0,
    jobsChange: 0,
    clientsChange: 0
  };

  // Calculate real completion rate from jobs data
  const completionRate = useMemo(() => {
    if (!jobs || jobs.length === 0) return 0;
    const completedJobs = jobs.filter(job => job.status === 'completed').length;
    return Math.round((completedJobs / jobs.length) * 100);
  }, [jobs]);

  // Calculate completion rate change (comparing with previous period)
  const completionRateChange = useMemo(() => {
    if (!jobs || jobs.length === 0) return 0;
    // This would ideally come from the API with historical data
    // For now, we'll use a calculated estimate based on recent completions
    const recentCompletions = jobs.filter(job => {
      if (job.status !== 'completed' || !job.updated_at) return false;
      const completedDate = new Date(job.updated_at);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return completedDate >= thirtyDaysAgo;
    }).length;

    const totalRecent = jobs.filter(job => {
      if (!job.created_at) return false;
      const createdDate = new Date(job.created_at);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return createdDate >= thirtyDaysAgo;
    }).length;

    if (totalRecent === 0) return 0;
    const recentRate = (recentCompletions / totalRecent) * 100;
    return recentRate - completionRate;
  }, [jobs, completionRate]);

  // Calculate urgent jobs count
  const urgentJobsCount = useMemo(() => {
    if (!jobs) return 0;
    const now = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(now.getDate() + 3);

    return jobs.filter(job => {
      if (job.status === 'completed' || job.status === 'cancelled') return false;
      if (!job.scheduled_at) return false;
      const scheduledDate = new Date(job.scheduled_at);
      return scheduledDate <= threeDaysFromNow && scheduledDate >= now;
    }).length;
  }, [jobs]);

  // Calculate this week's jobs
  const thisWeekJobsCount = useMemo(() => {
    if (!jobs) return 0;
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    return jobs.filter(job => {
      if (!job.scheduled_at) return false;
      const scheduledDate = new Date(job.scheduled_at);
      return scheduledDate >= startOfWeek && scheduledDate <= endOfWeek;
    }).length;
  }, [jobs]);

  // Show loading state while data is being fetched - MOVED AFTER ALL HOOKS
  if (statsLoading || jobsLoading || clientsLoading) {
    return (
      <div className="space-y-8">
        <div className="dashboard-stats-grid">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="card-elevated animate-pulse">
              <div className="h-24 bg-secondary-200 dark:bg-secondary-600 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Handle error state
  if (statsError) {
    return (
      <div className="space-y-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Error loading dashboard data:</strong> {statsError}
        </div>
      </div>
    );
  }

  if (!statsData) {
    return (
      <div className="space-y-8">
        <div className="text-center py-12">
          <div className="text-gray-400 dark:text-gray-600 mb-4">
            <BriefcaseIcon className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No stats available</h3>
          <p className="text-gray-600 dark:text-gray-400">Start by adding some clients and jobs to see your statistics.</p>
        </div>
      </div>
    );
  }

  return (


    <>
    <div className="space-y-6">
              <header className="bg-gradient-to-r from-white to-secondary-50 dark:from-secondary-900 dark:to-secondary-800 border-b border-secondary-200 dark:border-secondary-700 -mx-6 px-6 py-6">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                      <div>
                          <div className="flex items-center space-x-3 mb-2">
                            <h1 className="text-3xl font-bold text-secondary-900 dark:text-secondary-50">Dashboard</h1>
                            {(notificationStats?.unread || 0) > 0 && (
                              <span className="px-2 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 text-xs font-medium rounded-full">
                                {notificationStats?.unread} new
                              </span>
                            )}
                          </div>
                          <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                              Welcome back! Here's what's happening with your business today.
                          </p>
                          {statsData?.dateRange && (
                            <p className="text-xs text-primary-600 dark:text-primary-400 mt-1 font-medium">
                              📊 Showing data for: {statsData.dateRange.label}
                            </p>
                          )}
                      </div>
                      <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-3">
                          <DashboardQuickActions />
                      </div>
                  </div>

                  {/* Quick Stats Summary */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 bg-white dark:bg-secondary-800 rounded-lg p-4 border border-secondary-200 dark:border-secondary-700">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-success-600 dark:text-success-400">
                        £{(stats.totalInvoiceValue || 0).toLocaleString()}
                      </div>
                      <div className="text-xs text-secondary-600 dark:text-secondary-400">Revenue</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-jobs-600 dark:text-jobs-400">
                        {stats.totalJobs || 0}
                      </div>
                      <div className="text-xs text-secondary-600 dark:text-secondary-400">Active Jobs</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-clients-600 dark:text-clients-400">
                        {stats.totalClients || 0}
                      </div>
                      <div className="text-xs text-secondary-600 dark:text-secondary-400">Clients</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                        {completionRate}%
                      </div>
                      <div className="text-xs text-secondary-600 dark:text-secondary-400">Completion</div>
                    </div>
                  </div>
              </header>
          </div>
    <div className="space-y-8 overflow-hidden">




      {/* Date Range Filter */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Dashboard Overview</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Your business performance at a glance
            {statsData?.dateRange && (
              <span className="ml-2 font-medium text-blue-600 dark:text-blue-400">
                • {statsData.dateRange.label}
              </span>
            )}
          </p>
        </div>
        <DateRangeFilter
          selectedRange={selectedDateRange}
          onRangeChange={handleDateRangeChange}
        />
      </div>

      {/* Enhanced Stats Cards with Real Data */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Revenue"
          value={stats.totalInvoiceValue}
          change={stats.invoiceValueChange}
          icon={<CurrencyPoundIcon className="w-7 h-7" />}
          format="currency"
          color="success"
          variant="elevated"
        />
        <StatsCard
          title="Active Jobs"
          value={stats.totalJobs}
          change={stats.jobsChange}
          icon={<BriefcaseIcon className="w-7 h-7" />}
          format="number"
          color="jobs"
          variant="elevated"
        />
        <StatsCard
          title="Total Clients"
          value={stats.totalClients}
          change={stats.clientsChange}
          icon={<UsersIcon className="w-7 h-7" />}
          format="number"
          color="clients"
          variant="elevated"
        />
        <StatsCard
          title="Completion Rate"
          value={`${completionRate}%`}
          change={completionRateChange}
          icon={<ChartBarIcon className="w-7 h-7" />}
          format="percentage"
          color="primary"
          variant="elevated"
        />
      </div>

      {/* Secondary Stats Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="This Week's Jobs"
          value={thisWeekJobsCount}
          icon={<CalendarDaysIcon className="w-7 h-7" />}
          format="number"
          color="accent"
          variant="elevated"
        />
        <StatsCard
          title="Urgent Jobs"
          value={urgentJobsCount}
          icon={<ExclamationTriangleIcon className="w-7 h-7" />}
          format="number"
          color="warning"
          variant="elevated"
        />
        <StatsCard
          title="Unread Notifications"
          value={notificationStats?.unread || 0}
          icon={<BellIcon className="w-7 h-7" />}
          format="number"
          color="primary"
          variant="elevated"
        />
        <StatsCard
          title="Avg. Job Value"
          value={stats.totalJobs > 0 ? Math.round(stats.totalInvoiceValue / stats.totalJobs) : 0}
          icon={<ArrowTrendingUpIcon className="w-7 h-7" />}
          format="currency"
          color="accent"
          variant="elevated"
        />
      </div>

      {/* Quick Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="lg:col-span-1">
          <div className="card-interactive">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50">
                Recent Activity
              </h3>
              <Link
                href="/dashboard/jobs"
                className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
              >
                View all
              </Link>
            </div>
            <div className="space-y-3">
              {jobs?.slice(0, 5).map((job) => (
                <div key={job.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors">
                  <div className={`w-2 h-2 rounded-full ${
                    job.status === 'completed' ? 'bg-success-500' :
                    job.status === 'in_progress' ? 'bg-warning-500' :
                    job.status === 'pending' ? 'bg-primary-500' :
                    'bg-secondary-400'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-secondary-900 dark:text-secondary-50 truncate">
                      {job.title}
                    </p>
                    <p className="text-xs text-secondary-600 dark:text-secondary-400">
                      {job.client_name || 'No client'} • {job.status}
                    </p>
                  </div>
                  <div className="text-xs text-secondary-500 dark:text-secondary-400">
                    {job.scheduled_at ? new Date(job.scheduled_at).toLocaleDateString() : 'No date'}
                  </div>
                </div>
              )) || (
                <div className="text-center py-8">
                  <BriefcaseIcon className="w-12 h-12 mx-auto text-secondary-400 dark:text-secondary-600 mb-2" />
                  <p className="text-sm text-secondary-600 dark:text-secondary-400">No recent jobs</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Urgent Items */}
        <div className="lg:col-span-1">
          <div className="card-interactive">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50">
                Urgent Items
              </h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                urgentJobsCount > 0 ? 'bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200' :
                'bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200'
              }`}>
                {urgentJobsCount} urgent
              </span>
            </div>
            <div className="space-y-3">
              {jobs?.filter(job => {
                if (job.status === 'completed' || job.status === 'cancelled') return false;
                if (!job.scheduled_at) return false;
                const scheduledDate = new Date(job.scheduled_at);
                const now = new Date();
                const threeDaysFromNow = new Date();
                threeDaysFromNow.setDate(now.getDate() + 3);
                return scheduledDate <= threeDaysFromNow && scheduledDate >= now;
              }).slice(0, 5).map((job) => (
                <div key={job.id} className="flex items-center space-x-3 p-2 rounded-lg bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800">
                  <ExclamationTriangleIcon className="w-4 h-4 text-warning-600 dark:text-warning-400 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-secondary-900 dark:text-secondary-50 truncate">
                      {job.title}
                    </p>
                    <p className="text-xs text-secondary-600 dark:text-secondary-400">
                      Due: {job.scheduled_at ? new Date(job.scheduled_at).toLocaleDateString() : 'No date'}
                    </p>
                  </div>
                </div>
              )) || (
                <div className="text-center py-8">
                  <CheckCircleIcon className="w-12 h-12 mx-auto text-success-400 dark:text-success-600 mb-2" />
                  <p className="text-sm text-success-600 dark:text-success-400">All caught up!</p>
                  <p className="text-xs text-secondary-500 dark:text-secondary-400">No urgent items</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Recent Notifications */}
        <div className="lg:col-span-1">
          <div className="card-interactive">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-50">
                Recent Notifications
              </h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                (notificationStats?.unread || 0) > 0 ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200' :
                'bg-secondary-100 text-secondary-800 dark:bg-secondary-900 dark:text-secondary-200'
              }`}>
                {notificationStats?.unread || 0} unread
              </span>
            </div>
            <div className="space-y-3">
              {notifications?.slice(0, 5).map((notification) => (
                <div key={notification.id} className={`flex items-start space-x-3 p-2 rounded-lg transition-colors ${
                  !notification.is_read ? 'bg-primary-50 dark:bg-primary-900/20' : 'hover:bg-secondary-50 dark:hover:bg-secondary-800'
                }`}>
                  {!notification.is_read && (
                    <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0" />
                  )}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-secondary-900 dark:text-secondary-50 truncate">
                      {notification.title}
                    </p>
                    <p className="text-xs text-secondary-600 dark:text-secondary-400 line-clamp-2">
                      {notification.message}
                    </p>
                    <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                      {new Date(notification.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )) || (
                <div className="text-center py-8">
                  <BellIcon className="w-12 h-12 mx-auto text-secondary-400 dark:text-secondary-600 mb-2" />
                  <p className="text-sm text-secondary-600 dark:text-secondary-400">No notifications</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      {statsData?.monthlyInvoiceValue && statsData?.interactionData && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <InvoiceValueChart data={statsData.monthlyInvoiceValue} isDarkMode={isDarkMode} />
          <InteractionChart data={statsData.interactionData} isDarkMode={isDarkMode} />
        </div>
      )}
    </div>
    </>
  );
} 