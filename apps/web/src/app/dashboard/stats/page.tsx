'use client';

import { useEffect, useState } from 'react';
import { useRequireAuth } from '@/hooks/useRequireAuth';
import { useDashboardStats } from '@/hooks/useDashboardStats';
import StatsCard from '@/components/StatsCard';
import InvoiceValue<PERSON>hart from '@/components/RevenueChart';
import Interaction<PERSON>hart from '@/components/JobStatusChart';
import { DateRangeFilter, DateRange, getDefaultDateRange } from '@/components/DateRangeFilter';
import { 
  PlusIcon, 
  UserPlusIcon, 
  SparklesIcon,
  CurrencyPoundIcon,
  BriefcaseIcon,
  UsersIcon,
  ArrowTrendingUpIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline';

export default function StatsPage() {
  const { loading } = useRequireAuth();
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange>(getDefaultDateRange());
  const { data, isLoading, error, refetch } = useDashboardStats(selectedDateRange);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // Check for dark mode
    const isDark = document.documentElement.classList.contains('dark');
    setIsDarkMode(isDark);

    // Listen for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const isDark = document.documentElement.classList.contains('dark');
          setIsDarkMode(isDark);
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  const handleDateRangeChange = (newRange: DateRange) => {
    setSelectedDateRange(newRange);
  };

  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-2 space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-800 dark:text-red-200">{error}</p>
          <button 
            onClick={() => refetch()}
            className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 font-medium"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="py-2 space-y-6">
        <div className="text-center py-12">
          <div className="text-gray-400 dark:text-gray-600 mb-4">
            <BriefcaseIcon className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No stats available</h3>
          <p className="text-gray-600 dark:text-gray-400">Start by adding some clients and jobs to see your statistics.</p>
        </div>
      </div>
    );
  }

  // Calculate change percentages for new metrics
  const newClientsChange = data.stats.newClientsLastMonth > 0 
    ? ((data.stats.newClientsThisMonth - data.stats.newClientsLastMonth) / data.stats.newClientsLastMonth) * 100 
    : 0;
  
  const newJobsChange = data.stats.newJobsLastMonth > 0 
    ? ((data.stats.newJobsThisMonth - data.stats.newJobsLastMonth) / data.stats.newJobsLastMonth) * 100 
    : 0;

  return (
    <div className="py-2 space-y-6">
      {/* Header with Date Filter and Action Buttons */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Dashboard Statistics</h1>
            <p className="text-body text-muted">
              Overview of your business performance
              {data?.dateRange && (
                <span className="ml-2 text-sm font-medium text-blue-600 dark:text-blue-400">
                  • {data.dateRange.label}
                </span>
              )}
            </p>
          </div>

          {/*<div className="flex flex-col sm:flex-row gap-2">
            <button className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
              <PlusIcon className="w-4 h-4 mr-2" />
              New Job
            </button>
            <button className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
              <UserPlusIcon className="w-4 h-4 mr-2" />
              New Client
            </button>
            <button className="inline-flex items-center px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-lg transition-colors">
              <SparklesIcon className="w-4 h-4 mr-2" />
              Ask Dex
            </button>
          </div>*/}

        </div>
        
        {/* Date Range Filter */}
        <div className="flex justify-end">
          <DateRangeFilter
            selectedRange={selectedDateRange}
            onRangeChange={handleDateRangeChange}
          />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatsCard
          title="Total Invoice Value"
          value={data.stats.totalInvoiceValue}
          change={data.stats.invoiceValueChange}
          icon={<CurrencyPoundIcon />}
          format="currency"
        />
        <StatsCard
          title="Total Jobs"
          value={data.stats.totalJobs}
          change={data.stats.jobsChange}
          icon={<BriefcaseIcon />}
          format="number"
        />
        <StatsCard
          title="Total Clients"
          value={data.stats.totalClients}
          change={data.stats.clientsChange}
          icon={<UsersIcon />}
          format="number"
        />
        <StatsCard
          title="New Clients This Month"
          value={data.stats.newClientsThisMonth}
          change={newClientsChange}
          icon={<ArrowTrendingUpIcon />}
          format="number"
        />
        <StatsCard
          title="New Jobs This Month"
          value={data.stats.newJobsThisMonth}
          change={newJobsChange}
          icon={<CalendarDaysIcon />}
          format="number"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <InvoiceValueChart data={data.monthlyInvoiceValue} isDarkMode={isDarkMode} />
        <InteractionChart data={data.interactionData} isDarkMode={isDarkMode} />
      </div>
    </div>
  );
} 