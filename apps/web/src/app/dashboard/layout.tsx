import React from 'react';
import Layout from '@/components/Layout';
import { DashboardTabs } from '@/components/DashboardTabs';
import { DashboardQuickActions } from '@/components/DashboardQuickActions';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Layout>
      {/* Dashboard header that persists across Overview, Jobs, Clients, Stats & Archived sub-routes */}
      <div className="space-y-6">

        {/* Dynamic sub-page content */}
        <main className="px-6">
          {children}
        </main>
      </div>
    </Layout>
  );
} 