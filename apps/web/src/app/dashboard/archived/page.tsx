'use client';

import { useState } from 'react';
import JobCard from '@/components/JobCard';
import { useRequireAuth } from '@/hooks/useRequireAuth';
import { useArchivedJobs } from '@/hooks/useArchivedJobs';
import { JobDrawer } from '@/components/JobDrawer';
import { ClientDrawer } from '@/components/ClientDrawer';
import { JobLogDrawer } from '@/components/JobLogDrawer';
import { Job } from '@/types/Job';
import {
  MagnifyingGlassIcon,
  ArchiveBoxXMarkIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import React from 'react';

// Reusable Button component
const Button = ({ 
  theme = 'default', 
  variant = 'primary', 
  size = 'md', 
  children, 
  className = '', 
  ...props 
}: {
  theme?: 'default' | 'jobs' | 'clients' | 'ai';
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
  [key: string]: any;
}) => {
  const getThemeColors = () => {
    const themes = {
      default: 'bg-gray-500/90 hover:bg-gray-600 border-gray-500/90 text-white shadow-sm hover:shadow-md',
      jobs: 'bg-blue-500/90 hover:bg-blue-600 border-blue-500/90 text-white shadow-sm hover:shadow-md',
      clients: 'bg-green-500/90 hover:bg-green-600 border-green-500/90 text-white shadow-sm hover:shadow-md',
      ai: 'bg-orange-500/90 hover:bg-orange-600 border-orange-500/90 text-white shadow-sm hover:shadow-md'
    };
    return themes[theme];
  };

  const getVariantClasses = () => {
    if (variant === 'outline') {
      const themes = {
        default: 'border-gray-200 text-gray-600 hover:bg-gray-50 shadow-sm hover:shadow-md',
        jobs: 'border-blue-200 text-blue-600 hover:bg-blue-50 shadow-sm hover:shadow-md',
        clients: 'border-green-200 text-green-600 hover:bg-green-50 shadow-sm hover:shadow-md',
        ai: 'border-orange-200 text-orange-600 hover:bg-orange-50 shadow-sm hover:shadow-md'
      };
      return `border ${themes[theme]} bg-white/80 dark:bg-gray-700/80`;
    }
    return getThemeColors();
  };

  const getSizeClasses = () => {
    const sizes = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base'
    };
    return sizes[size];
  };

  return (
    <button
      className={`
        inline-flex items-center justify-center font-medium rounded-lg
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400
        transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed
        ${getVariantClasses()} ${getSizeClasses()} ${className}
      `}
      {...props}
    >
      {children}
    </button>
  );
};

// Enhanced JobCard wrapper for archived jobs
const ArchivedJobCardWrapper = ({ 
  job, 
  onViewClient, 
  onUnarchive,
  onNotesClick,
  onCallClick,
  onMessageClick,
  onEmailClick
}: { 
  job: Job; 
  onViewClient: (clientId: string) => void;
  onUnarchive: (jobId: string) => void;
  onNotesClick: (jobId: string) => void;
  onCallClick: (phone: string) => void;
  onMessageClick: (clientId: string) => void;
  onEmailClick: (email: string) => void;
}) => {
  const [isUnarchiving, setIsUnarchiving] = useState(false);

  const handleUnarchive = async () => {
    setIsUnarchiving(true);
    try {
      await onUnarchive(job.id);
    } catch (error) {
      console.error('Failed to unarchive job:', error);
    } finally {
      setIsUnarchiving(false);
    }
  };

  return (
    <div className="relative">
      {/* Unarchive button overlay */}
      <div className="absolute top-4 right-4 z-10">
        <Button
          onClick={handleUnarchive}
          disabled={isUnarchiving}
          variant="outline"
          size="sm"
          className="bg-white/90 hover:bg-white border-gray-300 text-gray-700 hover:text-gray-900"
        >
          {isUnarchiving ? (
            <ArrowPathIcon className="w-4 h-4 mr-1 animate-spin" />
          ) : (
            <ArchiveBoxXMarkIcon className="w-4 h-4 mr-1" />
          )}
          {isUnarchiving ? 'Unarchiving...' : 'Unarchive'}
        </Button>
      </div>

      {/* Standard JobCard but without click handler for main card */}
      <JobCard
        job={job}
        onJobClick={undefined} // Disable main card clicking
        onClientClick={onViewClient}
        onNotesClick={onNotesClick} // Enable notes/job log viewing
        onCallClick={onCallClick}
        onMessageClick={onMessageClick}
        onEmailClick={onEmailClick}
        onStatusChange={undefined} // Disable status changes for archived jobs
        onDateChange={undefined} // Disable date changes for archived jobs
        showStatusDropdown={false}
        showDatePicker={false}
      />
    </div>
  );
};

export default function ArchivedPage() {
  const { user, loading: authLoading, isAuthenticated } = useRequireAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [selectedJobLogId, setSelectedJobLogId] = useState<string | null>(null);

  const { data: jobs, isLoading, error, refetch, hasMore, loadMore, unarchiveJob } = useArchivedJobs({
    search: searchTerm,
  });

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500"></div>
      </div>
    );
  }

  // Don't render the page content if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  const handleClientClick = (clientId: string) => {
    setSelectedClientId(clientId);
  };

  const handleUnarchive = async (jobId: string) => {
    try {
      await unarchiveJob(jobId);
      refetch(); // Refresh the archived jobs list
    } catch (error) {
      console.error('Failed to unarchive job:', error);
    }
  };

  const handleCallClick = (phone: string) => {
    window.open(`tel:${phone}`, '_self');
  };

  const handleMessageClick = (clientId: string) => {
    // TODO: Open messaging interface
    console.log('Send message to client:', clientId);
  };

  const handleEmailClick = (email: string) => {
    window.open(`mailto:${email}`, '_self');
  };

  const handleNotesClick = (jobId: string) => {
    setSelectedJobLogId(jobId);
  };

  const JobSkeleton = () => (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 animate-pulse">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-full w-20"></div>
      </div>
      <div className="space-y-3">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
      </div>
    </div>
  );

  return (
    <>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-medium text-gray-900 dark:text-white">Archived Jobs</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              View and manage your archived jobs. Unarchive to restore them to active status.
            </p>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {jobs.length} archived job{jobs.length !== 1 ? 's' : ''}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 mb-6">
          <Button theme="jobs" className="flex items-center">
            📄 New Job
          </Button>
          <Button theme="clients" className="flex items-center">
            👤 New Client
          </Button>
          <Button theme="ai" className="flex items-center">
            🤖 Ask Dex
          </Button>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="Search archived jobs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
          />
        </div>

        {/* Content */}
        {isLoading && jobs.length === 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[...Array(4)].map((_, i) => (
              <JobSkeleton key={i} />
            ))}
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-12">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Loading Archived Jobs</h3>
            <p className="text-gray-500 dark:text-gray-400 text-center mb-4">{error}</p>
            <Button onClick={refetch} variant="outline">
              Try Again
            </Button>
          </div>
        ) : jobs.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <ArchiveBoxXMarkIcon className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Archived Jobs</h3>
            <p className="text-gray-500 dark:text-gray-400 text-center">
              {searchTerm ? 'No archived jobs match your search.' : 'You haven\'t archived any jobs yet.'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {jobs.map((job) => (
                             <ArchivedJobCardWrapper
                 key={job.id}
                 job={job}
                 onViewClient={handleClientClick}
                 onUnarchive={handleUnarchive}
                 onNotesClick={handleNotesClick}
                 onCallClick={handleCallClick}
                 onMessageClick={handleMessageClick}
                 onEmailClick={handleEmailClick}
               />
            ))}
          </div>
        )}

        {/* Load More */}
        {hasMore && jobs.length > 0 && (
          <div className="flex justify-center mt-8">
            <Button 
              onClick={loadMore} 
              variant="outline" 
              disabled={isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" />
                  Loading...
                </>
              ) : (
                'Load More'
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Client Drawer */}
      <ClientDrawer
        clientId={selectedClientId}
        onClose={() => setSelectedClientId(null)}
      />

      {/* Job Log Drawer (read-only for archived jobs) */}
      <JobLogDrawer
        jobId={selectedJobLogId}
        jobTitle={selectedJobLogId ? jobs.find(job => job.id === selectedJobLogId)?.title : undefined}
        onClose={() => setSelectedJobLogId(null)}
        readOnly={true} // Archived jobs are read-only
      />
    </>
  );
} 