'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Layout from '@/components/Layout';

export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to dashboard for now (later we'll check auth)
    router.push('/dashboard/jobs');
  }, [router]);

  return (
    <Layout>
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-jobs-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Redirecting to dashboard...</p>
        </div>
      </div>
    </Layout>
  );
} 