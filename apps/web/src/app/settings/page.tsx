'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import { useRequireAuth } from '@/hooks/useRequireAuth';
import { useSettings } from '@/hooks/useSettings';
import { Tabs, type TabItem } from '@deskbelt/ui';
import { 
  CogIcon,
  CurrencyPoundIcon,
  DocumentTextIcon,
  BellIcon,
  ClipboardDocumentCheckIcon,
  CalculatorIcon
} from '@heroicons/react/24/outline';

export default function SettingsPage() {
  const { user, loading, isAuthenticated } = useRequireAuth();
  const { 
    data: settings, 
    isLoading: settingsLoading, 
    updateBusinessSettings,
    updateNotificationSettings
  } = useSettings();

  const [isUpdating, setIsUpdating] = useState(false);
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    defaultQuoteTerms: '',
    defaultContractTerms: '',
    defaultPaymentTerms: '',
    // VAT & Pricing fields
    autoSelectVAT: false,
    vatRate: 0,
    dayRate: 0,
    hourlyRate: 0,
    calloutFee: 0,
    emergencyRate: 0,
    // Document Automation fields
    autoNumberQuotes: false,
    autoNumberInvoices: false,
    autoNumberContracts: false,
    quoteValidityDays: 0,
    invoicePaymentDays: 0,
    // Warranties fields
    defaultLabourWarranty: '',
    defaultMaterialsWarranty: '',
    // Notifications fields
    emailQuoteAccepted: false,
    emailInvoicePaid: false,
    emailJobCompleted: false
  });

  // Initialize form data when settings are loaded
  useEffect(() => {
    if (settings) {
      setFormData({
        defaultQuoteTerms: settings.business.defaultQuoteTerms,
        defaultContractTerms: settings.business.defaultContractTerms,
        defaultPaymentTerms: settings.business.defaultPaymentTerms,
        // VAT & Pricing fields
        autoSelectVAT: settings.business.autoSelectVAT,
        vatRate: settings.business.vatRate,
        dayRate: settings.business.dayRate,
        hourlyRate: settings.business.hourlyRate,
        calloutFee: settings.business.calloutFee,
        emergencyRate: settings.business.emergencyRate,
        // Document Automation fields
        autoNumberQuotes: settings.business.autoNumberQuotes,
        autoNumberInvoices: settings.business.autoNumberInvoices,
        autoNumberContracts: settings.business.autoNumberContracts,
        quoteValidityDays: settings.business.quoteValidityDays,
        invoicePaymentDays: settings.business.invoicePaymentDays,
        // Warranties fields
        defaultLabourWarranty: settings.business.defaultLabourWarranty,
        defaultMaterialsWarranty: settings.business.defaultMaterialsWarranty,
        // Notifications fields
        emailQuoteAccepted: settings.notifications.emailQuoteAccepted,
        emailInvoicePaid: settings.notifications.emailInvoicePaid,
        emailJobCompleted: settings.notifications.emailJobCompleted
      });
    }
  }, [settings]);

  // Debug logging
  console.log('Settings page render - UPDATED:', {
    loading,
    settingsLoading,
    isAuthenticated,
    settings: !!settings,
    user: !!user
  });

  // Show loading spinner while checking authentication
  if (loading || settingsLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="ml-4 text-gray-600">Loading: auth={loading}, settings={settingsLoading}</p>
        </div>
      </Layout>
    );
  }

  // Don't render the page content if not authenticated
  if (!isAuthenticated || !settings) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-red-600">Not authenticated or settings missing</p>
            <p className="text-sm text-gray-500">Auth: {isAuthenticated ? 'Yes' : 'No'}, Settings: {settings ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </Layout>
    );
  }


  const startEditing = (section: string, currentValue: string) => {
    setEditingSection(section);
    setFormData(prev => ({ ...prev, [section]: currentValue }));
  };

  const saveTextSetting = async (key: string) => {
    setIsUpdating(true);
    try {
      await updateBusinessSettings({ [key]: formData[key as keyof typeof formData] });
      setEditingSection(null);
    } catch (error) {
      console.error('Failed to update text setting:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const cancelEditing = () => {
    setEditingSection(null);
    // Reset to original settings values
    if (settings) {
      setFormData({
        defaultQuoteTerms: settings.business.defaultQuoteTerms,
        defaultContractTerms: settings.business.defaultContractTerms,
        defaultPaymentTerms: settings.business.defaultPaymentTerms,
        // VAT & Pricing fields
        autoSelectVAT: settings.business.autoSelectVAT,
        vatRate: settings.business.vatRate,
        dayRate: settings.business.dayRate,
        hourlyRate: settings.business.hourlyRate,
        calloutFee: settings.business.calloutFee,
        emergencyRate: settings.business.emergencyRate,
        // Document Automation fields
        autoNumberQuotes: settings.business.autoNumberQuotes,
        autoNumberInvoices: settings.business.autoNumberInvoices,
        autoNumberContracts: settings.business.autoNumberContracts,
        quoteValidityDays: settings.business.quoteValidityDays,
        invoicePaymentDays: settings.business.invoicePaymentDays,
        // Warranties fields
        defaultLabourWarranty: settings.business.defaultLabourWarranty,
        defaultMaterialsWarranty: settings.business.defaultMaterialsWarranty,
        // Notifications fields
        emailQuoteAccepted: settings.notifications.emailQuoteAccepted,
        emailInvoicePaid: settings.notifications.emailInvoicePaid,
        emailJobCompleted: settings.notifications.emailJobCompleted
      });
    }
  };

  const savePricingSettings = async () => {
    setIsUpdating(true);
    try {
      await updateBusinessSettings({
        autoSelectVAT: formData.autoSelectVAT,
        vatRate: formData.vatRate,
        dayRate: formData.dayRate,
        hourlyRate: formData.hourlyRate,
        calloutFee: formData.calloutFee,
        emergencyRate: formData.emergencyRate
      });
    } catch (error) {
      console.error('Failed to update pricing settings:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const saveDocumentSettings = async () => {
    setIsUpdating(true);
    try {
      await updateBusinessSettings({
        autoNumberQuotes: formData.autoNumberQuotes,
        autoNumberInvoices: formData.autoNumberInvoices,
        autoNumberContracts: formData.autoNumberContracts,
        quoteValidityDays: formData.quoteValidityDays,
        invoicePaymentDays: formData.invoicePaymentDays
      });
    } catch (error) {
      console.error('Failed to update document settings:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const saveWarrantySettings = async () => {
    setIsUpdating(true);
    try {
      await updateBusinessSettings({
        defaultLabourWarranty: formData.defaultLabourWarranty,
        defaultMaterialsWarranty: formData.defaultMaterialsWarranty
      });
    } catch (error) {
      console.error('Failed to update warranty settings:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const saveNotificationSettings = async () => {
    setIsUpdating(true);
    try {
      await updateNotificationSettings({
        emailQuoteAccepted: formData.emailQuoteAccepted,
        emailInvoicePaid: formData.emailInvoicePaid,
        emailJobCompleted: formData.emailJobCompleted
      });
    } catch (error) {
      console.error('Failed to update notification settings:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Define tab sections
  const vatPricingContent = (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Auto-select VAT */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Auto-select VAT
            </label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Automatically include VAT in quotes and invoices
            </p>
          </div>
          <button
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, autoSelectVAT: !prev.autoSelectVAT }))}
            disabled={isUpdating}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              formData.autoSelectVAT ? 'bg-green-600' : 'bg-gray-200 dark:bg-gray-700'
            } disabled:opacity-50`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                formData.autoSelectVAT ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* VAT Rate */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
            VAT Rate (%)
          </label>
          <input
            type="number"
            min="0"
            max="100"
            step="0.1"
            value={formData.vatRate}
            onChange={(e) => setFormData(prev => ({ ...prev, vatRate: parseFloat(e.target.value) || 0 }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Day Rate */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
            Day Rate (£)
          </label>
          <input
            type="number"
            min="0"
            step="10"
            value={formData.dayRate}
            onChange={(e) => setFormData(prev => ({ ...prev, dayRate: parseFloat(e.target.value) || 0 }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Hourly Rate */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
            Hourly Rate (£)
          </label>
          <input
            type="number"
            min="0"
            step="5"
            value={formData.hourlyRate}
            onChange={(e) => setFormData(prev => ({ ...prev, hourlyRate: parseFloat(e.target.value) || 0 }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Call-out Fee */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
            Call-out Fee (£)
          </label>
          <input
            type="number"
            min="0"
            step="5"
            value={formData.calloutFee}
            onChange={(e) => setFormData(prev => ({ ...prev, calloutFee: parseFloat(e.target.value) || 0 }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Emergency Rate */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
            Emergency Rate (£/hour)
          </label>
          <input
            type="number"
            min="0"
            step="5"
            value={formData.emergencyRate}
            onChange={(e) => setFormData(prev => ({ ...prev, emergencyRate: parseFloat(e.target.value) || 0 }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={savePricingSettings}
          disabled={isUpdating}
          className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          {isUpdating && (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
          )}
          <span>{isUpdating ? 'Saving...' : 'Save VAT & Pricing Settings'}</span>
        </button>
      </div>
    </div>
  );

  const documentAutomationContent = (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Auto Number toggles */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Auto-number Quotes
            </label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              QUO-2025-001, QUO-2025-002...
            </p>
          </div>
          <button
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, autoNumberQuotes: !prev.autoNumberQuotes }))}
            disabled={isUpdating}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              formData.autoNumberQuotes ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
            } disabled:opacity-50`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                formData.autoNumberQuotes ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Auto-number Invoices
            </label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              INV-2025-001, INV-2025-002...
            </p>
          </div>
          <button
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, autoNumberInvoices: !prev.autoNumberInvoices }))}
            disabled={isUpdating}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              formData.autoNumberInvoices ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
            } disabled:opacity-50`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                formData.autoNumberInvoices ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Auto-number Contracts
            </label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              CON-2025-001, CON-2025-002...
            </p>
          </div>
          <button
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, autoNumberContracts: !prev.autoNumberContracts }))}
            disabled={isUpdating}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              formData.autoNumberContracts ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
            } disabled:opacity-50`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                formData.autoNumberContracts ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Quote Validity Days */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
            Quote Valid for (days)
          </label>
          <input
            type="number"
            min="1"
            max="365"
            value={formData.quoteValidityDays}
            onChange={(e) => setFormData(prev => ({ ...prev, quoteValidityDays: parseInt(e.target.value) || 30 }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Invoice Payment Days */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
            Invoice Payment Due (days)
          </label>
          <input
            type="number"
            min="1"
            max="90"
            value={formData.invoicePaymentDays}
            onChange={(e) => setFormData(prev => ({ ...prev, invoicePaymentDays: parseInt(e.target.value) || 7 }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={saveDocumentSettings}
          disabled={isUpdating}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          {isUpdating && (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
          )}
          <span>{isUpdating ? 'Saving...' : 'Save Document Settings'}</span>
        </button>
      </div>
    </div>
  );

  const defaultTermsContent = (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
      <div className="space-y-6">
        {/* Default Payment Terms */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Default Payment Terms
            </label>
            {editingSection !== 'defaultPaymentTerms' && (
              <button
                onClick={() => startEditing('defaultPaymentTerms', settings.business.defaultPaymentTerms)}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              >
                Edit
              </button>
            )}
          </div>
          {editingSection === 'defaultPaymentTerms' ? (
            <div className="space-y-3">
              <textarea
                value={formData.defaultPaymentTerms}
                onChange={(e) => setFormData(prev => ({ ...prev, defaultPaymentTerms: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <div className="flex gap-2">
                <button
                  onClick={() => saveTextSetting('defaultPaymentTerms')}
                  disabled={isUpdating}
                  className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg disabled:opacity-50"
                >
                  Save
                </button>
                <button
                  onClick={cancelEditing}
                  className="px-3 py-1 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 text-sm rounded-lg"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300">
              {settings.business.defaultPaymentTerms}
            </div>
          )}
        </div>

        {/* Default Quote Terms */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Default Quote Terms & Conditions
            </label>
            {editingSection !== 'defaultQuoteTerms' && (
              <button
                onClick={() => startEditing('defaultQuoteTerms', settings.business.defaultQuoteTerms)}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              >
                Edit
              </button>
            )}
          </div>
          {editingSection === 'defaultQuoteTerms' ? (
            <div className="space-y-3">
              <textarea
                value={formData.defaultQuoteTerms}
                onChange={(e) => setFormData(prev => ({ ...prev, defaultQuoteTerms: e.target.value }))}
                rows={12}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <div className="flex gap-2">
                <button
                  onClick={() => saveTextSetting('defaultQuoteTerms')}
                  disabled={isUpdating}
                  className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg disabled:opacity-50"
                >
                  Save
                </button>
                <button
                  onClick={cancelEditing}
                  className="px-3 py-1 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 text-sm rounded-lg"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line max-h-48 overflow-y-auto">
              {settings.business.defaultQuoteTerms}
            </div>
          )}
        </div>

        {/* Default Contract Terms */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Default Contract Terms & Conditions
            </label>
            {editingSection !== 'defaultContractTerms' && (
              <button
                onClick={() => startEditing('defaultContractTerms', settings.business.defaultContractTerms)}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              >
                Edit
              </button>
            )}
          </div>
          {editingSection === 'defaultContractTerms' ? (
            <div className="space-y-3">
              <textarea
                value={formData.defaultContractTerms}
                onChange={(e) => setFormData(prev => ({ ...prev, defaultContractTerms: e.target.value }))}
                rows={16}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <div className="flex gap-2">
                <button
                  onClick={() => saveTextSetting('defaultContractTerms')}
                  disabled={isUpdating}
                  className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg disabled:opacity-50"
                >
                  Save
                </button>
                <button
                  onClick={cancelEditing}
                  className="px-3 py-1 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 text-sm rounded-lg"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line max-h-48 overflow-y-auto">
              {settings.business.defaultContractTerms}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const warrantiesContent = (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Labour Warranty */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
            Default Labour Warranty
          </label>
          <input
            type="text"
            value={formData.defaultLabourWarranty}
            onChange={(e) => setFormData(prev => ({ ...prev, defaultLabourWarranty: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder="e.g., 12 months workmanship warranty"
          />
        </div>

        {/* Materials Warranty */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
            Default Materials Warranty
          </label>
          <input
            type="text"
            value={formData.defaultMaterialsWarranty}
            onChange={(e) => setFormData(prev => ({ ...prev, defaultMaterialsWarranty: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder="e.g., Manufacturer's warranty applies"
          />
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={saveWarrantySettings}
          disabled={isUpdating}
          className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          {isUpdating && (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
          )}
          <span>{isUpdating ? 'Saving...' : 'Save Warranty Settings'}</span>
        </button>
      </div>
    </div>
  );

  const notificationsContent = (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
      <div className="space-y-6 mb-6">
        {/* Email Quote Accepted */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Quote Accepted Notifications
            </label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Receive email when clients accept your quotes
            </p>
          </div>
          <button
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, emailQuoteAccepted: !prev.emailQuoteAccepted }))}
            disabled={isUpdating}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              formData.emailQuoteAccepted ? 'bg-indigo-600' : 'bg-gray-200 dark:bg-gray-700'
            } disabled:opacity-50`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                formData.emailQuoteAccepted ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* Email Invoice Paid */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Invoice Payment Notifications
            </label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Receive email when invoices are paid
            </p>
          </div>
          <button
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, emailInvoicePaid: !prev.emailInvoicePaid }))}
            disabled={isUpdating}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              formData.emailInvoicePaid ? 'bg-indigo-600' : 'bg-gray-200 dark:bg-gray-700'
            } disabled:opacity-50`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                formData.emailInvoicePaid ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* Email Job Completed */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              Job Completion Notifications
            </label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Receive email summary when jobs are completed
            </p>
          </div>
          <button
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, emailJobCompleted: !prev.emailJobCompleted }))}
            disabled={isUpdating}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              formData.emailJobCompleted ? 'bg-indigo-600' : 'bg-gray-200 dark:bg-gray-700'
            } disabled:opacity-50`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                formData.emailJobCompleted ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={saveNotificationSettings}
          disabled={isUpdating}
          className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          {isUpdating && (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
          )}
          <span>{isUpdating ? 'Saving...' : 'Save Notification Settings'}</span>
        </button>
      </div>
    </div>
  );

  // Define tabs configuration
  const tabs: TabItem[] = [
    {
      id: 'pricing',
      label: 'VAT & Pricing',
      icon: <CurrencyPoundIcon className="w-5 h-5" />,
      content: vatPricingContent,
      color: 'green'
    },
    {
      id: 'automation',
      label: 'Document Automation',
      icon: <ClipboardDocumentCheckIcon className="w-5 h-5" />,
      content: documentAutomationContent,
      color: 'blue'
    },
    {
      id: 'terms',
      label: 'Default Terms',
      icon: <DocumentTextIcon className="w-5 h-5" />,
      content: defaultTermsContent,
      color: 'purple'
    },
    {
      id: 'warranties',
      label: 'Warranties',
      icon: <CalculatorIcon className="w-5 h-5" />,
      content: warrantiesContent,
      color: 'orange'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: <BellIcon className="w-5 h-5" />,
      content: notificationsContent,
      color: 'indigo'
    }
  ];

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Business Settings</h1>
          <p className="text-gray-600 dark:text-gray-400">Configure your business preferences and automation settings with our organized tab system</p>
        </div>

        {/* Settings Content with Tabs */}
        <Tabs 
          tabs={tabs}
          defaultTab="pricing"
          className="w-full"
          variant="default"
        />
      </div>
    </Layout>
  );
} 