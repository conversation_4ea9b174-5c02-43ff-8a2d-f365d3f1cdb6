'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';

interface Invitation {
  id: string;
  email: string;
  role: string;
  status: string;
  expires_at: string;
  personal_message?: string;
  workforce: {
    name: string;
    owner_id: string;
    owner: {
      full_name: string;
      email: string;
    };
  };
}

export default function InvitePage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const token = params.token as string;

  // Debug session state
  useEffect(() => {
    if (user) {
      console.log('Current user session:', { 
        id: user.id, 
        email: user.email,
        aud: user.aud,
        role: user.role
      });
    } else {
      console.log('No user session found');
    }
  }, [user]);

  const [invitation, setInvitation] = useState<Invitation | null>(null);
  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Function to clear only invitation-specific session data (NOT auth tokens)
  const clearInvitationData = async () => {
    try {
      // Only clear invitation-specific session storage
      sessionStorage.removeItem('pendingInviteToken');
      console.log('Cleared invitation-specific session data');
    } catch (err) {
      console.error('Error clearing invitation data:', err);
    }
  };

  // Emergency auth recovery function (use sparingly)
  const emergencyAuthReset = async () => {
    try {
      console.warn('Emergency auth reset initiated');
      // Sign out through Supabase (proper way)
      await supabase.auth.signOut();
      // Clear ONLY Supabase auth-specific keys
      const authKeys = Object.keys(localStorage).filter(key => 
        key.startsWith('sb-') && key.includes('auth-token')
      );
      authKeys.forEach(key => localStorage.removeItem(key));
      
      setError('Authentication reset. Please log in again to continue.');
    } catch (err) {
      console.error('Error in emergency auth reset:', err);
    }
  };

  useEffect(() => {
    if (token) {
      fetchInvitation();
    }
  }, [token]);

  // Handle redirect from login/register
  useEffect(() => {
    if (user) {
      const pendingToken = sessionStorage.getItem('pendingInviteToken');
      if (pendingToken && pendingToken === token) {
        // Clear the stored token
        sessionStorage.removeItem('pendingInviteToken');
        console.log('User authenticated after invitation redirect:', user.email);
        // Ensure invitation is loaded
        if (!invitation) {
          fetchInvitation();
        }
      }
    }
  }, [user, token, invitation]);

  const fetchInvitation = async () => {
    try {
      const response = await fetch(`/api/workforce/invitations/${token}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('This invitation link is invalid or has expired.');
        } else {
          setError('Failed to load invitation details.');
        }
        setLoading(false);
        return;
      }

      const data = await response.json();
      console.log('Invitation data fetched:', data);
      setInvitation(data);
    } catch (err) {
      console.error('Error fetching invitation:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load invitation details.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptInvitation = async () => {
    if (!user || !invitation) {
      setError('You must be logged in to accept this invitation.');
      return;
    }

    // Debug email comparison
    console.log('Email comparison debug:', {
      userEmail: user.email,
      userEmailType: typeof user.email,
      userEmailLength: user.email?.length,
      invitationEmail: invitation.email,
      invitationEmailType: typeof invitation.email,
      invitationEmailLength: invitation.email?.length,
      areEqual: user.email === invitation.email,
      trimmedEqual: user.email?.trim() === invitation.email?.trim(),
      lowerCaseEqual: user.email?.toLowerCase() === invitation.email?.toLowerCase()
    });

    // Check if the invitation email matches the current user's email
    if (user.email !== invitation.email) {
      setError(`This invitation was sent to ${invitation.email}. Please log in with that email address to accept this invitation.`);
      return;
    }

    setAccepting(true);
    setError(null);

    try {
      const response = await fetch(`/api/workforce/invitations/${token}/accept`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to accept invitation.');
        setAccepting(false);
        return;
      }

      setSuccess(true);
      
      // Redirect to teams page after 2 seconds
      setTimeout(() => {
        router.push('/dashboard/teams');
      }, 2000);

    } catch (err) {
      console.error('Error accepting invitation:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to accept invitation.';
      setError(errorMessage);
      setAccepting(false);
    }
  };

  const handleSignUp = () => {
    // Store the invitation token in sessionStorage so we can return after signup
    sessionStorage.setItem('pendingInviteToken', token);
    router.push('/register?redirect=invite');
  };

  const handleSignIn = () => {
    // Store the invitation token in sessionStorage so we can return after signin
    sessionStorage.setItem('pendingInviteToken', token);
    router.push('/login?redirect=invite');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full">
          <div className="bg-white shadow-sm rounded-lg p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-center text-gray-600 mt-4">Loading invitation...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error && !invitation) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full">
          <div className="bg-white shadow-sm rounded-lg p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-6">
              <div className="w-full h-full rounded-full bg-red-100 flex items-center justify-center">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Invalid Invitation</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => router.push('/dashboard')}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full">
          <div className="bg-white shadow-sm rounded-lg p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-6">
              <div className="w-full h-full rounded-full bg-green-100 flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Welcome to the Team!</h1>
            <p className="text-gray-600 mb-6">
              You've successfully joined <strong>{invitation?.workforce.name}</strong>. 
              You'll be redirected to the teams page shortly.
            </p>
            <div className="text-sm text-gray-500">
              Redirecting in a moment...
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full">
          <div className="bg-white shadow-sm rounded-lg p-8">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Team Invitation</h1>
              <p className="text-gray-600">You've been invited to join a team on DeskBelt</p>
            </div>

            {invitation && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-blue-900 mb-2">{invitation.workforce.name}</h3>
                <p className="text-blue-800 text-sm mb-1">
                  <strong>Role:</strong> {invitation.role}
                </p>
                <p className="text-blue-800 text-sm mb-1">
                  <strong>Invited by:</strong> {invitation.workforce.owner.full_name}
                </p>
                <p className="text-blue-800 text-sm">
                  <strong>Email:</strong> {invitation.email}
                </p>
                {invitation.personal_message && (
                  <div className="mt-3 pt-3 border-t border-blue-200">
                    <p className="text-blue-800 text-sm italic">"{invitation.personal_message}"</p>
                  </div>
                )}
              </div>
            )}

            <p className="text-gray-600 mb-6 text-center">
              Sign in or create an account to accept this invitation
            </p>

            <div className="space-y-3">
              <button
                onClick={handleSignIn}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                Sign In
              </button>
              <button
                onClick={handleSignUp}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium py-2 px-4 rounded-md transition-colors"
              >
                Create Account
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full">
        <div className="bg-white shadow-sm rounded-lg p-8">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Team Invitation</h1>
            <p className="text-gray-600">You've been invited to join a team</p>
          </div>

          {invitation && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-blue-900 mb-2">{invitation.workforce.name}</h3>
              <p className="text-blue-800 text-sm mb-1">
                <strong>Role:</strong> {invitation.role}
              </p>
              <p className="text-blue-800 text-sm mb-1">
                <strong>Invited by:</strong> {invitation.workforce.owner.full_name}
              </p>
              <p className="text-blue-800 text-sm">
                <strong>Your email:</strong> {invitation.email}
              </p>
              {invitation.personal_message && (
                <div className="mt-3 pt-3 border-t border-blue-200">
                  <p className="text-blue-800 text-sm italic">"{invitation.personal_message}"</p>
                </div>
              )}
              <div className="mt-3 pt-3 border-t border-blue-200">
                <p className="text-blue-700 text-xs">
                  <strong>Expires:</strong> {new Date(invitation.expires_at).toLocaleDateString()}
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800 text-sm">{error}</p>
              {error.includes('Refresh Token') && (
                <div className="mt-3 pt-3 border-t border-red-200">
                  <p className="text-red-700 text-xs">
                    If you're seeing authentication errors, try clearing your session:
                  </p>
                  <button
                    onClick={clearSessionData}
                    className="mt-2 text-xs bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded border"
                  >
                    Clear Session & Try Again
                  </button>
                </div>
              )}
            </div>
          )}

          <div className="space-y-3">
            <button
              onClick={handleAcceptInvitation}
              disabled={accepting}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center"
            >
              {accepting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Accepting...
                </>
              ) : (
                'Accept Invitation'
              )}
            </button>
            
            <button
              onClick={() => router.push('/dashboard')}
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium py-2 px-4 rounded-md transition-colors"
            >
              Maybe Later
            </button>
          </div>

          <div className="mt-6 pt-6 border-t border-gray-200 text-center">
            <p className="text-xs text-gray-500">
              If you're having trouble, please contact the team owner at{' '}
              <a href={`mailto:${invitation?.workforce.owner.email}`} className="text-blue-600 hover:underline">
                {invitation?.workforce.owner.email}
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}