'use client';

import React from 'react';

export default function DesignSystemPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-16">
          <div className="text-6xl mb-4">🎨</div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Design System
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Interactive showcase coming soon... The component library is being rebuilt.
          </p>
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Design System Foundation
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-600 rounded-lg mx-auto mb-2"></div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">Jobs Blue</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">#1D4ED8</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-600 rounded-lg mx-auto mb-2"></div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">Clients Green</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">#047857</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-600 rounded-lg mx-auto mb-2"></div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">AI Orange</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">#D97706</p>
              </div>
            </div>
              <div className="mt-8">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Typography Scale
                  </h3>
                  <button
                      className="p-1 text-secondary-400 hover:text-secondary-600 dark:text-secondary-500 dark:hover:text-secondary-300 transition-colors border-0 bg-transparent focus:outline-none">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                           stroke="currentColor" aria-hidden="true" data-slot="icon" className="w-5 h-5">
                          <path stroke-linecap="round" stroke-linejoin="round"
                                d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"></path>
                      </svg>
                  </button>
              </div>


              </div>
          </div>
        </div>
      </div>
        );
        };