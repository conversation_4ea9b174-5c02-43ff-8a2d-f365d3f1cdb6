/**
 * API utilities for making requests to the backend API
 */

// Get the API base URL from environment variables
export const getApiBaseUrl = (): string => {
  // In browser environment, use relative paths so Next.js rewrites can handle proxy
  if (typeof window !== 'undefined') {
    return '';
  }
  // In server environment, use full URL
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
};

// Construct full API URL
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  // Ensure endpoint starts with /
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  // If using relative paths (browser), endpoint should already include /api
  if (baseUrl === '') {
    return cleanEndpoint;
  }
  
  // If using full URL (server), append to base URL
  return `${baseUrl}${cleanEndpoint}`;
};