'use client';

import { supabase } from '@/lib/supabase';

/**
 * Session Recovery Utility
 * Provides robust session management and recovery mechanisms
 */

export interface SessionDiagnostics {
  hasValidSession: boolean;
  hasStoredTokens: boolean;
  tokenCount: number;
  sessionExpiry: Date | null;
  userId: string | null;
  email: string | null;
  lastActivity: Date | null;
  browserTabId: string;
  diagnostics: string[];
}

export class AuthRecovery {
  private static readonly RECOVERY_KEY = 'auth-recovery-attempt';
  private static readonly MAX_RECOVERY_ATTEMPTS = 3;
  private static readonly SESSION_CHECK_INTERVAL = 30000; // 30 seconds

  /**
   * Comprehensive session diagnostics
   */
  static async diagnoseSession(): Promise<SessionDiagnostics> {
    const diagnostics: string[] = [];
    const browserTabId = this.generateTabId();

    try {
      // Check current session
      const { data: { session }, error } = await supabase.auth.getSession();
      
      const hasValidSession = !!(session?.user && session.access_token);
      diagnostics.push(`Session valid: ${hasValidSession}`);

      // Check localStorage tokens
      const authKeys = Object.keys(localStorage).filter(key => 
        key.startsWith('sb-') && (key.includes('auth-token') || key.includes('session'))
      );
      
      const hasStoredTokens = authKeys.length > 0;
      diagnostics.push(`Stored tokens: ${authKeys.length} keys found`);

      // Session details
      const sessionExpiry = session?.expires_at ? new Date(session.expires_at * 1000) : null;
      const userId = session?.user?.id || null;
      const email = session?.user?.email || null;

      if (sessionExpiry) {
        const isExpired = sessionExpiry <= new Date();
        diagnostics.push(`Token expires: ${sessionExpiry.toLocaleString()} (expired: ${isExpired})`);
      }

      // Check for conflicting sessions
      const conflictingKeys = Object.keys(localStorage).filter(key => 
        key.includes('supabase') && !key.startsWith('sb-')
      );
      
      if (conflictingKeys.length > 0) {
        diagnostics.push(`Warning: ${conflictingKeys.length} potentially conflicting storage keys found`);
      }

      // Recent activity check
      const lastActivity = this.getLastActivity();
      if (lastActivity) {
        diagnostics.push(`Last activity: ${lastActivity.toLocaleString()}`);
      }

      return {
        hasValidSession,
        hasStoredTokens,
        tokenCount: authKeys.length,
        sessionExpiry,
        userId,
        email,
        lastActivity,
        browserTabId,
        diagnostics
      };

    } catch (error) {
      diagnostics.push(`Error during diagnosis: ${error}`);
      return {
        hasValidSession: false,
        hasStoredTokens: false,
        tokenCount: 0,
        sessionExpiry: null,
        userId: null,
        email: null,
        lastActivity: null,
        browserTabId,
        diagnostics
      };
    }
  }

  /**
   * Intelligent session recovery with progressive escalation
   */
  static async recoverSession(): Promise<{
    success: boolean;
    method: string;
    error?: string;
    diagnostics: SessionDiagnostics;
  }> {
    const initialDiagnostics = await this.diagnoseSession();
    
    // Check recovery attempt count
    const attempts = this.getRecoveryAttempts();
    if (attempts >= this.MAX_RECOVERY_ATTEMPTS) {
      return {
        success: false,
        method: 'max_attempts_reached',
        error: 'Maximum recovery attempts reached. Please log in manually.',
        diagnostics: initialDiagnostics
      };
    }

    this.incrementRecoveryAttempts();

    try {
      // Method 1: Simple session refresh
      if (initialDiagnostics.hasStoredTokens) {
        console.log('🔄 Attempting session refresh...');
        const { data, error } = await supabase.auth.refreshSession();
        
        if (data?.session?.user) {
          this.resetRecoveryAttempts();
          this.updateLastActivity();
          return {
            success: true,
            method: 'session_refresh',
            diagnostics: await this.diagnoseSession()
          };
        }
      }

      // Method 2: Re-initialize session
      console.log('🔄 Attempting session re-initialization...');
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (session?.user) {
        this.resetRecoveryAttempts();
        this.updateLastActivity();
        return {
          success: true,
          method: 'session_reinit',
          diagnostics: await this.diagnoseSession()
        };
      }

      // Method 3: Clean restart (last resort)
      console.log('🔄 Attempting clean restart...');
      await this.cleanRestart();
      
      return {
        success: false,
        method: 'clean_restart',
        error: 'Session cleared. Please log in again.',
        diagnostics: await this.diagnoseSession()
      };

    } catch (error) {
      return {
        success: false,
        method: 'recovery_failed',
        error: `Recovery failed: ${error}`,
        diagnostics: initialDiagnostics
      };
    }
  }

  /**
   * Clean restart - removes corrupted tokens but preserves user experience
   */
  static async cleanRestart(): Promise<void> {
    try {
      // Sign out properly through Supabase first
      await supabase.auth.signOut();
      
      // Wait for signout to complete
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Remove only authentication-related tokens
      const authKeys = Object.keys(localStorage).filter(key => 
        (key.startsWith('sb-') && key.includes('auth-token')) ||
        (key.startsWith('sb-') && key.includes('session'))
      );
      
      authKeys.forEach(key => {
        console.log(`Removing corrupted auth key: ${key}`);
        localStorage.removeItem(key);
      });

      // Clear session storage auth data
      sessionStorage.removeItem('supabase.auth.token');
      
      this.resetRecoveryAttempts();
      console.log('✅ Clean restart completed');
      
    } catch (error) {
      console.error('Error during clean restart:', error);
    }
  }

  /**
   * Preventive session maintenance
   */
  static startSessionMaintenance(): void {
    // Check session health periodically
    setInterval(async () => {
      const diagnostics = await this.diagnoseSession();
      
      if (!diagnostics.hasValidSession && diagnostics.hasStoredTokens) {
        console.warn('⚠️ Detected session inconsistency, attempting recovery...');
        await this.recoverSession();
      }
      
      this.updateLastActivity();
    }, this.SESSION_CHECK_INTERVAL);

    // Update activity on user interaction
    ['click', 'keydown', 'scroll', 'mousemove'].forEach(event => {
      document.addEventListener(event, this.throttledActivityUpdate, { passive: true });
    });
  }

  /**
   * Utility methods
   */
  private static generateTabId(): string {
    return `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private static getRecoveryAttempts(): number {
    const stored = localStorage.getItem(this.RECOVERY_KEY);
    return stored ? parseInt(stored, 10) : 0;
  }

  private static incrementRecoveryAttempts(): void {
    const current = this.getRecoveryAttempts();
    localStorage.setItem(this.RECOVERY_KEY, (current + 1).toString());
  }

  private static resetRecoveryAttempts(): void {
    localStorage.removeItem(this.RECOVERY_KEY);
  }

  private static updateLastActivity(): void {
    localStorage.setItem('last-activity', new Date().toISOString());
  }

  private static getLastActivity(): Date | null {
    const stored = localStorage.getItem('last-activity');
    return stored ? new Date(stored) : null;
  }

  private static throttledActivityUpdate = this.throttle(() => {
    this.updateLastActivity();
  }, 60000); // Update at most once per minute

  private static throttle(func: Function, delay: number) {
    let timeoutId: NodeJS.Timeout;
    let lastExecTime = 0;
    return function (...args: any[]) {
      const currentTime = Date.now();
      if (currentTime - lastExecTime > delay) {
        func.apply(null, args);
        lastExecTime = currentTime;
      } else {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(null, args), delay);
      }
    };
  }
}

/**
 * Debug utility for session issues
 */
export const debugSession = async (): Promise<void> => {
  console.group('🔍 Session Debug Report');
  
  const diagnostics = await AuthRecovery.diagnoseSession();
  
  console.log('Session Status:', diagnostics.hasValidSession ? '✅ Valid' : '❌ Invalid');
  console.log('Stored Tokens:', diagnostics.hasStoredTokens ? `✅ ${diagnostics.tokenCount} found` : '❌ None');
  console.log('User ID:', diagnostics.userId || 'None');
  console.log('Email:', diagnostics.email || 'None');
  
  if (diagnostics.sessionExpiry) {
    const isExpired = diagnostics.sessionExpiry <= new Date();
    console.log('Session Expiry:', diagnostics.sessionExpiry.toLocaleString(), isExpired ? '⚠️ EXPIRED' : '✅ Valid');
  }
  
  console.log('Tab ID:', diagnostics.browserTabId);
  
  console.group('Detailed Diagnostics:');
  diagnostics.diagnostics.forEach(item => console.log('•', item));
  console.groupEnd();
  
  // Show localStorage keys for debugging
  const allKeys = Object.keys(localStorage);
  const authKeys = allKeys.filter(key => key.includes('auth') || key.includes('supabase') || key.startsWith('sb-'));
  
  console.group('Storage Keys:');
  console.log('All keys:', allKeys.length);
  console.log('Auth-related:', authKeys);
  console.groupEnd();
  
  console.groupEnd();
};

// Export for browser console debugging
if (typeof window !== 'undefined') {
  (window as any).debugSession = debugSession;
  (window as any).AuthRecovery = AuthRecovery;
}