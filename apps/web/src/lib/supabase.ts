import { createClient } from '@supabase/supabase-js'

// Use environment variables for Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://nwwynkkigyahrjumqmrj.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'sb_publishable_yM7FhOlmtFzF_OmntM6vww_7L2kSKZD'

console.log('🔍 Frontend Supabase Configuration:')
console.log('   URL:', supabaseUrl)
console.log('   Anon Key present:', !!supabaseAnonKey)
console.log('   Anon Key format:', supabaseAnonKey?.startsWith('sb_publishable_') ? 'NEW FORMAT ✅' : 'OLD FORMAT ❌')

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database tables
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          phone: string | null
          company_name: string | null
          address: string | null
          website: string | null
          country: string | null
          role: 'tradesperson' | 'team_member' | 'admin' | 'super_admin'
          created_at: string
          updated_at: string
          last_login: string | null
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          phone?: string | null
          company_name?: string | null
          address?: string | null
          website?: string | null
          country?: string | null
          role?: 'tradesperson' | 'team_member' | 'admin' | 'super_admin'
          created_at?: string
          updated_at?: string
          last_login?: string | null
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          phone?: string | null
          company_name?: string | null
          address?: string | null
          website?: string | null
          country?: string | null
          role?: 'tradesperson' | 'team_member' | 'admin' | 'super_admin'
          created_at?: string
          updated_at?: string
          last_login?: string | null
        }
      }
      clients: {
        Row: {
          id: string
          name: string
          address: string | null
          email: string | null
          phone: string | null
          rating: number
          notes: string | null
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          address?: string | null
          email?: string | null
          phone?: string | null
          rating?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          address?: string | null
          email?: string | null
          phone?: string | null
          rating?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      jobs: {
        Row: {
          id: string
          title: string
          description: string | null
          client_id: string
          created_by: string
          team_id: string | null
          scheduled_at: string | null
          status: 'new' | 'quoted' | 'in_progress' | 'on_hold' | 'completed' | 'archived'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          client_id: string
          created_by: string
          team_id?: string | null
          scheduled_at?: string | null
          status?: 'new' | 'quoted' | 'in_progress' | 'on_hold' | 'completed' | 'archived'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          client_id?: string
          created_by?: string
          team_id?: string | null
          scheduled_at?: string | null
          status?: 'new' | 'quoted' | 'in_progress' | 'on_hold' | 'completed' | 'archived'
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

export default supabase 