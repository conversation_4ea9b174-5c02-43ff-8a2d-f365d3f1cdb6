'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface DrawerContextType {
  // Job drawer state
  isNewJobDrawerOpen: boolean;
  openNewJobDrawer: (preSelectedClientId?: string) => void;
  closeNewJobDrawer: () => void;
  preSelectedClientId: string | null;
  
  // Client drawer state
  isNewClientDrawerOpen: boolean;
  openNewClientDrawer: () => void;
  closeNewClientDrawer: () => void;
  
  // Request review drawer state
  isRequestReviewDrawerOpen: boolean;
  openRequestReviewDrawer: (clientData: { id: string; name: string; email?: string; phone?: string }) => void;
  closeRequestReviewDrawer: () => void;
  requestReviewClientData: { id: string; name: string; email?: string; phone?: string } | null;
  
  // Edit client drawer state
  isEditClientDrawerOpen: boolean;
  openEditClientDrawer: (clientData: { id: string; name: string; business_name?: string; address: string; phone?: string; email?: string }) => void;
  closeEditClientDrawer: () => void;
  editClientData: { id: string; name: string; business_name?: string; address: string; phone?: string; email?: string } | null;
  
  // Ask Dex drawer state
  isAskDexDrawerOpen: boolean;
  openAskDexDrawer: (context?: any) => void;
  closeAskDexDrawer: () => void;
  askDexContext: any;
  
  // Send email modal state
  isSendEmailModalOpen: boolean;
  openSendEmailModal: (jobData: any) => void;
  closeSendEmailModal: () => void;
  emailJobData: any;
  
  // Notifications drawer state
  isNotificationsDrawerOpen: boolean;
  openNotificationsDrawer: () => void;
  closeNotificationsDrawer: () => void;
  
  // Refresh callbacks
  onClientCreated?: (client?: any) => void;
  setOnClientCreated: (callback: () => void) => void;

  onJobCreated?: () => void;
  setOnJobCreated: (callback: () => void) => void;

  isWorkforceDrawerOpen: boolean;
  openWorkforceDrawer: () => void;
  closeWorkforceDrawer: () => void;
  
  // Invoice drawer state
  isCreateInvoiceDrawerOpen: boolean;
  openCreateInvoiceDrawer: (jobData?: any) => void;
  closeCreateInvoiceDrawer: () => void;
  createInvoiceJobData: any;
  
  // Invoice preview modal state
  isInvoicePreviewModalOpen: boolean;
  openInvoicePreviewModal: (invoiceData: any) => void;
  closeInvoicePreviewModal: () => void;
  previewInvoiceData: any;
  
  // Delete confirmation modal state
  isDeleteConfirmationModalOpen: boolean;
  openDeleteConfirmationModal: (data: { title: string; message: string; itemName?: string; onConfirm: () => void }) => void;
  closeDeleteConfirmationModal: () => void;
  deleteConfirmationData: { title: string; message: string; itemName?: string; onConfirm: () => void } | null;
  
  // Job details drawer state
  isJobDetailsDrawerOpen: boolean;
  openJobDetailsDrawer: (jobData: any) => void;
  closeJobDetailsDrawer: () => void;
  jobDetailsData: any;

  // Schedule job drawer state
  isScheduleJobDrawerOpen: boolean;
  openScheduleJobDrawer: (jobId?: string) => void;
  closeScheduleJobDrawer: () => void;
  scheduleJobId: string | null;
}

const DrawerContext = createContext<DrawerContextType | undefined>(undefined);

export const useDrawer = () => {
  const context = useContext(DrawerContext);
  if (context === undefined) {
    throw new Error('useDrawer must be used within a DrawerProvider');
  }
  return context;
};

interface DrawerProviderProps {
  children: ReactNode;
}

export const DrawerProvider: React.FC<DrawerProviderProps> = ({ children }) => {
  const [isNewJobDrawerOpen, setIsNewJobDrawerOpen] = useState(false);
  const [preSelectedClientId, setPreSelectedClientId] = useState<string | null>(null);
  const [isNewClientDrawerOpen, setIsNewClientDrawerOpen] = useState(false);
  const [isRequestReviewDrawerOpen, setIsRequestReviewDrawerOpen] = useState(false);
  const [requestReviewClientData, setRequestReviewClientData] = useState<{ id: string; name: string; email?: string; phone?: string } | null>(null);
  const [isEditClientDrawerOpen, setIsEditClientDrawerOpen] = useState(false);
  const [editClientData, setEditClientData] = useState<{ id: string; name: string; business_name?: string; address: string; phone?: string; email?: string } | null>(null);
  const [isAskDexDrawerOpen, setIsAskDexDrawerOpen] = useState(false);
  const [askDexContext, setAskDexContext] = useState<any>(null);
  const [isSendEmailModalOpen, setIsSendEmailModalOpen] = useState(false);
  const [emailJobData, setEmailJobData] = useState<any>(null);
  const [isNotificationsDrawerOpen, setIsNotificationsDrawerOpen] = useState(false);
  const [isWorkforceDrawerOpen, setIsWorkforceDrawerOpen] = useState(false);
  const [isCreateInvoiceDrawerOpen, setIsCreateInvoiceDrawerOpen] = useState(false);
  const [createInvoiceJobData, setCreateInvoiceJobData] = useState<any>(null);
  const [isInvoicePreviewModalOpen, setIsInvoicePreviewModalOpen] = useState(false);
  const [previewInvoiceData, setPreviewInvoiceData] = useState<any>(null);
  const [isDeleteConfirmationModalOpen, setIsDeleteConfirmationModalOpen] = useState(false);
  const [deleteConfirmationData, setDeleteConfirmationData] = useState<{ title: string; message: string; itemName?: string; onConfirm: () => void } | null>(null);
  const [isJobDetailsDrawerOpen, setIsJobDetailsDrawerOpen] = useState(false);
  const [jobDetailsData, setJobDetailsData] = useState<any>(null);
  const [isScheduleJobDrawerOpen, setIsScheduleJobDrawerOpen] = useState(false);
  const [scheduleJobId, setScheduleJobId] = useState<string | null>(null);
  // Use a ref so updating the callback does NOT trigger re-renders (prevents infinite update loops)
  const onClientCreatedRef = React.useRef<(() => void) | undefined>(undefined);
  const onJobCreatedRef = React.useRef<(() => void) | undefined>(undefined);

  const openNewJobDrawer = (clientId?: string) => {
    setPreSelectedClientId(clientId || null);
    setIsNewJobDrawerOpen(true);
  };
  const closeNewJobDrawer = () => {
    setIsNewJobDrawerOpen(false);
    setPreSelectedClientId(null);
  };
  
  const openNewClientDrawer = () => setIsNewClientDrawerOpen(true);
  const closeNewClientDrawer = () => setIsNewClientDrawerOpen(false);

  const openRequestReviewDrawer = (clientData: { id: string; name: string; email?: string; phone?: string }) => {
    setRequestReviewClientData(clientData);
    setIsRequestReviewDrawerOpen(true);
  };
  const closeRequestReviewDrawer = () => {
    setRequestReviewClientData(null);
    setIsRequestReviewDrawerOpen(false);
  };

  const openEditClientDrawer = (clientData: { id: string; name: string; business_name?: string; address: string; phone?: string; email?: string }) => {
    setEditClientData(clientData);
    setIsEditClientDrawerOpen(true);
  };
  const closeEditClientDrawer = () => {
    setEditClientData(null);
    setIsEditClientDrawerOpen(false);
  };

  const openAskDexDrawer = (context?: any) => {
    setAskDexContext(context || null);
    setIsAskDexDrawerOpen(true);
  };
  const closeAskDexDrawer = () => {
    setAskDexContext(null);
    setIsAskDexDrawerOpen(false);
  };

  const openSendEmailModal = (jobData: any) => {
    setEmailJobData(jobData);
    setIsSendEmailModalOpen(true);
  };
  const closeSendEmailModal = () => {
    setEmailJobData(null);
    setIsSendEmailModalOpen(false);
  };

  const openNotificationsDrawer = () => setIsNotificationsDrawerOpen(true);
  const closeNotificationsDrawer = () => setIsNotificationsDrawerOpen(false);

  const openWorkforceDrawer = () => setIsWorkforceDrawerOpen(true);
  const closeWorkforceDrawer = () => setIsWorkforceDrawerOpen(false);

  const openCreateInvoiceDrawer = (jobData?: any) => {
    setCreateInvoiceJobData(jobData || null);
    setIsCreateInvoiceDrawerOpen(true);
  };
  const closeCreateInvoiceDrawer = () => {
    setCreateInvoiceJobData(null);
    setIsCreateInvoiceDrawerOpen(false);
  };

  const openInvoicePreviewModal = (invoiceData: any) => {
    setPreviewInvoiceData(invoiceData);
    setIsInvoicePreviewModalOpen(true);
  };
  const closeInvoicePreviewModal = () => {
    setPreviewInvoiceData(null);
    setIsInvoicePreviewModalOpen(false);
  };

  const openDeleteConfirmationModal = (data: { title: string; message: string; itemName?: string; onConfirm: () => void }) => {
    setDeleteConfirmationData(data);
    setIsDeleteConfirmationModalOpen(true);
  };
  const closeDeleteConfirmationModal = () => {
    setDeleteConfirmationData(null);
    setIsDeleteConfirmationModalOpen(false);
  };

  const openJobDetailsDrawer = (jobData: any) => {
    setJobDetailsData(jobData);
    setIsJobDetailsDrawerOpen(true);
  };
  const closeJobDetailsDrawer = () => {
    setJobDetailsData(null);
    setIsJobDetailsDrawerOpen(false);
  };

  const openScheduleJobDrawer = (jobId?: string) => {
    setScheduleJobId(jobId || null);
    setIsScheduleJobDrawerOpen(true);
  };
  const closeScheduleJobDrawer = () => {
    setScheduleJobId(null);
    setIsScheduleJobDrawerOpen(false);
  };

  // Stable wrapper that calls the latest registered callback
  const handleClientCreated = React.useCallback((client: any) => {
    if (onClientCreatedRef.current) {
      try {
        onClientCreatedRef.current();
      } catch (err) {
        console.error('onClientCreated callback threw:', err);
      }
    }
  }, []);

  const handleJobCreated = React.useCallback(() => {
    if (onJobCreatedRef.current) {
      try {
        onJobCreatedRef.current();
      } catch (err) {
        console.error('onJobCreated callback threw:', err);
      }
    }
  }, []);

  const value: DrawerContextType = {
    isNewJobDrawerOpen,
    openNewJobDrawer,
    closeNewJobDrawer,
    preSelectedClientId,
    isNewClientDrawerOpen,
    openNewClientDrawer,
    closeNewClientDrawer,
    isRequestReviewDrawerOpen,
    openRequestReviewDrawer,
    closeRequestReviewDrawer,
    requestReviewClientData,
    isEditClientDrawerOpen,
    openEditClientDrawer,
    closeEditClientDrawer,
    editClientData,
    isAskDexDrawerOpen,
    openAskDexDrawer,
    closeAskDexDrawer,
    askDexContext,
    isSendEmailModalOpen,
    openSendEmailModal,
    closeSendEmailModal,
    emailJobData,
    isNotificationsDrawerOpen,
    openNotificationsDrawer,
    closeNotificationsDrawer,
    onClientCreated: handleClientCreated,
    setOnClientCreated: (callback: () => void) => {
      onClientCreatedRef.current = callback;
    },
    onJobCreated: handleJobCreated,
    setOnJobCreated: (callback: () => void) => {
      onJobCreatedRef.current = callback;
    },
    isWorkforceDrawerOpen,
    openWorkforceDrawer,
    closeWorkforceDrawer,
    isCreateInvoiceDrawerOpen,
    openCreateInvoiceDrawer,
    closeCreateInvoiceDrawer,
    createInvoiceJobData,
    isInvoicePreviewModalOpen,
    openInvoicePreviewModal,
    closeInvoicePreviewModal,
    previewInvoiceData,
    isDeleteConfirmationModalOpen,
    openDeleteConfirmationModal,
    closeDeleteConfirmationModal,
    deleteConfirmationData,
    isJobDetailsDrawerOpen,
    openJobDetailsDrawer,
    closeJobDetailsDrawer,
    jobDetailsData,
    isScheduleJobDrawerOpen,
    openScheduleJobDrawer,
    closeScheduleJobDrawer,
    scheduleJobId,
  };

  return (
    <DrawerContext.Provider value={value}>
      {children}
    </DrawerContext.Provider>
  );
}; 