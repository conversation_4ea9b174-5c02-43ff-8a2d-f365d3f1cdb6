'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { Toast, ToastType, ToastComponent } from '@/components/Toast';

interface ToastContextType {
  showToast: (toast: Omit<Toast, 'id'>) => void;
  showSuccess: (title: string, message?: string, duration?: number) => void;
  showError: (title: string, message?: string, duration?: number) => void;
  showInfo: (title: string, message?: string, duration?: number) => void;
  showWarning: (title: string, message?: string, duration?: number) => void;
  showApiError: (title: string, errorData: any, duration?: number) => void;
  removeToast: (id: string) => void;
  clearAllToasts: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: React.ReactNode;
  maxToasts?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ 
  children, 
  maxToasts = 5,
  position = 'top-right'
}) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const generateId = () => `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const showToast = useCallback((toast: Omit<Toast, 'id'>) => {
    const newToast: Toast = {
      ...toast,
      id: generateId(),
      duration: toast.duration || 5000
    };

    setToasts(prevToasts => {
      const updatedToasts = [newToast, ...prevToasts];
      // Limit the number of toasts
      return updatedToasts.slice(0, maxToasts);
    });
  }, [maxToasts]);

  const showSuccess = useCallback((title: string, message?: string, duration?: number) => {
    showToast({ type: 'success', title, message, duration });
  }, [showToast]);

  const showError = useCallback((title: string, message?: string, duration?: number) => {
    showToast({ type: 'error', title, message, duration: duration || 7000 }); // Errors stay longer
  }, [showToast]);

  const showInfo = useCallback((title: string, message?: string, duration?: number) => {
    showToast({ type: 'info', title, message, duration });
  }, [showToast]);

  const showWarning = useCallback((title: string, message?: string, duration?: number) => {
    showToast({ type: 'warning', title, message, duration });
  }, [showToast]);

  const showApiError = useCallback((title: string, errorData: any, duration?: number) => {
    // Construct comprehensive error message from API response
    let errorMessage = errorData.error || errorData.message || 'An unexpected error occurred.';
    
    // Add details if available
    if (errorData.details) {
      errorMessage += `\n\nDetails: ${errorData.details}`;
    }
    
    // Add error code if available
    if (errorData.code) {
      errorMessage += `\nError Code: ${errorData.code}`;
    }
    
    // Add hint if available (common in database errors)
    if (errorData.hint) {
      errorMessage += `\nHint: ${errorData.hint}`;
    }
    
    showToast({ 
      type: 'error', 
      title, 
      message: errorMessage, 
      duration: duration || 8000 // Longer duration for detailed errors
    });
  }, [showToast]);

  const removeToast = useCallback((id: string) => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
  }, []);

  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  const getPositionClasses = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      default:
        return 'top-4 right-4';
    }
  };

  return (
    <ToastContext.Provider value={{
      showToast,
      showSuccess,
      showError,
      showInfo,
      showWarning,
      showApiError,
      removeToast,
      clearAllToasts
    }}>
      {children}
      
      {/* Toast Container */}
      {toasts.length > 0 && (
        <div 
          className={`fixed z-50 pointer-events-none ${getPositionClasses()}`}
          aria-live="polite"
          aria-label="Notifications"
        >
          <div className="flex flex-col space-y-3 pointer-events-auto">
            {toasts.map((toast) => (
              <ToastComponent
                key={toast.id}
                toast={toast}
                onClose={removeToast}
              />
            ))}
          </div>
        </div>
      )}
    </ToastContext.Provider>
  );
};