'use client'

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { User, AuthError } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { AuthRecovery } from '@/lib/authRecovery'

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  signUp: (email: string, password: string) => Promise<{ error?: string }>
  signIn: (email: string, password: string) => Promise<{ error?: string }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ error?: string }>
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error?: string }>
  recoverSession: () => Promise<{ success: boolean; error?: string }>
}

interface UserProfile {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  company_name: string | null
  address: string | null
  website: string | null
  country: string | null
  role: 'tradesperson' | 'team_member' | 'admin' | 'super_admin'
  created_at: string
  updated_at: string
  last_login: string | null
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  const createUserProfile = async (user: User) => {
    try {
      console.log('Creating user profile via API for:', user.email);

      const response = await fetch('/api/create-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          email: user.email!,
          full_name: user.user_metadata?.full_name || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error creating profile:', errorData);
        throw new Error(errorData.error || 'Failed to create profile');
      }

      const data = await response.json();
      console.log('User profile created successfully via API:', data);
      return data.profile;
    } catch (error) {
      console.error('Failed to create user profile via API:', error);
      throw error;
    }
  }

  const fetchProfile = useCallback(async (userId: string) => {
    try {
      console.log('🔍 AuthContext: Fetching profile via API for user:', userId);

      // Get session first and validate it
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        console.error('❌ Session error:', sessionError);
        setLoading(false);
        return;
      }

      if (!sessionData.session?.access_token) {
        console.error('❌ No access token available');
        setLoading(false);
        return;
      }

      console.log('🔑 Using access token for API request');

      // Use API endpoint instead of direct database access to bypass RLS
      const response = await fetch('/api/user/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionData.session.access_token}`
        }
      });

      console.log('📡 API Response status:', response.status);

      if (!response.ok) {
        if (response.status === 404) {
          // Profile not found, try to create one
          console.log('User profile not found via API, attempting to create...');
          if (sessionData.session?.user) {
            await createUserProfile(sessionData.session.user);
            // Retry fetching the profile via API
            const retryResponse = await fetch('/api/user/profile', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${sessionData.session.access_token}`
              }
            });

            if (!retryResponse.ok) {
              console.error('❌ Profile retry fetch via API failed:', retryResponse.status);
              setLoading(false);
              return;
            }

            const retryResult = await retryResponse.json();
            console.log('✅ Profile created and fetched successfully via API');
            setProfile(retryResult.profile);
            setLoading(false);
            return;
          } else {
            console.log('❌ No session available for profile creation');
            setLoading(false);
            return;
          }
        } else {
          // Get response text for better error debugging
          const errorText = await response.text();
          console.error('❌ API profile fetch failed with status:', response.status, 'Response:', errorText);
          setLoading(false);
          return;
        }
      }

      const result = await response.json();
      console.log('✅ Profile fetched successfully via API for:', result.profile.email);
      setProfile(result.profile);
      setLoading(false);
    } catch (error) {
      console.error('❌ Profile fetch error:', error);
      setLoading(false);
    }
  }, [])

  useEffect(() => {
    let isMounted = true

    const initAuth = async () => {
      try {
        // Get initial session
        const { data: { session } } = await supabase.auth.getSession()
        
        if (isMounted) {
          if (session?.user) {
            setUser(session.user)
            await fetchProfile(session.user.id)
          } else {
            setUser(null)
            setLoading(false)
          }
        }
      } catch (error) {
        console.error('Auth init error:', error)
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    initAuth()

    // Auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!isMounted) return;

      // Only clear user data on an explicit sign-out event.
      // This prevents the user from being logged out on window focus changes.
      if (event === 'SIGNED_OUT') {
        setUser(null);
        setProfile(null);
        setLoading(false);
        return;
      }

      // For any other event (SIGNED_IN, TOKEN_REFRESHED, INITIAL_SESSION, etc.),
      // if we have a session, we ensure the user is set.
      if (session?.user) {
        setUser(session.user);
        if (!profile || profile.id !== session.user.id) {
          try {
            await fetchProfile(session.user.id);
          } catch (error) {
            console.error('Profile fetch failed during auth state change:', error);
            // Even if profile fetch fails, ensure loading state is resolved
            setLoading(false);
          }
        } else {
          setLoading(false);
        }
      } else {
        // Only clear user if this is not a TOKEN_REFRESHED event
        if (event !== 'TOKEN_REFRESHED') {
          setUser(null);
          setProfile(null);
          setLoading(false);
        }
      }
    });

    return () => {
      isMounted = false
      subscription.unsubscribe()
    }
  }, [fetchProfile])


  const signUp = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({ email, password })
      if (error) return { error: error.message }

      if (data.user) {
        try {
          await createUserProfile(data.user);
          console.log('User profile created during signup');
        } catch (profileError) {
          console.error('Failed to create profile during signup:', profileError);
          return { error: 'Failed to set up your account. Please try again.' }
        }
      }
      return {}
    } catch (error) {
      return { error: 'An unexpected error occurred' }
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        return { error: error.message }
      }

      if (data.user) {
        // Check user profile via API instead of direct database access
        console.log('🔍 SignIn: Checking user profile via API');

        try {
          const profileResponse = await fetch('/api/user/profile', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${data.session?.access_token}`
            }
          });

          console.log('📡 SignIn: Profile check response status:', profileResponse.status);

          if (profileResponse.status === 404) {
            // Profile not found, create it
            console.log('User profile not found during sign-in, creating...');
            try {
              const newProfile = await createUserProfile(data.user);
              console.log('✅ User profile created during sign-in');
            } catch (createError) {
              console.error('❌ Failed to create profile during sign-in:', createError);
              await supabase.auth.signOut()
              return {
                error: 'Failed to set up your account. Please try again or contact support.'
              }
            }
          } else if (!profileResponse.ok) {
            const errorText = await profileResponse.text();
            console.error('❌ Profile API check failed with status:', profileResponse.status, 'Response:', errorText);
            await supabase.auth.signOut()
            return {
              error: 'Failed to verify account status. Please try again.'
            }
          } else {
            const profileResult = await profileResponse.json();
            console.log('✅ User profile verified via API for:', profileResult.profile.email);
          }
        } catch (profileCheckError) {
          console.error('❌ Profile check error during sign-in:', profileCheckError);
          await supabase.auth.signOut()
          return {
            error: 'Failed to verify account status. Please try again.'
          }
        }

        // Note: No status field in users table, all authenticated users are considered active
        // Status checking removed as the database schema doesn't include this field

        setUser(data.user)
      }

      return {}
    } catch (error) {
      return { error: 'An unexpected error occurred' }
    }
  }

  const signOut = async () => {
    await supabase.auth.signOut()
  }

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email)
      return error ? { error: error.message } : {}
    } catch (error) {
      return { error: 'An unexpected error occurred' }
    }
  }

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return { error: 'Not authenticated' }

    try {
      console.log('🔍 AuthContext: Updating profile via API');

      // Get session first and validate it
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        console.error('❌ Session error during profile update:', sessionError);
        return { error: 'Authentication error. Please sign in again.' };
      }

      if (!sessionData.session?.access_token) {
        console.error('❌ No access token available for profile update');
        return { error: 'Authentication error. Please sign in again.' };
      }

      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionData.session.access_token}`
        },
        body: JSON.stringify(updates)
      });

      console.log('📡 Profile update response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: errorText };
        }
        console.error('❌ Profile update API failed:', response.status, errorData);
        return { error: errorData.error || 'Profile update failed' };
      }

      const result = await response.json();
      console.log('✅ Profile updated successfully via API');

      // Update local profile state
      setProfile(result.profile);

      return {}
    } catch (error) {
      console.error('❌ Profile update error:', error);
      return { error: 'An unexpected error occurred' }
    }
  }

  const recoverSession = async () => {
    try {
      console.log('🔄 Starting session recovery...');
      const result = await AuthRecovery.recoverSession();
      
      if (result.success) {
        console.log('✅ Session recovered successfully via:', result.method);
        // Refresh auth state
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          setUser(session.user);
          await fetchProfile(session.user.id);
        }
        return { success: true };
      } else {
        console.log('❌ Session recovery failed:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Session recovery error:', error);
      return { success: false, error: 'Recovery failed' };
    }
  }

  return (
    <AuthContext.Provider value={{
      user,
      profile,
      loading,
      signUp,
      signIn,
      signOut,
      resetPassword,
      updateProfile,
      recoverSession,
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext 