export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

// DeskBelt specific types
export * from './Job';
export * from './Client';
export * from './Team';
export * from './Document';
export * from './Chat';
export * from './Notification';

export interface Project {
  id: string
  user_id: string
  client_id: string
  name: string
  description?: string
  status: 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled'
  start_date?: string
  end_date?: string
  budget?: number
  hourly_rate?: number
  created_at: string
  updated_at: string
}

export interface Invoice {
  id: string
  user_id: string
  client_id: string
  project_id?: string
  invoice_number: string
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  issue_date: string
  due_date: string
  subtotal: number
  tax_amount: number
  total_amount: number
  notes?: string
  created_at: string
  updated_at: string
  project?: Project
}

export interface InvoiceItem {
  id: string
  invoice_id: string
  description: string
  quantity: number
  rate: number
  amount: number
  created_at: string
}

export interface TimeEntry {
  id: string
  user_id: string
  project_id: string
  description?: string
  start_time: string
  end_time?: string
  duration?: number
  hourly_rate?: number
  is_billable: boolean
  created_at: string
  updated_at: string
  project?: Project
} 