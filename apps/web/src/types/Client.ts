export interface Client {
  id: string;
  name: string;
  business_name?: string | null;
  address?: string | null;
  email?: string | null;
  phone?: string | null;
  website?: string | null;
  rating: number; // 0 to 5
  totalJobs: number;
  activeJobs: number;
  created_at: string;
}

export interface ClientNote {
  id: string;
  author: { 
    id: string; 
    full_name: string; 
    role: string; 
  };
  message: string;
  created_at: string;
}

export interface ClientDetails extends Client {
  notes: ClientNote[];
}

// For new client creation
export interface CreateClientData {
  name: string;
  business_name?: string;
  address?: string;
  email?: string;
  phone?: string;
  website?: string;
  rating?: number;
  notes?: string;
}

// For AI parsing response
export interface ParsedClientData {
  name?: string;
  business_name?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  rating?: number;
  notes?: string;
} 