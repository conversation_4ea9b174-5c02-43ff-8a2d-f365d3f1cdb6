export type NotificationType = 
  | 'info' 
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'job_assigned' 
  | 'team_invite' 
  | 'quote_accepted' 
  | 'contract_signed' 
  | 'payment_received';

export interface Notification {
  id: string;
  user_id: string;
  type: NotificationType;
  title: string;
  message: string;
  action_url?: string;
  icon?: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
  expires_at?: string;
}

export interface NotificationPreferences {
  id: string;
  user_id: string;
  email_notifications: boolean;
  push_notifications: boolean;
  in_app_notifications: boolean;
  job_updates: boolean;
  team_updates: boolean;
  payment_updates: boolean;
  marketing_emails: boolean;
  quiet_hours_start: string; // TIME format "HH:MM"
  quiet_hours_end: string;   // TIME format "HH:MM"
  timezone: string;
  created_at: string;
  updated_at: string;
}

export interface CreateNotificationData {
  type: NotificationType;
  title: string;
  message: string;
  action_url?: string;
  icon?: string;
  expires_at?: string;
}

export interface NotificationBannerProps {
  message: string;
  type: NotificationType;
  duration?: number; // Auto-dismiss duration in milliseconds
  onDismiss?: () => void;
}

export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<NotificationType, number>;
} 