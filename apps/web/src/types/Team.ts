export interface Team {
  id: string;
  name: string;
  role: 'owner' | 'manager' | 'member';
  membersCount: number;
  created_at: string;
  allow_invites: boolean;
  require_job_approval: boolean;
  auto_assign_jobs: boolean;
  default_job_visibility: 'owner_only' | 'team_only' | 'public';
}

export interface TeamMember {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  role: 'owner' | 'manager' | 'member';
  avatar_url?: string;
  joined_at: string;
  last_active?: string;
}

export interface TeamDetails extends Team {
  members: TeamMember[];
  pending_invites: TeamInvite[];
}

export interface TeamInvite {
  id: string;
  email: string;
  role: 'manager' | 'member';
  invited_by: string;
  invited_at: string;
  expires_at: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
}

export interface CreateTeamData {
  name: string;
  allow_invites: boolean;
  require_job_approval: boolean;
  auto_assign_jobs: boolean;
  default_job_visibility: 'owner_only' | 'team_only' | 'public';
}

export interface InviteTeamMemberData {
  email: string;
  role: 'manager' | 'member';
}

export interface UpdateTeamData {
  name?: string;
  allow_invites?: boolean;
  require_job_approval?: boolean;
  auto_assign_jobs?: boolean;
  default_job_visibility?: 'owner_only' | 'team_only' | 'public';
} 