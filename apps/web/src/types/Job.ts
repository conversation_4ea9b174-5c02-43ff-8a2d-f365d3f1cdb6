export interface Job {
  id: string;
  title: string;
  client: { 
    id: string; 
    name: string; 
    phone?: string | null; 
    email?: string | null; 
  };
  status: 'new' | 'quoted' | 'in_progress' | 'on_hold' | 'completed' | 'archived';
  scheduled_at: string | null;
  created_at: string;
  updated_at: string;
  assigned_to?: { 
    id: string; 
    full_name: string; 
  } | null;
  hasNotes: boolean;
  
  // Enhanced scheduling fields (optional for backward compatibility)
  scheduled_start_time?: string | null;
  scheduled_end_time?: string | null;
  estimated_duration?: string | null;
  scheduling_notes?: string | null;
}

export interface JobNote {
  id: string;
  author: { 
    id: string; 
    full_name: string; 
    role: string; 
  };
  message: string;
  message_type: 'text' | 'system' | 'AI';
  created_at: string;
}

export interface JobDetails extends Job {
  description?: string | null;
  client: { 
    id: string; 
    name: string; 
    address: string; 
    phone: string; 
    email: string; 
    rating: number; 
  };
  team_id: string | null;
  jobNotes: JobNote[];
  quote_summary?: { 
    amount: number; 
    status: 'draft' | 'sent' | 'accepted' | 'rejected'; 
  };
}

export type JobStatus = Job['status'];

// For new job creation
export interface CreateJobData {
  id?: string; // Optional for new jobs, present after creation
  title: string;
  client_id: string;
  client_name?: string;
  description: string;
  scheduled_at?: string;
  team_id?: string;
  status: JobStatus;
  created_at?: string;
  updated_at?: string;
  
  // Enhanced scheduling fields
  scheduled_start_time?: string;
  scheduled_end_time?: string;
  estimated_duration?: string;
  scheduling_notes?: string;
}

// For AI parsing response
export interface ParsedJobData {
  title?: string;
  description?: string;
  estimated_duration?: string;
  suggested_client?: string;
}

// Note: Client interface is defined in Client.ts to avoid conflicts 

// Enhanced scheduling types and utilities
export interface JobScheduleDetails {
  date: string;
  startTime?: string;
  endTime?: string;
  duration?: string;
  notes?: string;
}

export interface ScheduleTimeSlot {
  start: string; // Time in HH:MM format
  end: string;   // Time in HH:MM format
  label: string; // Display label like "9:00 AM - 5:00 PM"
}

// Common time slots for scheduling
export const COMMON_TIME_SLOTS: ScheduleTimeSlot[] = [
  { start: '08:00', end: '09:00', label: '8:00 AM - 9:00 AM' },
  { start: '09:00', end: '10:00', label: '9:00 AM - 10:00 AM' },
  { start: '10:00', end: '11:00', label: '10:00 AM - 11:00 AM' },
  { start: '11:00', end: '12:00', label: '11:00 AM - 12:00 PM' },
  { start: '12:00', end: '13:00', label: '12:00 PM - 1:00 PM' },
  { start: '13:00', end: '14:00', label: '1:00 PM - 2:00 PM' },
  { start: '14:00', end: '15:00', label: '2:00 PM - 3:00 PM' },
  { start: '15:00', end: '16:00', label: '3:00 PM - 4:00 PM' },
  { start: '16:00', end: '17:00', label: '4:00 PM - 5:00 PM' },
  { start: '17:00', end: '18:00', label: '5:00 PM - 6:00 PM' }
];

// Common duration presets
export const COMMON_DURATIONS = [
  { value: '30 minutes', label: '30 minutes' },
  { value: '1 hour', label: '1 hour' },
  { value: '2 hours', label: '2 hours' },
  { value: '3 hours', label: '3 hours' },
  { value: '4 hours', label: '4 hours' },
  { value: '1 day', label: 'Full day' },
  { value: '2 days', label: '2 days' },
  { value: '1 week', label: '1 week' }
];

// Utility function to check if a job has time-based scheduling
export const hasTimeScheduling = (job: Job): boolean => {
  return !!(job.scheduled_start_time || job.scheduled_end_time);
};

// Utility function to format job scheduling info
export const formatJobSchedule = (job: Job): string => {
  if (!job.scheduled_at) return 'Not scheduled';
  
  const date = new Date(job.scheduled_at).toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  });
  
  if (job.scheduled_start_time && job.scheduled_end_time) {
    return `${date} at ${job.scheduled_start_time} - ${job.scheduled_end_time}`;
  } else if (job.scheduled_start_time) {
    return `${date} at ${job.scheduled_start_time}`;
  }
  
  return date;
};