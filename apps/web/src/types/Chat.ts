export interface ChatMessage {
  id: string;
  user_id: string;
  message: string;
  is_from_user: boolean;
  context?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface ChatContext {
  currentPage?: string;
  jobId?: string;
  clientId?: string;
  teamId?: string;
  module?: 'jobs' | 'clients' | 'teams' | 'stats' | 'settings' | 'profile' | 'ask-dex';
}

export interface ChatResponse {
  message: string;
  model?: string;
  suggestions?: string[];
}

export interface SendMessageRequest {
  message: string;
  context?: ChatContext;
}

export interface DexChatState {
  messages: ChatMessage[];
  isLoading: boolean;
  isTyping: boolean;
  error: string | null;
} 