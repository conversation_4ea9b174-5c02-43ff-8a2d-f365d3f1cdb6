// Document types for DeskBelt app

export interface Quote {
  id: string;
  job_id: string;
  amount: number;
  details: string;
  terms?: string;
  status: 'draft' | 'sent' | 'accepted' | 'rejected';
  created_at: string;
  updated_at: string;
  created_by: string;
  
  // Market rate intelligence fields
  market_rate_work_type?: string;
  market_rate_suggestion?: string;
  market_rate_estimated_range?: string;
}

export interface Invoice {
  id: string;
  job_id: string;
  quote_id?: string;
  amount: number;
  tax: number;
  details: string;
  due_date: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface Contract {
  id: string;
  job_id: string;
  terms: string;
  status: 'draft' | 'sent' | 'signed' | 'completed';
  created_at: string;
  updated_at: string;
  created_by: string;
}

// AI Response types for document parsing
export interface QuoteParseResponse {
  amount: number | null;
  breakdown?: string[];
  message?: string;
  professionalizedDescription?: string;
}

export interface ContractGenerateRequest {
  jobDescription: string;
  clientName: string;
  clientAddress: string;
}

export interface ContractGenerateResponse {
  terms: string;
}

// Creation data types
export interface CreateQuoteData {
  job_id: string;
  amount: number;
  details: string;
  terms?: string;
  status?: string;
  
  // Market rate intelligence fields
  market_rate_work_type?: string;
  market_rate_suggestion?: string;
  market_rate_estimated_range?: string;
}

export interface CreateInvoiceData {
  job_id: string;
  quote_id?: string;
  amount: number;
  tax: number;
  details: string;
  due_date: string;
}

export interface CreateContractData {
  job_id: string;
  terms: string;
} 