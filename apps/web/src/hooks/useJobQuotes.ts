import { useState, useEffect } from 'react';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface Quote {
  id: string;
  job_id: string;
  amount: number;
  details: string;
  terms?: string;
  status: 'draft' | 'sent' | 'accepted' | 'rejected';
  created_at: string;
  updated_at: string;
  created_by: string;
}

interface UseJobQuotesReturn {
  quotes: Quote[];
  draftQuotes: Quote[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useJobQuotes = (jobId: string | null): UseJobQuotesReturn => {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { authenticatedGet } = useAuthenticatedFetch();

  const fetchQuotes = async () => {
    if (!jobId) {
      setQuotes([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await authenticatedGet(getApiUrl(`/api/jobs/${jobId}/quotes`));
      
      if (response.ok) {
        const data = await response.json();
        setQuotes(data.quotes || []);
      } else {
        setError('Failed to fetch quotes');
      }
    } catch (err) {
      setError('Error fetching quotes');
      console.error('Error fetching quotes:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchQuotes();
  }, [jobId]);

  const draftQuotes = quotes.filter(quote => quote.status === 'draft');

  return {
    quotes,
    draftQuotes,
    isLoading,
    error,
    refetch: fetchQuotes
  };
}; 