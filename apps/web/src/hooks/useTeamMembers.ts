'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface TeamMember {
  user_id: string;
  role: 'owner' | 'member';
  joined_at: string;
  can_manage_jobs: boolean;
  can_manage_clients: boolean;
  can_manage_invoices: boolean;
  can_manage_quotes: boolean;
  can_view_reports: boolean;
  can_manage_team: boolean;
  users: {
    id: string;
    email: string;
    full_name: string | null;
    phone: string | null;
    company_name: string | null;
  };
}

export interface TeamMemberPermissions {
  can_manage_jobs: boolean;
  can_manage_clients: boolean;
  can_manage_invoices: boolean;
  can_manage_quotes: boolean;
  can_view_reports: boolean;
  can_manage_team: boolean;
}

export function useTeamMembers(teamId: string | null) {
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { authenticatedGet, authenticatedPut } = useAuthenticatedFetch();

  const fetchMembers = useCallback(async () => {
    if (!teamId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await authenticatedGet(`/api/workforce/${teamId}/members`);
      
      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('You do not have permission to view team members');
        }
        throw new Error(`Failed to fetch team members: ${response.status}`);
      }

      const memberData: TeamMember[] = await response.json();
      setMembers(Array.isArray(memberData) ? memberData : []);
    } catch (err) {
      console.error('Error fetching team members:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch team members');
      setMembers([]); // Ensure we always have a valid array even on error
    } finally {
      setIsLoading(false);
    }
  }, [teamId, authenticatedGet]);

  const updateMemberPermissions = useCallback(async (
    memberId: string, 
    permissions: TeamMemberPermissions
  ) => {
    if (!teamId) return false;

    try {
      const response = await authenticatedPut(
        `/api/workforce/${teamId}/members/${memberId}/permissions`,
        permissions
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update permissions');
      }

      // Update local state optimistically
      setMembers(prevMembers => 
        prevMembers.map(member => 
          member.user_id === memberId 
            ? { ...member, ...permissions }
            : member
        )
      );

      return true;
    } catch (err) {
      console.error('Error updating member permissions:', err);
      throw err;
    }
  }, [teamId, authenticatedPut]);

  const removeMember = useCallback(async (memberId: string) => {
    if (!teamId) return false;

    try {
      // Note: This endpoint doesn't exist yet in the backend
      // We'll implement it in the next phase
      console.log('Remove member not implemented yet:', memberId);
      return false;
    } catch (err) {
      console.error('Error removing member:', err);
      throw err;
    }
  }, [teamId]);

  const getPermissionSummary = useCallback((member: TeamMember): string => {
    const permissions = [];
    if (member.can_manage_jobs) permissions.push('Jobs');
    if (member.can_manage_clients) permissions.push('Clients');
    if (member.can_manage_invoices) permissions.push('Invoices');
    if (member.can_manage_quotes) permissions.push('Quotes');
    if (member.can_view_reports) permissions.push('Reports');
    if (member.can_manage_team) permissions.push('Team');

    if (permissions.length === 0) return 'Limited Access';
    if (permissions.length >= 6) return 'All Access';
    if (permissions.length >= 4) return 'Most Access';
    return permissions.slice(0, 2).join(', ') + (permissions.length > 2 ? '...' : '');
  }, []);

  const getMemberDisplayName = useCallback((member: TeamMember): string => {
    return member.users.full_name || member.users.email.split('@')[0];
  }, []);

  const getMemberInitials = useCallback((member: TeamMember): string => {
    const fullName = member.users.full_name;
    if (fullName) {
      const names = fullName.split(' ');
      return names.length >= 2 
        ? `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase()
        : names[0][0].toUpperCase();
    }
    return member.users.email[0].toUpperCase();
  }, []);

  useEffect(() => {
    fetchMembers();
  }, [fetchMembers]);

  return {
    members,
    isLoading,
    error,
    refetch: fetchMembers,
    updateMemberPermissions,
    removeMember,
    getPermissionSummary,
    getMemberDisplayName,
    getMemberInitials
  };
}