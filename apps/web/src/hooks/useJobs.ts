import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Job } from '@/types/Job';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useAuth } from '@/contexts/AuthContext';

interface UseJobsParams {
  search?: string;
  status?: string[];
  limit?: number;
  offset?: number;
}

interface UseJobsReturn {
  data: Job[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  hasMore: boolean;
  loadMore: () => void;
}

export const useJobs = ({
  search = '',
  status = [],
  limit = 20,
  offset = 0,
}: UseJobsParams = {}): UseJobsReturn => {
  const [data, setData] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);

  const { user, loading: authLoading } = useAuth();
  const { authenticatedGet } = useAuthenticatedFetch();
  const isAuthenticated = !authLoading && !!user;

  // Create stable parameters to prevent infinite re-renders
  const stableParams = useMemo(() => ({
    search,
    status: [...status].sort(), // Sort to ensure consistent array comparison
    limit,
    offset
  }), [search, status, limit, offset]);

  // Use refs to track previous values and prevent unnecessary fetches
  const prevParamsRef = useRef<string>('');
  const prevAuthStateRef = useRef<boolean>(false);

  useEffect(() => {
    const fetchJobs = async (isLoadMore = false) => {
      if (!isAuthenticated) {
        setIsLoading(false);
        return;
      }

      try {
        console.log('📋 Fetching jobs with auth state:', { isAuthenticated, authLoading, userId: user?.id });
        setIsLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (stableParams.limit) params.append('limit', stableParams.limit.toString());
        if (stableParams.offset && isLoadMore) params.append('offset', stableParams.offset.toString());
        if (stableParams.search) params.append('search', stableParams.search);
        if (stableParams.status.length > 0) params.append('status', stableParams.status.join(','));

        const response = await authenticatedGet(`/api/jobs?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`API call failed: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.error) {
          throw new Error(result.error);
        }

        const jobs = result.jobs || [];

        if (isLoadMore) {
          setData(prevData => [...prevData, ...jobs]);
        } else {
          setData(jobs);
        }

        setHasMore(result.hasMore || false);
      } catch (err) {
        console.error('Error fetching jobs:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch jobs');
      } finally {
        setIsLoading(false);
      }
    };

    const currentParamsKey = JSON.stringify(stableParams);
    const currentAuthState = isAuthenticated && !authLoading;

    const authStateChanged = currentAuthState !== prevAuthStateRef.current;
    const paramsChanged = currentParamsKey !== prevParamsRef.current;

    // Only fetch if we have a legitimate state change and are authenticated
    // Combine conditions to prevent duplicate calls
    const shouldFetch = currentAuthState && (authStateChanged || (paramsChanged && !authStateChanged));

    if (shouldFetch) {
      fetchJobs(false);
    }

    // Always update refs to prevent stale comparisons
    prevParamsRef.current = currentParamsKey;
    prevAuthStateRef.current = currentAuthState;
  }, [stableParams, isAuthenticated, authLoading, authenticatedGet]);

  const refetch = useCallback(() => {
    if (!isAuthenticated) return;

    const fetchJobs = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (stableParams.limit) params.append('limit', stableParams.limit.toString());
        if (stableParams.search) params.append('search', stableParams.search);
        if (stableParams.status.length > 0) params.append('status', stableParams.status.join(','));

        const response = await authenticatedGet(`/api/jobs?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`API call failed: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.error) {
          throw new Error(result.error);
        }

        const jobs = result.jobs || [];
        setData(jobs);
        setHasMore(result.hasMore || false);
      } catch (err) {
        console.error('Error fetching jobs:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch jobs');
      } finally {
        setIsLoading(false);
      }
    };

    fetchJobs();
  }, [isAuthenticated, stableParams, authenticatedGet]);

  const loadMore = useCallback(() => {
    if (!isAuthenticated || isLoading || !hasMore) return;

    const fetchMoreJobs = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (stableParams.limit) params.append('limit', stableParams.limit.toString());
        if (data.length) params.append('offset', data.length.toString());
        if (stableParams.search) params.append('search', stableParams.search);
        if (stableParams.status.length > 0) params.append('status', stableParams.status.join(','));

        const response = await authenticatedGet(`/api/jobs?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`API call failed: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.error) {
          throw new Error(result.error);
        }

        const jobs = result.jobs || [];
        setData(prevData => [...prevData, ...jobs]);
        setHasMore(result.hasMore || false);
      } catch (err) {
        console.error('Error loading more jobs:', err);
        setError(err instanceof Error ? err.message : 'Failed to load more jobs');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMoreJobs();
  }, [isAuthenticated, isLoading, hasMore, data.length, stableParams, authenticatedGet]);

  return {
    data,
    isLoading,
    error,
    refetch,
    hasMore,
    loadMore,
  };
};