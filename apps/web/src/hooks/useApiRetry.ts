import { useCallback, useState } from 'react';
import { useAuthenticatedFetch } from './useAuthenticatedFetch';

interface RetryConfig {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
}

interface UseApiRetryReturn {
  call: <T>(
    url: string,
    options?: RequestInit,
    config?: RetryConfig
  ) => Promise<T>;
  isRetrying: boolean;
  retryCount: number;
}

export const useApiRetry = (): UseApiRetryReturn => {
  const { authenticatedFetch } = useAuthenticatedFetch();
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const call = useCallback(async <T>(
    url: string,
    options: RequestInit = {},
    config: RetryConfig = {}
  ): Promise<T> => {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      exponentialBackoff = true
    } = config;

    setIsRetrying(false);
    setRetryCount(0);

    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Show retry status for attempts after the first
        if (attempt > 0) {
          setIsRetrying(true);
          setRetryCount(attempt);
        }

        const response = await authenticatedFetch(url, options);

        // If response is successful, reset state and return
        if (response.ok) {
          setIsRetrying(false);
          setRetryCount(0);
          return await response.json();
        }

        // For 4xx errors (client errors), don't retry
        if (response.status >= 400 && response.status < 500) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.message ||
            errorData.error ||
            `Request failed with status ${response.status}`
          );
        }

        // For 5xx errors (server errors), prepare to retry
        throw new Error(`Server error: ${response.status} ${response.statusText}`);

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Network error occurred');

        // Check if this is a connection error that should be retried
        const isConnectionError =
          lastError.message.includes('ECONNREFUSED') ||
          lastError.message.includes('Failed to fetch') ||
          lastError.message.includes('Network error') ||
          lastError.message.includes('Server error') ||
          lastError.message.includes('Failed to proxy') ||
          lastError.message.includes('Internal Server Error') ||
          lastError.message.includes('connect ECONNREFUSED') ||
          lastError.message.includes('ENOTFOUND') ||
          lastError.message.includes('ETIMEDOUT');

        // Don't retry for authentication or client errors
        if (!isConnectionError || attempt === maxRetries) {
          break;
        }

        // Calculate delay for next retry
        const delay = exponentialBackoff
          ? retryDelay * Math.pow(2, attempt)
          : retryDelay;

        console.warn(`API call failed (attempt ${attempt + 1}/${maxRetries + 1}): ${lastError.message}`);
        console.warn(`Retrying in ${delay}ms...`);

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // All retries exhausted
    setIsRetrying(false);
    setRetryCount(0);

    console.error(`API call failed after ${maxRetries + 1} attempts:`, lastError);
    throw lastError;
  }, [authenticatedFetch]);

  return {
    call,
    isRetrying,
    retryCount
  };
};