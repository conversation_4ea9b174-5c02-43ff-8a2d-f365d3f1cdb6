import { renderHook, waitFor } from '@testing-library/react';
import { useNavigationCounts } from '@/hooks/useNavigationCounts';

// Mock the required modules
jest.mock('@/hooks/useAuthenticatedFetch');
jest.mock('@/contexts/AuthContext');

const mockAuthenticatedGet = jest.fn();
const mockUseAuth = {
  user: { id: 'test-user' },
  loading: false,
};

beforeEach(() => {
  jest.clearAllMocks();
  (require('@/hooks/useAuthenticatedFetch').useAuthenticatedFetch as jest.Mock).mockReturnValue({
    authenticatedGet: mockAuthenticatedGet,
  });
  (require('@/contexts/AuthContext').useAuth as jest.Mock).mockReturnValue(mockUseAuth);
});

describe('useNavigationCounts', () => {
  it('should fetch and return navigation counts successfully', async () => {
    // Mock API responses
    mockAuthenticatedGet
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ total: 15, jobs: [] }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ total: 8, clients: [] }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([
          { user_id: '1', role: 'owner' },
          { user_id: '2', role: 'member' },
          { user_id: '3', role: 'member' },
        ]),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ pagination: { total: 12 }, invoices: [] }),
      });

    const { result } = renderHook(() => useNavigationCounts());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.counts).toEqual({
      jobs: 15,
      clients: 8,
      team: 3,
      invoices: 12,
    });
    expect(result.current.error).toBeNull();
  });

  it('should handle API errors gracefully', async () => {
    // Mock API error responses
    mockAuthenticatedGet
      .mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ error: 'Failed to fetch jobs' }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ total: 8, clients: [] }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([]),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ pagination: { total: 5 } }),
      });

    const { result } = renderHook(() => useNavigationCounts());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.counts).toEqual({
      jobs: 0, // Should fallback to 0 when API fails
      clients: 8,
      team: 0,
      invoices: 5,
    });
  });

  it('should not fetch when user is not authenticated', async () => {
    (require('@/contexts/AuthContext').useAuth as jest.Mock).mockReturnValue({
      user: null,
      loading: false,
    });

    const { result } = renderHook(() => useNavigationCounts());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockAuthenticatedGet).not.toHaveBeenCalled();
    expect(result.current.counts).toEqual({
      jobs: 0,
      clients: 0,
      team: 0,
      invoices: 0,
    });
  });

  it('should handle different response formats', async () => {
    // Test different API response structures
    mockAuthenticatedGet
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ count: 20 }), // Alternative format
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ total: 5 }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([{ user_id: '1' }]),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ total: 3 }), // Without pagination wrapper
      });

    const { result } = renderHook(() => useNavigationCounts());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.counts).toEqual({
      jobs: 20, // Should use 'count' when 'total' is not available
      clients: 5,
      team: 1,
      invoices: 3,
    });
  });
});