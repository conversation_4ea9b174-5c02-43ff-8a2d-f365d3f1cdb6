import { useState, useEffect } from 'react';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface Invoice {
  id: string;
  job_id: string;
  amount: number;
  tax: number;
  details: string;
  line_items?: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  due_date: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  created_at: string;
  updated_at: string;
  created_by: string;
}

interface UseJobInvoicesReturn {
  invoices: Invoice[];
  draftInvoices: Invoice[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useJobInvoices = (jobId: string | null): UseJobInvoicesReturn => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { authenticatedGet } = useAuthenticatedFetch();

  const fetchInvoices = async () => {
    if (!jobId) {
      console.log('📋 No jobId provided, clearing invoices');
      setInvoices([]);
      return;
    }

    console.log('📋 Fetching invoices for job ID:', jobId);
    setIsLoading(true);
    setError(null);

    try {
      const response = await authenticatedGet(getApiUrl(`/api/jobs/${jobId}/invoices`));
      
      if (response.ok) {
        const data = await response.json();
        console.log('📋 Fetched invoices for job:', jobId, data);
        
        // Convert string amounts to numbers (Supabase returns numeric as strings)
        const invoicesWithNumbers = (data.invoices || data || []).map((invoice: any) => ({
          ...invoice,
          amount: parseFloat(invoice.amount) || 0,
          tax: parseFloat(invoice.tax) || 0
        }));
        
        setInvoices(invoicesWithNumbers);
      } else {
        console.error('❌ Failed to fetch invoices:', response.status, response.statusText);
        setError('Failed to fetch invoices');
      }
    } catch (err) {
      setError('Error fetching invoices');
      console.error('Error fetching invoices:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInvoices();
  }, [jobId]);

  const draftInvoices = invoices.filter(invoice => invoice.status === 'draft');

  return {
    invoices,
    draftInvoices,
    isLoading,
    error,
    refetch: fetchInvoices
  };
}; 