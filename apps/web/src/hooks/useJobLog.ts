import { useState, useEffect, useCallback } from 'react';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface JobLogEntry {
  id: string;
  jobId: string;
  type: 'system' | 'user';
  action: string;
  details?: string;
  userId?: string;
  userName?: string;
  createdAt: string;
}

export interface UseJobLogParams {
  jobId: string | null;
}

export interface UseJobLogReturn {
  data: JobLogEntry[];
  isLoading: boolean;
  error: string | null;
  addLogEntry: (entry: { type: 'system' | 'user'; action: string; jobId: string; details?: string }) => Promise<void>;
  refetch: () => void;
}

export function useJobLog(jobId: string | null): UseJobLogReturn {
  const [data, setData] = useState<JobLogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authenticatedGet, authenticatedPost } = useAuthenticatedFetch();

  // Helper functions for localStorage persistence
  const getStorageKey = (id: string) => `jobLog_${id}`;
  
  const saveToStorage = (id: string, entries: JobLogEntry[]) => {
    try {
      localStorage.setItem(getStorageKey(id), JSON.stringify(entries));
    } catch (err) {
      console.warn('Failed to save job log to localStorage:', err);
    }
  };

  const loadFromStorage = (id: string): JobLogEntry[] => {
    try {
      const stored = localStorage.getItem(getStorageKey(id));
      return stored ? JSON.parse(stored) : [];
    } catch (err) {
      console.warn('Failed to load job log from localStorage:', err);
      return [];
    }
  };

  const fetchJobLog = useCallback(async () => {
    if (!jobId) {
      setData([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Try to fetch from API first
      try {
        const response = await authenticatedGet(getApiUrl(`/api/jobs/${jobId}/log`));
        if (!response.ok) {
          throw new Error('API not available');
        }
        const result = await response.json();
        setData(result.logEntries || []);
      } catch (apiError) {
        console.log('Job log API not available, loading from localStorage');
        
        // Try to load from localStorage first
        const storedEntries = loadFromStorage(jobId);
        
        if (storedEntries.length > 0) {
          setData(storedEntries);
        } else {
          // Start with just the job created entry for new jobs
          const basicLogEntries: JobLogEntry[] = [
            {
              id: `log-${jobId}-created`,
              jobId,
              type: 'system',
              action: 'Job created',
              details: 'New job created in the system',
              createdAt: new Date().toISOString(),
            }
          ];

          setData(basicLogEntries);
          saveToStorage(jobId, basicLogEntries);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [jobId]);

  const addLogEntry = useCallback(async (entry: { type: 'system' | 'user'; action: string; jobId: string; details?: string }) => {
    try {
      // Try to add via API first
      try {
        const response = await authenticatedPost(getApiUrl(`/api/jobs/${entry.jobId}/log`), entry);
        
        if (!response.ok) {
          throw new Error('API not available');
        }
        
        const result = await response.json();
        
        // Refresh data after successful add
        fetchJobLog();
      } catch (apiError) {
        // If API fails, add to local mock data
        const newEntry: JobLogEntry = {
          id: `log-${Date.now()}`,
          jobId: entry.jobId,
          type: entry.type,
          action: entry.action,
          details: entry.details,
          userName: entry.type === 'user' ? 'Current User' : undefined,
          createdAt: new Date().toISOString(),
        };
        
        setData(prevData => {
          const updatedData = [...prevData, newEntry];
          saveToStorage(entry.jobId, updatedData);
          return updatedData;
        });
      }
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to add log entry');
    }
  }, [fetchJobLog]);

  const refetch = useCallback(() => {
    fetchJobLog();
  }, [fetchJobLog]);

  useEffect(() => {
    fetchJobLog();
  }, [fetchJobLog]);

  return {
    data,
    isLoading,
    error,
    addLogEntry,
    refetch,
  };
} 