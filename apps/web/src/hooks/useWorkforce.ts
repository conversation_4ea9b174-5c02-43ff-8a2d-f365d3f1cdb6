'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface Workforce {
  id: string;
  name: string;
  owner_id: string;
}

export function useWorkforce() {
  const [workforce, setWorkforce] = useState<Workforce | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const hasLoadedRef = useRef(false);
  const { authenticatedGet, authenticatedPost } = useAuthenticatedFetch();

  const fetchWorkforce = useCallback(async () => {
    // If we've already loaded successfully and have data, don't fetch again
    if (hasLoadedRef.current && workforce) {
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    try {
      const response = await authenticatedGet('/api/workforce');
      if (!response.ok) {
        throw new Error(`Failed to fetch workforce: ${response.status}`);
      }
      const teams: Workforce[] = await response.json();

      if (teams.length > 0) {
        const { data: { session } } = await supabase.auth.getSession();
        const currentUserId = session?.user?.id;
        
        const myTeam = teams.find(t => t.owner_id === currentUserId) || teams[0];
        setWorkforce(myTeam);
        hasLoadedRef.current = true;
      } else {
        // No teams found, create a default team for the user
        try {
          const createRes = await authenticatedPost('/api/workforce', { name: 'My Workforce' });
          
          if (createRes.ok) {
            const newTeam: Workforce = await createRes.json();
            setWorkforce(newTeam);
            hasLoadedRef.current = true;
          } else {
            const errorData = await createRes.json();
            console.error('Failed to create workforce:', errorData);
            setError('Failed to create your workforce. Please try again.');
            setWorkforce(null);
          }
        } catch (createError) {
          console.error('Error creating workforce:', createError);
          setError('Failed to create your workforce. Please try again.');
          setWorkforce(null);
        }
      }
    } catch (err) {
      console.error('Fetch workforce error:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [authenticatedGet, authenticatedPost, workforce]);

  useEffect(() => {
    fetchWorkforce();
  }, [fetchWorkforce]);

  const refetch = useCallback(() => {
    hasLoadedRef.current = false;
    fetchWorkforce();
  }, [fetchWorkforce]);

  return { workforce, isLoading, error, refetch };
} 