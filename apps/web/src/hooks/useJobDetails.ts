import { useState, useEffect } from 'react';
import { JobDetails } from '@/types/Job';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

interface UseJobDetailsResult {
  data: JobDetails | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useJobDetails = (jobId: string | null): UseJobDetailsResult => {
  const [data, setData] = useState<JobDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authenticatedGet } = useAuthenticatedFetch();

  const fetchJobDetails = async () => {
    if (!jobId) {
      setData(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await authenticatedGet(getApiUrl(`/api/jobs/${jobId}`));
      if (response.status === 404) {
        setError('Job not found');
        setData(null);
        return;
      }
      if (!response.ok) {
        throw new Error(`Failed to fetch job details (status ${response.status})`);
      }
      const jobDetails: JobDetails = await response.json();
      setData(jobDetails);
    } catch (err) {
      console.error('Error fetching job details:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch job details');
      setData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refetch = () => {
    fetchJobDetails();
  };

  useEffect(() => {
    fetchJobDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobId]);

  return {
    data,
    isLoading,
    error,
    refetch,
  };
};
