import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Client } from '@/types/Client';
import { useAuthenticatedAPI } from '@/hooks/useAuthenticatedAPI';

interface UseClientsParams {
  search?: string;
  sortBy?: 'name' | 'created_at' | 'rating';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

interface UseClientsReturn {
  data: Client[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  hasMore: boolean;
  loadMore: () => void;
  updateClient: (clientId: string, updates: Partial<Client>) => void;
  removeClient: (clientId: string) => void;
  totalCount: number;
}

export const useClients = ({
  search = '',
  sortBy = 'created_at',
  sortOrder = 'desc',
  limit = 20,
  offset = 0,
}: UseClientsParams = {}): UseClientsReturn => {
  const [data, setData] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  
  const { authenticatedGet, isAuthenticated } = useAuthenticatedAPI();

  const fetchClients = useCallback(async (isLoadMore = false) => {
    // Don't fetch if user is not authenticated
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      if (limit) params.append('limit', limit.toString());
      if (offset && isLoadMore) params.append('offset', offset.toString());
      if (search) params.append('search', search);
      if (sortBy) params.append('sortBy', sortBy);
      if (sortOrder) params.append('sortOrder', sortOrder);

      const response = await authenticatedGet(`/api/clients?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch clients: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.error);
      }

      const clients = result.clients || [];
      
      if (isLoadMore) {
        setData(prevData => [...prevData, ...clients]);
      } else {
        setData(clients);
      }
      
      setHasMore(result.hasMore || false);
      setTotalCount(result.totalCount || clients.length);
    } catch (err) {
      console.error('Error fetching clients:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch clients');
    } finally {
      setIsLoading(false);
    }
  }, [search, sortBy, sortOrder, limit, offset, authenticatedGet, isAuthenticated]);

  const refetch = useCallback(() => {
    fetchClients(false);
  }, [fetchClients]);

  const loadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      fetchClients(true);
    }
  }, [isLoading, hasMore, fetchClients]);

  const updateClient = useCallback((clientId: string, updates: Partial<Client>) => {
    setData(prevData => 
      prevData.map(client => 
        client.id === clientId 
          ? { ...client, ...updates }
          : client
      )
    );
  }, []);

  const removeClient = useCallback((clientId: string) => {
    setData(prevData => prevData.filter(client => client.id !== clientId));
    setTotalCount(prev => Math.max(0, prev - 1));
  }, []);

  useEffect(() => {
    // Only fetch if user is authenticated
    if (isAuthenticated) {
      refetch();
    }
  }, [search, sortBy, sortOrder, limit, isAuthenticated, refetch]);

  return {
    data,
    isLoading,
    error,
    refetch,
    hasMore,
    loadMore,
    updateClient,
    removeClient,
    totalCount,
  };
}; 