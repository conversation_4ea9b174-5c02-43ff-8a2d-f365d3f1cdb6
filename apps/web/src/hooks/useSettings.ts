import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface BusinessSettings {
  autoSelectVAT: boolean;
  vatRate: number;
  defaultQuoteTerms: string;
  defaultContractTerms: string;
  defaultPaymentTerms: string;
  dayRate: number;
  hourlyRate: number;
  calloutFee: number;
  emergencyRate: number;
  preferredCurrency: 'GBP' | 'EUR' | 'USD';
  autoNumberQuotes: boolean;
  autoNumberInvoices: boolean;
  autoNumberContracts: boolean;
  quoteValidityDays: number;
  invoicePaymentDays: number;
  defaultLabourWarranty: string;
  defaultMaterialsWarranty: string;
}

interface NotificationSettings {
  emailQuoteAccepted: boolean;
  emailInvoicePaid: boolean;
  emailJobCompleted: boolean;
}

interface UserSettings {
  business: BusinessSettings;
  notifications: NotificationSettings;
}

interface ActiveSession {
  id: string;
  device: string;
  location: string;
  lastActive: string;
  current: boolean;
}

interface UseSettingsReturn {
  data: UserSettings | null;
  isLoading: boolean;
  error: string | null;
  updateBusinessSettings: (settings: Partial<BusinessSettings>) => Promise<void>;
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => Promise<void>;
  getActiveSessions: () => Promise<ActiveSession[]>;
  logoutAllDevices: () => Promise<void>;
  deleteAccount: () => Promise<void>;
  refetch: () => void;
}

// Mock settings data with realistic UK trade business defaults
const mockSettings: UserSettings = {
  business: {
    autoSelectVAT: true,
    vatRate: 20,
    defaultQuoteTerms: `Quote valid for 30 days from date of issue.

Payment Terms:
- 25% deposit required to secure booking
- Remaining balance due on completion
- Payment accepted by bank transfer, cash, or card

Work includes:
- All labour and materials as specified
- Site clearance and waste disposal
- 12-month warranty on workmanship

Exclusions:
- Any work not specifically mentioned in this quote
- Structural alterations requiring building control approval
- Unforeseen complications due to hidden issues

All work carried out to current British Standards and Building Regulations where applicable.`,
    defaultContractTerms: `TERMS AND CONDITIONS

1. SCOPE OF WORK
The contractor agrees to provide the services described in the attached specification.

2. PAYMENT TERMS
- 25% deposit required before work commences
- Progress payments as agreed in writing
- Final payment due within 7 days of completion
- Late payment charges: 8% per annum

3. WARRANTIES
- 12 months warranty on workmanship
- Materials carry manufacturers' warranties
- Emergency call-out available within 24 hours

4. LIABILITY
- Public liability insurance: £2,000,000
- Employer's liability insurance: £10,000,000
- All work complies with current Health & Safety regulations

5. VARIATIONS
Any changes to the original specification must be agreed in writing with costs confirmed before proceeding.

6. CANCELLATION
48 hours notice required for cancellation. Deposit may be retained to cover costs incurred.`,
    defaultPaymentTerms: "25% deposit required, balance due on completion. Payment within 7 days of invoice date.",
    dayRate: 250,
    hourlyRate: 45,
    calloutFee: 75,
    emergencyRate: 85,
    preferredCurrency: 'GBP',
    autoNumberQuotes: true,
    autoNumberInvoices: true,
    autoNumberContracts: true,
    quoteValidityDays: 30,
    invoicePaymentDays: 7,
    defaultLabourWarranty: "12 months workmanship warranty",
    defaultMaterialsWarranty: "Manufacturer's warranty applies"
  },
  notifications: {
    emailQuoteAccepted: true,
    emailInvoicePaid: true,
    emailJobCompleted: false
  }
};

export function useSettings(): UseSettingsReturn {
  const { user } = useAuth();
  const [data, setData] = useState<UserSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = () => {
    if (!user) {
      setData(null);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);
    
    // Simulate API call
    setTimeout(() => {
      try {
        // Simulate potential API errors (2% chance)
        if (Math.random() < 0.02) {
          throw new Error('Failed to fetch settings');
        }
        
        setData(mockSettings);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        setData(null);
      } finally {
        setIsLoading(false);
      }
    }, 400);
  };

  const updateBusinessSettings = async (settings: Partial<BusinessSettings>): Promise<void> => {
    if (!data) throw new Error('No settings data available');

    setIsLoading(true);
    setError(null);

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Simulate potential API errors (3% chance)
          if (Math.random() < 0.03) {
            throw new Error('Failed to update business settings');
          }

          // Update local data
          setData(prev => prev ? {
            ...prev,
            business: {
              ...prev.business,
              ...settings
            }
          } : null);

          console.log('Business settings updated:', settings);
          resolve();
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';
          setError(errorMessage);
          reject(new Error(errorMessage));
        } finally {
          setIsLoading(false);
        }
      }, 800);
    });
  };

  const updateNotificationSettings = async (settings: Partial<NotificationSettings>): Promise<void> => {
    if (!data) throw new Error('No settings data available');

    setIsLoading(true);
    setError(null);

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Simulate potential API errors (3% chance)
          if (Math.random() < 0.03) {
            throw new Error('Failed to update notification settings');
          }

          // Update local data
          setData(prev => prev ? {
            ...prev,
            notifications: {
              ...prev.notifications,
              ...settings
            }
          } : null);

          console.log('Notification settings updated:', settings);
          resolve();
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to update notifications';
          setError(errorMessage);
          reject(new Error(errorMessage));
        } finally {
          setIsLoading(false);
        }
      }, 800);
    });
  };

  const getActiveSessions = async (): Promise<ActiveSession[]> => {
    setIsLoading(true);
    setError(null);

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Simulate potential API errors (2% chance)
          if (Math.random() < 0.02) {
            throw new Error('Failed to fetch active sessions');
          }

          const sessions: ActiveSession[] = [
            {
              id: 'session-1',
              device: 'Chrome on Windows',
              location: 'London, UK',
              lastActive: new Date().toISOString(),
              current: true
            },
            {
              id: 'session-2',
              device: 'Mobile Safari on iPhone',
              location: 'London, UK',
              lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
              current: false
            }
          ];

          console.log('Active sessions fetched:', sessions);
          resolve(sessions);
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to fetch sessions';
          setError(errorMessage);
          reject(new Error(errorMessage));
        } finally {
          setIsLoading(false);
        }
      }, 600);
    });
  };

  const logoutAllDevices = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Simulate potential API errors (2% chance)
          if (Math.random() < 0.02) {
            throw new Error('Failed to logout from all devices');
          }

          console.log('Logged out from all devices');
          resolve();
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to logout from all devices';
          setError(errorMessage);
          reject(new Error(errorMessage));
        } finally {
          setIsLoading(false);
        }
      }, 1500);
    });
  };

  const deleteAccount = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Simulate potential API errors (5% chance)
          if (Math.random() < 0.05) {
            throw new Error('Failed to delete account');
          }

          console.log('Account deletion request submitted');
          resolve();
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to delete account';
          setError(errorMessage);
          reject(new Error(errorMessage));
        } finally {
          setIsLoading(false);
        }
      }, 2000);
    });
  };

  const refetch = () => {
    fetchSettings();
  };

  useEffect(() => {
    fetchSettings();
  }, [user]);

  return {
    data,
    isLoading,
    error,
    updateBusinessSettings,
    updateNotificationSettings,
    getActiveSessions,
    logoutAllDevices,
    deleteAccount,
    refetch
  };
} 