import { useState, useEffect } from 'react';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { DateRange } from '@/components/DateRangeFilter';

export interface StatsData {
  totalInvoiceValue: number;
  totalJobs: number;
  totalClients: number;
  newClientsThisMonth: number;
  newClientsLastMonth: number;
  newJobsThisMonth: number;
  newJobsLastMonth: number;
  invoiceValueChange: number;
  jobsChange: number;
  clientsChange: number;
}

export interface MonthlyInvoiceValue {
  month: string;
  value: number;
}

export interface InteractionData {
  type: string;
  count: number;
  color: string;
}

export interface DashboardStats {
  stats: StatsData;
  monthlyInvoiceValue: MonthlyInvoiceValue[];
  interactionData: InteractionData[];
  dateRange?: {
    startDate: string;
    endDate: string;
    label: string;
  };
}

export function useDashboardStats(dateRange?: DateRange) {
  const [data, setData] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authenticatedGet } = useAuthenticatedFetch();

  const refetch = async (customDateRange?: DateRange) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Use custom date range if provided, otherwise use the hook's date range
      const currentDateRange = customDateRange || dateRange;
      
      // Build query parameters
      const params = new URLSearchParams({
        include_comparison: 'true'
      });
      
      if (currentDateRange) {
        params.append('start_date', currentDateRange.startDate);
        params.append('end_date', currentDateRange.endDate);
        params.append('period_label', currentDateRange.label);
      }
      
      const response = await authenticatedGet(`/api/user/analytics?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.status} ${response.statusText}`);
      }
      
      const analyticsData = await response.json();
      
      // Add date range info to response
      if (currentDateRange) {
        analyticsData.dateRange = {
          startDate: currentDateRange.startDate,
          endDate: currentDateRange.endDate,
          label: currentDateRange.label
        };
      }
      
      setData(analyticsData);
    } catch (err) {
      console.error('Analytics fetch error:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
      
      // Fallback to mock data on error
      console.log('Falling back to mock data...');
      const mockData = { ...mockDashboardStats };
      if (dateRange) {
        mockData.dateRange = {
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
          label: dateRange.label
        };
      }
      setData(mockData);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refetch();
  }, [dateRange?.startDate, dateRange?.endDate]);

  return {
    data,
    isLoading,
    error,
    refetch
  };
}

const mockDashboardStats: DashboardStats = {
  stats: {
    totalInvoiceValue: 47250,
    totalJobs: 127,
    totalClients: 43,
    newClientsThisMonth: 7,
    newClientsLastMonth: 5,
    newJobsThisMonth: 12,
    newJobsLastMonth: 11,
    invoiceValueChange: 12.4,
    jobsChange: 8.7,
    clientsChange: 15.8
  },
  monthlyInvoiceValue: [
    { month: 'Jan', value: 3200 },
    { month: 'Feb', value: 2800 },
    { month: 'Mar', value: 4100 },
    { month: 'Apr', value: 3600 },
    { month: 'May', value: 4200 },
    { month: 'Jun', value: 3900 },
    { month: 'Jul', value: 4800 },
    { month: 'Aug', value: 5200 },
    { month: 'Sep', value: 4600 },
    { month: 'Oct', value: 5400 },
    { month: 'Nov', value: 4750 },
    { month: 'Dec', value: 4800 }
  ],
  interactionData: [
    { type: 'Invoices Sent', count: 87, color: '#3B82F6' },
    { type: 'Quotes Sent', count: 156, color: '#10B981' },
    { type: 'Contracts Sent', count: 42, color: '#8B5CF6' },
    { type: 'Reviews Requested', count: 73, color: '#F59E0B' }
  ]
}; 