import { useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'next/navigation';

/**
 * Session timeout detection and automatic recovery hook
 */
export const useSessionTimeout = () => {
  const { user, loading, recoverSession } = useAuth();
  const router = useRouter();
  const recoveryAttemptRef = useRef(false);
  const lastActivityRef = useRef(Date.now());

  const updateLastActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
  }, []);

  const checkSessionValidity = useCallback(async () => {
    if (loading || !user || recoveryAttemptRef.current) return;

    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (!session || error) {
        console.log('🔍 Invalid session detected, attempting recovery...');
        recoveryAttemptRef.current = true;
        
        const recovery = await recoverSession();
        
        if (!recovery.success) {
          console.log('🚪 Session recovery failed, redirecting to login...');
          router.push('/login?reason=session_expired');
        } else {
          console.log('✅ Session recovered successfully');
        }
        
        recoveryAttemptRef.current = false;
      }
    } catch (error) {
      console.error('Session validity check failed:', error);
      recoveryAttemptRef.current = false;
    }
  }, [loading, user, recoverSession, router]);

  const checkForIdleTimeout = useCallback(() => {
    const now = Date.now();
    const idleTime = now - lastActivityRef.current;
    const IDLE_TIMEOUT = 30 * 60 * 1000; // 30 minutes

    if (idleTime > IDLE_TIMEOUT && user && !loading) {
      console.log('⏰ Idle timeout detected, checking session...');
      checkSessionValidity();
    }
  }, [user, loading, checkSessionValidity]);

  useEffect(() => {
    if (!user || loading) return;

    // Set up periodic session checks
    const sessionCheckInterval = setInterval(checkSessionValidity, 5 * 60 * 1000); // Every 5 minutes
    const idleCheckInterval = setInterval(checkForIdleTimeout, 60 * 1000); // Every minute

    // Track user activity
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    activityEvents.forEach(event => {
      document.addEventListener(event, updateLastActivity, { passive: true });
    });

    return () => {
      clearInterval(sessionCheckInterval);
      clearInterval(idleCheckInterval);
      activityEvents.forEach(event => {
        document.removeEventListener(event, updateLastActivity);
      });
    };
  }, [user, loading, checkSessionValidity, checkForIdleTimeout, updateLastActivity]);

  return {
    checkSessionValidity,
    updateLastActivity,
  };
};