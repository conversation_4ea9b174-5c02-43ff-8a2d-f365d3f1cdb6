'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

/**
 * Hook that redirects to login if user is not authenticated
 * Use this in any protected page or component
 */
export function useRequireAuth(redirectTo: string = '/login') {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    // Don't redirect while still loading auth state or not mounted client-side
    if (!isClient || loading) return

    // Redirect to login if no user
    if (!user) {
      router.push(redirectTo)
      return
    }
  }, [user, loading, router, redirectTo, isClient])

  return { 
    user, 
    loading: loading || !isClient, 
    isAuthenticated: !!user && isClient 
  }
}

/**
 * Hook that redirects authenticated users away from auth pages
 * Use this on login/register pages
 */
export function useRedirectIfAuthenticated(redirectTo: string = '/dashboard/jobs') {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    // Don't redirect while still loading auth state or not mounted client-side
    if (!isClient || loading) return

    // Redirect to dashboard if already authenticated
    if (user) {
      // Check if there's a pending invitation token to redirect back to
      const pendingToken = sessionStorage.getItem('pendingInviteToken');
      if (pendingToken) {
        router.push(`/invite/${pendingToken}`);
        return;
      }
      
      // Otherwise, redirect to default location
      router.push(redirectTo)
      return
    }
  }, [user, loading, router, redirectTo, isClient])

  return { 
    user, 
    loading: loading || !isClient, 
    isAuthenticated: !!user && isClient 
  }
}

export default useRequireAuth 