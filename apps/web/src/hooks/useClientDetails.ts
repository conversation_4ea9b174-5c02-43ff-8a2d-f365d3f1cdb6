import { useState, useEffect } from 'react';
import { ClientDetails } from '@/types/Client';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface UseClientDetailsResult {
  data: ClientDetails | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useClientDetails = (clientId: string | null): UseClientDetailsResult => {
  const [data, setData] = useState<ClientDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authenticatedGet } = useAuthenticatedFetch();

  const fetchClientDetails = async () => {
    if (!clientId) {
      setData(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await authenticatedGet(getApiUrl(`/api/clients/${clientId}`));
      if (!response.ok) {
        throw new Error('Failed to fetch client details');
      }
      const clientDetails = await response.json();
      setData(clientDetails as ClientDetails);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch client details');
      setData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refetch = () => {
    fetchClientDetails();
  };

  useEffect(() => {
    fetchClientDetails();
  }, [clientId]);

  return {
    data,
    isLoading,
    error,
    refetch
  };
}; 