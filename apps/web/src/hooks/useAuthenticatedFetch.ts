import { useCallback } from 'react';
import { supabase } from '@/lib/supabase';

/**
 * Custom hook for making authenticated API requests
 * Automatically includes Authorization header with JWT token
 */
export const useAuthenticatedFetch = () => {
  
  const getAuthHeaders = useCallback(async () => {
    const { data: { session } } = await supabase.auth.getSession();

    // Always include Content-Type for JSON requests
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // Add Authorization header if we have a session
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
      console.log('🔑 Auth headers prepared with token:', session.access_token.substring(0, 20) + '...');
    } else {
      console.warn('⚠️ No access token available for authenticated request');
    }

    return headers;
  }, []);

  /**
   * Authenticated fetch wrapper
   */
  const authenticatedFetch = useCallback(async (
    url: string, 
    options: RequestInit = {}
  ): Promise<Response> => {
    const authHeaders = await getAuthHeaders();
    
    // Merge auth headers with any existing headers
    const mergedHeaders = {
      ...authHeaders,
      ...(options.headers || {})
    };

    const response = await fetch(url, {
      ...options,
      headers: mergedHeaders
    });

    return response;
  }, [getAuthHeaders]);

  /**
   * Convenience method for POST requests with JSON body
   */
  const authenticatedPost = useCallback(async (
    url: string,
    data: any,
    options: RequestInit = {}
  ): Promise<Response> => {
    return authenticatedFetch(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    });
  }, [authenticatedFetch]);

  /**
   * Convenience method for GET requests
   */
  const authenticatedGet = useCallback(async (
    url: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    return authenticatedFetch(url, {
      ...options,
      method: 'GET'
    });
  }, [authenticatedFetch]);

  /**
   * Convenience method for PUT requests with JSON body
   */
  const authenticatedPut = useCallback(async (
    url: string,
    data: any,
    options: RequestInit = {}
  ): Promise<Response> => {
    return authenticatedFetch(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }, [authenticatedFetch]);

  /**
   * Convenience method for DELETE requests
   */
  const authenticatedDelete = useCallback(async (
    url: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    return authenticatedFetch(url, {
      ...options,
      method: 'DELETE'
    });
  }, [authenticatedFetch]);

  return {
    authenticatedFetch,
    authenticatedPost,
    authenticatedGet,
    authenticatedPut,
    authenticatedDelete,
    getAuthHeaders
  };
};