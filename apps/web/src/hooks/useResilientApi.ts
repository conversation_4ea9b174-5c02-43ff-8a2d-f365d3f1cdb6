import { useCallback, useState } from 'react';
import { useApiRetry } from './useApiRetry';

interface ResilientApiOptions {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
}

interface UseResilientApiReturn {
  apiCall: <T>(
    url: string,
    options?: RequestInit,
    config?: ResilientApiOptions
  ) => Promise<T>;
  isRetrying: boolean;
  retryCount: number;
  lastError: string | null;
}

export const useResilientApi = (): UseResilientApiReturn => {
  const { call: apiRetryCall, isRetrying, retryCount } = useApiRetry();
  const [lastError, setLastError] = useState<string | null>(null);

  const apiCall = useCallback(async <T>(
    url: string,
    options: RequestInit = {},
    config: ResilientApiOptions = {}
  ): Promise<T> => {
    try {
      setLastError(null);

      // Use the existing retry logic
      const result = await apiRetryCall<T>(url, options, {
        maxRetries: config.maxRetries || 5, // Increase default retries
        retryDelay: config.retryDelay || 500, // Faster initial retry
        exponentialBackoff: config.exponentialBackoff ?? true
      });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Resilient API call failed:', errorMessage);
      setLastError(errorMessage);

      // Check if this looks like a development server restart issue
      if (errorMessage.includes('ECONNREFUSED') || errorMessage.includes('Failed to proxy')) {
        console.warn('🔄 API server may be restarting, this is normal in development');
      }

      throw error;
    }
  }, [apiRetryCall]);

  return {
    apiCall,
    isRetrying,
    retryCount,
    lastError
  };
};