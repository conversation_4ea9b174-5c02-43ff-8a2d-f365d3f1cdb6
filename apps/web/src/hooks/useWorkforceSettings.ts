'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

// Use relative URLs to leverage Next.js proxy
const API_BASE_URL = '';

export interface WorkforceSettings {
  name: string;
  allow_invites: boolean;
  require_job_approval: boolean;
  auto_assign_jobs: boolean;
  default_job_visibility: 'owner_only' | 'entire_team' | 'assigned_only';
}

export interface TeamMember {
  user_id: string;
  role: string;
  can_manage_jobs: boolean;
  can_manage_clients: boolean;
  can_manage_invoices: boolean;
  can_manage_quotes: boolean;
  can_view_reports: boolean;
  can_manage_team: boolean;
  joined_at: string;
  users: {
    email: string;
    full_name: string | null;
  };
}

export function useWorkforceSettings() {
  const [settings, setSettings] = useState<WorkforceSettings | null>(null);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [teamId, setTeamId] = useState<string | null>(null);
  const { user } = useAuth();

  // Get auth headers
  const getAuthHeaders = useCallback(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session?.access_token) {
      throw new Error('Not authenticated');
    }
    
    return {
      'Authorization': `Bearer ${session.access_token}`,
      'Content-Type': 'application/json'
    };
  }, []);

  const fetchSettings = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // First get the team ID
      const headers = await getAuthHeaders();
      const workforceResponse = await fetch(`${API_BASE_URL}/api/workforce`, {
        headers
      });

      if (!workforceResponse.ok) {
        console.error('Workforce API error:', workforceResponse.status, workforceResponse.statusText);
        throw new Error(`Failed to fetch workforce: ${workforceResponse.status}`);
      }
      
      const workforceList = await workforceResponse.json();
      
      if (!workforceList || workforceList.length === 0) {
        console.log('No workforce found for user');
        setSettings(null);
        setMembers([]);
        setTeamId(null);
        return;
      }

      const userTeam = workforceList.find((team: any) => team.owner_id === user.id) || workforceList[0];
      setTeamId(userTeam.id);

      // Fetch team settings
      const settingsResponse = await fetch(`${API_BASE_URL}/api/workforce/${userTeam.id}/settings`, {
        headers
      });

      if (!settingsResponse.ok) {
        console.error('Settings API error:', settingsResponse.status, settingsResponse.statusText);
        throw new Error(`Failed to fetch settings: ${settingsResponse.status}`);
      }
      const settingsData = await settingsResponse.json();
      setSettings(settingsData);

      // Fetch team members
      const membersResponse = await fetch(`${API_BASE_URL}/api/workforce/${userTeam.id}/members`, {
        headers
      });

      if (!membersResponse.ok) {
        console.error('Members API error:', membersResponse.status, membersResponse.statusText);
        throw new Error(`Failed to fetch members: ${membersResponse.status}`);
      }
      const membersData = await membersResponse.json();
      setMembers(membersData || []);

    } catch (err) {
      console.error('Error fetching workforce settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch workforce settings');
    } finally {
      setLoading(false);
    }
  }, [user, getAuthHeaders]);

  const updateSettings = async (updates: Partial<WorkforceSettings>) => {
    if (!user || !teamId) return false;

    try {
      setError(null);
      const headers = await getAuthHeaders();

      const response = await fetch(`${API_BASE_URL}/api/workforce/${teamId}/settings`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(updates)
      });

      if (!response.ok) throw new Error('Failed to update settings');

      // Update local state
      setSettings(prev => prev ? { ...prev, ...updates } : null);
      return true;
    } catch (err) {
      console.error('Error updating workforce settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to update workforce settings');
      return false;
    }
  };

  const updateMemberPermissions = async (memberId: string, permissions: Partial<TeamMember>) => {
    if (!user || !teamId) return false;

    try {
      setError(null);
      const headers = await getAuthHeaders();

      const response = await fetch(`${API_BASE_URL}/api/workforce/${teamId}/members/${memberId}/permissions`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(permissions)
      });

      if (!response.ok) throw new Error('Failed to update member permissions');

      // Update local state
      setMembers(prev => prev.map(member => 
        member.user_id === memberId 
          ? { ...member, ...permissions }
          : member
      ));
      return true;
    } catch (err) {
      console.error('Error updating member permissions:', err);
      setError(err instanceof Error ? err.message : 'Failed to update member permissions');
      return false;
    }
  };

  useEffect(() => {
    fetchSettings();
  }, [user]);

  return {
    settings,
    members,
    loading,
    error,
    updateSettings,
    updateMemberPermissions,
    refetch: fetchSettings
  };
} 