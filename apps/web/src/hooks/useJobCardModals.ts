import { useState, useEffect, useCallback, RefObject, useMemo } from 'react';

// Z-Index constants for consistent layering
export const Z_INDEX = {
  CARD_ACTIVE: 40,
  MODAL_BACKDROP: 9998,
  MODAL_CONTENT: 9999,
} as const;

export interface PortalPosition {
  top: number;
  left: number;
  right?: number;
}

export const usePortalPosition = (
  triggerRef: RefObject<HTMLElement | null>, 
  isOpen: boolean,
  preferredAlignment: 'left' | 'right' = 'right'
): PortalPosition | null => {
  return useMemo(() => {
    if (!isOpen || !triggerRef.current) return null;
    
    const rect = triggerRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const modalWidth = 280; // Approximate modal width
    
    // Calculate position based on available space
    let left: number;
    let right: number | undefined;
    
    if (preferredAlignment === 'right') {
      right = Math.max(8, viewportWidth - rect.right);
      // If modal would overflow, align to left instead
      if (rect.right - modalWidth < 8) {
        left = Math.max(8, rect.left);
        right = undefined;
      }
    } else {
      left = Math.max(8, rect.left);
      // If modal would overflow, align to right instead
      if (rect.left + modalWidth > viewportWidth - 8) {
        right = Math.max(8, viewportWidth - rect.right);
        left = undefined!;
      }
    }
    
    return {
      top: rect.bottom + 8,
      left: left!,
      right,
    };
  }, [isOpen, triggerRef.current, preferredAlignment]);
};

export const useJobCardModals = (jobId: string) => {
  const [activeModal, setActiveModal] = useState<'date' | 'time' | null>(null);
  
  const openDatePicker = useCallback(() => {
    setActiveModal('date');
  }, []);
  
  const openTimePicker = useCallback(() => {
    setActiveModal('time');
  }, []);
  
  const closeModals = useCallback(() => {
    setActiveModal(null);
  }, []);
  
  // Keyboard event handling with cleanup
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && activeModal) {
        closeModals();
      }
    };
    
    const handleClickOutside = (e: MouseEvent) => {
      // Let the backdrop handle click outside events
      // This is just for additional safety
    };

    if (activeModal) {
      document.addEventListener('keydown', handleEscape);
      return () => {
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [activeModal, closeModals]);
  
  // Focus management for accessibility
  const manageFocus = useCallback((containerRef: RefObject<HTMLElement | null>) => {
    if (!activeModal || !containerRef.current) return;
    
    const focusableElements = containerRef.current.querySelectorAll(
      'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length > 0) {
      (focusableElements[0] as HTMLElement).focus();
    }
  }, [activeModal]);
  
  return {
    activeModal,
    showDatePicker: activeModal === 'date',
    showTimePicker: activeModal === 'time',
    openDatePicker,
    openTimePicker,
    closeModals,
    manageFocus,
  };
};

export const useFocusTrap = (isActive: boolean, containerRef: RefObject<HTMLElement | null>) => {
  useEffect(() => {
    if (!isActive || !containerRef.current) return;
    
    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length === 0) return;
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    
    // Focus first element
    firstElement.focus();
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey && document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    };
    
    container.addEventListener('keydown', handleTabKey);
    return () => container.removeEventListener('keydown', handleTabKey);
  }, [isActive]);
};

// Media query hook for responsive behavior
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);
    
    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };
    
    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);
  
  return matches;
};