import { useState } from 'react';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { getApiUrl } from '@/lib/api';

interface UseDeleteInvoiceReturn {
  deleteInvoice: (invoiceId: string) => Promise<{ success: boolean; error?: string }>;
  isDeleting: boolean;
}

export const useDeleteInvoice = (): UseDeleteInvoiceReturn => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { authenticatedFetch } = useAuthenticatedFetch();

  const deleteInvoice = async (invoiceId: string): Promise<{ success: boolean; error?: string }> => {
    console.log('🗑️ Deleting invoice:', invoiceId);
    setIsDeleting(true);

    try {
      const response = await authenticatedFetch(getApiUrl(`/api/invoices/${invoiceId}`), {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        console.log('✅ Invoice deleted successfully');
        return { success: true };
      } else {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || `Failed to delete invoice (${response.status})`;
        console.error('❌ Failed to delete invoice:', errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = 'Error deleting invoice';
      console.error('❌ Error deleting invoice:', err);
      return { success: false, error: errorMessage };
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    deleteInvoice,
    isDeleting,
  };
};