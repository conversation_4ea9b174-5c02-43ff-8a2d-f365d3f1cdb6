'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface PendingInvitationStats {
  count: number;
  invitations: any[];
}

export function usePendingInvitations(teamId: string | null) {
  const [stats, setStats] = useState<PendingInvitationStats>({ count: 0, invitations: [] });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { authenticatedGet } = useAuthenticatedFetch();

  const fetchInvitations = useCallback(async () => {
    if (!teamId) {
      setStats({ count: 0, invitations: [] });
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await authenticatedGet(`/api/workforce/${teamId}/invitations`);
      
      if (!response.ok) {
        if (response.status === 403) {
          // User doesn't have permission to view invitations, that's okay
          setStats({ count: 0, invitations: [] });
          return;
        }
        throw new Error(`Failed to fetch invitations: ${response.status}`);
      }

      const invitations = await response.json();
      const pendingInvitations = Array.isArray(invitations) 
        ? invitations.filter(inv => inv.status === 'pending')
        : [];

      setStats({
        count: pendingInvitations.length,
        invitations: pendingInvitations
      });
    } catch (err) {
      console.error('Error fetching pending invitations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch pending invitations');
      setStats({ count: 0, invitations: [] });
    } finally {
      setIsLoading(false);
    }
  }, [teamId, authenticatedGet]);

  useEffect(() => {
    fetchInvitations();
  }, [fetchInvitations]);

  return {
    stats,
    isLoading,
    error,
    refetch: fetchInvitations
  };
}