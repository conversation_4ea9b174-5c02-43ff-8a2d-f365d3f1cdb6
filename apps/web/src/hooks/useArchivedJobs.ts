import { useState, useEffect, useCallback } from 'react';
import { Job } from '@/types/Job';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface UseArchivedJobsParams {
  search?: string;
  limit?: number;
  offset?: number;
}

export interface UseArchivedJobsReturn {
  data: Job[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  unarchiveJob: (jobId: string) => Promise<void>;
  hasMore: boolean;
  loadMore: () => void;
}

export function useArchivedJobs({
  search = '',
  limit = 20,
  offset = 0,
}: UseArchivedJobsParams = {}): UseArchivedJobsReturn {
  const [data, setData] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const { authenticatedGet, authenticatedFetch } = useAuthenticatedFetch();

  const fetchArchivedJobs = useCallback(async (isLoadMore = false) => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters for archived jobs only
      const params = new URLSearchParams();
      params.append('limit', limit.toString());
      params.append('offset', (isLoadMore ? data.length : 0).toString());
      params.append('status', 'archived'); // Only get archived jobs
      
      if (search) {
        params.append('search', search);
      }

      try {
        // Try calling the actual jobs API for archived jobs
        const response = await authenticatedGet(getApiUrl(`/api/jobs?${params.toString()}`));
        if (!response.ok) {
          if (response.status >= 500) {
            // Treat as empty dataset
            setData([]);
            setHasMore(false);
            setIsLoading(false);
            return;
          }
          throw new Error('API not available');
        }
        const result = await response.json();

        // Use real API data
        const archivedJobs = result.jobs || [];

        // Update state with real data
        if (isLoadMore) {
          setData(prev => [...prev, ...archivedJobs]);
        } else {
          setData(archivedJobs);
        }
        
        setHasMore(result.hasMore || false);
        
      } catch (apiError) {
        console.error('Error fetching archived jobs:', apiError);
        
        // Generate mock archived jobs for demonstration
        const mockArchivedJobs: Job[] = [
          {
            id: 'job-arch-001',
            title: 'Kitchen Renovation - Smith Residence',
            client: {
              id: 'client-003',
              name: 'John & Sarah Smith',
              email: '<EMAIL>',
              phone: '+44 7700 900123'
            },
            status: 'archived' as const,
            scheduled_at: '2024-11-01T09:00:00Z',
            created_at: '2024-10-15T09:00:00Z',
            updated_at: '2024-12-20T17:00:00Z',
            assigned_to: { 
              id: 'user-001',
              full_name: 'Mike Johnson' 
            },
            hasNotes: true
          },
          {
            id: 'job-arch-002',
            title: 'Bathroom Refurbishment - Davis Property',
            client: {
              id: 'client-004',
              name: 'Emma Davis',
              email: '<EMAIL>',
              phone: '+44 7700 900124'
            },
            status: 'archived' as const,
            scheduled_at: '2024-10-05T08:30:00Z',
            created_at: '2024-09-20T10:00:00Z',
            updated_at: '2024-11-30T16:30:00Z',
            assigned_to: { 
              id: 'user-002',
              full_name: 'Tom Wilson' 
            },
            hasNotes: true
          },
          {
            id: 'job-arch-003',
            title: 'Garden Landscaping - Wilson Estate',
            client: {
              id: 'client-005',
              name: 'Robert Wilson',
              email: '<EMAIL>',
              phone: '+44 7700 900125'
            },
            status: 'archived' as const,
            scheduled_at: '2024-09-01T07:00:00Z',
            created_at: '2024-08-10T11:00:00Z',
            updated_at: '2024-10-15T15:00:00Z',
            assigned_to: { 
              id: 'user-003',
              full_name: 'Dave Smith' 
            },
            hasNotes: true
          },
          {
            id: 'job-arch-004',
            title: 'Roof Repair - Thompson House',
            client: {
              id: 'client-006',
              name: 'Lisa Thompson',
              email: '<EMAIL>',
              phone: '+44 7700 900126'
            },
            status: 'archived' as const,
            scheduled_at: '2024-11-07T08:00:00Z',
            created_at: '2024-11-05T12:00:00Z',
            updated_at: '2024-11-20T14:00:00Z',
            assigned_to: { 
              id: 'user-001',
              full_name: 'Mike Johnson' 
            },
            hasNotes: true
          },
          {
            id: 'job-arch-005',
            title: 'Office Renovation - Tech Startup',
            client: {
              id: 'client-007',
              name: 'InnovateTech Ltd',
              email: '<EMAIL>',
              phone: '+44 7700 900127'
            },
            status: 'archived' as const,
            scheduled_at: '2024-08-01T09:00:00Z',
            created_at: '2024-07-15T13:00:00Z',
            updated_at: '2024-09-30T17:00:00Z',
            assigned_to: { 
              id: 'user-002',
              full_name: 'Tom Wilson' 
            },
            hasNotes: true
          }
        ];

        // Filter by search term if provided
        const filteredJobs = search 
          ? mockArchivedJobs.filter(job => 
              job.title.toLowerCase().includes(search.toLowerCase()) ||
              job.client.name.toLowerCase().includes(search.toLowerCase())
            )
          : mockArchivedJobs;

        // Apply pagination
        const startIndex = isLoadMore ? data.length : 0;
        const endIndex = startIndex + limit;
        const paginatedJobs = filteredJobs.slice(startIndex, endIndex);

        if (isLoadMore) {
          setData(prev => [...prev, ...paginatedJobs]);
        } else {
          setData(paginatedJobs);
        }
        
        setHasMore(endIndex < filteredJobs.length);
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch archived jobs');
    } finally {
      setIsLoading(false);
    }
  }, [search, limit, data.length]);

  const unarchiveJob = async (jobId: string): Promise<void> => {
    try {
      setError(null);

      // Try calling the actual API to unarchive the job
      try {
        const response = await authenticatedFetch(getApiUrl(`/api/jobs/${jobId}/status`), {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ status: 'completed' }), // Move back to completed status
        });

        if (!response.ok) {
          throw new Error('API not available');
        }

        // Remove the job from the archived list
        setData(prev => prev.filter(job => job.id !== jobId));
        
      } catch (apiError) {
        console.error('Error unarchiving job:', apiError);
        
        // Mock the unarchive action
        setData(prev => prev.filter(job => job.id !== jobId));
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to unarchive job');
      throw err;
    }
  };

  const refetch = useCallback(() => {
    fetchArchivedJobs(false);
  }, [fetchArchivedJobs]);

  const loadMore = () => {
    if (!isLoading && hasMore) {
      fetchArchivedJobs(true);
    }
  };

  useEffect(() => {
    refetch();
  }, [search]); // Re-fetch when search changes

  return {
    data,
    isLoading,
    error,
    refetch,
    unarchiveJob,
    hasMore,
    loadMore,
  };
} 