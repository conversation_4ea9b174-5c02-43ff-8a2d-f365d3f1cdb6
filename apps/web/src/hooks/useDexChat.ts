import { useState, useEffect, useCallback } from 'react';
import { ChatMessage, ChatContext, ChatResponse, DexChatState } from '@/types/Chat';
import { supabase } from '@/lib/supabase';

export const useDexChat = (context?: ChatContext) => {
  const [state, setState] = useState<DexChatState>({
    messages: [],
    isLoading: false,
    isTyping: false,
    error: null
  });

  // Helper function to get auth headers
  const getAuthHeaders = useCallback(async () => {
    const { data: { session } } = await supabase.auth.getSession();

    // Always include Content-Type for JSON requests
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // Only attach Authorization header if we actually have a token
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }

    return headers;
  }, []);

  const loadMessages = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const headers = await getAuthHeaders();
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const response = await fetch(`${apiUrl}/api/chat/messages`, {
        headers
      });

      if (!response.ok) {
        // Don't treat auth failures as critical errors - just start with empty chat
        console.warn('Could not load chat history:', response.statusText);
        setState(prev => ({ ...prev, messages: [], isLoading: false }));
        return;
      }

      const messages: ChatMessage[] = await response.json();
      setState(prev => ({ ...prev, messages, isLoading: false }));
    } catch (error) {
      console.warn('Error loading chat messages, starting fresh:', error);
      // Always clear loading state, even on error
      setState(prev => ({ 
        ...prev, 
        messages: [], 
        isLoading: false, 
        error: null // Don't show error for initial load failure
      }));
    }
  }, [getAuthHeaders]);

  // Load chat messages on mount
  useEffect(() => {
    loadMessages();
  }, [loadMessages]);

  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;

    const userMessage: Partial<ChatMessage> = {
      message: message.trim(),
      is_from_user: true,
      context: (context || {}) as Record<string, unknown>,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add user message to UI immediately and start typing
    setState(prev => {
      if (prev.isTyping) return prev; // Prevent multiple sends
      return {
        ...prev,
        messages: [...prev.messages, userMessage as ChatMessage],
        isTyping: true,
        error: null
      };
    });

    try {
      // Save user message to database
      const userHeaders = await getAuthHeaders();
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const saveUserResponse = await fetch(`${apiUrl}/api/chat/messages`, {
        method: 'POST',
        headers: userHeaders,
        body: JSON.stringify({
          message: message.trim(),
          is_from_user: true,
          context: (context || {}) as Record<string, unknown>
        })
      });

      if (!saveUserResponse.ok) {
        throw new Error('Failed to save user message');
      }

      // Get AI response from Dex
      const aiHeaders = await getAuthHeaders();
      
      const aiResponse = await fetch(`${apiUrl}/api/ai/dex-chat`, {
        method: 'POST',
        headers: aiHeaders,
        body: JSON.stringify({
          message: message.trim(),
          context: (context || {}) as Record<string, unknown>
        })
      });

      if (!aiResponse.ok) {
        const errorData = await aiResponse.json();
        
        // Handle rate limiting specifically
        if (aiResponse.status === 429) {
          const fallbackMessage = errorData.fallback || "I'm taking a break. Please try again in a few minutes.";
          const dexMessage: Partial<ChatMessage> = {
            message: fallbackMessage,
            is_from_user: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          
          setState(prev => ({
            ...prev,
            messages: [...prev.messages, dexMessage as ChatMessage],
            isTyping: false
          }));
          return;
        }
        
        throw new Error(errorData.details || 'Failed to get AI response');
      }

      const chatResponse: ChatResponse = await aiResponse.json();
      
      // Save AI response to database and add to UI
      const saveAiHeaders = await getAuthHeaders();
      const saveAiResponse = await fetch(`${apiUrl}/api/chat/messages`, {
        method: 'POST',
        headers: saveAiHeaders,
        body: JSON.stringify({
          message: chatResponse.message,
          is_from_user: false,
          context: (context || {}) as Record<string, unknown>
        })
      });

      if (!saveAiResponse.ok) {
        throw new Error('Failed to save AI response');
      }

      const savedAiMessage: ChatMessage = await saveAiResponse.json();

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, savedAiMessage],
        isTyping: false
      }));

    } catch (error) {
      console.error('Error sending message:', error);
      
      // Add error message to chat
      const errorMessage: Partial<ChatMessage> = {
        message: "Sorry, I'm having trouble right now. Please try again or ask about job pricing, customer management, or business advice.",
        is_from_user: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      setState(prev => ({
        ...prev,
        messages: [...prev.messages, errorMessage as ChatMessage],
        isTyping: false,
        error: error instanceof Error ? error.message : 'Failed to send message'
      }));
    }
  }, [context, getAuthHeaders]);

  const clearChat = useCallback(async () => {
    try {
      const headers = await getAuthHeaders();
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const response = await fetch(`${apiUrl}/api/chat/messages`, {
        method: 'DELETE',
        headers
      });

      if (!response.ok) {
        throw new Error('Failed to clear chat');
      }

      setState(prev => ({ ...prev, messages: [], error: null }));
    } catch (error) {
      console.error('Error clearing chat:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to clear chat'
      }));
    }
  }, [getAuthHeaders]);

  const retryLastMessage = useCallback(() => {
    const lastUserMessage = [...state.messages].reverse().find(msg => msg.is_from_user);
    if (lastUserMessage) {
      // Remove the last AI response if it was an error
      const messagesWithoutLastAi = state.messages.filter((msg, index) => {
        if (index === state.messages.length - 1 && !msg.is_from_user) {
          return false;
        }
        return true;
      });
      
      setState(prev => ({ ...prev, messages: messagesWithoutLastAi }));
      sendMessage(lastUserMessage.message);
    }
  }, [state.messages, sendMessage]);

  return {
    ...state,
    sendMessage,
    clearChat,
    retryLastMessage,
    refreshMessages: loadMessages
  };
};
