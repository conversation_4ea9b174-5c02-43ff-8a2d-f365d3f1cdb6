import { useState, useEffect } from 'react';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface Contract {
  id: string;
  job_id: string;
  terms: string;
  status: 'draft' | 'sent' | 'signed' | 'completed';
  created_at: string;
  updated_at: string;
  created_by: string;
  signed_at?: string;
}

export const useJobContracts = (jobId: string | null) => {
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { authenticatedGet } = useAuthenticatedFetch();

  const fetchContracts = async () => {
    if (!jobId) {
      setContracts([]);
      return;
    }

    console.log('📋 Fetching contracts for job:', jobId);
    setLoading(true);
    setError(null);

    try {
      const response = await authenticatedGet(getApiUrl(`/api/jobs/${jobId}/contracts`));
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Contracts fetched successfully:', data);
        setContracts(data.contracts || data);
      } else {
        console.error('Failed to fetch contracts:', response.status, response.statusText);
        setError('Failed to fetch contracts');
        setContracts([]);
      }
    } catch (error) {
      console.error('Error fetching contracts:', error);
      setError('Error fetching contracts');
      setContracts([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContracts();
  }, [jobId]);

  const refetch = () => {
    fetchContracts();
  };

  return {
    contracts,
    loading,
    error,
    refetch
  };
}; 