import { useState, useEffect, useCallback } from 'react';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useAuth } from '@/contexts/AuthContext';

export interface NavigationCounts {
  jobs: number;
  clients: number;
  team: number;
  invoices: number;
}

interface UseNavigationCountsReturn {
  counts: NavigationCounts;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useNavigationCounts = (): UseNavigationCountsReturn => {
  const [counts, setCounts] = useState<NavigationCounts>({
    jobs: 0,
    clients: 0,
    team: 0,
    invoices: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { user, loading: authLoading } = useAuth();
  const { authenticatedGet } = useAuthenticatedFetch();
  const isAuthenticated = !authLoading && !!user;

  const fetchCounts = useCallback(async () => {
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Fetch all counts in parallel for better performance
      const [
        jobsResponse,
        clientsResponse,
        teamResponse,
        invoicesResponse
      ] = await Promise.all([
        authenticatedGet('/api/jobs?limit=1'),
        authenticatedGet('/api/clients?limit=1'),
        authenticatedGet('/api/workforce'),
        authenticatedGet('/api/invoices?limit=1')
      ]);

      // Initialize counts
      let newCounts: NavigationCounts = {
        jobs: 0,
        clients: 0,
        team: 0,
        invoices: 0,
      };

      // Process jobs count
      if (jobsResponse.ok) {
        const jobsData = await jobsResponse.json();
        newCounts.jobs = jobsData.total || jobsData.count || 0;
      }

      // Process clients count
      if (clientsResponse.ok) {
        const clientsData = await clientsResponse.json();
        newCounts.clients = clientsData.total || clientsData.count || 0;
      }

      // Process team count - we need to get total team members across all teams
      if (teamResponse.ok) {
        const teamData = await teamResponse.json();
        if (Array.isArray(teamData) && teamData.length > 0) {
          // For each team, get the member count
          let totalMembers = 0;

          for (const team of teamData) {
            try {
              const membersResponse = await authenticatedGet(`/api/workforce/${team.id}/members`);
              if (membersResponse.ok) {
                const membersData = await membersResponse.json();
                if (Array.isArray(membersData)) {
                  totalMembers += membersData.length;
                }
              }
            } catch (error) {
              console.warn('Error fetching members for team:', team.id, error);
            }
          }

          newCounts.team = totalMembers;
        } else {
          newCounts.team = 0;
        }
      }

      // Process invoices count - check pagination.total first
      if (invoicesResponse.ok) {
        const invoicesData = await invoicesResponse.json();
        newCounts.invoices = invoicesData.pagination?.total || invoicesData.total || invoicesData.count || 0;
      }

      setCounts(newCounts);
    } catch (err) {
      console.error('Error fetching navigation counts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch navigation counts');
      
      // Set fallback counts to avoid showing zeros if there's an error
      setCounts({
        jobs: 0,
        clients: 0,
        team: 0,
        invoices: 0,
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, authenticatedGet]);

  useEffect(() => {
    fetchCounts();
  }, [fetchCounts]);

  return {
    counts,
    isLoading,
    error,
    refetch: fetchCounts,
  };
};