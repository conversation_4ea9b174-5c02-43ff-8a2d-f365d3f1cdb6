import { useState, useEffect } from 'react';
import { getApiUrl } from '@/lib/api';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface Invoice {
  id: string;
  job_id: string;
  amount: number;
  tax: number;
  details: string;
  line_items?: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  due_date: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  created_at: string;
  updated_at: string;
  created_by: string;
  // Join fields from related tables
  job?: {
    id: string;
    title: string;
    client_id: string;
  };
  client?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
}

interface UseInvoicesParams {
  search?: string;
  status?: string[];
  limit?: number;
  offset?: number;
}

interface UseInvoicesReturn {
  invoices: Invoice[];
  stats: {
    totalRevenue: number;
    pendingAmount: number;
    overdueAmount: number;
    draftCount: number;
  };
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  hasMore: boolean;
  loadMore: () => void;
}

export const useInvoices = (params: UseInvoicesParams = {}): UseInvoicesReturn => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [stats, setStats] = useState({
    totalRevenue: 0,
    pendingAmount: 0,
    overdueAmount: 0,
    draftCount: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentOffset, setCurrentOffset] = useState(0);
  const { authenticatedGet } = useAuthenticatedFetch();

  const { search, status, limit = 20 } = params;

  const fetchInvoices = async (offset = 0, isLoadMore = false) => {
    console.log('📋 Fetching invoices with params:', { search, status, limit, offset });
    setIsLoading(true);
    if (!isLoadMore) {
      setError(null);
    }

    try {
      const searchParams = new URLSearchParams();
      if (search) searchParams.append('search', search);
      if (status && status.length > 0) {
        status.forEach(s => searchParams.append('status', s));
      }
      searchParams.append('limit', limit.toString());
      searchParams.append('offset', offset.toString());
      searchParams.append('include_relations', 'true');

      const response = await authenticatedGet(getApiUrl(`/api/invoices?${searchParams.toString()}`));
      
      if (response.ok) {
        const data = await response.json();
        console.log('📋 Fetched invoices:', data);
        
        // Convert string amounts to numbers (Supabase returns numeric as strings)
        const invoicesWithNumbers = (data.invoices || []).map((invoice: any) => ({
          ...invoice,
          amount: parseFloat(invoice.amount) || 0,
          tax: parseFloat(invoice.tax) || 0
        }));
        
        if (isLoadMore) {
          setInvoices(prev => [...prev, ...invoicesWithNumbers]);
        } else {
          setInvoices(invoicesWithNumbers);
        }

        // Update stats
        if (data.stats) {
          setStats({
            totalRevenue: parseFloat(data.stats.totalRevenue) || 0,
            pendingAmount: parseFloat(data.stats.pendingAmount) || 0,
            overdueAmount: parseFloat(data.stats.overdueAmount) || 0,
            draftCount: parseInt(data.stats.draftCount) || 0
          });
        }

        setHasMore(invoicesWithNumbers.length === limit);
        setCurrentOffset(offset + invoicesWithNumbers.length);
      } else {
        console.error('❌ Failed to fetch invoices:', response.status, response.statusText);
        setError('Failed to fetch invoices');
      }
    } catch (err) {
      setError('Error fetching invoices');
      console.error('Error fetching invoices:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setCurrentOffset(0);
    setHasMore(true);
    fetchInvoices(0, false);
  }, [search, status]);

  const loadMore = () => {
    if (!isLoading && hasMore) {
      fetchInvoices(currentOffset, true);
    }
  };

  const refetch = () => {
    setCurrentOffset(0);
    setHasMore(true);
    fetchInvoices(0, false);
  };

  return {
    invoices,
    stats,
    isLoading,
    error,
    refetch,
    hasMore,
    loadMore
  };
};