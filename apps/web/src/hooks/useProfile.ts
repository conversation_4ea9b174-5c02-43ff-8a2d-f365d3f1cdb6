import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface UserProfile {
  id: string;
  email: string;
  full_name: string | null;
  company_name: string | null;
  phone: string | null;
  website: string | null;
  address: string | null;
  country: string | null;
  vat_number: string | null;
  created_at: string;
  updated_at: string;
}

interface UpdateProfileData {
  full_name: string;
  company_name: string;
  phone: string;
  website: string;
  address: string;
  country: string;
  vat_number: string;
}

interface UseProfileReturn {
  data: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  updateProfile: (data: UpdateProfileData) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  refetch: () => void;
}

// Mock profile data
const mockProfile: UserProfile = {
  id: 'user-1',
  email: '<EMAIL>',
  full_name: '<PERSON>',
  company_name: 'Ryan West Electrical Ltd',
  phone: '+44 7700 900123',
  website: 'https://westelectrical.co.uk',
  address: '123 High Street\nLondon SW1A 1AA\nUnited Kingdom',
  country: 'United Kingdom',
  vat_number: 'GB123456789',
  created_at: '2024-01-15T09:00:00Z',
  updated_at: '2025-01-20T14:30:00Z'
};

export function useProfile(): UseProfileReturn {
  const { user, profile: authProfile, loading: authLoading } = useAuth();
  const [data, setData] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProfile = () => {
    if (!user) {
      setData(null);
      setIsLoading(false);
      return;
    }

    if (authProfile) {
      // Use actual profile data from auth context
              setData({
          id: authProfile.id,
          email: authProfile.email,
          full_name: authProfile.full_name,
          company_name: authProfile.company_name,
          phone: authProfile.phone,
          website: authProfile.website,
          address: authProfile.address,
          country: authProfile.country,
          vat_number: authProfile.vat_number,
          created_at: authProfile.created_at, // Real user registration date!
          updated_at: authProfile.updated_at
        });
      setIsLoading(false);
    } else if (!authLoading) {
      // Fallback to mock data only if auth profile is not available and auth is not loading
      setIsLoading(true);
      setError(null);
      
      // Simulate API call
      setTimeout(() => {
        try {
          // Simulate potential API errors (2% chance)
          if (Math.random() < 0.02) {
            throw new Error('Failed to fetch profile');
          }
          
          // Return profile data with user's actual email and creation date
          setData({
            ...mockProfile,
            id: user.id,
            email: user.email || mockProfile.email,
            created_at: user.created_at || mockProfile.created_at // Use actual user creation date
          });
        } catch (err) {
          setError(err instanceof Error ? err.message : 'An error occurred');
          setData(null);
        } finally {
          setIsLoading(false);
        }
      }, 600);
    }
  };

  const updateProfile = async (profileData: UpdateProfileData): Promise<void> => {
    if (!data) throw new Error('No profile data available');

    setIsLoading(true);
    setError(null);

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Simulate potential API errors (5% chance)
          if (Math.random() < 0.05) {
            throw new Error('Failed to update profile');
          }

          // Update local data
          setData(prev => prev ? {
            ...prev,
            ...profileData,
            updated_at: new Date().toISOString()
          } : null);

          console.log('Profile updated:', profileData);
          resolve();
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to update profile';
          setError(errorMessage);
          reject(new Error(errorMessage));
        } finally {
          setIsLoading(false);
        }
      }, 1000);
    });
  };

  const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    setIsLoading(true);
    setError(null);

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Simulate current password validation
          if (currentPassword.length < 6) {
            throw new Error('Current password is incorrect');
          }

          // Simulate new password validation
          if (newPassword.length < 6) {
            throw new Error('New password must be at least 6 characters');
          }

          // Simulate potential API errors (3% chance)
          if (Math.random() < 0.03) {
            throw new Error('Failed to change password');
          }

          console.log('Password changed successfully');
          resolve();
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to change password';
          setError(errorMessage);
          reject(new Error(errorMessage));
        } finally {
          setIsLoading(false);
        }
      }, 1500);
    });
  };

  useEffect(() => {
    fetchProfile();
  }, [user, authProfile, authLoading]);

  return {
    data,
    isLoading,
    error,
    updateProfile,
    changePassword,
    refetch: fetchProfile
  };
} 