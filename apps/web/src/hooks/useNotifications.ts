'use client';

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { Notification, NotificationStats, CreateNotificationData } from '@/types';
import { useAuthenticatedAPI } from '@/hooks/useAuthenticatedAPI';

interface UseNotificationsOptions {
  includeRead?: boolean;
  type?: string;
  limit?: number;
  autoRefresh?: boolean;
}

interface UseNotificationsReturn {
  notifications: Notification[];
  stats: NotificationStats | null;
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  createNotification: (data: CreateNotificationData) => Promise<void>;
  refetch: () => Promise<void>;
  loadMore: () => Promise<void>;
}

// Remove the hardcoded API_BASE_URL to use Next.js proxy instead
const API_BASE_URL = '';

export const useNotifications = (options: UseNotificationsOptions = {}): UseNotificationsReturn => {
  const {
    includeRead = false,
    type,
    limit = 20,
    autoRefresh = true
  } = options;

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);

  const { authenticatedGet, authenticatedPost, authenticatedFetch, isAuthenticated } = useAuthenticatedAPI();

  // Fetch notifications
  const fetchNotifications = useCallback(async (loadOffset = 0, append = false) => {
    // Don't fetch if user is not authenticated
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: loadOffset.toString(),
        ...(includeRead ? {} : { is_read: 'false' }),
        ...(type && { type })
      });

      const response = await authenticatedGet(`${API_BASE_URL}/api/notifications?${params}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch notifications: ${response.statusText}`);
      }

      const data: Notification[] = await response.json();
      
      if (append) {
        setNotifications(prev => [...prev, ...data]);
      } else {
        setNotifications(data);
      }

      setHasMore(data.length === limit);
      setOffset(loadOffset + data.length);
      
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err instanceof Error ? err.message : 'Failed to load notifications');
    }
  }, [authenticatedGet, includeRead, type, limit]);

  // Fetch notification stats
  const fetchStats = useCallback(async () => {
    try {
      const response = await authenticatedGet(`${API_BASE_URL}/api/notifications/stats`);

      if (!response.ok) {
        throw new Error(`Failed to fetch notification stats: ${response.statusText}`);
      }

      const data: NotificationStats = await response.json();
      setStats(data);
      
    } catch (err) {
      console.error('Error fetching notification stats:', err);
    }
  }, [authenticatedGet]);

  // Initial load
  useEffect(() => {
    const loadData = async () => {
      // Only load if user is authenticated
      if (!isAuthenticated) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);
      
      await Promise.all([
        fetchNotifications(0, false),
        fetchStats()
      ]);
      
      setIsLoading(false);
    };

    loadData();
  }, [fetchNotifications, fetchStats, isAuthenticated]);

  // Real-time subscription disabled for simplicity in development
  // useEffect(() => {
  //   if (!autoRefresh) return;
  //   const channel = supabase.channel('notifications').subscribe();
  //   return () => supabase.removeChannel(channel);
  // }, [autoRefresh]);

  // Mark single notification as read
  const markAsRead = useCallback(async (id: string) => {
    try {
      const response = await authenticatedFetch(`${API_BASE_URL}/api/notifications/${id}`, {
        method: 'PATCH',
        body: JSON.stringify({ is_read: true })
      });

      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true }
            : notification
        )
      );

      // Update stats
      await fetchStats();
      
    } catch (err) {
      console.error('Error marking notification as read:', err);
      throw err;
    }
  }, [authenticatedFetch, fetchStats]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await authenticatedPost(`${API_BASE_URL}/api/notifications/mark-all-read`, {});

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, is_read: true }))
      );

      // Update stats
      await fetchStats();
      
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      throw err;
    }
  }, [authenticatedPost, fetchStats]);

  // Delete notification
  const deleteNotification = useCallback(async (id: string) => {
    try {
      const response = await authenticatedFetch(`${API_BASE_URL}/api/notifications/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete notification');
      }

      // Update local state
      setNotifications(prev => prev.filter(notification => notification.id !== id));

      // Update stats
      await fetchStats();
      
    } catch (err) {
      console.error('Error deleting notification:', err);
      throw err;
    }
  }, [authenticatedFetch, fetchStats]);

  // Create notification
  const createNotification = useCallback(async (data: CreateNotificationData) => {
    try {
      const response = await authenticatedPost(`${API_BASE_URL}/api/notifications`, data);

      if (!response.ok) {
        throw new Error('Failed to create notification');
      }

      // Refetch data to include new notification
      await fetchNotifications(0, false);
      await fetchStats();
      
    } catch (err) {
      console.error('Error creating notification:', err);
      throw err;
    }
  }, [authenticatedPost, fetchNotifications, fetchStats]);

  // Refetch data
  const refetch = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    await Promise.all([
      fetchNotifications(0, false),
      fetchStats()
    ]);
    
    setIsLoading(false);
  }, [fetchNotifications, fetchStats]);

  // Load more notifications
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoading) return;
    
    await fetchNotifications(offset, true);
  }, [hasMore, isLoading, offset, fetchNotifications]);

  return {
    notifications,
    stats,
    isLoading,
    error,
    hasMore,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    createNotification,
    refetch,
    loadMore
  };
}; 