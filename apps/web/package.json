{"name": "@deskbelt/web", "version": "1.0.0", "private": true, "description": "DeskBelt Web Application", "scripts": {"dev": "next dev --turbo", "dev:legacy": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "cypress open", "test:headless": "cypress run"}, "keywords": ["deskbelt", "freelance", "management"], "author": "", "license": "ISC", "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.9", "@tanstack/react-query": "^5.80.2", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "date-fns": "^4.1.0", "next": "^15.3.3", "openai": "^4.63.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "tailwindcss": "^3.4.17", "zod": "^3.25.49"}, "devDependencies": {"@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "cypress": "^14.4.1", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "^5.8.3"}}