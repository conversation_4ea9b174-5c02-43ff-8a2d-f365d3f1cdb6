'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { User, AuthError } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  isAdmin: boolean
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isAdmin: false,
  loading: true,
  signIn: async () => ({ error: null }),
  signOut: async () => {},
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isAdmin, setIsAdmin] = useState(false)
  const [loading, setLoading] = useState(true)

  const checkAdminRole = async (userId: string, email?: string) => {
    try {
      // Development bypass for specific admin email
      if (email === '<EMAIL>') {
        console.log('🔧 Development admin bypass for:', email)
        return true
      }

      const { data, error } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error checking admin role:', error)
        // If user doesn't exist in database <NAME_EMAIL>, allow admin access
        if (email === '<EMAIL>') {
          console.log('🔧 Admin bypass for database user creation')
          return true
        }
        return false
      }

      return data?.role === 'admin' || data?.role === 'super_admin'
    } catch (error) {
      console.error('Error checking admin role:', error)
      return email === '<EMAIL>' // Fallback for admin email
    }
  }

  useEffect(() => {
    const initAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (session?.user) {
          setUser(session.user)
          const adminStatus = await checkAdminRole(session.user.id, session.user.email)
          setIsAdmin(adminStatus)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
      } finally {
        setLoading(false)
      }
    }

    initAuth()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          setUser(session.user)
          const adminStatus = await checkAdminRole(session.user.id, session.user.email)
          setIsAdmin(adminStatus)
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setIsAdmin(false)
        }
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        return { error }
      }

      if (data.user) {
        const adminStatus = await checkAdminRole(data.user.id, data.user.email)
        if (!adminStatus) {
          await supabase.auth.signOut()
          return { 
            error: { 
              message: 'You do not have admin privileges to access this portal.',
              name: 'AuthError'
            } as AuthError 
          }
        }
        setUser(data.user)
        setIsAdmin(true)
      }

      return { error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      return { 
        error: { 
          message: 'An unexpected error occurred',
          name: 'AuthError'
        } as AuthError 
      }
    }
  }

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      setUser(null)
      setIsAdmin(false)
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  return (
    <AuthContext.Provider value={{
      user,
      isAdmin,
      loading,
      signIn,
      signOut,
    }}>
      {children}
    </AuthContext.Provider>
  )
} 