'use client'

import {
  UsersIcon,
  UserGroupIcon,
  BriefcaseIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  BanknotesIcon,
  DocumentIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline'
import { useAdminStats } from '../../hooks/useAdminStats'

export default function AdminDashboard() {
  const { stats, loading, error, refetch } = useAdminStats()

  const formatGrowth = (value: number) => {
    const isPositive = value >= 0
    return (
      <div className={`flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? (
          <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
        ) : (
          <ArrowTrendingDownIcon className="w-4 h-4 mr-1" />
        )}
        <span className="text-sm font-medium">{Math.abs(value)}%</span>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

    if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-700">{error}</div>
          <button
            onClick={refetch}
            className="mt-2 text-red-600 hover:text-red-500 underline"
          >
            Try again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="text-gray-600">System overview and management controls</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.totalUsers || 0}</p>
            </div>
            <UsersIcon className="w-8 h-8 text-primary-600" />
          </div>
          <div className="mt-4 flex items-center justify-between">
            {formatGrowth(stats?.userGrowth || 0)}
            <span className="text-sm text-gray-500">vs last month</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.activeUsers || 0}</p>
            </div>
            <UsersIcon className="w-8 h-8 text-green-600" />
          </div>
          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm text-gray-500">Last 30 days</span>
            <span className="text-sm font-medium text-gray-900">
              {stats?.totalUsers ? Math.round((stats.activeUsers / stats.totalUsers) * 100) : 0}% active
            </span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Workforces</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.totalTeams || 0}</p>
            </div>
            <UserGroupIcon className="w-8 h-8 text-blue-600" />
          </div>
          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm text-gray-500">With multiple members</span>
            <span className="text-sm font-medium text-gray-900">
              Business teams
            </span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Jobs</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.totalJobs || 0}</p>
            </div>
            <BriefcaseIcon className="w-8 h-8 text-blue-600" />
          </div>
          <div className="mt-4 flex items-center justify-between">
            {formatGrowth(stats?.jobGrowth || 0)}
            <span className="text-sm text-gray-500">vs last month</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Clients</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.totalClients || 0}</p>
            </div>
            <BuildingOfficeIcon className="w-8 h-8 text-green-600" />
          </div>
          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm text-gray-500">Across all users</span>
            <span className="text-sm font-medium text-gray-900">
              {stats?.totalUsers ? (stats.totalClients / stats.totalUsers).toFixed(1) : '0.0'} avg/user
            </span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Quotes</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.totalQuotes || 0}</p>
            </div>
            <DocumentTextIcon className="w-8 h-8 text-purple-600" />
          </div>
          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm text-gray-500">All quotes created</span>
            <span className="text-sm font-medium text-gray-900">
              Business docs
            </span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Invoices</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.totalInvoices || 0}</p>
            </div>
            <BanknotesIcon className="w-8 h-8 text-emerald-600" />
          </div>
          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm text-gray-500">All invoices created</span>
            <span className="text-sm font-medium text-gray-900">
              Billing docs
            </span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Contracts</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.totalContracts || 0}</p>
            </div>
            <DocumentIcon className="w-8 h-8 text-indigo-600" />
          </div>
          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm text-gray-500">All contracts created</span>
            <span className="text-sm font-medium text-gray-900">
              Legal docs
            </span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg shadow max-w-2xl">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <a 
            href="/user-management"
            className="p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors block"
          >
            <div className="flex items-center">
              <UsersIcon className="w-5 h-5 text-primary-600 mr-3" />
              <span className="font-medium">Manage Users</span>
            </div>
            <p className="text-sm text-gray-500 mt-1">View and manage user accounts</p>
          </a>
          
          <a 
            href="/team-management"
            className="p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors block"
          >
            <div className="flex items-center">
              <UserGroupIcon className="w-5 h-5 text-blue-600 mr-3" />
              <span className="font-medium">Team Management</span>
            </div>
            <p className="text-sm text-gray-500 mt-1">View workforce management</p>
          </a>
          
          <a 
            href="/analytics"
            className="p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors block"
          >
            <div className="flex items-center">
              <ChartBarIcon className="w-5 h-5 text-green-600 mr-3" />
              <span className="font-medium">Analytics</span>
            </div>
            <p className="text-sm text-gray-500 mt-1">View system analytics</p>
          </a>

          <a 
            href="/audit"
            className="p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors block"
          >
            <div className="flex items-center">
              <ExclamationTriangleIcon className="w-5 h-5 text-orange-600 mr-3" />
              <span className="font-medium">Audit Logs</span>
            </div>
            <p className="text-sm text-gray-500 mt-1">Review system activity</p>
          </a>
        </div>
      </div>
    </div>
  )
}