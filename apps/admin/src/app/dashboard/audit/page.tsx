'use client'

import { useState, useEffect } from 'react'
import {
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  FunnelIcon,
  DocumentDuplicateIcon,
  ShieldCheckIcon,
  UserIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline'

interface AuditLog {
  id: string
  timestamp: string
  user_id: string
  user_email: string
  action: string
  resource: string
  resource_id: string | null
  details: string
  ip_address: string
  user_agent: string
  severity: 'info' | 'warning' | 'error' | 'critical'
  category: 'auth' | 'user' | 'team' | 'job' | 'client' | 'system'
}

interface AuditData {
  logs: AuditLog[]
  totalCount: number
  currentPage: number
  totalPages: number
}

export default function AuditLogs() {
  const [data, setData] = useState<AuditData>({
    logs: [],
    totalCount: 0,
    currentPage: 1,
    totalPages: 1,
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [severityFilter, setSeverityFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [dateRange, setDateRange] = useState('7d')

  useEffect(() => {
    fetchAuditLogs()
  }, [data.currentPage, searchTerm, severityFilter, categoryFilter, dateRange])

  const fetchAuditLogs = async () => {
    try {
      setLoading(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const mockLogs: AuditLog[] = [
        {
          id: '1',
          timestamp: '2024-01-25T15:30:00Z',
          user_id: 'admin-1',
          user_email: '<EMAIL>',
          action: 'USER_ROLE_UPDATED',
          resource: 'user',
          resource_id: 'user-123',
          details: 'Changed user role from tradesperson to admin',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          severity: 'warning',
          category: 'user',
        },
        {
          id: '2',
          timestamp: '2024-01-25T14:15:00Z',
          user_id: 'user-456',
          user_email: '<EMAIL>',
          action: 'LOGIN_SUCCESS',
          resource: 'auth',
          resource_id: null,
          details: 'Successful login via email/password',
          ip_address: '*********',
          user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)',
          severity: 'info',
          category: 'auth',
        },
        {
          id: '3',
          timestamp: '2024-01-25T13:45:00Z',
          user_id: 'user-789',
          user_email: '<EMAIL>',
          action: 'JOB_CREATED',
          resource: 'job',
          resource_id: 'job-1001',
          details: 'Created new job: Kitchen Electrical Rewiring',
          ip_address: '************',
          user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          severity: 'info',
          category: 'job',
        },
        {
          id: '4',
          timestamp: '2024-01-25T13:20:00Z',
          user_id: 'system',
          user_email: '<EMAIL>',
          action: 'BACKUP_FAILED',
          resource: 'system',
          resource_id: 'backup-daily',
          details: 'Daily database backup failed - disk space issue',
          ip_address: '127.0.0.1',
          user_agent: 'System/1.0',
          severity: 'error',
          category: 'system',
        },
        {
          id: '5',
          timestamp: '2024-01-25T12:00:00Z',
          user_id: 'user-321',
          user_email: '<EMAIL>',
          action: 'TEAM_MEMBER_ADDED',
          resource: 'team',
          resource_id: 'team-001',
          details: 'Added <EMAIL> to BuildRight Construction team',
          ip_address: '***********',
          user_agent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
          severity: 'info',
          category: 'team',
        },
        {
          id: '6',
          timestamp: '2024-01-25T10:30:00Z',
          user_id: 'user-654',
          user_email: '<EMAIL>',
          action: 'LOGIN_FAILED',
          resource: 'auth',
          resource_id: null,
          details: 'Failed login attempt - incorrect password (5th attempt)',
          ip_address: '*************',
          user_agent: 'curl/7.68.0',
          severity: 'critical',
          category: 'auth',
        },
      ]

      setData({
        logs: mockLogs,
        totalCount: mockLogs.length,
        currentPage: 1,
        totalPages: 1,
      })
    } catch (err) {
      setError('Failed to load audit logs')
      console.error('Audit logs error:', err)
    } finally {
      setLoading(false)
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
      case 'error':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />
      case 'info':
      default:
        return <InformationCircleIcon className="w-5 h-5 text-blue-500" />
    }
  }

  const getSeverityBadge = (severity: string) => {
    const colors = {
      critical: 'bg-red-100 text-red-800',
      error: 'bg-red-50 text-red-700',
      warning: 'bg-yellow-100 text-yellow-800',
      info: 'bg-blue-100 text-blue-800',
    }
    
    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${colors[severity as keyof typeof colors]}`}>
        {getSeverityIcon(severity)}
        {severity}
      </span>
    )
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'auth':
        return <ShieldCheckIcon className="w-4 h-4" />
      case 'user':
        return <UserIcon className="w-4 h-4" />
      case 'system':
        return <DocumentDuplicateIcon className="w-4 h-4" />
      default:
        return <InformationCircleIcon className="w-4 h-4" />
    }
  }

  const getCategoryBadge = (category: string) => {
    const colors = {
      auth: 'bg-purple-100 text-purple-800',
      user: 'bg-green-100 text-green-800',
      team: 'bg-blue-100 text-blue-800',
      job: 'bg-indigo-100 text-indigo-800',
      client: 'bg-orange-100 text-orange-800',
      system: 'bg-gray-100 text-gray-800',
    }
    
    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${colors[category as keyof typeof colors]}`}>
        {getCategoryIcon(category)}
        {category}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <div className="h-10 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div className="divide-y divide-gray-200">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="h-5 w-5 bg-gray-200 rounded"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-700">{error}</div>
          <button
            onClick={fetchAuditLogs}
            className="mt-2 text-red-600 hover:text-red-500 underline"
          >
            Try again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Audit Logs</h1>
        <p className="text-gray-600">Track user actions, system events, and security logs</p>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Severity</label>
            <select
              value={severityFilter}
              onChange={(e) => setSeverityFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">All Severities</option>
              <option value="info">Info</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">All Categories</option>
              <option value="auth">Authentication</option>
              <option value="user">User</option>
              <option value="team">Team</option>
              <option value="job">Job</option>
              <option value="client">Client</option>
              <option value="system">System</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Time Range</label>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="1h">Last Hour</option>
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
            </select>
          </div>
        </div>
      </div>

      {/* Audit Logs Table */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            Audit Events ({data.totalCount})
          </h2>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.logs.map((log) => (
                <tr key={log.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-500">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      {formatTimestamp(log.timestamp)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">
                        {log.user_email}
                      </div>
                      <div className="text-gray-500 text-xs">
                        {log.ip_address}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {log.action.replace(/_/g, ' ')}
                    </div>
                    {log.resource_id && (
                      <div className="text-xs text-gray-500">
                        {log.resource}:{log.resource_id}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getCategoryBadge(log.category)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getSeverityBadge(log.severity)}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {log.details}
                    </div>
                    {log.user_agent && log.user_agent !== 'System/1.0' && (
                      <div className="text-xs text-gray-500 mt-1 max-w-xs truncate">
                        {log.user_agent.split(' ')[0]}
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="bg-white px-6 py-4 border-t border-gray-200">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              disabled={data.currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              disabled={data.currentPage === data.totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">1</span> to{' '}
                <span className="font-medium">{data.logs.length}</span> of{' '}
                <span className="font-medium">{data.totalCount}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  disabled={data.currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeftIcon className="h-5 w-5" />
                </button>
                <button
                  disabled={data.currentPage === data.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRightIcon className="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 