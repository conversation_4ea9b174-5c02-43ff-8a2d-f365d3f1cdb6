'use client'

import { useState } from 'react'
// Using native HTML date inputs instead of external library
import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  UsersIcon,
  BriefcaseIcon,
  ClockIcon,
  CalendarIcon,
  CurrencyPoundIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline'
import { useAdminStats, formatCurrency, formatDate, type AnalyticsRange, type AnalyticsMetric } from '../../../hooks/useAdminStats'

export default function Analytics() {
  const [timeRange, setTimeRange] = useState<AnalyticsRange>('last_30d')
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [selectedMetrics] = useState<AnalyticsMetric[]>([
    'users_new', 
    'users_active', 
    'clients_new',
    'jobs_created', 
    'quotes_created',
    'invoices_created',
    'contracts_created',
    'ai_messages'
  ])

  const { stats, analytics, loading, error, refetch } = useAdminStats({
    range: timeRange,
    startDate: startDate?.toISOString(),
    endDate: endDate?.toISOString(),
    metrics: selectedMetrics,
    timeSeries: true,
    includeComparison: true
  })

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    format = 'number' 
  }: {
    title: string
    value: number
    change?: number
    icon: any
    format?: 'number' | 'currency' | 'percentage' | 'time'
  }) => {
    const formatValue = () => {
      switch (format) {
        case 'currency':
          return formatCurrency(value)
        case 'percentage':
          return `${value}%`
        case 'time':
          return `${value}min`
        default:
          return value.toLocaleString()
      }
    }

    return (
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{formatValue()}</p>
          </div>
          <Icon className="w-6 h-6 text-primary-600" />
        </div>
        {change !== undefined && (
          <div className="mt-2 flex items-center">
            {change >= 0 ? (
              <ArrowTrendingUpIcon className="w-3 h-3 text-green-600 mr-1" />
            ) : (
              <ArrowTrendingDownIcon className="w-3 h-3 text-red-600 mr-1" />
            )}
            <span className={`text-xs font-medium ${
              change >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatPercentage(change)}
            </span>
            <span className="text-xs text-gray-500 ml-1">vs last period</span>
          </div>
        )}
      </div>
    )
  }

  const TimeSeriesChart = ({ 
    data, 
    title, 
    color = 'primary',
    format = 'number'
  }: { 
    data: Array<{date: string, value: number}>, 
    title: string,
    color?: 'primary' | 'green' | 'blue' | 'purple',
    format?: 'number' | 'currency'
  }) => {
    if (!data || data.length === 0) {
      return (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
          <div className="flex items-center justify-center h-32 text-gray-500">
            No activity in this period
          </div>
        </div>
      )
    }

    const max = Math.max(...data.map(d => d.value))
    const min = Math.min(...data.map(d => d.value))
    const range = max - min || 1

    // Detect all-zero dataset for friendly placeholder
    const allZero = max === 0 && min === 0
    if (allZero) {
      return (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
          <div className="flex items-center justify-center h-32 text-gray-500">
            No activity in this period
          </div>
        </div>
      )
    }

    const colorClasses = {
      primary: 'bg-primary-600',
      green: 'bg-green-600',
      blue: 'bg-blue-600',
      purple: 'bg-purple-600'
    }

    const formatValue = (value: number) => {
      if (format === 'currency') {
        return formatCurrency(value)
      }
      return value.toLocaleString()
    }

    return (
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
        <div className="relative overflow-x-auto">
          {/* Chart area - bars with value labels */}
          <div className="h-40 flex items-end space-x-1 mb-2">
            {data.map((point, index) => {
              const maxBarHeight = 140 // Maximum height for bars (leaving space for labels)
              const height = range > 0 ? ((point.value - min) / range) * maxBarHeight : 8
              return (
                <div key={index} className="flex-shrink-0 flex flex-col items-center">
                  {/* Value label above bar (only if value > 0) */}
                  {point.value > 0 && (
                    <div className="text-xs font-medium text-gray-700 mb-1">
                      {format === 'currency' ? formatCurrency(point.value) : point.value}
                    </div>
                  )}
                  {/* Bar */}
                  <div 
                    className={`w-6 ${point.value === 0 ? 'bg-gray-300' : colorClasses[color]} rounded-t transition-all duration-300 hover:opacity-80 cursor-pointer`}
                    style={{ height: `${Math.max(height, 8)}px` }}
                    title={`${formatDate(point.date)}: ${formatValue(point.value)}`}
                  />
                </div>
              )
            })}
          </div>
          
          {/* Date labels - separate row with more space */}
          <div className="flex space-x-1 h-8">
            {data.map((point, index) => (
              <div key={index} className="w-6 flex-shrink-0 flex justify-center items-start">
                <span className="text-xs text-gray-500 transform -rotate-45 whitespace-nowrap leading-none">
                  {formatDate(point.date)}
                </span>
              </div>
            ))}
          </div>
        </div>
        
        <div className="mt-4 text-sm text-gray-600">
          <span className="font-medium">Sum:</span> {formatValue(data.reduce((sum, point) => sum + point.value, 0))}
          {data.length > 0 && (
            <span className="ml-4"><span className="font-medium">Avg/day:</span> {formatValue(Math.round(data.reduce((s,p)=>s+p.value,0) / data.length * 100) / 100)}</span>
          )}
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-700 font-medium">Failed to load analytics data</div>
          <div className="text-red-600 text-sm mt-1">{error}</div>
          <button
            onClick={refetch}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  // Calculate summary stats from time-series data
  const getSummaryStats = () => {
    if (!analytics) return null

    const totalNewUsers = analytics.users_new?.reduce((sum, point) => sum + point.value, 0) || 0
    const totalActiveUsers = analytics.users_active?.reduce((sum, point) => sum + point.value, 0) || 0
    const totalNewClients = analytics.clients_new?.reduce((sum, point) => sum + point.value, 0) || 0
    const totalJobsCreated = analytics.jobs_created?.reduce((sum, point) => sum + point.value, 0) || 0
    const totalQuotesCreated = analytics.quotes_created?.reduce((sum, point) => sum + point.value, 0) || 0
    const totalInvoicesCreated = analytics.invoices_created?.reduce((sum, point) => sum + point.value, 0) || 0
    const totalContractsCreated = analytics.contracts_created?.reduce((sum, point) => sum + point.value, 0) || 0
    const totalAiMessages = analytics.ai_messages?.reduce((sum, point) => sum + point.value, 0) || 0

    return {
      totalNewUsers,
      totalActiveUsers,
      totalNewClients,
      totalJobsCreated,
      totalQuotesCreated,
      totalInvoicesCreated,
      totalContractsCreated,
      totalAiMessages
    }
  }

  const summaryStats = getSummaryStats()

  return (
    <div className="p-6">
      <div className="mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">System performance and business metrics</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => {
              const newRange = e.target.value as AnalyticsRange
              setTimeRange(newRange)
              if (newRange !== 'custom') {
                setStartDate(null)
                setEndDate(null)
                setShowDatePicker(false)
              } else {
                setShowDatePicker(true)
              }
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="last_7d">Last 7 Days</option>
            <option value="last_30d">Last 30 Days</option>
            <option value="last_90d">Last 90 Days</option>
            <option value="last_6m">Last 6 Months</option>
            <option value="last_1y">Last 1 Year</option>
            <option value="custom">Custom Range</option>
          </select>
          
                     {showDatePicker && (
             <div className="flex items-center space-x-2">
               <input
                 type="date"
                 value={startDate ? startDate.toISOString().split('T')[0] : ''}
                 onChange={(e) => setStartDate(e.target.value ? new Date(e.target.value) : null)}
                 className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                 placeholder="Start Date"
               />
               <input
                 type="date"
                 value={endDate ? endDate.toISOString().split('T')[0] : ''}
                 onChange={(e) => setEndDate(e.target.value ? new Date(e.target.value) : null)}
                 min={startDate ? startDate.toISOString().split('T')[0] : undefined}
                 className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                 placeholder="End Date"
               />
             </div>
           )}
        </div>
      </div>

      {/* Summary Metrics */}
      {summaryStats && (
        <div className="mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Summary Metrics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4">
            <MetricCard
              title="New Users"
              value={summaryStats.totalNewUsers}
              change={analytics?.percentage_changes?.users_new}
              icon={UsersIcon}
            />
            <MetricCard
              title="Active Users"
              value={summaryStats.totalActiveUsers}
              change={analytics?.percentage_changes?.users_active}
              icon={ArrowTrendingUpIcon}
            />
            <MetricCard
              title="New Clients"
              value={summaryStats.totalNewClients}
              change={analytics?.percentage_changes?.clients_new}
              icon={DocumentTextIcon}
            />
            <MetricCard
              title="Jobs Created"
              value={summaryStats.totalJobsCreated}
              change={analytics?.percentage_changes?.jobs_created}
              icon={BriefcaseIcon}
            />
            <MetricCard
              title="Quotes Created"
              value={summaryStats.totalQuotesCreated}
              change={analytics?.percentage_changes?.quotes_created}
              icon={DocumentTextIcon}
            />
            <MetricCard
              title="Invoices Created"
              value={summaryStats.totalInvoicesCreated}
              change={analytics?.percentage_changes?.invoices_created}
              icon={CurrencyPoundIcon}
            />
            <MetricCard
              title="Contracts Created"
              value={summaryStats.totalContractsCreated}
              change={analytics?.percentage_changes?.contracts_created}
              icon={ChartBarIcon}
            />
            <MetricCard
              title="AI Messages"
              value={summaryStats.totalAiMessages}
              change={analytics?.percentage_changes?.ai_messages}
              icon={CalendarIcon}
            />
          </div>
        </div>
      )}

      {/* Time Series Charts */}
      {analytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {analytics.users_new && (
            <TimeSeriesChart 
              data={analytics.users_new} 
              title="Daily New Users" 
              color="blue"
            />
          )}
          {analytics.users_active && (
            <TimeSeriesChart 
              data={analytics.users_active} 
              title="Daily Active Users" 
              color="green"
            />
          )}
          {analytics.jobs_created && (
            <TimeSeriesChart 
              data={analytics.jobs_created} 
              title="Daily Jobs Created" 
              color="primary"
            />
          )}
          {analytics.clients_new && (
            <TimeSeriesChart 
              data={analytics.clients_new} 
              title="Daily New Clients" 
              color="blue"
            />
          )}
          {analytics.quotes_created && (
            <TimeSeriesChart 
              data={analytics.quotes_created} 
              title="Daily Quotes Created" 
              color="purple"
            />
          )}
          {analytics.invoices_created && (
            <TimeSeriesChart 
              data={analytics.invoices_created} 
              title="Daily Invoices Created" 
              color="green"
            />
          )}
          {analytics.contracts_created && (
            <TimeSeriesChart 
              data={analytics.contracts_created} 
              title="Daily Contracts Created" 
              color="primary"
            />
          )}
          {analytics.ai_messages && (
            <TimeSeriesChart 
              data={analytics.ai_messages} 
              title="Daily AI Messages" 
              color="blue"
            />
          )}
        </div>
      )}

      {/* Business Performance Summary */}
      {summaryStats && (
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Business Performance Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">
                {summaryStats.totalQuotesCreated + summaryStats.totalInvoicesCreated + summaryStats.totalContractsCreated}
              </div>
              <div className="text-sm text-gray-600">Total Documents Created</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {summaryStats.totalJobsCreated > 0 
                  ? ((summaryStats.totalQuotesCreated / summaryStats.totalJobsCreated) * 100).toFixed(1)
                  : '0'
                }%
              </div>
              <div className="text-sm text-gray-600">Jobs with Quotes</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {summaryStats.totalActiveUsers > 0 && summaryStats.totalNewUsers > 0
                  ? ((summaryStats.totalActiveUsers / summaryStats.totalNewUsers) * 100).toFixed(1)
                  : '0'
                }%
              </div>
              <div className="text-sm text-gray-600">User Retention</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 