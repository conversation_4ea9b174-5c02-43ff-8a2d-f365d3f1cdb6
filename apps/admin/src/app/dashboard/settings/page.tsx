'use client'

import { useState, useEffect } from 'react'
import {
  CogIcon,
  ShieldCheckIcon,
  EnvelopeIcon,
  RocketLaunchIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline'

interface SystemSettings {
  general: {
    siteName: string
    supportEmail: string
    maintenanceMode: boolean
    registrationEnabled: boolean
    inviteOnlyMode: boolean
  }
  security: {
    passwordMinLength: number
    sessionTimeout: number
    maxLoginAttempts: number
    twoFactorRequired: boolean
    apiRateLimit: number
  }
  features: {
    aiIntegration: boolean
    teamCollaboration: boolean
    clientPortal: boolean
    mobileApp: boolean
    advancedAnalytics: boolean
  }
  ai: {
    provider: string
    model: string
    maxTokens: number
    temperature: number
    apiUsageLimit: number
  }
  notifications: {
    emailNotifications: boolean
    pushNotifications: boolean
    smsNotifications: boolean
    webhookUrl: string
  }
  performance: {
    cachingEnabled: boolean
    cdnEnabled: boolean
    compressionEnabled: boolean
    maxFileSize: number
  }
  aiChatLimits: {
    defaultRequestsPerHour: number
    defaultRequestsPerDay: number
    defaultRequestsPerWeek: number
    defaultRequestsPerMonth: number
  }
  jobLimits: {
    defaultJobsPerWeek: number
    defaultJobsPerMonth: number
  }
}

export default function SystemSettings() {
  const [settings, setSettings] = useState<SystemSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState('')

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const mockSettings: SystemSettings = {
        general: {
          siteName: 'DeskBelt',
          supportEmail: '<EMAIL>',
          maintenanceMode: false,
          registrationEnabled: true,
          inviteOnlyMode: false,
        },
        security: {
          passwordMinLength: 8,
          sessionTimeout: 30,
          maxLoginAttempts: 5,
          twoFactorRequired: false,
          apiRateLimit: 100,
        },
        features: {
          aiIntegration: true,
          teamCollaboration: true,
          clientPortal: false,
          mobileApp: false,
          advancedAnalytics: true,
        },
        ai: {
          provider: 'OpenRouter',
          model: 'meta-llama/llama-3.3-70b-instruct',
          maxTokens: 1000,
          temperature: 0.7,
          apiUsageLimit: 10000,
        },
        notifications: {
          emailNotifications: true,
          pushNotifications: false,
          smsNotifications: false,
          webhookUrl: '',
        },
        performance: {
          cachingEnabled: true,
          cdnEnabled: false,
          compressionEnabled: true,
          maxFileSize: 10,
        },
        aiChatLimits: {
          defaultRequestsPerHour: 20,
          defaultRequestsPerDay: 150,
          defaultRequestsPerWeek: 150,
          defaultRequestsPerMonth: 500,
        },
        jobLimits: {
          defaultJobsPerWeek: 10,
          defaultJobsPerMonth: 50,
        },
      }

      setSettings(mockSettings)
    } catch (err) {
      setError('Failed to load system settings')
      console.error('Settings error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!settings) return

    try {
      setSaving(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setSuccessMessage('Settings saved successfully!')
      setTimeout(() => setSuccessMessage(''), 3000)
    } catch (err) {
      setError('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  const updateSetting = (category: keyof SystemSettings, key: string, value: any) => {
    if (!settings) return

    setSettings(prev => ({
      ...prev!,
      [category]: {
        ...prev![category],
        [key]: value,
      },
    }))
  }

  const SettingCard = ({ 
    title, 
    description, 
    icon: Icon, 
    children 
  }: {
    title: string
    description: string
    icon: any
    children: React.ReactNode
  }) => (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center mb-4">
        <Icon className="w-6 h-6 text-primary-600 mr-3" />
        <div>
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
      </div>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )

  const ToggleSwitch = ({ 
    label, 
    description, 
    enabled, 
    onChange,
    danger = false 
  }: {
    label: string
    description?: string
    enabled: boolean
    onChange: (enabled: boolean) => void
    danger?: boolean
  }) => (
    <div className="flex items-start justify-between">
      <div className="flex-1">
        <label className="text-sm font-medium text-gray-900">{label}</label>
        {description && (
          <p className="text-sm text-gray-500 mt-1">{description}</p>
        )}
      </div>
      <button
        type="button"
        onClick={() => onChange(!enabled)}
        className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 ${
          enabled 
            ? danger 
              ? 'bg-red-600 focus:ring-red-500' 
              : 'bg-primary-600 focus:ring-primary-500'
            : 'bg-gray-200 focus:ring-primary-500'
        }`}
      >
        <span
          className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
            enabled ? 'translate-x-5' : 'translate-x-0'
          }`}
        />
      </button>
    </div>
  )

  const InputField = ({ 
    label, 
    value, 
    onChange, 
    type = 'text',
    placeholder = '',
    min,
    max 
  }: {
    label: string
    value: string | number
    onChange: (value: string | number) => void
    type?: string
    placeholder?: string
    min?: number
    max?: number
  }) => (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(type === 'number' ? Number(e.target.value) : e.target.value)}
        placeholder={placeholder}
        min={min}
        max={max}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
      />
    </div>
  )

  const SelectField = ({ 
    label, 
    value, 
    onChange, 
    options 
  }: {
    label: string
    value: string
    onChange: (value: string) => void
    options: Array<{ value: string; label: string }>
  }) => (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  )

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-700">{error}</div>
          <button
            onClick={fetchSettings}
            className="mt-2 text-red-600 hover:text-red-500 underline"
          >
            Try again
          </button>
        </div>
      </div>
    )
  }

  if (!settings) return null

  return (
    <div className="p-6">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
          <p className="text-gray-600">Configure global system settings and preferences</p>
        </div>
        
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </button>
      </div>

      {successMessage && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-700">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={(e) => e.preventDefault()}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* General Settings */}
        <SettingCard
          title="General Settings"
          description="Basic site configuration and preferences"
          icon={CogIcon}
        >
          <InputField
            label="Site Name"
            value={settings.general.siteName}
            onChange={(value) => updateSetting('general', 'siteName', value)}
          />
          <InputField
            label="Support Email"
            value={settings.general.supportEmail}
            onChange={(value) => updateSetting('general', 'supportEmail', value)}
            type="email"
          />
          <ToggleSwitch
            label="Maintenance Mode"
            description="Temporarily disable access for maintenance"
            enabled={settings.general.maintenanceMode}
            onChange={(enabled) => updateSetting('general', 'maintenanceMode', enabled)}
            danger
          />
          <ToggleSwitch
            label="User Registration"
            description="Allow new users to register accounts"
            enabled={settings.general.registrationEnabled}
            onChange={(enabled) => updateSetting('general', 'registrationEnabled', enabled)}
          />
          <ToggleSwitch
            label="Invite Only Mode"
            description="Require invitations for new registrations"
            enabled={settings.general.inviteOnlyMode}
            onChange={(enabled) => updateSetting('general', 'inviteOnlyMode', enabled)}
          />
        </SettingCard>

        {/* Security Settings */}
        <SettingCard
          title="Security Settings"
          description="Authentication and security configuration"
          icon={ShieldCheckIcon}
        >
          <InputField
            label="Minimum Password Length"
            value={settings.security.passwordMinLength}
            onChange={(value) => updateSetting('security', 'passwordMinLength', value)}
            type="number"
            min={6}
            max={50}
          />
          <InputField
            label="Session Timeout (minutes)"
            value={settings.security.sessionTimeout}
            onChange={(value) => updateSetting('security', 'sessionTimeout', value)}
            type="number"
            min={5}
            max={1440}
          />
          <InputField
            label="Max Login Attempts"
            value={settings.security.maxLoginAttempts}
            onChange={(value) => updateSetting('security', 'maxLoginAttempts', value)}
            type="number"
            min={3}
            max={10}
          />
          <InputField
            label="API Rate Limit (requests/minute)"
            value={settings.security.apiRateLimit}
            onChange={(value) => updateSetting('security', 'apiRateLimit', value)}
            type="number"
            min={10}
            max={1000}
          />
          <ToggleSwitch
            label="Two-Factor Authentication"
            description="Require 2FA for admin accounts"
            enabled={settings.security.twoFactorRequired}
            onChange={(enabled) => updateSetting('security', 'twoFactorRequired', enabled)}
          />
        </SettingCard>

        {/* Feature Flags */}
        <SettingCard
          title="Feature Flags"
          description="Enable or disable platform features"
          icon={RocketLaunchIcon}
        >
          <ToggleSwitch
            label="AI Integration"
            description="Enable AI-powered features"
            enabled={settings.features.aiIntegration}
            onChange={(enabled) => updateSetting('features', 'aiIntegration', enabled)}
          />
          <ToggleSwitch
            label="Team Collaboration"
            description="Enable team features and workspaces"
            enabled={settings.features.teamCollaboration}
            onChange={(enabled) => updateSetting('features', 'teamCollaboration', enabled)}
          />
          <ToggleSwitch
            label="Client Portal"
            description="Enable client access portal"
            enabled={settings.features.clientPortal}
            onChange={(enabled) => updateSetting('features', 'clientPortal', enabled)}
          />
          <ToggleSwitch
            label="Mobile App"
            description="Enable mobile application features"
            enabled={settings.features.mobileApp}
            onChange={(enabled) => updateSetting('features', 'mobileApp', enabled)}
          />
          <ToggleSwitch
            label="Advanced Analytics"
            description="Enable detailed analytics and reporting"
            enabled={settings.features.advancedAnalytics}
            onChange={(enabled) => updateSetting('features', 'advancedAnalytics', enabled)}
          />
        </SettingCard>

        {/* AI Configuration */}
        <SettingCard
          title="AI Configuration"
          description="Configure AI model and usage settings"
          icon={ChartBarIcon}
        >
          <SelectField
            label="AI Provider"
            value={settings.ai.provider}
            onChange={(value) => updateSetting('ai', 'provider', value)}
            options={[
              { value: 'OpenRouter', label: 'OpenRouter' },
              { value: 'OpenAI', label: 'OpenAI' },
              { value: 'Anthropic', label: 'Anthropic' },
            ]}
          />
          <SelectField
            label="AI Model"
            value={settings.ai.model}
            onChange={(value) => updateSetting('ai', 'model', value)}
            options={[
              { value: 'meta-llama/llama-3.3-70b-instruct', label: 'LLaMA 3.3 70B' },
              { value: 'meta-llama/llama-3.3-8b-instruct', label: 'LLaMA 3.3 8B' },
              { value: 'gpt-4', label: 'GPT-4' },
              { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
            ]}
          />
          <InputField
            label="Max Tokens"
            value={settings.ai.maxTokens}
            onChange={(value) => updateSetting('ai', 'maxTokens', value)}
            type="number"
            min={100}
            max={4000}
          />
          <InputField
            label="Temperature"
            value={settings.ai.temperature}
            onChange={(value) => updateSetting('ai', 'temperature', value)}
            type="number"
            min={0}
            max={2}
          />
          <InputField
            label="Monthly Usage Limit"
            value={settings.ai.apiUsageLimit}
            onChange={(value) => updateSetting('ai', 'apiUsageLimit', value)}
            type="number"
            min={1000}
            max={100000}
          />
        </SettingCard>

        {/* AI Chat Limits */}
        <SettingCard
          title="AI Chat Limits"
          description="Configure AI chat limits"
          icon={InformationCircleIcon}
        >
          <InputField
            label="Default Requests Per Hour"
            value={settings.aiChatLimits.defaultRequestsPerHour}
            onChange={(value) => updateSetting('aiChatLimits', 'defaultRequestsPerHour', value)}
            type="number"
            min={1}
            max={100}
          />
          <InputField
            label="Default Requests Per Day"
            value={settings.aiChatLimits.defaultRequestsPerDay}
            onChange={(value) => updateSetting('aiChatLimits', 'defaultRequestsPerDay', value)}
            type="number"
            min={1}
            max={1000}
          />
          <InputField
            label="Default Requests Per Week"
            value={settings.aiChatLimits.defaultRequestsPerWeek}
            onChange={(value) => updateSetting('aiChatLimits', 'defaultRequestsPerWeek', value)}
            type="number"
            min={1}
            max={1000}
          />
          <InputField
            label="Default Requests Per Month"
            value={settings.aiChatLimits.defaultRequestsPerMonth}
            onChange={(value) => updateSetting('aiChatLimits', 'defaultRequestsPerMonth', value)}
            type="number"
            min={1}
            max={1000}
          />
        </SettingCard>

        {/* Job Limits */}
        <SettingCard
          title="Default Job Limits"
          description="Configure default job limits"
          icon={RocketLaunchIcon}
        >
          <InputField
            label="Default Jobs Per Week"
            value={settings.jobLimits.defaultJobsPerWeek}
            onChange={(value) => updateSetting('jobLimits', 'defaultJobsPerWeek', value)}
            type="number"
            min={1}
            max={100}
          />
          <InputField
            label="Default Jobs Per Month"
            value={settings.jobLimits.defaultJobsPerMonth}
            onChange={(value) => updateSetting('jobLimits', 'defaultJobsPerMonth', value)}
            type="number"
            min={1}
            max={100}
          />
        </SettingCard>

        {/* Notification Settings */}
        <SettingCard
          title="Notifications"
          description="Configure notification channels and settings"
          icon={EnvelopeIcon}
        >
          <ToggleSwitch
            label="Email Notifications"
            description="Send notifications via email"
            enabled={settings.notifications.emailNotifications}
            onChange={(enabled) => updateSetting('notifications', 'emailNotifications', enabled)}
          />
          <ToggleSwitch
            label="Push Notifications"
            description="Send browser push notifications"
            enabled={settings.notifications.pushNotifications}
            onChange={(enabled) => updateSetting('notifications', 'pushNotifications', enabled)}
          />
          <ToggleSwitch
            label="SMS Notifications"
            description="Send SMS notifications (requires SMS provider)"
            enabled={settings.notifications.smsNotifications}
            onChange={(enabled) => updateSetting('notifications', 'smsNotifications', enabled)}
          />
          <InputField
            label="Webhook URL"
            value={settings.notifications.webhookUrl}
            onChange={(value) => updateSetting('notifications', 'webhookUrl', value)}
            placeholder="https://your-webhook-url.com/notify"
          />
        </SettingCard>

        {/* Performance Settings */}
        <SettingCard
          title="Performance"
          description="Configure caching and performance optimizations"
          icon={RocketLaunchIcon}
        >
          <ToggleSwitch
            label="Caching Enabled"
            description="Enable application-level caching"
            enabled={settings.performance.cachingEnabled}
            onChange={(enabled) => updateSetting('performance', 'cachingEnabled', enabled)}
          />
          <ToggleSwitch
            label="CDN Enabled"
            description="Use Content Delivery Network for static assets"
            enabled={settings.performance.cdnEnabled}
            onChange={(enabled) => updateSetting('performance', 'cdnEnabled', enabled)}
          />
          <ToggleSwitch
            label="Compression Enabled"
            description="Enable GZIP compression for responses"
            enabled={settings.performance.compressionEnabled}
            onChange={(enabled) => updateSetting('performance', 'compressionEnabled', enabled)}
          />
          <InputField
            label="Max File Size (MB)"
            value={settings.performance.maxFileSize}
            onChange={(value) => updateSetting('performance', 'maxFileSize', value)}
            type="number"
            min={1}
            max={100}
          />
        </SettingCard>
        </div>
      </form>
    </div>
  )
} 