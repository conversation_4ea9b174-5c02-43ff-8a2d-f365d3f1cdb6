import { Fragment, useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { supabase } from '../lib/supabase'

interface UserStats {
  workforceCount: number
  totalJobs: number
  archivedJobs: number
  totalClients: number
  totalQuotes: number
  totalInvoices: number
  totalContracts: number
}

interface AIUsageStats {
  hourly: number
  daily: number
  weekly: number
  monthly: number
}

interface UserDetails {
  id: string
  email: string
  full_name: string | null
  role: string
  status: string
  created_at: string
  last_login: string | null
  stats: UserStats
  limits?: {
    jobsPerWeek?: number
    jobsPerMonth?: number
    ai_limits?: Record<string, number>
  }
}

interface Props {
  open: boolean
  onClose: () => void
  user: UserDetails | null
}

export default function UserStatsDrawer({ open, onClose, user }: Props) {
  const [aiUsage, setAiUsage] = useState<AIUsageStats | null>(null)
  const [aiLimits, setAiLimits] = useState<Record<string, number>>({})
  const [loadingAiStats, setLoadingAiStats] = useState(false)

  useEffect(() => {
    if (open && user) {
      fetchAIUsageStats()
      fetchAILimits()
    }
  }, [open, user])

  const fetchAIUsageStats = async () => {
    if (!user) return
    try {
      setLoadingAiStats(true)
      const token = (await supabase.auth.getSession()).data.session?.access_token
      if (!token) return

      const res = await fetch(`http://localhost:4000/api/admin/users/${user.id}/ai-usage`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      if (res.ok) {
        const data = await res.json()
        setAiUsage(data)
      }
    } catch (err) {
      console.error('Failed to fetch AI usage:', err)
    } finally {
      setLoadingAiStats(false)
    }
  }

  const fetchAILimits = async () => {
    if (!user) return
    try {
      // Get user's custom limits
      const userLimits = user.limits?.ai_limits || {}
      
      // Get system defaults for missing limits
      const res = await fetch('http://localhost:4000/api/admin/defaults/ai_limits', { credentials: 'include' })
      if (res.ok) {
        const systemDefaults = await res.json()
        setAiLimits({ ...systemDefaults, ...userLimits })
      } else {
        setAiLimits(userLimits)
      }
    } catch (err) {
      console.error('Failed to fetch AI limits:', err)
      setAiLimits(user.limits?.ai_limits || {})
    }
  }

  if (!user) return null

  const formatDate = (date: string | null) => {
    if (!date) return '—'
    return new Date(date).toLocaleDateString('en-GB', {
      day: '2-digit', month: '2-digit', year: 'numeric'
    })
  }

  const StatRow = ({ label, value }: { label: string; value: number }) => (
    <div className="flex items-center justify-between py-2 text-sm border-b border-gray-200 dark:border-gray-700">
      <span className="text-gray-600 dark:text-gray-300">{label}</span>
      <span className="font-medium">{value}</span>
    </div>
  )

  const UsageRow = ({ label, current, limit }: { label: string; current: number; limit: number | null }) => (
    <div className="flex items-center justify-between py-2 text-sm border-b border-gray-200 dark:border-gray-700">
      <span className="text-gray-600 dark:text-gray-300">{label}</span>
      <span className="font-medium">
        {current}{limit ? ` / ${limit}` : ' / ∞'}
        {limit && current >= limit && <span className="text-red-600 ml-1">⚠️</span>}
      </span>
    </div>
  )

  const [jobsPerWeek, setJobsPerWeek] = useState<number | ''>(user.limits?.jobsPerWeek ?? '')
  const [jobsPerMonth, setJobsPerMonth] = useState<number | ''>(user.limits?.jobsPerMonth ?? '')
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const saveLimits = async () => {
    try {
      setSaving(true)
      const token = (await supabase.auth.getSession()).data.session?.access_token
      if (!token) throw new Error('No auth')

      await fetch(`http://localhost:4000/api/admin/users/${user.id}/limits`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${token}` },
        body: JSON.stringify({
          jobsPerWeek: jobsPerWeek === '' ? null : jobsPerWeek,
          jobsPerMonth: jobsPerMonth === '' ? null : jobsPerMonth,
        })
      })
      setError(null)
    } catch (e:any) {
      setError(e.message)
    } finally {
      setSaving(false)
    }
  }

  const resetPassword = async () => {
    try {
      const token = (await supabase.auth.getSession()).data.session?.access_token
      if (!token) throw new Error('No auth')
      await fetch(`http://localhost:4000/api/admin/users/${user.id}/reset-password`, {
        method: 'POST',
        headers: { Authorization: `Bearer ${token}` }
      })
      alert('Password reset email sent.')
    } catch (err:any) {
      alert('Failed to send reset email: ' + err.message)
    }
  }

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100"
          leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/30" />
        </Transition.Child>
        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-300"
                enterFrom="translate-x-full" enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-200"
                leaveFrom="translate-x-0" leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md bg-white dark:bg-gray-900 shadow-xl ring-1 ring-black/5 flex flex-col">
                  <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <Dialog.Title className="text-lg font-medium">User Details</Dialog.Title>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <XMarkIcon className="w-5 h-5" />
                    </button>
                  </div>
                  <div className="p-6 flex-1 overflow-y-auto space-y-6">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Profile</h3>
                      <div className="text-sm space-y-1">
                        <p><span className="font-medium">ID:</span> {user.id}</p>
                        <p><span className="font-medium">Email:</span> {user.email}</p>
                        <p><span className="font-medium">Name:</span> {user.full_name || '—'}</p>
                        <p><span className="font-medium">Role:</span> {user.role}</p>
                        <p><span className="font-medium">Status:</span> {user.status}</p>
                        <p><span className="font-medium">Joined:</span> {formatDate(user.created_at)}</p>
                        <p><span className="font-medium">Last Login:</span> {formatDate(user.last_login)}</p>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Usage Stats</h3>
                      <div className="divide-y divide-gray-200 dark:divide-gray-700">
                        <StatRow label="Workforces" value={user.stats.workforceCount} />
                        <StatRow label="Jobs" value={user.stats.totalJobs} />
                        <StatRow label="Archived Jobs" value={user.stats.archivedJobs} />
                        <StatRow label="Clients" value={user.stats.totalClients} />
                        <StatRow label="Quotes" value={user.stats.totalQuotes} />
                        <StatRow label="Invoices" value={user.stats.totalInvoices} />
                        <StatRow label="Contracts" value={user.stats.totalContracts} />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">AI Usage</h3>
                      {loadingAiStats ? (
                        <div className="text-sm text-gray-500">Loading AI usage stats...</div>
                      ) : aiUsage ? (
                        <div className="divide-y divide-gray-200 dark:divide-gray-700">
                          <UsageRow label="This Hour" current={aiUsage.hourly} limit={aiLimits.hour || null} />
                          <UsageRow label="Today" current={aiUsage.daily} limit={aiLimits.day || null} />
                          <UsageRow label="This Week" current={aiUsage.weekly} limit={aiLimits.week || null} />
                          <UsageRow label="This Month" current={aiUsage.monthly} limit={aiLimits.month || null} />
                        </div>
                      ) : (
                        <div className="text-sm text-gray-500">No AI usage data</div>
                      )}
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Job Limits</h3>
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 text-sm">
                          <label className="w-32">Per Week</label>
                          <input type="number" min={1} placeholder="Unlimited" value={jobsPerWeek}
                            onChange={e => setJobsPerWeek(e.target.value === '' ? '' : parseInt(e.target.value))}
                            className="flex-1 px-2 py-1 border rounded-md" />
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <label className="w-32">Per Month</label>
                          <input type="number" min={1} placeholder="Unlimited" value={jobsPerMonth}
                            onChange={e => setJobsPerMonth(e.target.value === '' ? '' : parseInt(e.target.value))}
                            className="flex-1 px-2 py-1 border rounded-md" />
                        </div>
                        {error && <p className="text-red-600 text-xs">{error}</p>}
                        <button disabled={saving} onClick={saveLimits} className="inline-flex items-center px-3 py-1 bg-primary-600 text-white rounded-md text-xs">
                          {saving ? 'Saving...' : 'Save Limits'}
                        </button>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Security</h3>
                      <button onClick={resetPassword} className="inline-flex items-center px-3 py-1 bg-red-600 text-white rounded-md text-xs">
                        Send Password Reset Email
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
} 