import { useState, useEffect } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'

interface Props {
  isOpen: boolean
  onClose: () => void
  userId: string
  initialLimits: Partial<Record<'hour' | 'day' | 'week' | 'month', number | null>>
  onSaved?: () => void
}

export default function AILimitsModal({ isOpen, onClose, userId, initialLimits, onSaved }: Props) {
  const [limits, setLimits] = useState({
    hour: initialLimits.hour ?? null,
    day: initialLimits.day ?? null,
    week: initialLimits.week ?? null,
    month: initialLimits.month ?? null,
  })
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Update limits when modal opens or initialLimits change
  useEffect(() => {
    if (isOpen) {
      setLimits({
        hour: initialLimits.hour ?? null,
        day: initialLimits.day ?? null,
        week: initialLimits.week ?? null,
        month: initialLimits.month ?? null,
      })
    }
  }, [isOpen, initialLimits])

  if (!isOpen) return null

  const handleChange = (key: keyof typeof limits, value: string) => {
    setLimits(prev => ({
      ...prev,
      [key]: value === '' ? null : parseInt(value),
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    setError(null)
    try {
      const res = await fetch(`http://localhost:4000/api/admin/users/${userId}/ai-limits`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ aiLimits: limits }),
        credentials: 'include',
      })
      if (!res.ok) throw new Error(`Failed: ${res.status}`)
      onSaved?.()
      onClose()
    } catch (err: any) {
      setError(err.message)
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white dark:bg-gray-900 w-full max-w-md p-6 rounded-lg shadow-lg relative">
        <button onClick={onClose} className="absolute top-3 right-3 text-gray-500 hover:text-gray-700">
          <XMarkIcon className="w-5 h-5" />
        </button>
        <h2 className="text-lg font-medium mb-4">AI Chat Limits</h2>
        <div className="space-y-4">
          {(['hour', 'day', 'week', 'month'] as const).map(p => (
            <div key={p} className="flex items-center justify-between">
              <label className="text-sm capitalize">{p} limit</label>
              <input
                type="number"
                min={1}
                className="w-28 rounded border px-2 py-1 text-sm bg-white dark:bg-gray-800"
                placeholder="∞"
                value={limits[p] ?? ''}
                onChange={e => handleChange(p, e.target.value)}
              />
            </div>
          ))}
          {error && <p className="text-sm text-red-600">{error}</p>}
        </div>
        <button
          onClick={handleSave}
          disabled={saving}
          className="mt-6 w-full bg-amber-600 text-white py-2 rounded hover:bg-amber-700 disabled:opacity-50"
        >
          {saving ? 'Saving…' : 'Save'}
        </button>
      </div>
    </div>
  )
} 