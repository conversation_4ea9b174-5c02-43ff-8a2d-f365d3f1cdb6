import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
})

// Admin helper functions
export const isAdminUser = async (userId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error checking admin role:', error)
      return false
    }

    return data?.role === 'admin' || data?.role === 'super_admin'
  } catch (error) {
    console.error('Error checking admin role:', error)
    return false
  }
}

export const requireAdmin = async (userId: string) => {
  const isAdmin = await isAdminUser(userId)
  if (!isAdmin) {
    throw new Error('Admin access required')
  }
  return true
} 