import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  totalTeams: number
  totalJobs: number
  totalClients: number
  totalQuotes: number
  totalInvoices: number
  totalContracts: number
  userGrowth: number
  teamGrowth: number
  jobGrowth: number
}

// New analytics types
interface AnalyticsPoint {
  date: string
  value: number
}

interface AnalyticsData {
  users_new?: AnalyticsPoint[]
  users_active?: AnalyticsPoint[]
  clients_new?: AnalyticsPoint[]
  jobs_created?: AnalyticsPoint[]
  jobs_completed?: AnalyticsPoint[]
  invoice_value?: AnalyticsPoint[]
  quote_conversion?: AnalyticsPoint[]
  quotes_created?: AnalyticsPoint[]
  invoices_created?: AnalyticsPoint[]
  contracts_created?: AnalyticsPoint[]
  ai_messages?: AnalyticsPoint[]
  comparison?: AnalyticsData
  percentage_changes?: Record<string, number>
}

type AnalyticsRange = 'last_7d' | 'last_30d' | 'last_90d' | 'last_6m' | 'last_1y' | 'custom'
type AnalyticsMetric = 'users_new' | 'users_active' | 'clients_new' | 'jobs_created' | 'jobs_completed' | 'invoice_value' | 'quote_conversion' | 'quotes_created' | 'invoices_created' | 'contracts_created' | 'ai_messages'

interface UseAdminStatsOptions {
  range?: AnalyticsRange
  startDate?: string
  endDate?: string
  metrics?: AnalyticsMetric[]
  timeSeries?: boolean
  includeComparison?: boolean
}

interface UseAdminStatsResult {
  stats: DashboardStats | null
  analytics: AnalyticsData | null
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useAdminStats(options: UseAdminStatsOptions = {}): UseAdminStatsResult {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const {
    range = 'last_30d',
    startDate,
    endDate,
    metrics = ['users_new', 'users_active', 'jobs_created', 'quotes_created', 'invoices_created', 'contracts_created'],
    timeSeries = false,
    includeComparison = false
  } = options

  const fetchStats = async () => {
    try {
      setLoading(true)
      setError(null)

      // Retrieve the current Supabase session to get the access token
      const { data: { session } } = await supabase.auth.getSession()

      if (!session?.access_token) {
        throw new Error('No authentication token found')
      }

      // Always fetch basic dashboard stats
      const statsResponse = await fetch('http://localhost:4000/api/admin/dashboard/stats', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      if (!statsResponse.ok) {
        throw new Error(`Failed to fetch stats: ${statsResponse.status}`)
      }

      const statsData = await statsResponse.json()
      setStats(statsData)

      // Early-exit guard: avoid hitting analytics endpoint while the user is still picking
      // dates for a custom range. This prevents backend 500 errors when start_date/end_date
      // are missing.
      if (range === 'custom' && (!startDate || !endDate)) {
        // Skip analytics fetch until both dates are selected.
        setAnalytics(null)
        return
      }

      // Fetch analytics data if time-series is requested
      if (timeSeries) {
        const analyticsParams = new URLSearchParams({
          range,
          ...(startDate && { start_date: startDate }),
          ...(endDate && { end_date: endDate }),
          ...(includeComparison && { include_comparison: 'true' }),
        })

        // Add metrics as array parameters
        metrics.forEach(metric => {
          analyticsParams.append('metrics[]', metric)
        })

        const analyticsResponse = await fetch(`http://localhost:4000/api/admin/analytics?${analyticsParams}`, {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        })

        if (!analyticsResponse.ok) {
          throw new Error(`Failed to fetch analytics: ${analyticsResponse.status}`)
        }

        const analyticsData = await analyticsResponse.json()
        setAnalytics(analyticsData)
      } else {
        setAnalytics(null)
      }

    } catch (err) {
      console.error('Error fetching admin data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch admin data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [range, startDate, endDate, JSON.stringify(metrics), timeSeries, includeComparison])

  return {
    stats,
    analytics,
    loading,
    error,
    refetch: fetchStats,
  }
}

// Utility functions for formatting analytics data
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value)
}

export const formatDate = (dateStr: string): string => {
  return new Date(dateStr).toLocaleDateString('en-GB', {
    month: 'short',
    day: 'numeric',
  })
}

export const formatDateFull = (dateStr: string): string => {
  return new Date(dateStr).toLocaleDateString('en-GB', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
  })
}

// Type exports for use in components
export type { AnalyticsPoint, AnalyticsData, AnalyticsRange, AnalyticsMetric, UseAdminStatsOptions } 