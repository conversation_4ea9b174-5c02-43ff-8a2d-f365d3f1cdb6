{"name": "@deskbelt/admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "type-check": "tsc --noEmit"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@headlessui/react": "^2.2.0", "@headlessui/tailwindcss": "^0.2.2", "@heroicons/react": "^2.1.1", "@supabase/supabase-js": "^2.39.3", "@tanstack/react-query": "^5.17.15", "@types/react-datepicker": "^6.2.0", "chart.js": "^4.4.1", "clsx": "^2.0.0", "date-fns": "^3.2.0", "lucide-react": "^0.317.0", "next": "^15.1.0", "react": "^19.0.0", "react-chartjs-2": "^5.2.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-hook-form": "^7.49.2", "tailwind-merge": "^2.2.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "^15.1.0", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}