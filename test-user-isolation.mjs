#!/usr/bin/env node

/**
 * User Data Isolation Test Script
 * 
 * This script tests that users can only see their own data by:
 * 1. Creating test users in Supabase
 * 2. Creating test data for each user
 * 3. Verifying API endpoints return only user-specific data
 * 4. Testing RLS policies are working correctly
 */

import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';

// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://nwwynkkigyahrjumqmrj.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

const API_BASE = 'http://localhost:4000/api';

// Test configuration - use timestamp to avoid conflicts
const timestamp = Date.now();
const TEST_USERS = [
  { email: `testuser1-${timestamp}@example.com`, password: 'TestPass123!' },
  { email: `testuser2-${timestamp}@example.com`, password: 'TestPass123!' }
];

class UserIsolationTester {
  constructor() {
    this.testUsers = [];
    this.testData = {
      jobs: [],
      clients: [],
      notifications: []
    };
  }

  async log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async setup() {
    await this.log('🚀 Starting User Data Isolation Test');
    await this.log('📝 Setting up test users...');
    
    // Create test users
    for (const userData of TEST_USERS) {
      try {
        // Delete user if exists (cleanup from previous runs)
        const { data: existingUsers } = await supabase.auth.admin.listUsers();
        const existingUser = existingUsers.users.find(u => u.email === userData.email);
        
        if (existingUser) {
          await supabase.auth.admin.deleteUser(existingUser.id);
          await this.log(`🗑️ Cleaned up existing user: ${userData.email}`);
        }

        // Create new user
        const { data: user, error } = await supabase.auth.admin.createUser({
          email: userData.email,
          password: userData.password,
          email_confirm: true
        });

        if (error) throw error;

        // Sign in to get tokens
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: userData.email,
          password: userData.password
        });

        if (signInError) throw signInError;

        // Create user profile in our users table using service role
        const serviceClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
        const { error: profileError } = await serviceClient
          .from('users')
          .insert({
            id: user.user.id,
            email: user.user.email,
            full_name: `Test User ${userData.email}`,
            role: 'tradesperson'
          });

        if (profileError) {
          await this.log(`Warning: Failed to create user profile: ${profileError.message}`);
        }

        this.testUsers.push({
          ...userData,
          id: user.user.id,
          accessToken: signInData.session.access_token
        });

        await this.log(`✅ Created test user: ${userData.email} (ID: ${user.user.id})`);
      } catch (error) {
        await this.log(`Failed to create user ${userData.email}: ${error.message}`, 'error');
        throw error;
      }
    }
  }

  async createTestData() {
    await this.log('📝 Creating test data for each user...');

    for (let i = 0; i < this.testUsers.length; i++) {
      const user = this.testUsers[i];
      await this.log(`Creating data for user ${i + 1}: ${user.email}`);

      try {
        // Create test client
        const clientResponse = await fetch(`${API_BASE}/clients`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${user.accessToken}`
          },
          body: JSON.stringify({
            name: `Contact ${i + 1}`,
            business_name: `Test Client ${i + 1}`,
            email: `client${i + 1}@example.com`,
            phone: `555-000${i + 1}`,
            address: `${i + 1} Test Street`
          })
        });

        if (!clientResponse.ok) {
          throw new Error(`Client creation failed: ${clientResponse.status} ${clientResponse.statusText}`);
        }

        const clientData = await clientResponse.json();
        this.testData.clients.push({ userId: user.id, data: clientData });

        // Create test job
        const jobResponse = await fetch(`${API_BASE}/jobs`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${user.accessToken}`
          },
          body: JSON.stringify({
            title: `Test Job ${i + 1}`,
            description: `Test job description for user ${i + 1}`,
            status: 'new',
            client_id: clientData.id,
            priority: 'medium'
          })
        });

        if (!jobResponse.ok) {
          throw new Error(`Job creation failed: ${jobResponse.status} ${jobResponse.statusText}`);
        }

        const jobData = await jobResponse.json();
        this.testData.jobs.push({ userId: user.id, data: jobData });

        // Create test notification
        const notificationResponse = await fetch(`${API_BASE}/notifications`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${user.accessToken}`
          },
          body: JSON.stringify({
            title: `Test Notification ${i + 1}`,
            message: `Test notification for user ${i + 1}`,
            type: 'info'
          })
        });

        if (!notificationResponse.ok) {
          throw new Error(`Notification creation failed: ${notificationResponse.status} ${notificationResponse.statusText}`);
        }

        const notificationData = await notificationResponse.json();
        this.testData.notifications.push({ userId: user.id, data: notificationData });

        await this.log(`✅ Created test data for user ${i + 1}`);
      } catch (error) {
        await this.log(`Failed to create test data for ${user.email}: ${error.message}`, 'error');
        throw error;
      }
    }
  }

  async testDataIsolation() {
    await this.log('🔒 Testing user data isolation...');
    let testsPassed = 0;
    let totalTests = 0;

    for (let i = 0; i < this.testUsers.length; i++) {
      const user = this.testUsers[i];
      await this.log(`Testing data isolation for user ${i + 1}: ${user.email}`);

      // Test Jobs isolation
      totalTests++;
      try {
        const jobsResponse = await fetch(`${API_BASE}/jobs`, {
          headers: { 'Authorization': `Bearer ${user.accessToken}` }
        });

        if (!jobsResponse.ok) {
          throw new Error(`Jobs fetch failed: ${jobsResponse.status}`);
        }

        const jobsData = await jobsResponse.json();
        const userJobs = jobsData.jobs || jobsData;
        
        // Should only see own jobs
        const otherUserJobs = userJobs.filter(job => 
          this.testData.jobs.some(testJob => 
            testJob.userId !== user.id && testJob.data.id === job.id
          )
        );

        if (otherUserJobs.length === 0) {
          await this.log(`✅ Jobs isolation: User ${i + 1} sees only own jobs (${userJobs.length} jobs)`);
          testsPassed++;
        } else {
          await this.log(`❌ Jobs isolation: User ${i + 1} can see other users' jobs!`, 'error');
        }
      } catch (error) {
        await this.log(`❌ Jobs test failed for user ${i + 1}: ${error.message}`, 'error');
      }

      // Test Clients isolation
      totalTests++;
      try {
        const clientsResponse = await fetch(`${API_BASE}/clients`, {
          headers: { 'Authorization': `Bearer ${user.accessToken}` }
        });

        if (!clientsResponse.ok) {
          throw new Error(`Clients fetch failed: ${clientsResponse.status}`);
        }

        const clientsData = await clientsResponse.json();
        const userClients = clientsData.clients || clientsData;
        
        // Should only see own clients
        const otherUserClients = userClients.filter(client => 
          this.testData.clients.some(testClient => 
            testClient.userId !== user.id && testClient.data.id === client.id
          )
        );

        if (otherUserClients.length === 0) {
          await this.log(`✅ Clients isolation: User ${i + 1} sees only own clients (${userClients.length} clients)`);
          testsPassed++;
        } else {
          await this.log(`❌ Clients isolation: User ${i + 1} can see other users' clients!`, 'error');
        }
      } catch (error) {
        await this.log(`❌ Clients test failed for user ${i + 1}: ${error.message}`, 'error');
      }

      // Test Notifications isolation
      totalTests++;
      try {
        const notificationsResponse = await fetch(`${API_BASE}/notifications`, {
          headers: { 'Authorization': `Bearer ${user.accessToken}` }
        });

        if (!notificationsResponse.ok) {
          throw new Error(`Notifications fetch failed: ${notificationsResponse.status}`);
        }

        const notificationsData = await notificationsResponse.json();
        const userNotifications = Array.isArray(notificationsData) ? notificationsData : [];
        
        // Should only see own notifications
        const otherUserNotifications = userNotifications.filter(notification => 
          this.testData.notifications.some(testNotification => 
            testNotification.userId !== user.id && testNotification.data.id === notification.id
          )
        );

        if (otherUserNotifications.length === 0) {
          await this.log(`✅ Notifications isolation: User ${i + 1} sees only own notifications (${userNotifications.length} notifications)`);
          testsPassed++;
        } else {
          await this.log(`❌ Notifications isolation: User ${i + 1} can see other users' notifications!`, 'error');
        }
      } catch (error) {
        await this.log(`❌ Notifications test failed for user ${i + 1}: ${error.message}`, 'error');
      }
    }

    return { testsPassed, totalTests };
  }

  async testUnauthorizedAccess() {
    await this.log('🚫 Testing unauthorized access protection...');
    let testsPassed = 0;
    let totalTests = 0;

    const endpoints = ['/jobs', '/clients', '/notifications', '/notifications/stats'];

    for (const endpoint of endpoints) {
      totalTests++;
      try {
        const response = await fetch(`${API_BASE}${endpoint}`);
        
        if (response.status === 401) {
          await this.log(`✅ Unauthorized access blocked for ${endpoint} (401)`);
          testsPassed++;
        } else {
          await this.log(`❌ Unauthorized access allowed for ${endpoint} (${response.status})`, 'error');
        }
      } catch (error) {
        await this.log(`❌ Error testing ${endpoint}: ${error.message}`, 'error');
      }
    }

    return { testsPassed, totalTests };
  }

  async cleanup() {
    await this.log('🧹 Cleaning up test data...');
    
    // Delete test users (this will cascade delete their data via RLS)
    for (const user of this.testUsers) {
      try {
        await supabase.auth.admin.deleteUser(user.id);
        await this.log(`🗑️ Deleted test user: ${user.email}`);
      } catch (error) {
        await this.log(`Failed to delete user ${user.email}: ${error.message}`, 'error');
      }
    }
  }

  async run() {
    try {
      await this.setup();
      await this.createTestData();
      
      const isolationResults = await this.testDataIsolation();
      const authResults = await this.testUnauthorizedAccess();
      
      const totalPassed = isolationResults.testsPassed + authResults.testsPassed;
      const totalTests = isolationResults.totalTests + authResults.totalTests;
      
      await this.log('📊 Test Results Summary:');
      await this.log(`   Data Isolation Tests: ${isolationResults.testsPassed}/${isolationResults.totalTests} passed`);
      await this.log(`   Authorization Tests: ${authResults.testsPassed}/${authResults.totalTests} passed`);
      await this.log(`   Total: ${totalPassed}/${totalTests} tests passed`);
      
      if (totalPassed === totalTests) {
        await this.log('🎉 ALL TESTS PASSED! User data isolation is working correctly.', 'success');
      } else {
        await this.log(`⚠️ ${totalTests - totalPassed} tests failed. Review the issues above.`, 'error');
      }
      
      await this.cleanup();
      
      return totalPassed === totalTests;
      
    } catch (error) {
      await this.log(`Test execution failed: ${error.message}`, 'error');
      await this.cleanup();
      return false;
    }
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new UserIsolationTester();
  tester.run().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

export default UserIsolationTester;