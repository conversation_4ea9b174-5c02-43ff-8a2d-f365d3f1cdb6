const { createClient } = require('@supabase/supabase-js');

// This script creates a test client for testing the review request feature
async function createTestClient() {
  const supabaseUrl = process.env.SUPABASE_URL || 'https://nwwynkkigyahrjumqmrj.supabase.co';
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!serviceKey) {
    console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, serviceKey);

  const userId = 'dee299de-a274-45e4-9540-b60a58dbd40e'; // From debug log
  
  const testClient = {
    created_by: userId,
    name: 'Test Client for Review Request',
    email: '<EMAIL>',
    phone: '+44 ************',
    address: '123 Test Street, Test City, TC1 2AB',
    rating: 5
  };

  try {
    console.log('🔧 Creating test client...');
    const { data, error } = await supabase
      .from('clients')
      .insert([testClient])
      .select()
      .single();

    if (error) {
      console.error('❌ Error creating test client:', error);
      return;
    }

    console.log('✅ Test client created successfully!');
    console.log('📋 Client details:');
    console.log('   ID:', data.id);
    console.log('   Name:', data.name);
    console.log('   Email:', data.email);
    console.log('   Phone:', data.phone);
    console.log('');
    console.log('🎯 You can now test the review request feature with this client!');
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

createTestClient();