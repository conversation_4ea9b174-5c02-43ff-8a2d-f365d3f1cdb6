-- =============================================
-- Fix: Grant necessary permissions to authenticated role for users table
-- Description: Fix "permission denied for table users" error
-- Issue: authenticated role lacks INSERT permissions on public.users table
-- =============================================

-- Grant necessary permissions to authenticated role for users table
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;

-- Grant usage on the sequence if it exists (for auto-incrementing columns)
-- Note: users table uses UUID so this might not be needed, but adding for completeness
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.sequences WHERE sequence_schema = 'public' AND sequence_name LIKE '%users%') THEN
        GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
    END IF;
END $$;

-- Grant permissions for the helper function we created
GRANT EXECUTE ON FUNCTION create_user_profile_securely(UUID, TEXT, TEXT, TEXT) TO authenticated;

-- Also ensure anon role has minimal required permissions for invitation acceptance
GRANT SELECT ON public.workforce_invitations TO anon;