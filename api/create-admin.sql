-- Create admin user for DeskBelt Admin Portal
-- Run this directly in Supabase Dashboard SQL Editor

-- Insert or update the admin user
INSERT INTO public.users (
    id, 
    email, 
    full_name, 
    phone, 
    company_name, 
    address, 
    country, 
    role,
    created_at,
    updated_at
) VALUES (
    '6354360e-fe9d-4e1b-b184-479f46a3a06e',
    '<EMAIL>',
    'Nassar (Admin)',
    '+44 20 7946 0958',
    'DeskBelt Admin',
    'Admin Office',
    'UK', 
    'super_admin',
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    updated_at = NOW();

-- Verify the admin user was created
SELECT id, email, full_name, role FROM public.users WHERE email = '<EMAIL>'; 