const { Resend } = require('resend');
require('dotenv').config();

console.log('🧪 Testing Resend directly...');
console.log('API Key:', process.env.RESEND_API_KEY ? `${process.env.RESEND_API_KEY.substring(0, 8)}...` : 'Missing');
console.log('From Email:', process.env.FROM_EMAIL);

const resend = new Resend(process.env.RESEND_API_KEY);

async function testEmail() {
  try {
    console.log('📧 Sending test email...');
    
    const { data, error } = await resend.emails.send({
      from: `DeskBelt Test <${process.env.FROM_EMAIL}>`,
      to: ['<EMAIL>'],
      subject: 'DeskBelt Email Test - Direct',
      html: `
        <h1>🚀 DeskBelt Email Test</h1>
        <p>This is a direct test from Node.js script!</p>
        <p>✅ Resend integration is working!</p>
        <p>Time: ${new Date().toISOString()}</p>
      `,
      text: 'DeskBelt Email Test - This is a direct test from Node.js script! Resend integration is working!'
    });

    if (error) {
      console.error('❌ Resend error:', error);
      return;
    }

    console.log('✅ Email sent successfully!');
    console.log('📧 Email ID:', data.id);
    console.log('📨 To:', '<EMAIL>');
    
  } catch (error) {
    console.error('💥 Error:', error);
  }
}

testEmail();