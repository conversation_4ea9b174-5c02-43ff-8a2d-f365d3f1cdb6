const nodemailer = require('nodemailer');
require('dotenv').config();

console.log('🧪 Testing Gmail SMTP directly...');
console.log('Gmail User:', process.env.GMAIL_USER_EMAIL);
console.log('App Password:', process.env.GMAIL_APP_PASSWORD ? `${process.env.GMAIL_APP_PASSWORD.substring(0, 4)}...` : 'Missing');

async function testGmail() {
  try {
    console.log('🔧 Creating Gmail transporter...');
    
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER_EMAIL,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
    });

    console.log('🔍 Verifying Gmail connection...');
    await transporter.verify();
    console.log('✅ Gmail SMTP connection verified!');

    console.log('📧 Sending test email...');
    
    const result = await transporter.sendMail({
      from: `DeskBelt Test <${process.env.GMAIL_USER_EMAIL}>`,
      to: '<EMAIL>',
      subject: 'DeskBelt Gmail SMTP Test - Direct',
      html: `
        <h1>📧 Gmail SMTP Test Success!</h1>
        <p>This email was sent directly via Gmail SMTP using Nodemailer!</p>
        <p><strong>Features tested:</strong></p>
        <ul>
          <li>✅ Gmail App Password authentication</li>
          <li>✅ SMTP connection verification</li>
          <li>✅ HTML email template rendering</li>
          <li>✅ Professional email formatting</li>
        </ul>
        <p>🎯 <strong>Ready for DeskBelt integration!</strong></p>
        <hr>
        <p style="color: #666; font-size: 12px;">
          Sent via Gmail SMTP + Nodemailer • ${new Date().toISOString()}<br>
          From: ${process.env.GMAIL_USER_EMAIL}
        </p>
      `,
      text: `Gmail SMTP Test Success!
      
This email was sent directly via Gmail SMTP using Nodemailer!

Features tested:
- Gmail App Password authentication
- SMTP connection verification  
- HTML email template rendering
- Professional email formatting

Ready for DeskBelt integration!

---
Sent via Gmail SMTP + Nodemailer
From: ${process.env.GMAIL_USER_EMAIL}`
    });

    console.log('✅ Gmail email sent successfully!');
    console.log('📧 Message ID:', result.messageId);
    console.log('📨 To:', '<EMAIL>');
    console.log('📮 From:', process.env.GMAIL_USER_EMAIL);
    
  } catch (error) {
    console.error('❌ Gmail SMTP Error:', error.message);
    
    if (error.code === 'EAUTH') {
      console.error('🔐 Authentication Error: Please check your Gmail App Password');
    } else if (error.code === 'ENOTFOUND') {
      console.error('🌐 Network Error: Please check your internet connection');
    } else {
      console.error('💥 Full Error:', error);
    }
  }
}

testGmail();