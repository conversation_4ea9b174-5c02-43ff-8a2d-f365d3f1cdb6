# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=4000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Supabase Configuration (Optional - server will use file storage if not provided)
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_ANON_KEY=your_supabase_anon_key

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# Database Configuration (if using direct PostgreSQL)
DATABASE_URL=your_database_connection_string

# AI Configuration (OpenRouter)
OPENROUTER_API_KEY=your_openrouter_api_key 