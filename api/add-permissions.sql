-- Add permission columns to teams table
ALTER TABLE public.teams
  ADD COLUMN IF NOT EXISTS "permission_manage_jobs" BOOLEAN NOT NULL DEFAULT true,
  ADD COLUMN IF NOT EXISTS "permission_manage_clients" BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN IF NOT EXISTS "permission_view_financials" BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN IF NOT EXISTS "permission_generate_documents" BOOLEAN NOT NULL DEFAULT false;

-- Update constraint for job visibility
ALTER TABLE public.teams
  DROP CONSTRAINT IF EXISTS teams_default_job_visibility_check;

UPDATE public.teams
SET default_job_visibility = 'entire_team'
WHERE default_job_visibility = 'team_only' OR default_job_visibility = 'public';

ALTER TABLE public.teams
  ADD CONSTRAINT teams_default_job_visibility_check
  CHECK (default_job_visibility IN ('owner_only', 'entire_team', 'assigned_only'));

-- Update team member roles
ALTER TABLE public.team_members
  DROP CONSTRAINT IF EXISTS team_members_role_check;

UPDATE public.team_members
SET role = 'member'
WHERE role = 'manager';

ALTER TABLE public.team_members
  ADD CONSTRAINT team_members_role_check
  CHECK (role IN ('owner', 'member')); 