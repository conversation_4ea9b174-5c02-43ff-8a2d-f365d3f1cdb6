const nodemailer = require('nodemailer');
require('dotenv').config();

console.log('🧪 Testing Gmail SMTP to external email addresses...');
console.log('Gmail User:', process.env.GMAIL_USER_EMAIL);
console.log('App Password:', process.env.GMAIL_APP_PASSWORD ? 'Configured' : 'Missing');

async function testExternalEmails() {
  try {
    console.log('🔧 Creating Gmail transporter...');
    
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER_EMAIL,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
    });

    console.log('🔍 Verifying Gmail connection...');
    await transporter.verify();
    console.log('✅ Gmail SMTP connection verified!');

    const testEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ];

    console.log(`📧 Sending test emails to ${testEmails.length} external addresses...`);

    for (const email of testEmails) {
      try {
        console.log(`\n📮 Sending to: ${email}`);
        
        const result = await transporter.sendMail({
          from: `DeskBelt Team <${process.env.GMAIL_USER_EMAIL}>`,
          to: email,
          subject: 'DeskBelt External Email Test - Gmail SMTP',
          html: `
            <h1>📧 External Email Test Success!</h1>
            <p>Hello! This email was sent to <strong>${email}</strong> using Gmail SMTP via Nodemailer.</p>
            
            <div style="background: #f0f9ff; padding: 20px; border-left: 4px solid #0ea5e9; margin: 20px 0;">
              <h3 style="color: #0369a1; margin-top: 0;">✅ Test Results:</h3>
              <ul style="color: #1e40af;">
                <li>Gmail SMTP can send to external email providers</li>
                <li>Yahoo and Outlook addresses are supported</li>
                <li>No domain restrictions for recipients</li>
                <li>Professional HTML formatting works</li>
              </ul>
            </div>
            
            <p><strong>🎯 This confirms Gmail SMTP works with ANY email address!</strong></p>
            
            <h3>Comparison with Resend:</h3>
            <ul>
              <li><strong>Gmail SMTP:</strong> Can send to any email address immediately</li>
              <li><strong>Resend (free):</strong> Limited to your verified email only</li>
            </ul>
            
            <hr style="margin: 30px 0;">
            <p style="color: #666; font-size: 12px;">
              <strong>DeskBelt Email Service</strong><br>
              Sent via Gmail SMTP + Nodemailer • ${new Date().toISOString()}<br>
              From: ${process.env.GMAIL_USER_EMAIL}<br>
              To: ${email}
            </p>
          `,
          text: `External Email Test Success!

Hello! This email was sent to ${email} using Gmail SMTP via Nodemailer.

Test Results:
- Gmail SMTP can send to external email providers
- Yahoo and Outlook addresses are supported  
- No domain restrictions for recipients
- Professional HTML formatting works

This confirms Gmail SMTP works with ANY email address!

Comparison with Resend:
- Gmail SMTP: Can send to any email address immediately
- Resend (free): Limited to your verified email only

---
DeskBelt Email Service
Sent via Gmail SMTP + Nodemailer
From: ${process.env.GMAIL_USER_EMAIL}
To: ${email}`
        });

        console.log(`✅ Email sent successfully to ${email}!`);
        console.log(`📧 Message ID: ${result.messageId}`);
        
      } catch (error) {
        console.error(`❌ Failed to send to ${email}:`, error.message);
      }
    }
    
    console.log('\n🎉 External email test completed!');
    console.log('📬 Check both Yahoo and Outlook inboxes for the test emails.');
    
  } catch (error) {
    console.error('❌ Gmail SMTP Error:', error.message);
    
    if (error.code === 'EAUTH') {
      console.error('🔐 Authentication Error: Please check your Gmail App Password');
    } else if (error.code === 'ENOTFOUND') {
      console.error('🌐 Network Error: Please check your internet connection');
    } else {
      console.error('💥 Full Error:', error);
    }
  }
}

testExternalEmails();