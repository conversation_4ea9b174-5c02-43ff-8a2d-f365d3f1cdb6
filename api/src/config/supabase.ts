import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

console.log('🔍 Supabase Configuration:')
console.log('   URL:', supabaseUrl)
console.log('   Service Key present:', !!supabaseServiceKey)
console.log('   Service Key length:', supabaseServiceKey?.length)
console.log('   Service Key starts with:', supabaseServiceKey?.substring(0, 20) + '...')

// Supabase is REQUIRED for production - no fallbacks allowed
if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ CRITICAL ERROR: Supabase configuration missing!')
  console.error('   Required environment variables:')
  console.error('   - SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  console.error('')
  console.error('   This application requires a database connection.')
  console.error('   Please configure Supabase in your .env file.')
  process.exit(1)
}

// Create Supabase client with service role key for backend operations
// Using the proper configuration for server-side service role as per Supabase docs
export const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false
  }
})

console.log('✅ Supabase database connected successfully')

// Create user-impersonated Supabase client for RLS-aware queries
export const createUserClient = (accessToken: string) => {
  if (!supabaseUrl) {
    throw new Error('SUPABASE_URL is not configured')
  }
  
  // Use anon key with user token for RLS enforcement
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY
  if (!supabaseAnonKey) {
    throw new Error('SUPABASE_ANON_KEY is not configured')
  }
  
  return createClient(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    },
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false
    }
  })
}

export default supabase 