import { Request, Response, NextFunction } from 'express';
import { supabase } from '../config/supabase';



// Extend the Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        full_name?: string;
        status: string;
        role?: string;
      };
      userToken?: string; // Add JWT token for user-impersonated queries
    }
  }
}

export const authenticateUser = async (req: Request, res: Response, next: NextFunction) => {
  try {




    // Extract JWT token from Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Authorization header missing or invalid' });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      res.status(401).json({ error: 'Invalid or expired token' });
      return;
    }

    // Get user profile from our users table
    console.log('🔍 Looking for user profile:', user.id, user.email);
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('id, email, full_name, role')
      .eq('id', user.id)
      .single();

    if (profileError || !userProfile) {
      console.error('❌ User profile not found:', {
        userId: user.id,
        userEmail: user.email,
        error: profileError ? {
          message: profileError.message,
          details: profileError.details,
          code: profileError.code
        } : 'No profile data'
      });
      res.status(401).json({ error: 'User profile not found' });
      return;
    }

    console.log('✅ User profile found:', userProfile.email);

    // Note: No status field in users table, all authenticated users are considered active

    // Update last_login timestamp for active users
    try {
      await supabase
        .from('users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', user.id);
    } catch (loginUpdateError) {
      // Don't fail auth if last_login update fails, just log it
      console.warn('Failed to update last_login for user:', user.id, loginUpdateError);
    }

    // Add user and token to request object
    req.user = {
      id: userProfile.id,
      email: userProfile.email,
      full_name: userProfile.full_name,
      status: 'active', // Default status since no status field in users table
      role: userProfile.role
    };
    req.userToken = token; // Store token for user-impersonated queries

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ error: 'Authentication failed' });
    return;
  }
};

// Optional middleware for development - use hardcoded user if no auth
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Try to authenticate normally first
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      authenticateUser(req, res, next);
      return;
    }

    // Fallback to hardcoded user for development
    if (process.env.NODE_ENV === 'development') {
      // Use the same hardcoded user ID from actual data
      req.user = {
        id: '6354360e-fe9d-4e1b-b184-479f46a3a06e',
        email: '<EMAIL>',
        full_name: 'Nassar',
        status: 'active',
        role: 'admin'
      };
      next();
      return;
    }

    res.status(401).json({ error: 'Authentication required' });
    return;
  } catch (error) {
    console.error('Optional auth error:', error);
    res.status(500).json({ error: 'Authentication failed' });
    return;
  }
}; 