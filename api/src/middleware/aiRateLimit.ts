// @ts-nocheck
// AI rate limiting middleware
import { Request, Response, NextFunction } from 'express'
import { supabase } from '../config/supabase'

interface AuthenticatedRequest extends Request {
  user?: { id: string }
}

// Helper to fetch system defaults (cached in-memory for 5 minutes)
let cachedDefaults: { limits: any; fetchedAt: number } | null = null
const FIVE_MINUTES = 5 * 60 * 1000

const getSystemDefaultLimits = async () => {
  const now = Date.now()
  if (cachedDefaults && now - cachedDefaults.fetchedAt < FIVE_MINUTES) {
    return cachedDefaults.limits
  }
  const { data, error } = await supabase
    .from('system_defaults')
    .select('value')
    .eq('key', 'ai_limits')
    .single()

  if (error || !data) {
    // Fallback hard-coded defaults
    return { hour: 20, week: 150, month: 500 }
  }

  cachedDefaults = { limits: data.value, fetchedAt: now }
  return data.value
}

export const aiRateLimit = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ error: 'Authentication required' })
    }
    const userId = req.user.id

    // 1. Get user specific limits
    const { data: limitRow, error: limitErr } = await supabase
      .from('user_limits')
      .select('ai_limits')
      .eq('user_id', userId)
      .single()

    if (limitErr && limitErr.code !== 'PGRST116') {
      console.error('aiRateLimit: failed to fetch user_limits', limitErr)
    }

    let limits = limitRow?.ai_limits || {}

    // 2. Fill missing keys with system defaults
    const systemDefaults = await getSystemDefaultLimits()
    limits = { ...systemDefaults, ...limits }

    // 3. Fetch usage counts in a single query
    const { data: usageCounts, error: usageErr } = await supabase.rpc(
      'fn_ai_usage_counts',
      { p_user_id: userId }
    )

    /*
      fn_ai_usage_counts should be created in SQL (optional helper):
      returns record (hour_count, day_count, week_count, month_count)
      We'll fall back to simple queries if missing
    */

    let counts
    if (usageErr) {
      // Fallback implementation (4 separate counts)
      const now = new Date()
      const periods: { key: keyof typeof limits; from: Date }[] = [
        { key: 'hour', from: new Date(now.getTime() - 60 * 60 * 1000) },
        { key: 'day', from: new Date(now.getTime() - 24 * 60 * 60 * 1000) },
        { key: 'week', from: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) },
        { key: 'month', from: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) },
      ]

      counts = {}
      for (const { key, from } of periods) {
        const { count } = await supabase
          .from('ai_usage_logs')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', userId)
          .gte('request_timestamp', from.toISOString())
        // @ts-ignore – dynamic key
        counts[`${key}_count`] = count || 0
      }
    } else {
      counts = usageCounts
    }

    // 4. Compare counts with limits
    type Key = 'hour' | 'day' | 'week' | 'month'
    const periodNames: Key[] = ['hour', 'day', 'week', 'month']
    for (const p of periodNames) {
      const limitValue = limits[p]
      if (limitValue && counts[`${p}_count`] >= limitValue) {
        return res.status(429).json({
          error: 'AI chat limit reached',
          period: p,
          limit: limitValue,
          reset_in_seconds: getResetSeconds(p),
        })
      }
    }

    // Pass down limits & counts so route can log usage afterwards
    res.locals.aiLimits = limits
    res.locals.aiCounts = counts

    next()
  } catch (err) {
    console.error('aiRateLimit middleware error', err)
    next(err)
  }
}

// Helper to compute seconds until period reset (approx)
function getResetSeconds(period: 'hour' | 'day' | 'week' | 'month'): number {
  const now = new Date()
  switch (period) {
    case 'hour':
      return 3600 - (now.getMinutes() * 60 + now.getSeconds())
    case 'day':
      return 86400 - (now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds())
    case 'week':
      // seconds until next Monday 00:00
      const dayOfWeek = now.getUTCDay()
      const daysUntilMonday = (7 - dayOfWeek) % 7
      return daysUntilMonday * 86400 + getResetSeconds('day')
    case 'month':
      const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1)
      return Math.floor((nextMonth.getTime() - now.getTime()) / 1000)
  }
} 