// @ts-nocheck
import nodemailer from 'nodemailer';

export interface ReviewRequestParams {
  clientId: string;
  clientEmail: string;
  clientName: string;
  message: string;
  tone: 'friendly' | 'professional' | 'casual' | 'follow_up';
  businessName?: string;
  senderName?: string;
  userId: string;
}

export interface EmailResult {
  success: boolean;
  emailId?: string;
  error?: string;
  message: string;
}

class ReviewRequestEmailService {
  private transporter: nodemailer.Transporter | null = null;

  private async createTransporter(): Promise<nodemailer.Transporter> {
    if (this.transporter) {
      return this.transporter;
    }

    console.log('🔧 Creating Gmail SMTP transporter for review requests...');

    if (!process.env.GMAIL_USER_EMAIL || !process.env.GMAIL_APP_PASSWORD) {
      throw new Error('Gmail credentials not configured. Please set GMAIL_USER_EMAIL and GMAIL_APP_PASSWORD in .env file');
    }

    this.transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER_EMAIL,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
      tls: {
        // Don't fail on invalid certificates (for development)
        rejectUnauthorized: process.env.NODE_ENV === 'production' ? true : false
      }
    });

    console.log('✅ Gmail transporter created for review requests');
    return this.transporter!;
  }

  async sendReviewRequest(params: ReviewRequestParams): Promise<EmailResult> {
    try {
      console.log('🚀 Sending review request email...');
      console.log('📧 To:', params.clientEmail);
      console.log('👤 Client:', params.clientName);
      console.log('🎨 Tone:', params.tone);

      const transporter = await this.createTransporter();
      const firstName = params.clientName.split(' ')[0];

      const mailOptions = {
        from: `${params.businessName || 'DeskBelt'} <${process.env.GMAIL_USER_EMAIL}>`,
        to: params.clientEmail,
        subject: `Review Request - ${params.businessName || 'Your Recent Service'}`,
        html: this.generateReviewRequestHTML(params, firstName),
        text: this.generateReviewRequestText(params, firstName),
        headers: {
          'X-Mailer': 'DeskBelt Review System',
          'X-Priority': '3',
          'X-MSMail-Priority': 'Normal',
        },
      };

      console.log('📮 Sending review request email...');
      const result = await transporter.sendMail(mailOptions);

      console.log('✅ Review request email sent successfully!');
      console.log('📧 Message ID:', result.messageId);

      return {
        success: true,
        emailId: result.messageId,
        message: 'Review request sent successfully via Gmail SMTP'
      };

    } catch (error) {
      console.error('❌ Review request email error:', error);
      
      let errorMessage = 'Failed to send review request email';
      
      if (error instanceof Error) {
        if (error.message.includes('Invalid login')) {
          errorMessage = 'Gmail authentication failed. Please check your email and app password.';
        } else if (error.message.includes('ENOTFOUND')) {
          errorMessage = 'Network error. Please check your internet connection.';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: errorMessage
      };
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      console.log('🔍 Verifying Gmail SMTP connection for review requests...');
      const transporter = await this.createTransporter();
      await transporter.verify();
      console.log('✅ Gmail SMTP connection verified for review requests');
      return true;
    } catch (error) {
      console.error('❌ Gmail SMTP verification failed for review requests:', error);
      return false;
    }
  }

  private generateReviewRequestHTML(params: ReviewRequestParams, firstName: string): string {
    const toneColors = {
      friendly: { primary: '#10B981', secondary: '#D1FAE5', accent: '#059669' },
      professional: { primary: '#1D4ED8', secondary: '#DBEAFE', accent: '#1E40AF' },
      casual: { primary: '#8B5CF6', secondary: '#EDE9FE', accent: '#7C3AED' },
      follow_up: { primary: '#F59E0B', secondary: '#FEF3C7', accent: '#D97706' }
    };

    const colors = toneColors[params.tone];
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Review Request - ${params.businessName}</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            line-height: 1.6; 
            color: #1f2937; 
            max-width: 600px; 
            margin: 0 auto; 
            padding: 20px; 
            background-color: #f9fafb;
          }
          .container { 
            background: #fff; 
            border-radius: 12px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
            overflow: hidden; 
          }
          .header { 
            background: linear-gradient(135deg, ${colors.primary} 0%, ${colors.accent} 100%); 
            color: white; 
            padding: 40px 30px; 
            text-align: center; 
          }
          .content { 
            padding: 40px 30px; 
          }
          .message { 
            background: ${colors.secondary}; 
            border-left: 4px solid ${colors.primary}; 
            padding: 25px; 
            margin: 25px 0; 
            border-radius: 0 8px 8px 0; 
            font-size: 16px;
            line-height: 1.7;
          }
          .cta-section {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: #f8fafc;
            border-radius: 8px;
          }
          .review-button {
            display: inline-block;
            background: ${colors.primary};
            color: white;
            text-decoration: none;
            padding: 15px 35px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .review-button:hover {
            background: ${colors.accent};
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
          }
          .stars {
            font-size: 24px;
            margin: 15px 0;
            color: #FCD34D;
          }
          .footer { 
            background: #f8fafc; 
            padding: 25px 30px; 
            text-align: center; 
            color: #6b7280; 
            font-size: 14px; 
            border-top: 1px solid #e5e7eb; 
          }
          .signature {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #4b5563;
          }
          @media only screen and (max-width: 600px) {
            body { padding: 10px; }
            .content, .header { padding: 25px 20px; }
            .cta-section { padding: 20px; }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 style="margin: 0; font-size: 28px; font-weight: 700;">⭐ Review Request</h1>
            <p style="margin: 15px 0 0 0; opacity: 0.9; font-size: 16px;">${params.businessName || 'DeskBelt Services'}</p>
          </div>
          
          <div class="content">
            <h2 style="color: #1f2937; margin-bottom: 20px; font-size: 24px;">Hi ${firstName}!</h2>
            
            <div class="message">
              ${params.message.replace(/\n/g, '<br><br>')}
            </div>
            
            <div class="cta-section">
              <div class="stars">⭐⭐⭐⭐⭐</div>
              <h3 style="color: #1f2937; margin: 0 0 15px 0;">We'd love your feedback!</h3>
              <a href="#" class="review-button">Leave a Review</a>
              <p style="margin: 20px 0 0 0; color: #6b7280; font-size: 14px;">
                Your review helps us improve and helps other customers find our services
              </p>
            </div>
            
            <div class="signature">
              <p style="margin: 0; font-weight: 600; color: #1f2937;">
                Thank you for choosing ${params.businessName || 'our services'}!
              </p>
              <p style="margin: 10px 0 0 0;">
                Best regards,<br>
                <strong>${params.senderName || 'The Team'}</strong>
              </p>
            </div>
          </div>
          
          <div class="footer">
            <p style="margin: 0;"><strong>${params.businessName || 'DeskBelt Services'}</strong></p>
            <p style="margin: 10px 0;">Professional Service Provider</p>
            <p style="margin: 15px 0 0 0; font-size: 12px;">
              This email was sent because you recently used our services. 
              If you no longer wish to receive these emails, please contact us.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateReviewRequestText(params: ReviewRequestParams, firstName: string): string {
    return `Hi ${firstName}!

${params.message}

We'd love your feedback!

Your review helps us improve and helps other customers find our services.

Thank you for choosing ${params.businessName || 'our services'}!

Best regards,
${params.senderName || 'The Team'}

---
${params.businessName || 'DeskBelt Services'}
Professional Service Provider

This email was sent because you recently used our services.`.trim();
  }
}

export const reviewRequestEmailService = new ReviewRequestEmailService();
export default ReviewRequestEmailService;