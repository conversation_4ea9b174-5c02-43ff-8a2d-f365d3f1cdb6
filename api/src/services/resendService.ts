import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

interface TestEmailParams {
  to: string;
  subject: string;
  message: string;
}

interface EmailResult {
  success: boolean;
  emailId?: string;
  error?: string;
  message: string;
}

export const sendTestEmail = async (params: TestEmailParams): Promise<EmailResult> => {
  try {
    console.log('🚀 Attempting to send email via Resend...');
    console.log('📧 To:', params.to);
    console.log('📝 Subject:', params.subject);
    console.log('🔑 Using API key:', process.env.RESEND_API_KEY ? 'Configured' : 'Missing');
    console.log('📮 From email:', process.env.FROM_EMAIL);

    const { data, error } = await resend.emails.send({
      from: `DeskBelt Test <${process.env.FROM_EMAIL}>`,
      to: [params.to],
      subject: params.subject,
      html: generateTestEmailHTML(params),
      text: generateTestEmailText(params),
    });

    if (error) {
      console.error('❌ Resend error:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to send email via Resend'
      };
    }

    console.log('✅ Email sent successfully!');
    console.log('📧 Email ID:', data.id);

    return {
      success: true,
      emailId: data.id,
      message: 'Email sent successfully via Resend'
    };

  } catch (error) {
    console.error('💥 Unexpected error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Unexpected error while sending email'
    };
  }
};

const generateTestEmailHTML = (params: TestEmailParams): string => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>${params.subject}</title>
      <style>
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
          line-height: 1.6; 
          color: #333; 
          max-width: 600px; 
          margin: 0 auto; 
          padding: 20px; 
        }
        .container { 
          background: #fff; 
          border-radius: 8px; 
          box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
          overflow: hidden; 
        }
        .header { 
          background: linear-gradient(135deg, #1D4ED8 0%, #3B82F6 100%); 
          color: white; 
          padding: 30px 20px; 
          text-align: center; 
        }
        .content { 
          padding: 30px 20px; 
        }
        .message { 
          background: #f8fafc; 
          border-left: 4px solid #1D4ED8; 
          padding: 20px; 
          margin: 20px 0; 
          border-radius: 0 8px 8px 0; 
        }
        .footer { 
          background: #f8fafc; 
          padding: 20px; 
          text-align: center; 
          color: #6b7280; 
          font-size: 14px; 
          border-top: 1px solid #e5e7eb; 
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1 style="margin: 0; font-size: 24px;">🚀 DeskBelt Email Test</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">Resend Integration Test</p>
        </div>
        
        <div class="content">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Test Email Successfully Sent!</h2>
          
          <div class="message">
            ${params.message.replace(/\n/g, '<br>')}
          </div>
          
          <p style="color: #10B981; font-weight: 600; margin-top: 30px;">
            ✅ This confirms that Resend is working correctly with your DeskBelt application!
          </p>
          
          <h3 style="color: #1f2937; margin-top: 30px;">Next Steps:</h3>
          <ul style="color: #4b5563;">
            <li>Integrate with review request feature</li>
            <li>Add HTML email templates</li>
            <li>Implement proper error handling</li>
            <li>Add email analytics tracking</li>
          </ul>
        </div>
        
        <div class="footer">
          <p><strong>DeskBelt Email Service</strong></p>
          <p>Powered by Resend • ${new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </body>
    </html>
  `;
};

const generateTestEmailText = (params: TestEmailParams): string => {
  return `
🚀 DeskBelt Email Test

Test Email Successfully Sent!

${params.message}

✅ This confirms that Resend is working correctly with your DeskBelt application!

Next Steps:
- Integrate with review request feature
- Add HTML email templates  
- Implement proper error handling
- Add email analytics tracking

---
DeskBelt Email Service
Powered by Resend • ${new Date().toLocaleDateString()}
  `.trim();
};