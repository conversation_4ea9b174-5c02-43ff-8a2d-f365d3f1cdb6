// @ts-nocheck
import nodemailer from 'nodemailer';

export interface GenericEmailParams {
  jobId?: string;
  recipientEmail: string;
  recipientName?: string;
  subject: string;
  message: string;
  emailType: 'job_communication' | 'review_request' | 'custom';
  businessName?: string;
  senderName?: string;
  userId: string;
  jobTitle?: string;
  jobDescription?: string;
}

export interface EmailResult {
  success: boolean;
  emailId?: string;
  error?: string;
  message: string;
}

class GenericEmailService {
  private transporter: nodemailer.Transporter | null = null;

  private async createTransporter(): Promise<nodemailer.Transporter> {
    if (this.transporter) {
      return this.transporter;
    }

    console.log('🔧 Creating Gmail SMTP transporter for generic emails...');

    if (!process.env.GMAIL_USER_EMAIL || !process.env.GMAIL_APP_PASSWORD) {
      throw new Error('Gmail credentials not configured. Please set GMAIL_USER_EMAIL and GMAIL_APP_PASSWORD in .env file');
    }

    this.transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER_EMAIL,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
      tls: {
        // Don't fail on invalid certificates (for development)
        rejectUnauthorized: process.env.NODE_ENV === 'production' ? true : false
      }
    });

    console.log('✅ Gmail transporter created for generic emails');
    return this.transporter!;
  }

  async sendEmail(params: GenericEmailParams): Promise<EmailResult> {
    try {
      console.log('🚀 Sending generic email...');
      console.log('📧 Type:', params.emailType);
      console.log('📧 To:', params.recipientEmail);
      console.log('📋 Subject:', params.subject);

      const transporter = await this.createTransporter();
      const firstName = params.recipientName ? params.recipientName.split(' ')[0] : 'there';

      const mailOptions = {
        from: `${params.businessName || 'DeskBelt'} <${process.env.GMAIL_USER_EMAIL}>`,
        to: params.recipientEmail,
        subject: params.subject,
        html: this.generateEmailHTML(params, firstName),
        text: this.generateEmailText(params, firstName),
        headers: {
          'X-Mailer': 'DeskBelt Communication System',
          'X-Priority': '3',
          'X-MSMail-Priority': 'Normal',
          'X-Email-Type': params.emailType,
        },
      };

      console.log('📮 Sending email...');
      const result = await transporter.sendMail(mailOptions);

      console.log('✅ Email sent successfully!');
      console.log('📧 Message ID:', result.messageId);

      return {
        success: true,
        emailId: result.messageId,
        message: `Email sent successfully via Gmail SMTP (${params.emailType})`
      };

    } catch (error) {
      console.error('❌ Generic email error:', error);
      
      let errorMessage = 'Failed to send email';
      
      if (error instanceof Error) {
        if (error.message.includes('Invalid login')) {
          errorMessage = 'Gmail authentication failed. Please check your email and app password.';
        } else if (error.message.includes('ENOTFOUND')) {
          errorMessage = 'Network error. Please check your internet connection.';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: errorMessage
      };
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      console.log('🔍 Verifying Gmail SMTP connection for generic emails...');
      const transporter = await this.createTransporter();
      await transporter.verify();
      console.log('✅ Gmail SMTP connection verified for generic emails');
      return true;
    } catch (error) {
      console.error('❌ Gmail SMTP verification failed for generic emails:', error);
      return false;
    }
  }

  private generateEmailHTML(params: GenericEmailParams, firstName: string): string {
    const emailTypeColors = {
      job_communication: { primary: '#1D4ED8', secondary: '#DBEAFE', accent: '#1E40AF' }, // Blue
      review_request: { primary: '#059669', secondary: '#D1FAE5', accent: '#047857' }, // Green
      custom: { primary: '#7C3AED', secondary: '#EDE9FE', accent: '#6D28D9' } // Purple
    };

    const colors = emailTypeColors[params.emailType];
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>${params.subject} - ${params.businessName}</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            line-height: 1.6; 
            color: #1f2937; 
            max-width: 600px; 
            margin: 0 auto; 
            padding: 20px; 
            background-color: #f9fafb;
          }
          .container { 
            background: #fff; 
            border-radius: 12px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
            overflow: hidden; 
          }
          .header { 
            background: linear-gradient(135deg, ${colors.primary} 0%, ${colors.accent} 100%); 
            color: white; 
            padding: 40px 30px; 
            text-align: center; 
          }
          .header h1 {
            margin: 0; 
            font-size: 24px; 
            font-weight: 700;
          }
          .header p {
            margin: 15px 0 0 0; 
            opacity: 0.9; 
            font-size: 16px;
          }
          .content { 
            padding: 40px 30px; 
          }
          .job-info {
            background: ${colors.secondary}; 
            border-left: 4px solid ${colors.primary}; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 0 8px 8px 0;
          }
          .job-info h3 {
            margin: 0 0 10px 0;
            color: ${colors.accent};
            font-size: 16px;
          }
          .job-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #4b5563;
          }
          .message { 
            background: #f8fafc; 
            border: 1px solid #e5e7eb; 
            padding: 25px; 
            margin: 25px 0; 
            border-radius: 8px; 
            font-size: 16px;
            line-height: 1.7;
          }
          .footer { 
            background: #f8fafc; 
            padding: 25px 30px; 
            text-align: center; 
            color: #6b7280; 
            font-size: 14px; 
            border-top: 1px solid #e5e7eb; 
          }
          .signature {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #4b5563;
          }
          @media only screen and (max-width: 600px) {
            body { padding: 10px; }
            .content, .header { padding: 25px 20px; }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${this.getEmailTypeTitle(params.emailType)}</h1>
            <p>${params.businessName || 'DeskBelt Services'}</p>
          </div>
          
          <div class="content">
            <h2 style="color: #1f2937; margin-bottom: 20px; font-size: 20px;">Hi ${firstName}!</h2>
            
            ${params.jobTitle || params.jobDescription ? `
            <div class="job-info">
              <h3>📋 Job Details</h3>
              ${params.jobTitle ? `<p><strong>Job:</strong> ${params.jobTitle}</p>` : ''}
              ${params.jobDescription ? `<p><strong>Description:</strong> ${params.jobDescription}</p>` : ''}
            </div>
            ` : ''}
            
            <div class="message">
              ${params.message.replace(/\n/g, '<br><br>')}
            </div>
            
            <div class="signature">
              <p style="margin: 0; font-weight: 600; color: #1f2937;">
                Thank you for choosing ${params.businessName || 'our services'}!
              </p>
              <p style="margin: 10px 0 0 0;">
                Best regards,<br>
                <strong>${params.senderName || 'The Team'}</strong>
              </p>
            </div>
          </div>
          
          <div class="footer">
            <p style="margin: 0;"><strong>${params.businessName || 'DeskBelt Services'}</strong></p>
            <p style="margin: 10px 0;">Professional Service Provider</p>
            <p style="margin: 15px 0 0 0; font-size: 12px;">
              This email was sent from your DeskBelt account. 
              If you have any questions, please contact us directly.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateEmailText(params: GenericEmailParams, firstName: string): string {
    const jobInfo = (params.jobTitle || params.jobDescription) ? `
JOB DETAILS:
${params.jobTitle ? `Job: ${params.jobTitle}` : ''}
${params.jobDescription ? `Description: ${params.jobDescription}` : ''}

` : '';

    return `Hi ${firstName}!

${jobInfo}${params.message}

Thank you for choosing ${params.businessName || 'our services'}!

Best regards,
${params.senderName || 'The Team'}

---
${params.businessName || 'DeskBelt Services'}
Professional Service Provider

This email was sent from your DeskBelt account.`.trim();
  }

  private getEmailTypeTitle(emailType: string): string {
    switch (emailType) {
      case 'job_communication':
        return '💼 Job Communication';
      case 'review_request':
        return '⭐ Review Request';
      case 'custom':
        return '📧 Message';
      default:
        return '📧 Communication';
    }
  }
}

export const genericEmailService = new GenericEmailService();
export default GenericEmailService;