import nodemailer from 'nodemailer';

interface TestEmailParams {
  to: string;
  subject: string;
  message: string;
}

interface EmailResult {
  success: boolean;
  emailId?: string;
  error?: string;
  message: string;
}

class NodemailerGmailService {
  private transporter: nodemailer.Transporter | null = null;

  private async createTransporter(): Promise<nodemailer.Transporter> {
    if (this.transporter) {
      return this.transporter;
    }

    console.log('🔧 Creating Gmail SMTP transporter...');
    console.log('📧 Gmail User:', process.env.GMAIL_USER_EMAIL);
    console.log('🔑 App Password:', process.env.GMAIL_APP_PASSWORD ? 'Configured' : 'Missing');

    if (!process.env.GMAIL_USER_EMAIL || !process.env.GMAIL_APP_PASSWORD) {
      throw new Error('Gmail credentials not configured. Please set GMAIL_USER_EMAIL and GMAIL_APP_PASSWORD in .env file');
    }

    this.transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER_EMAIL,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
      tls: {
        // Don't fail on invalid certificates (for development)
        rejectUnauthorized: process.env.NODE_ENV === 'production' ? true : false
      }
    });

    console.log('✅ Gmail transporter created');
    return this.transporter!;
  }

  async sendTestEmail(params: TestEmailParams): Promise<EmailResult> {
    try {
      console.log('🚀 Attempting to send email via Gmail SMTP...');
      console.log('📧 To:', params.to);
      console.log('📝 Subject:', params.subject);

      const transporter = await this.createTransporter();

      const mailOptions = {
        from: `DeskBelt Team <${process.env.GMAIL_USER_EMAIL}>`,
        to: params.to,
        subject: params.subject,
        html: this.generateTestEmailHTML(params),
        text: this.generateTestEmailText(params),
      };

      console.log('📮 Sending email...');
      const result = await transporter.sendMail(mailOptions);

      console.log('✅ Email sent successfully via Gmail!');
      console.log('📧 Message ID:', result.messageId);

      return {
        success: true,
        emailId: result.messageId,
        message: 'Email sent successfully via Gmail SMTP'
      };

    } catch (error) {
      console.error('❌ Gmail SMTP error:', error);
      
      let errorMessage = 'Failed to send email via Gmail SMTP';
      
      if (error instanceof Error) {
        if (error.message.includes('Invalid login')) {
          errorMessage = 'Gmail authentication failed. Please check your email and app password.';
        } else if (error.message.includes('Less secure app access')) {
          errorMessage = 'Please enable 2-factor authentication and use an App Password for Gmail.';
        } else if (error.message.includes('ENOTFOUND')) {
          errorMessage = 'Network error. Please check your internet connection.';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: errorMessage
      };
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      console.log('🔍 Verifying Gmail SMTP connection...');
      const transporter = await this.createTransporter();
      await transporter.verify();
      console.log('✅ Gmail SMTP connection verified');
      return true;
    } catch (error) {
      console.error('❌ Gmail SMTP verification failed:', error);
      return false;
    }
  }

  private generateTestEmailHTML(params: TestEmailParams): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>${params.subject}</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            line-height: 1.6; 
            color: #333; 
            max-width: 600px; 
            margin: 0 auto; 
            padding: 20px; 
          }
          .container { 
            background: #fff; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
            overflow: hidden; 
          }
          .header { 
            background: linear-gradient(135deg, #059669 0%, #10B981 100%); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
          }
          .content { 
            padding: 30px 20px; 
          }
          .message { 
            background: #f0fdf4; 
            border-left: 4px solid #10B981; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 0 8px 8px 0; 
          }
          .comparison {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
          }
          .footer { 
            background: #f8fafc; 
            padding: 20px; 
            text-align: center; 
            color: #6b7280; 
            font-size: 14px; 
            border-top: 1px solid #e5e7eb; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 style="margin: 0; font-size: 24px;">📧 Gmail SMTP Test</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Nodemailer + Gmail Integration</p>
          </div>
          
          <div class="content">
            <h2 style="color: #1f2937; margin-bottom: 20px;">Gmail SMTP Working!</h2>
            
            <div class="message">
              ${params.message.replace(/\n/g, '<br>')}
            </div>
            
            <div class="comparison">
              <h3 style="color: #92400e; margin-top: 0;">📊 Comparison Test</h3>
              <p style="margin-bottom: 0; color: #92400e;">
                This email was sent via <strong>Gmail SMTP using Nodemailer</strong>. 
                Compare with the Resend email to see the differences in delivery speed, 
                formatting, and overall experience.
              </p>
            </div>
            
            <p style="color: #10B981; font-weight: 600; margin-top: 30px;">
              ✅ Gmail SMTP is working correctly with your DeskBelt application!
            </p>
            
            <h3 style="color: #1f2937; margin-top: 30px;">Gmail SMTP Features:</h3>
            <ul style="color: #4b5563;">
              <li>✅ Free for up to 500 emails/day</li>
              <li>✅ Uses your existing Gmail account</li>
              <li>✅ No third-party dependencies</li>
              <li>✅ Excellent deliverability reputation</li>
              <li>✅ Self-hosted control</li>
            </ul>
          </div>
          
          <div class="footer">
            <p><strong>DeskBelt Email Service</strong></p>
            <p>Powered by Gmail SMTP + Nodemailer • ${new Date().toLocaleDateString()}</p>
            <p style="font-size: 12px; margin-top: 10px;">
              Sent from: ${process.env.GMAIL_USER_EMAIL}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateTestEmailText(params: TestEmailParams): string {
    return `Gmail SMTP Test

Gmail SMTP Working!

${params.message}

Comparison Test
This email was sent via Gmail SMTP using Nodemailer. Compare with the Resend email to see the differences in delivery speed, formatting, and overall experience.

Gmail SMTP is working correctly with your DeskBelt application!

Gmail SMTP Features:
- Free for up to 500 emails/day
- Uses your existing Gmail account
- No third-party dependencies
- Excellent deliverability reputation
- Self-hosted control

---
DeskBelt Email Service
Powered by Gmail SMTP + Nodemailer
Sent from: ${process.env.GMAIL_USER_EMAIL}`;
  }
}

export const gmailService = new NodemailerGmailService();
export { NodemailerGmailService };