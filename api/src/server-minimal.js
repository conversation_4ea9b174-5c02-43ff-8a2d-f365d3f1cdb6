// Minimal server just for the create-profile endpoint
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');

const app = express();
const PORT = process.env.PORT || 4000;

// Create Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🔍 Supabase Configuration:');
console.log('   URL:', supabaseUrl);
console.log('   Service Key present:', !!supabaseServiceKey);

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false
  }
});

console.log('✅ Supabase client created');

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
  credentials: true,
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'API is running' });
});

// Create profile endpoint with Supabase integration
app.post('/api/create-profile', async (req, res) => {
  try {
    const { user_id, email, full_name } = req.body;

    if (!user_id || !email) {
      return res.status(400).json({ error: 'User ID and email are required' });
    }

    console.log('Creating profile via API for:', email);

    // Use service role to bypass RLS policies
    const { data, error } = await supabase
      .from('users')
      .insert({
        id: user_id,
        email: email,
        full_name: full_name || null,
        role: 'tradesperson',
        country: 'UK',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating profile via API:', error);
      return res.status(500).json({ error: error.message });
    }

    console.log('Profile created successfully via API:', data);
    res.json({ success: true, profile: data });

  } catch (error) {
    console.error('API endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Minimal API server running on localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 Create profile: http://localhost:${PORT}/api/create-profile`);
});