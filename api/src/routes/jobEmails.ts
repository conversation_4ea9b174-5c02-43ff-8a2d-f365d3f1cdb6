// @ts-nocheck
import express from 'express'
import { body, validationResult } from 'express-validator'
import { authenticateUser } from '../middleware/auth'
import { genericEmailService } from '../services/genericEmailService'
import { supabase } from '../config/supabase'

const router = express.Router()

// Rate limiting: 50 job emails per hour per user (shared with review requests)
const jobEmailAttempts = new Map()

const checkRateLimit = (userId: string): boolean => {
  const now = Date.now()
  const userAttempts = jobEmailAttempts.get(userId) || []
  
  // Remove attempts older than 1 hour
  const validAttempts = userAttempts.filter((time: number) => now - time < 60 * 60 * 1000)
  
  if (validAttempts.length >= 50) {
    return false // Rate limit exceeded
  }
  
  validAttempts.push(now)
  jobEmailAttempts.set(userId, validAttempts)
  return true
}

// Validation rules for job emails
const jobEmailValidation = [
  body('recipientEmail').isEmail().withMessage('Valid email address required'),
  body('recipientName').optional().isLength({ min: 1, max: 100 }).withMessage('Recipient name too long (max 100 characters)'),
  body('subject').isLength({ min: 5, max: 100 }).withMessage('Subject must be 5-100 characters'),
  body('message').isLength({ min: 10, max: 5000 }).withMessage('Message must be 10-5000 characters'),
  body('businessName').optional().isLength({ max: 100 }).withMessage('Business name too long (max 100 characters)'),
  body('senderName').optional().isLength({ max: 100 }).withMessage('Sender name too long (max 100 characters)')
]

// Send job email
router.post('/:jobId/send-email', 
  authenticateUser,
  jobEmailValidation,
  async (req: any, res: any): Promise<void> => {
    try {
      console.log('📬 Job email endpoint called')
      console.log('👤 User ID:', req.user.id)
      console.log('📋 Job ID:', req.params.jobId)
      
      // Validate input
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        })
        return
      }

      // Check rate limit
      if (!checkRateLimit(req.user.id)) {
        res.status(429).json({
          success: false,
          message: 'Rate limit exceeded. You can send up to 50 emails per hour.',
          retryAfter: 3600
        })
        return
      }

      const {
        recipientEmail,
        recipientName,
        subject,
        message,
        businessName,
        senderName
      } = req.body

      const jobId = req.params.jobId

      console.log('📧 Sending job email to:', recipientEmail)
      console.log('📋 Subject:', subject)

      // Verify job belongs to authenticated user and get job details
      const { data: job, error: jobError } = await supabase
        .from('jobs')
        .select(`
          id, 
          title, 
          description, 
          status, 
          client_id,
          clients!inner(
            id,
            name,
            email,
            created_by
          )
        `)
        .eq('id', jobId)
        .eq('created_by', req.user.id)
        .single()

      if (jobError || !job) {
        console.error('❌ Job verification failed:', jobError)
        res.status(403).json({
          success: false,
          message: 'Job not found or access denied'
        })
        return
      }

      console.log('✅ Job verified:', job.title)
      console.log('👥 Client:', job.clients.name)

      // Send email using generic email service
      const result = await genericEmailService.sendEmail({
        jobId: jobId,
        recipientEmail,
        recipientName: recipientName || job.clients.name,
        subject,
        message,
        emailType: 'job_communication',
        businessName: businessName || req.user?.businessName || 'DeskBelt',
        senderName: senderName || req.user?.name || 'The Team',
        userId: req.user.id,
        jobTitle: job.title,
        jobDescription: job.description
      })

      if (result.success) {
        console.log('📧 Job email sent successfully')

        // Log the email activity to job notes
        try {
          const { error: logError } = await supabase
            .from('job_notes')
            .insert({
              job_id: jobId,
              author_id: req.user.id,
              message: `📧 Email sent to ${recipientName ? `${recipientName} (${recipientEmail})` : recipientEmail} on ${new Date().toLocaleString('en-GB', {
                day: '2-digit',
                month: 'short', 
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              })}

Subject: ${subject}`,
              message_type: 'system'
            })

          if (logError) {
            console.error('⚠️ Failed to log job email:', logError)
          } else {
            console.log('📝 Job email logged to notes')
          }
        } catch (logError) {
          console.error('⚠️ Error logging job email:', logError)
        }

        res.json({
          success: true,
          emailId: result.emailId,
          message: 'Job email sent successfully',
          timestamp: new Date().toISOString(),
          job: {
            id: job.id,
            title: job.title,
            client: {
              id: job.clients.id,
              name: job.clients.name,
              email: job.clients.email
            }
          }
        })
      } else {
        console.error('❌ Job email failed:', result.error)
        res.status(500).json({
          success: false,
          message: result.message,
          error: result.error
        })
      }

    } catch (error) {
      console.error('💥 Job email endpoint error:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
)

// Test job email connection
router.get('/test-connection', authenticateUser, async (req, res) => {
  try {
    console.log('🔍 Testing job email connection...')
    
    const isConnected = await genericEmailService.verifyConnection()
    
    res.json({
      success: isConnected,
      message: isConnected ? 'Job email service is operational' : 'Job email service connection failed',
      service: 'Gmail SMTP',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Job email connection test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get job email statistics for user
router.get('/stats', authenticateUser, async (req, res) => {
  try {
    // Get job email count from job notes (system notes with email content)
    const { data: jobEmails, error } = await supabase
      .from('job_notes')
      .select('id, created_at, job_id')
      .eq('author_id', req.user?.id)
      .eq('message_type', 'system')
      .like('message', '%Email sent%')
      .order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    // Calculate stats
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    const todayCount = jobEmails?.filter(email => 
      new Date(email.created_at) >= today
    ).length || 0

    const monthCount = jobEmails?.filter(email => 
      new Date(email.created_at) >= thisMonth
    ).length || 0

    const totalCount = jobEmails?.length || 0

    res.json({
      success: true,
      stats: {
        today: todayCount,
        thisMonth: monthCount,
        total: totalCount,
        hourlyLimit: 50,
        monthlyLimit: 1500 // Gmail limit (conservative estimate)
      },
      recentEmails: jobEmails?.slice(0, 5).map(email => ({
        id: email.id,
        jobId: email.job_id,
        sentAt: email.created_at
      })) || []
    })

  } catch (error) {
    console.error('❌ Job email stats error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get job email statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

export default router