// @ts-nocheck
import express from 'express'
import { supabase, createUserClient } from '../config/supabase'
import { authenticateUser } from '../middleware/auth'

const router = express.Router()
console.log('4444444444444444444444')
// GET /api/clients - List clients from Supabase
router.get('/', authenticateUser, async (req, res) => {
  try {
    const { search, limit = 20, offset = 0 } = req.query

    console.log('🔍 Received client list request:', {
      search,
      limit,
      offset
    })


    // Use explicit user filtering (like notifications route) - more reliable than RLS
    let query = supabase
      .from('clients')
      .select(`
        id,
        name,
        business_name,
        email,
        phone,
        address,
        rating,
        created_at,
        updated_at
      `)
      .eq('created_by', req.user.id)  // Explicit user filtering
      .order('created_at', { ascending: false })

    // Filter by search term
    if (search) {
      const searchTerm = search as string
      query = query.or(`name.ilike.%${searchTerm}%,business_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,phone.ilike.%${searchTerm}%,address.ilike.%${searchTerm}%`)
    }

    // Pagination
    const limitNum = parseInt(limit as string)
    const offsetNum = parseInt(offset as string)
    query = query.range(offsetNum, offsetNum + limitNum - 1)

    const { data: clients, error } = await query

    if (error) {
      console.error('Supabase error fetching clients:', error)
      return res.status(500).json({ error: 'Failed to fetch clients from database' })
    }

    // Get job counts for each client (with explicit user filtering)
    const clientsWithJobCounts = await Promise.all((clients || []).map(async (client) => {
      // Get total job count for this client
      const { count: totalJobs } = await supabase
        .from('jobs')
        .select('*', { count: 'exact', head: true })
        .eq('client_id', client.id)
        .eq('created_by', req.user.id)

      // Get active job count for this client
      const { count: activeJobs } = await supabase
        .from('jobs')
        .select('*', { count: 'exact', head: true })
        .eq('client_id', client.id)
        .eq('created_by', req.user.id)
        .in('status', ['new', 'in_progress', 'scheduled'])

      return {
        ...client,
        totalJobs: totalJobs || 0,
        activeJobs: activeJobs || 0
      }
    }))

    // Get total count for pagination (with explicit user filtering)
    const { count: totalCount } = await supabase
      .from('clients')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', req.user.id)

    res.json({
      clients: clientsWithJobCounts,
      total: totalCount || 0,
      hasMore: offsetNum + limitNum < (totalCount || 0)
    })

  } catch (error) {
    console.error('Error fetching clients:', error)
    res.status(500).json({ error: 'Failed to fetch clients' })
  }
})

// POST /api/clients - Create new client in Supabase
router.post('/', authenticateUser, async (req, res) => {
  try {
    const { name, email, phone, address, rating, business_name } = req.body

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'name is required'
      })
    }

    // Create new client in Supabase
    const { data: newClient, error } = await supabase
      .from('clients')
      .insert({
        name: name.trim(),
        business_name: business_name?.trim() || null,
        email: email?.trim() || null,
        phone: phone?.trim() || null,
        address: address?.trim() || null,
        rating: rating || null,
        created_by: req.user.id
      })
      .select()
      .single()

    if (error) {
      console.error('Supabase error creating client:', error)
      return res.status(500).json({ 
        error: 'Failed to create client in database',
        details: error.message 
      })
    }

    res.status(201).json({
      id: newClient.id,
      message: 'Client created successfully',
      client: {
        ...newClient,
        totalJobs: 0,
        activeJobs: 0
      }
    })

  } catch (error) {
    console.error('Error creating client:', error)
    res.status(500).json({ error: 'Failed to create client' })
  }
})

// GET /api/clients/:id - Get single client from Supabase
router.get('/:id', authenticateUser, async (req, res) => {
  try {
    const { data: client, error } = await supabase
      .from('clients')
      .select(`
        id,
        name,
        business_name,
        email,
        phone,
        address,
        rating,
        created_at,
        updated_at
      `)
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (error || !client) {
      return res.status(404).json({
        error: 'Client not found',
        details: 'Client ID does not exist'
      })
    }

    // Get job counts (with explicit user filtering)
    const { count: totalJobs } = await supabase
      .from('jobs')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', client.id)
      .eq('created_by', req.user.id)

    const { count: activeJobs } = await supabase
      .from('jobs')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', client.id)
      .eq('created_by', req.user.id)
      .in('status', ['new', 'in_progress', 'scheduled'])

    // Get client notes (with explicit user filtering)
    const { data: notes, error: notesError } = await supabase
      .from('client_notes')
      .select(`
        id,
        message,
        created_at,
        author_id
      `)
      .eq('client_id', client.id)
      .eq('author_id', req.user.id)
      .order('created_at', { ascending: true })

    if (notesError) {
      console.error('Error fetching client notes:', notesError)
    }

    // Format notes with author info
    const formattedNotes = (notes || []).map(note => ({
      ...note,
      author: {
        id: note.author_id,
        full_name: 'Current User', // TODO: get from users table
        role: 'user'
      }
    }))

    const clientWithDetails = {
      ...client,
      totalJobs: totalJobs || 0,
      activeJobs: activeJobs || 0,
      notes: formattedNotes
    }

    res.json(clientWithDetails)

  } catch (error) {
    console.error('Error fetching client:', error)
    res.status(500).json({ error: 'Failed to fetch client' })
  }
})

// PUT /api/clients/:id - Update client in Supabase
router.put('/:id', authenticateUser, async (req, res) => {
  try {
    console.log('📝 Received client update request:', {
      id: req.params.id,
      body: req.body
    })
    
    const { name, email, phone, address, rating, business_name } = req.body

    // Verify client exists and belongs to current user
    const { data: existingClient, error: fetchError } = await supabase
      .from('clients')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (fetchError || !existingClient) {
      return res.status(404).json({
        error: 'Client not found',
        details: 'Client ID does not exist'
      })
    }

    // Build update object with only provided fields
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (name !== undefined) updateData.name = name.trim();
    if (business_name !== undefined) updateData.business_name = business_name?.trim() || null;
    if (email !== undefined) updateData.email = email?.trim() || null;
    if (phone !== undefined) updateData.phone = phone?.trim() || null;
    if (address !== undefined) updateData.address = address?.trim() || null;
    if (rating !== undefined) updateData.rating = rating;

    // Update client in Supabase (with explicit user check)
    const { data: updatedClient, error } = await supabase
      .from('clients')
      .update(updateData)
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .select()
      .single()

    if (error) {
      console.error('Supabase error updating client:', error)
      return res.status(500).json({ 
        error: 'Failed to update client in database',
        details: error.message 
      })
    }

    console.log('✅ Updated client in Supabase:', updatedClient)

    res.json({
      id: updatedClient.id,
      message: 'Client updated successfully'
    })

  } catch (error) {
    console.error('Error updating client:', error)
    res.status(500).json({ error: 'Failed to update client' })
  }
})

// PATCH /api/clients/:id/rating - Update client rating only
router.patch('/:id/rating', authenticateUser, async (req, res) => {
  try {
    const { rating } = req.body
    const clientId = req.params.id

    // Validate rating
    if (rating === undefined || rating === null) {
      return res.status(400).json({
        error: 'Missing required field',
        details: 'rating is required'
      })
    }

    if (typeof rating !== 'number' || rating < 0 || rating > 5) {
      return res.status(400).json({
        error: 'Invalid rating',
        details: 'rating must be a number between 0 and 5'
      })
    }

    // Verify client exists and belongs to current user
    const { data: existingClient, error: fetchError } = await supabase
      .from('clients')
      .select('id, name')
      .eq('id', clientId)
      .eq('created_by', req.user.id)
      .single()

    if (fetchError || !existingClient) {
      return res.status(404).json({
        error: 'Client not found',
        details: 'Client ID does not exist'
      })
    }

    // Update only the rating (with explicit user check)
    const { data: updatedClient, error } = await supabase
      .from('clients')
      .update({
        rating,
        updated_at: new Date().toISOString()
      })
      .eq('id', clientId)
      .eq('created_by', req.user.id)
      .select('id, name, rating, updated_at')
      .single()

    if (error) {
      console.error('Supabase error updating client rating:', error)
      return res.status(500).json({ 
        error: 'Failed to update client rating',
        details: error.message 
      })
    }

    console.log(`✅ Updated rating for client "${existingClient.name}" to ${rating} stars`)

    res.json({
      id: updatedClient.id,
      rating: updatedClient.rating,
      message: `Client rating updated to ${rating} stars`
    })

  } catch (error) {
    console.error('Error updating client rating:', error)
    res.status(500).json({ error: 'Failed to update client rating' })
  }
})

// DELETE /api/clients/:id - Delete client from Supabase
router.delete('/:id', authenticateUser, async (req, res) => {
  try {
    // Check if client has any jobs (with explicit user filtering)
    const { count: jobCount } = await supabase
      .from('jobs')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', req.params.id)
      .eq('created_by', req.user.id)

    if (jobCount && jobCount > 0) {
      return res.status(400).json({
        error: 'Cannot delete client',
        details: 'Client has existing jobs. Please delete or reassign jobs first.'
      })
    }

    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)

    if (error) {
      console.error('Supabase error deleting client:', error)
      return res.status(500).json({ 
        error: 'Failed to delete client from database',
        details: error.message 
      })
    }

    console.log('✅ Deleted client from Supabase:', req.params.id)

    res.json({
      message: 'Client deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting client:', error)
    res.status(500).json({ error: 'Failed to delete client' })
  }
})

// POST /api/clients/:id/notes - Add note to client
router.post('/:id/notes', authenticateUser, async (req, res) => {
  try {
    const { message } = req.body
    const clientId = req.params.id

    if (!message || !message.trim()) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'message is required'
      })
    }

    // Verify client exists and belongs to current user
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .select('id')
      .eq('id', clientId)
      .eq('created_by', req.user.id)
      .single()

    if (clientError || !client) {
      return res.status(404).json({
        error: 'Client not found',
        details: 'Client ID does not exist'
      })
    }

    // Create new client note
    const { data: newNote, error } = await supabase
      .from('client_notes')
      .insert({
        client_id: clientId,
        author_id: req.user.id,
        message: message.trim()
      })
      .select(`
        id,
        message,
        created_at,
        author_id
      `)
      .single()

    if (error) {
      console.error('Supabase error creating client note:', error)
      return res.status(500).json({ 
        error: 'Failed to create client note',
        details: error.message 
      })
    }

    // Return the note with author info
    const noteWithAuthor = {
      ...newNote,
      author: {
        id: newNote.author_id,
        full_name: 'Current User', // TODO: get from auth
        role: 'user'
      }
    }

    res.status(201).json({
      message: 'Note added successfully',
      note: noteWithAuthor
    })

  } catch (error) {
    console.error('Error creating client note:', error)
    res.status(500).json({ error: 'Failed to create client note' })
  }
})

export default router 