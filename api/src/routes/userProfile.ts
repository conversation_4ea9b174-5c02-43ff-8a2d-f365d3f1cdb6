// @ts-nocheck
import express from 'express';
import { authenticateUser } from '../middleware/auth';
import { supabase } from '../config/supabase';

const router = express.Router();

// GET /api/user/profile - Get current user's profile
router.get('/profile', authenticateUser, async (req, res) => {
  try {
    const userId = req.user!.id;

    console.log('🔍 API: Fetching profile for user:', userId);

    // Use service role to get user profile (bypasses RLS)
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('❌ API: Profile fetch error:', error);
      return res.status(404).json({ error: 'Profile not found' });
    }

    console.log('✅ API: Profile fetched successfully for:', data.email);
    return res.json({ profile: data });
  } catch (error) {
    console.error('❌ API: Profile fetch server error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/user/profile - Update current user's profile
router.put('/profile', authenticateUser, async (req, res) => {
  try {
    const userId = req.user!.id;
    const updates = req.body;

    console.log('🔍 API: Updating profile for user:', userId);

    // Use service role to update user profile (bypasses RLS)
    const { data, error } = await supabase
      .from('users')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('❌ API: Profile update error:', error);
      return res.status(400).json({ error: error.message });
    }

    console.log('✅ API: Profile updated successfully for:', data.email);
    return res.json({ profile: data });
  } catch (error) {
    console.error('❌ API: Profile update server error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;