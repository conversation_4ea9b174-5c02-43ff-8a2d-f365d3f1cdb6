// @ts-nocheck
import express from 'express';
import webpush from 'web-push';
import { supabase } from '../config/supabase';
import { authenticateUser } from '../middleware/auth';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateUser);

// Configure web-push with VAPID keys
// In production, these should be environment variables
const VAPID_PUBLIC_KEY = process.env.VAPID_PUBLIC_KEY || 'BEl62iUYgUivxIkv69yViEuiBIa40HcCWLrUjHLjdMorxb8NJWd2R-6hLZ-VSXBdVFxmcE0A_aEeHkvm6khs-Aw';
const VAPID_PRIVATE_KEY = process.env.VAPID_PRIVATE_KEY || 'your-vapid-private-key-here';
const VAPID_EMAIL = process.env.VAPID_EMAIL || 'mailto:<EMAIL>';

webpush.setVapidDetails(
  VAPID_EMAIL,
  VAPID_PUBLIC_KEY,
  VAPID_PRIVATE_KEY
);

// POST /api/notifications/subscribe - Subscribe to push notifications
router.post('/subscribe', async (req, res) => {
  try {
    const { subscription, userId } = req.body;
    
    if (!subscription || !userId) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'subscription and userId are required'
      });
    }

    // Validate subscription format
    if (!subscription.endpoint || !subscription.keys) {
      return res.status(400).json({
        error: 'Invalid subscription format',
        details: 'subscription must include endpoint and keys'
      });
    }

    // Store subscription in database
    const { data, error } = await supabase
      .from('push_subscriptions')
      .upsert({
        user_id: userId,
        endpoint: subscription.endpoint,
        p256dh_key: subscription.keys.p256dh,
        auth_key: subscription.keys.auth,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (error) {
      console.error('Error storing push subscription:', error);
      return res.status(500).json({
        error: 'Failed to store subscription',
        details: error.message
      });
    }

    res.json({
      success: true,
      message: 'Successfully subscribed to push notifications'
    });

  } catch (error) {
    console.error('Error in push subscription:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/notifications/unsubscribe - Unsubscribe from push notifications
router.post('/unsubscribe', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        error: 'Missing required field',
        details: 'userId is required'
      });
    }

    // Remove subscription from database
    const { error } = await supabase
      .from('push_subscriptions')
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error('Error removing push subscription:', error);
      return res.status(500).json({
        error: 'Failed to remove subscription',
        details: error.message
      });
    }

    res.json({
      success: true,
      message: 'Successfully unsubscribed from push notifications'
    });

  } catch (error) {
    console.error('Error in push unsubscription:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/notifications/test - Send test push notification
router.post('/test', async (req, res) => {
  try {
    const { userId, title, body, url } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        error: 'Missing required field',
        details: 'userId is required'
      });
    }

    // Get user's push subscription
    const { data: subscription, error } = await supabase
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !subscription) {
      return res.status(404).json({
        error: 'No push subscription found',
        details: 'User is not subscribed to push notifications'
      });
    }

    // Prepare push notification payload
    const payload = JSON.stringify({
      title: title || 'Test Notification',
      body: body || 'This is a test push notification from DeskBelt!',
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      tag: 'test-notification',
      data: {
        url: url || '/dashboard',
        timestamp: Date.now()
      }
    });

    // Reconstruct subscription object for web-push
    const pushSubscription = {
      endpoint: subscription.endpoint,
      keys: {
        p256dh: subscription.p256dh_key,
        auth: subscription.auth_key
      }
    };

    // Send push notification
    await webpush.sendNotification(pushSubscription, payload);

    res.json({
      success: true,
      message: 'Test notification sent successfully'
    });

  } catch (error) {
    console.error('Error sending test notification:', error);
    
    // Handle specific web-push errors
    if (error.statusCode === 410) {
      // Subscription is no longer valid, remove it
      const { userId } = req.body;
      if (userId) {
        await supabase
          .from('push_subscriptions')
          .delete()
          .eq('user_id', userId);
      }
      
      return res.status(410).json({
        error: 'Subscription expired',
        details: 'Push subscription is no longer valid'
      });
    }

    res.status(500).json({
      error: 'Failed to send notification',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/notifications/send - Send push notification to specific user
router.post('/send', async (req, res) => {
  try {
    const { userId, title, body, url, tag, actions } = req.body;
    
    if (!userId || !title || !body) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'userId, title, and body are required'
      });
    }

    // Get user's push subscription
    const { data: subscription, error } = await supabase
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !subscription) {
      return res.status(404).json({
        error: 'No push subscription found',
        details: 'User is not subscribed to push notifications'
      });
    }

    // Prepare push notification payload
    const payload = JSON.stringify({
      title,
      body,
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      tag: tag || 'deskbelt-notification',
      requireInteraction: false,
      actions: actions || [
        {
          action: 'view',
          title: 'View',
          icon: '/action-view.png'
        },
        {
          action: 'dismiss',
          title: 'Dismiss',
          icon: '/action-dismiss.png'
        }
      ],
      data: {
        url: url || '/dashboard',
        timestamp: Date.now()
      }
    });

    // Reconstruct subscription object for web-push
    const pushSubscription = {
      endpoint: subscription.endpoint,
      keys: {
        p256dh: subscription.p256dh_key,
        auth: subscription.auth_key
      }
    };

    // Send push notification
    await webpush.sendNotification(pushSubscription, payload);

    res.json({
      success: true,
      message: 'Push notification sent successfully'
    });

  } catch (error) {
    console.error('Error sending push notification:', error);
    
    // Handle specific web-push errors
    if (error.statusCode === 410) {
      // Subscription is no longer valid, remove it
      const { userId } = req.body;
      if (userId) {
        await supabase
          .from('push_subscriptions')
          .delete()
          .eq('user_id', userId);
      }
      
      return res.status(410).json({
        error: 'Subscription expired',
        details: 'Push subscription is no longer valid'
      });
    }

    res.status(500).json({
      error: 'Failed to send notification',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/notifications/broadcast - Send push notification to all subscribed users
router.post('/broadcast', async (req, res) => {
  try {
    const { title, body, url, tag, actions } = req.body;
    
    if (!title || !body) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'title and body are required'
      });
    }

    // Get all push subscriptions
    const { data: subscriptions, error } = await supabase
      .from('push_subscriptions')
      .select('*');

    if (error) {
      console.error('Error fetching push subscriptions:', error);
      return res.status(500).json({
        error: 'Failed to fetch subscriptions',
        details: error.message
      });
    }

    if (!subscriptions || subscriptions.length === 0) {
      return res.json({
        success: true,
        message: 'No subscriptions found',
        sent: 0
      });
    }

    // Prepare push notification payload
    const payload = JSON.stringify({
      title,
      body,
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      tag: tag || 'deskbelt-broadcast',
      requireInteraction: false,
      actions: actions || [
        {
          action: 'view',
          title: 'View',
          icon: '/action-view.png'
        },
        {
          action: 'dismiss',
          title: 'Dismiss',
          icon: '/action-dismiss.png'
        }
      ],
      data: {
        url: url || '/dashboard',
        timestamp: Date.now()
      }
    });

    // Send notifications to all subscriptions
    const results = await Promise.allSettled(
      subscriptions.map(async (subscription) => {
        const pushSubscription = {
          endpoint: subscription.endpoint,
          keys: {
            p256dh: subscription.p256dh_key,
            auth: subscription.auth_key
          }
        };

        try {
          await webpush.sendNotification(pushSubscription, payload);
          return { success: true, userId: subscription.user_id };
        } catch (error) {
          console.error(`Failed to send to user ${subscription.user_id}:`, error);
          
          // Remove invalid subscriptions
          if (error.statusCode === 410) {
            await supabase
              .from('push_subscriptions')
              .delete()
              .eq('user_id', subscription.user_id);
          }
          
          return { success: false, userId: subscription.user_id, error };
        }
      })
    );

    const successful = results.filter(result => 
      result.status === 'fulfilled' && result.value.success
    ).length;

    const failed = results.length - successful;

    res.json({
      success: true,
      message: 'Broadcast notification completed',
      sent: successful,
      failed,
      total: subscriptions.length
    });

  } catch (error) {
    console.error('Error broadcasting push notification:', error);
    res.status(500).json({
      error: 'Failed to broadcast notification',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;