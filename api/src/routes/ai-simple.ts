import express from 'express'

const router = express.Router()

// Simple test endpoint
router.get('/test-connection', (req, res) => {
  res.json({
    connected: true,
    message: 'AI route is working'
  })
})

router.post('/parse-job', (req, res) => {
  res.json({
    title: 'Test Job',
    description: 'This is a test response',
    category: 'General Building',
    estimatedDuration: 1,
    estimatedValue: 100,
    materials: ['test material'],
    skills: ['test skill'],
    urgency: 'medium'
  })
})

export default router 