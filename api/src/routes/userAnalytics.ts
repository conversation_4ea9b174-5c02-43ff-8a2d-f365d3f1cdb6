import { Router } from 'express';
import { supabase } from '../config/supabase';
import { authenticateUser } from '../middleware/auth';

const router = Router();

// Simple test route
router.get('/analytics/test', (req, res) => {
  res.json({ message: 'User analytics route is working' });
});

// Debug endpoint to test database queries
// @ts-ignore
router.get('/analytics/debug', authenticateUser, async (req, res) => {
  // @ts-ignore
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication required' });
    }

    console.log('🔍 Debug - Testing database queries for user:', userId);

    // @ts-ignore
    const debugResults: any = {
      userId,
      jobs: { count: 0, error: null, sample: null },
      clients: { count: 0, error: null, sample: null },
      invoices: { count: 0, error: null, sample: null },
      quotes: { count: 0, error: null, sample: null },
      contracts: { count: 0, error: null, sample: null }
    };

    // Test jobs table
    try {
      const { data: jobs, error: jobsError } = await supabase
        .from('jobs')
        .select('id, status, created_at')
        .eq('created_by', userId)
        .limit(5);
      
      debugResults.jobs.count = jobs?.length || 0;
      debugResults.jobs.error = jobsError?.message || null;
      debugResults.jobs.sample = jobs?.[0] || null;
    } catch (error) {
      debugResults.jobs.error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Test clients table
    try {
      const { data: clients, error: clientsError } = await supabase
        .from('clients')
        .select('id, created_at')
        .eq('created_by', userId)
        .limit(5);
      
      debugResults.clients.count = clients?.length || 0;
      debugResults.clients.error = clientsError?.message || null;
      debugResults.clients.sample = clients?.[0] || null;
    } catch (error) {
      debugResults.clients.error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Test invoices table
    try {
      const { data: invoices, error: invoicesError } = await supabase
        .from('invoices')
        .select('amount, tax, created_at')
        .eq('created_by', userId)
        .limit(5);
      
      debugResults.invoices.count = invoices?.length || 0;
      debugResults.invoices.error = invoicesError?.message || null;
      debugResults.invoices.sample = invoices?.[0] || null;
    } catch (error) {
      debugResults.invoices.error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Test quotes table
    try {
      const { data: quotes, error: quotesError } = await supabase
        .from('quotes')
        .select('id, created_at')
        .eq('created_by', userId)
        .limit(5);
      
      debugResults.quotes.count = quotes?.length || 0;
      debugResults.quotes.error = quotesError?.message || null;
      debugResults.quotes.sample = quotes?.[0] || null;
    } catch (error) {
      debugResults.quotes.error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Test contracts table
    try {
      const { data: contracts, error: contractsError } = await supabase
        .from('contracts')
        .select('id, created_at')
        .eq('created_by', userId)
        .limit(5);
      
      debugResults.contracts.count = contracts?.length || 0;
      debugResults.contracts.error = contractsError?.message || null;
      debugResults.contracts.sample = contracts?.[0] || null;
    } catch (error) {
      debugResults.contracts.error = error instanceof Error ? error.message : 'Unknown error';
    }

    console.log('🔍 Debug results:', debugResults);
    res.json(debugResults);
  } catch (error) {
    console.error('Debug endpoint error:', error);
    res.status(500).json({ 
      error: 'Debug endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// User analytics endpoint with real database queries and date range support
// @ts-ignore
router.get('/analytics', authenticateUser, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication required' });
    }

    console.log('📊 Analytics request for user:', userId);

    // Parse query parameters
    const { start_date, end_date, period_label, include_comparison } = req.query;
    
    console.log('📅 Query params:', { start_date, end_date, period_label });

    // Default to current month if no dates provided
    const now = new Date();
    const startDate = start_date ? new Date(start_date as string) : new Date(now.getFullYear(), now.getMonth(), 1);
    const endDate = end_date ? new Date(end_date as string) : new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Calculate previous period for comparison
    const periodLength = endDate.getTime() - startDate.getTime();
    const prevStartDate = new Date(startDate.getTime() - periodLength);
    const prevEndDate = new Date(startDate.getTime() - 1);

    console.log('📊 Fetching analytics for period:', {
      current: { start: startDate.toISOString().split('T')[0], end: endDate.toISOString().split('T')[0] },
      previous: { start: prevStartDate.toISOString().split('T')[0], end: prevEndDate.toISOString().split('T')[0] }
    });

    // Fetch current period analytics
    const currentPeriodAnalytics = await fetchPeriodAnalytics(userId, startDate, endDate);
    
    // Fetch previous period analytics for comparison
    const previousPeriodAnalytics = await fetchPeriodAnalytics(userId, prevStartDate, prevEndDate);

    // Calculate change percentages
    const calculateChange = (current: number, previous: number): number => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const response = {
      stats: {
        totalInvoiceValue: currentPeriodAnalytics.totalInvoiceValue,
        totalJobs: currentPeriodAnalytics.totalJobs,
        totalClients: currentPeriodAnalytics.totalClients,
        newClientsThisMonth: currentPeriodAnalytics.newClientsThisMonth,
        newClientsLastMonth: previousPeriodAnalytics.newClientsThisMonth,
        newJobsThisMonth: currentPeriodAnalytics.newJobsThisMonth,
        newJobsLastMonth: previousPeriodAnalytics.newJobsThisMonth,
        invoiceValueChange: calculateChange(currentPeriodAnalytics.totalInvoiceValue, previousPeriodAnalytics.totalInvoiceValue),
        jobsChange: calculateChange(currentPeriodAnalytics.totalJobs, previousPeriodAnalytics.totalJobs),
        clientsChange: calculateChange(currentPeriodAnalytics.totalClients, previousPeriodAnalytics.totalClients)
      },
      monthlyInvoiceValue: currentPeriodAnalytics.monthlyInvoiceValue,
      interactionData: currentPeriodAnalytics.interactionData,
      dateRange: period_label ? {
        startDate: start_date as string || startDate.toISOString().split('T')[0],
        endDate: end_date as string || endDate.toISOString().split('T')[0],
        label: period_label as string
      } : undefined
    };

    console.log('📈 Real analytics response for period:', period_label || 'default');
    res.json(response);
  } catch (error) {
    console.error('User analytics error:', error);
    res.status(500).json({ 
      error: 'Failed to fetch user analytics data',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper function to fetch analytics data for a specific period with optimized queries
async function fetchPeriodAnalytics(userId: string, startDate: Date, endDate: Date) {
  try {
    console.log('🔍 Fetching analytics for user:', userId, 'from', startDate.toISOString().split('T')[0], 'to', endDate.toISOString().split('T')[0]);
    
    const startDateStr = startDate.toISOString();
    const endDateStr = endDate.toISOString();
    
    // Use Promise.allSettled for parallel execution with timeout protection
    const queryPromises = [
      // Jobs query - get ALL jobs for totals, plus status info
      supabase
        .from('jobs')
        .select('id, status, created_at')
        .eq('created_by', userId)
        .limit(1000),
      
      // Clients query - get ALL clients for totals
      supabase
        .from('clients')
        .select('id, created_at')
        .eq('created_by', userId)
        .limit(1000),
      
      // Invoices query - get ALL invoices for totals, filter for period later
      supabase
        .from('invoices')
        .select('amount, tax, created_at')
        .eq('created_by', userId)
        .limit(1000),
      
      // Quotes query - get ALL quotes for totals
      supabase
        .from('quotes')
        .select('id, created_at')
        .eq('created_by', userId)
        .limit(1000),
      
      // Contracts query - get ALL contracts for totals
      supabase
        .from('contracts')
        .select('id, created_at')
        .eq('created_by', userId)
        .limit(1000)
    ];

    // Execute all queries in parallel with timeout protection
    const results = await Promise.allSettled(queryPromises);
    
    // Initialize default values
    let totalJobs = 0;
    let totalClients = 0;
    let totalInvoiceValue = 0;
    let monthlyInvoiceValue: { month: string; value: number }[] = [];
    let interactionData = [
      { type: 'Invoices Sent', count: 0, color: '#3B82F6' },
      { type: 'Quotes Sent', count: 0, color: '#10B981' },
      { type: 'Contracts Sent', count: 0, color: '#8B5CF6' },
      { type: 'Jobs Completed', count: 0, color: '#F59E0B' }
    ];

    // Process jobs result
    if (results[0].status === 'fulfilled' && !results[0].value.error) {
      const allJobs = results[0].value.data || [];
      console.log('🔍 Raw jobs data:', allJobs.length, 'records found');
      
      // Total jobs count (all time)
      totalJobs = allJobs.length;
      
      // Filter jobs for the current period
      const periodJobs = allJobs.filter((job: any) => {
        const jobDate = new Date(job.created_at);
        return jobDate >= startDate && jobDate <= endDate;
      });
      
      // Count completed jobs in the period
      const completedJobs = periodJobs.filter((job: any) => job.status === 'completed').length;
      interactionData[3].count = completedJobs;
      
      console.log('✅ Jobs - Total:', totalJobs, 'Period:', periodJobs.length, 'Completed in period:', completedJobs);
    } else {
      console.warn('Jobs query failed:', results[0].status === 'fulfilled' ? results[0].value.error : 'Promise rejected');
    }

    // Process clients result
    if (results[1].status === 'fulfilled' && !results[1].value.error) {
      const allClients = results[1].value.data || [];
      console.log('🔍 Raw clients data:', allClients.length, 'records found');
      
      // Total clients count (all time)
      totalClients = allClients.length;
      
      // Filter clients for the current period (new clients)
      const periodClients = allClients.filter((client: any) => {
        const clientDate = new Date(client.created_at);
        return clientDate >= startDate && clientDate <= endDate;
      });
      
      console.log('✅ Clients - Total:', totalClients, 'New in period:', periodClients.length);
    } else {
      console.warn('Clients query failed:', results[1].status === 'fulfilled' ? results[1].value.error : 'Promise rejected');
    }

    // Process invoices result
    if (results[2].status === 'fulfilled' && !results[2].value.error) {
      const allInvoices = results[2].value.data || [];
      console.log('🔍 Raw invoices data:', allInvoices.length, 'records found');
      
      // Filter invoices for the current period
      const periodInvoices = allInvoices.filter((invoice: any) => {
        const invoiceDate = new Date(invoice.created_at);
        return invoiceDate >= startDate && invoiceDate <= endDate;
      });
      
      // Calculate total invoice value for the period
      totalInvoiceValue = periodInvoices.reduce((sum: number, invoice: any) => {
        const amount = Number(invoice.amount) || 0;
        const tax = Number(invoice.tax) || 0;
        return sum + amount + tax;
      }, 0);
      
      monthlyInvoiceValue = generateMonthlyData(periodInvoices, startDate, endDate);
      interactionData[0].count = periodInvoices.length;
      
      console.log('✅ Invoices - Total:', allInvoices.length, 'Period:', periodInvoices.length, 'Value:', totalInvoiceValue);
    } else {
      console.warn('Invoices query failed:', results[2].status === 'fulfilled' ? results[2].value.error : 'Promise rejected');
      monthlyInvoiceValue = generateMonthlyData([], startDate, endDate);
    }

    // Process quotes result
    if (results[3].status === 'fulfilled' && !results[3].value.error) {
      const allQuotes = results[3].value.data || [];
      console.log('🔍 Raw quotes data:', allQuotes.length, 'records found');
      
      // Filter quotes for the current period
      const periodQuotes = allQuotes.filter((quote: any) => {
        const quoteDate = new Date(quote.created_at);
        return quoteDate >= startDate && quoteDate <= endDate;
      });
      
      interactionData[1].count = periodQuotes.length;
      console.log('✅ Quotes - Total:', allQuotes.length, 'Period:', periodQuotes.length);
    } else {
      console.warn('Quotes query failed:', results[3].status === 'fulfilled' ? results[3].value.error : 'Promise rejected');
    }

    // Process contracts result
    if (results[4].status === 'fulfilled' && !results[4].value.error) {
      const allContracts = results[4].value.data || [];
      console.log('🔍 Raw contracts data:', allContracts.length, 'records found');
      
      // Filter contracts for the current period
      const periodContracts = allContracts.filter((contract: any) => {
        const contractDate = new Date(contract.created_at);
        return contractDate >= startDate && contractDate <= endDate;
      });
      
      interactionData[2].count = periodContracts.length;
      console.log('✅ Contracts - Total:', allContracts.length, 'Period:', periodContracts.length);
    } else {
      console.warn('Contracts query failed:', results[4].status === 'fulfilled' ? results[4].value.error : 'Promise rejected');
    }

    // Calculate new clients and jobs for this period
    const newClientsThisMonth = results[1].status === 'fulfilled' && !results[1].value.error
      ? (results[1].value.data || []).filter((client: any) => {
          const clientDate = new Date(client.created_at);
          return clientDate >= startDate && clientDate <= endDate;
        }).length
      : 0;

    const newJobsThisMonth = results[0].status === 'fulfilled' && !results[0].value.error
      ? (results[0].value.data || []).filter((job: any) => {
          const jobDate = new Date(job.created_at);
          return jobDate >= startDate && jobDate <= endDate;
        }).length
      : 0;

    console.log('📊 Final analytics:', { 
      totalJobs, 
      totalClients, 
      totalInvoiceValue, 
      newClientsThisMonth, 
      newJobsThisMonth 
    });

    return {
      totalInvoiceValue,
      totalJobs,
      totalClients,
      newClientsThisMonth,
      newJobsThisMonth,
      monthlyInvoiceValue,
      interactionData
    };
  } catch (error) {
    console.error('Error fetching period analytics:', error);
    // Return default values instead of throwing
    return {
      totalInvoiceValue: 0,
      totalJobs: 0,
      totalClients: 0,
      newClientsThisMonth: 0,
      newJobsThisMonth: 0,
      monthlyInvoiceValue: generateMonthlyData([], startDate, endDate),
      interactionData: [
        { type: 'Invoices Sent', count: 0, color: '#3B82F6' },
        { type: 'Quotes Sent', count: 0, color: '#10B981' },
        { type: 'Contracts Sent', count: 0, color: '#8B5CF6' },
        { type: 'Jobs Completed', count: 0, color: '#F59E0B' }
      ]
    };
  }
}

// Optimized helper function to generate monthly/weekly invoice data
function generateMonthlyData(invoices: any[], startDate: Date, endDate: Date) {
  const monthlyData: { month: string; value: number }[] = [];
  
  try {
    // Calculate period length in days
    const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // Use weekly grouping for periods 2 months or shorter
    if (periodDays <= 60) {
      // Weekly grouping for shorter periods
      const current = new Date(startDate);
      let weekNumber = 1;
      
      while (current <= endDate && weekNumber <= 12) { // Safety limit
        const weekEnd = new Date(current);
        weekEnd.setDate(current.getDate() + 6);
        if (weekEnd > endDate) weekEnd.setTime(endDate.getTime());
        
        const weekValue = invoices
          .filter((invoice: any) => {
            const invoiceDate = new Date(invoice.created_at);
            return invoiceDate >= current && invoiceDate <= weekEnd;
          })
          .reduce((sum: number, invoice: any) => sum + (Number(invoice.amount || 0) + Number(invoice.tax || 0)), 0);
        
        monthlyData.push({ month: `Week ${weekNumber}`, value: weekValue });
        
        current.setDate(current.getDate() + 7);
        weekNumber++;
      }
    } else {
      // Monthly grouping for longer periods
      const current = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
      const end = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
      
      while (current <= end) {
        const monthName = current.toLocaleDateString('en-US', { month: 'short' });
        const monthValue = invoices
          .filter((invoice: any) => {
            const invoiceDate = new Date(invoice.created_at);
            return invoiceDate.getFullYear() === current.getFullYear() && 
                   invoiceDate.getMonth() === current.getMonth();
          })
          .reduce((sum: number, invoice: any) => sum + (Number(invoice.amount || 0) + Number(invoice.tax || 0)), 0);
        
        monthlyData.push({ month: monthName, value: monthValue });
        
        current.setMonth(current.getMonth() + 1);
        
        // Safety check to prevent infinite loops
        if (monthlyData.length > 24) break;
      }
    }
    
    // Ensure we have at least one entry
    if (monthlyData.length === 0) {
      monthlyData.push({ month: 'Current', value: 0 });
    }
    
  } catch (error) {
    console.error('Error generating chart data:', error);
    // Fallback data
    monthlyData.push({ month: 'Current', value: 0 });
  }
  
  return monthlyData;
}

export default router;