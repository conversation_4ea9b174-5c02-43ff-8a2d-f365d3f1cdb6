// @ts-nocheck
import express from 'express'
import { supabase } from '../config/supabase'

const router = express.Router()

// Test database connection without RLS
router.get('/db-test', async (req, res) => {
  try {
    // Test 1: Simple count query
    const { count: jobsCount, error: jobsError } = await supabase
      .from('jobs')
      .select('*', { count: 'exact', head: true })

    if (jobsError) {
      console.error('Jobs count error:', jobsError)
      return res.status(500).json({ 
        error: 'Jobs count failed',
        details: jobsError.message 
      })
    }

    // Test 2: Simple client count query
    const { count: clientsCount, error: clientsError } = await supabase
      .from('clients')
      .select('*', { count: 'exact', head: true })

    if (clientsError) {
      console.error('Clients count error:', clientsError)
      return res.status(500).json({ 
        error: 'Clients count failed',
        details: clientsError.message 
      })
    }

    // Test 3: Get first job
    const { data: firstJob, error: firstJobError } = await supabase
      .from('jobs')
      .select('id, title, status')
      .limit(1)
      .single()

    if (firstJobError) {
      console.error('First job error:', firstJobError)
    }

    // Test 4: Get first client
    const { data: firstClient, error: firstClientError } = await supabase
      .from('clients')
      .select('id, name, email')
      .limit(1)
      .single()

    if (firstClientError) {
      console.error('First client error:', firstClientError)
    }

    res.json({
      status: 'success',
      results: {
        jobsCount: jobsCount || 0,
        clientsCount: clientsCount || 0,
        firstJob: firstJob || null,
        firstClient: firstClient || null,
        errors: {
          firstJobError: firstJobError?.message || null,
          firstClientError: firstClientError?.message || null
        }
      }
    })

  } catch (error) {
    console.error('Database test error:', error)
    res.status(500).json({ 
      error: 'Database test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

export default router 