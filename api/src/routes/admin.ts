// @ts-nocheck
import { Router, Request, Response, NextFunction } from 'express'
import { authenticateUser } from '../middleware/auth'
import { supabase } from '../config/supabase'

// Extend Request interface to include user from auth middleware
interface AuthenticatedRequest extends Request {
  user?: { id: string; email: string; role: string }
}

const router = Router()

// Middleware to require admin access
const requireAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    await authenticateUser(req, res, () => {})
    
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    // Check if user has admin role
    const { data: userData, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', req.user.id)
      .single()

    if (error || !userData) {
      return res.status(403).json({ error: 'Access denied: Unable to verify admin status' })
    }

    if (userData.role !== 'admin' && userData.role !== 'super_admin') {
      return res.status(403).json({ error: 'Access denied: Admin privileges required' })
    }

    next()
  } catch (error) {
    console.error('Admin middleware error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}

// Apply admin middleware to all routes
router.use(requireAdmin)

// Dashboard stats endpoint
router.get('/dashboard/stats', async (req, res) => {
  try {
    // Get total users
    const { count: totalUsers, error: usersError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })

    if (usersError) throw usersError

    // Get active users (logged in within last 30 days AND have active status)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const { count: activeUsers, error: activeUsersError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('last_login', thirtyDaysAgo.toISOString())
      .eq('status', 'active')

    if (activeUsersError) throw activeUsersError

    // Get active workforces (≥ 2 members)
    const { data: workforceMembers, error: wfMembersError } = await supabase
      .from('workforce_members')
      .select('workforce_id')

    if (wfMembersError) {
      console.error('Failed to fetch workforce members:', wfMembersError)
    }

    let activeWorkforcesCount = 0
    if (workforceMembers) {
      const memberMap: Record<string, number> = {}
      workforceMembers.forEach((row: { workforce_id: string }) => {
        memberMap[row.workforce_id] = (memberMap[row.workforce_id] || 0) + 1
      })
      activeWorkforcesCount = Object.values(memberMap).filter(c => c > 1).length
    }

    // Lifetime counts = existing rows + deleted audit log entries

    // Helper to get existing count
    const getTableCount = async (table: string) => {
      const { count, error } = await supabase
        .from(table)
        .select('*', { head: true, count: 'exact' })
      if (error) throw error
      return count || 0
    }

    // Helper to get deleted count from audit_logs
    const getDeletedCount = async (action: string) => {
      const { count } = await supabase
        .from('audit_logs')
        .select('*', { head: true, count: 'exact' })
        .eq('action', action)
      return count || 0
    }

    const [
      existingJobs,
      existingClients,
      existingQuotes,
      existingInvoices,
      existingContracts,
      deletedJobs,
      deletedClients,
      deletedQuotes,
      deletedInvoices,
      deletedContracts,
    ] = await Promise.all([
      getTableCount('jobs'),
      getTableCount('clients'),
      getTableCount('quotes'),
      getTableCount('invoices'),
      getTableCount('contracts'),
      getDeletedCount('delete_job'),
      getDeletedCount('delete_client'),
      getDeletedCount('delete_quote'),
      getDeletedCount('delete_invoice'),
      getDeletedCount('delete_contract'),
    ])

    const totalJobs = existingJobs + deletedJobs
    const totalClients = existingClients + deletedClients
    const totalQuotes = existingQuotes + deletedQuotes
    const totalInvoices = existingInvoices + deletedInvoices
    const totalContracts = existingContracts + deletedContracts

    // Calculate growth percentages (simplified - in real app, compare with previous month)
    const userGrowth = 12.5 // Mock growth percentage
    const teamGrowth = 8.3
    const jobGrowth = 15.2

    const stats = {
      totalUsers: totalUsers || 0,
      activeUsers: activeUsers || 0,
      totalTeams: activeWorkforcesCount,
      totalJobs,
      totalClients,
      totalQuotes,
      totalInvoices,
      totalContracts,
      userGrowth,
      teamGrowth,
      jobGrowth,
    }

    res.json(stats)
  } catch (error) {
    console.error('Dashboard stats error:', error)
    res.status(500).json({ error: 'Failed to fetch dashboard statistics' })
  }
})

// Get all users with pagination and search
router.get('/users', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1
    const limit = parseInt(req.query.limit as string) || 10
    const search = req.query.search as string || ''
    const role = req.query.role as string
    
    const offset = (page - 1) * limit

    let query = supabase
      .from('users')
      .select(`
        id,
        email,
        full_name,
        role,
        status,
        created_at,
        last_login
      `)

    // Add search filter
    if (search) {
      query = query.or(`email.ilike.%${search}%,full_name.ilike.%${search}%`)
    }

    // Add role filter
    if (role && role !== 'all') {
      query = query.eq('role', role)
    }

    // Get total count for pagination
    const { count: totalCount } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })

    // Get paginated results
    const { data: users, error } = await query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false })

    if (error) throw error

    const totalPages = Math.ceil((totalCount || 0) / limit)

    res.json({
      users: users || [],
      totalCount: totalCount || 0,
      currentPage: page,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    })
  } catch (error) {
    console.error('Users fetch error:', error)
    res.status(500).json({ error: 'Failed to fetch users' })
  }
})

// Update user role
router.put('/users/:userId/role', async (req, res) => {
  try {
    const { userId } = req.params
    const { role } = req.body

    // Validate role
    const validRoles = ['tradesperson', 'team_member', 'admin', 'super_admin']
    if (!validRoles.includes(role)) {
      return res.status(400).json({ error: 'Invalid role specified' })
    }

    // Check if trying to modify super_admin (only super_admin can do this)
    const { data: currentUser } = await supabase
      .from('users')
      .select('role')
      .eq('id', req.user.id)
      .single()

    const { data: targetUser } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (targetUser?.role === 'super_admin' && currentUser?.role !== 'super_admin') {
      return res.status(403).json({ error: 'Only super admins can modify super admin accounts' })
    }

    if (role === 'super_admin' && currentUser?.role !== 'super_admin') {
      return res.status(403).json({ error: 'Only super admins can grant super admin role' })
    }

    // Update user role
    const { data, error } = await supabase
      .from('users')
      .update({ role, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error

    res.json({ 
      message: 'User role updated successfully',
      user: data 
    })
  } catch (error) {
    console.error('User role update error:', error)
    res.status(500).json({ error: 'Failed to update user role' })
  }
})

// Toggle user status (enable/disable)
router.put('/users/:userId/status', async (req, res) => {
  try {
    const { userId } = req.params
    const { status } = req.body

    // Validate status
    if (!['active', 'paused', 'suspended', 'disabled'].includes(status)) {
      return res.status(400).json({ error: 'Invalid status specified' })
    }

    // Check if trying to modify super_admin (only super_admin can do this)
    const { data: currentUser } = await supabase
      .from('users')
      .select('role')
      .eq('id', req.user.id)
      .single()

    const { data: targetUser } = await supabase
      .from('users')
      .select('role, status')
      .eq('id', userId)
      .single()

    if (targetUser?.role === 'super_admin' && currentUser?.role !== 'super_admin') {
      return res.status(403).json({ error: 'Only super admins can modify super admin accounts' })
    }

    // Update user status
    const { data, error } = await supabase
      .from('users')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error

    res.json({ 
      message: `User status updated to ${status} successfully`,
      user: data
    })
  } catch (error) {
    console.error('User status update error:', error)
    res.status(500).json({ error: 'Failed to update user status' })
  }
})

// Get user details
router.get('/users/:userId', async (req, res) => {
  try {
    const { userId } = req.params

    const { data: user, error } = await supabase
      .from('users')
      .select(`
        id,
        email,
        full_name,
        phone,
        company_name,
        address,
        website,
        country,
        role,
        status,
        created_at,
        updated_at,
        last_login
      `)
      .eq('id', userId)
      .single()

    if (error) throw error

    if (!user) {
      return res.status(404).json({ error: 'User not found' })
    }

    // Workforce(s) owned by the user
    const { count: workforceCount } = await supabase
      .from('workforce')
      .select('*', { count: 'exact', head: true })
      .eq('owner_id', userId)

    // Jobs created by the user (all & archived)
    const { count: jobsCount } = await supabase
      .from('jobs')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', userId)

    const { count: archivedJobsCount } = await supabase
      .from('jobs')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', userId)
      .eq('status', 'archived')

    // Clients created by the user
    const { count: clientsCount } = await supabase
      .from('clients')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', userId)

    // Quotes / Invoices / Contracts created by the user
    const { count: quotesCount } = await supabase
      .from('quotes')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', userId)

    const { count: invoicesCount } = await supabase
      .from('invoices')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', userId)

    const { count: contractsCount } = await supabase
      .from('contracts')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', userId)

    // Fetch limits (if any)
    const { data: limitsRow } = await supabase
      .from('user_limits')
      .select('jobs_per_week, jobs_per_month')
      .eq('user_id', userId)
      .single()

    const userWithStats = {
      ...user,
      stats: {
        workforceCount: workforceCount || 0,
        totalJobs: jobsCount || 0,
        archivedJobs: archivedJobsCount || 0,
        totalClients: clientsCount || 0,
        totalQuotes: quotesCount || 0,
        totalInvoices: invoicesCount || 0,
        totalContracts: contractsCount || 0,
      },
      limits: {
        jobsPerWeek: limitsRow?.jobs_per_week ?? null,
        jobsPerMonth: limitsRow?.jobs_per_month ?? null,
      }
    }

    res.json(userWithStats)
  } catch (error) {
    console.error('User details fetch error:', error)
    res.status(500).json({ error: 'Failed to fetch user details' })
  }
})

// Get system alerts
router.get('/alerts', async (req, res) => {
  try {
    // In a real implementation, this would fetch from a system_alerts table
    const mockAlerts = [
      {
        id: '1',
        type: 'warning',
        title: 'High API Usage',
        message: 'AI API usage is at 85% of monthly limit',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        severity: 'medium',
      },
      {
        id: '2',
        type: 'info',
        title: 'Database Backup',
        message: 'Daily backup completed successfully',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        severity: 'low',
      },
      {
        id: '3',
        type: 'error',
        title: 'Failed Email Delivery',
        message: '2 notification emails failed to send',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        severity: 'high',
      },
    ]

    res.json(mockAlerts)
  } catch (error) {
    console.error('Alerts fetch error:', error)
    res.status(500).json({ error: 'Failed to fetch system alerts' })
  }
})

// Get audit logs
router.get('/audit-logs', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1
    const limit = parseInt(req.query.limit as string) || 20
    const offset = (page - 1) * limit

    const { data: logs, error } = await supabase
      .from('audit_logs')
      .select(`
        id,
        timestamp,
        action,
        target_type,
        target_id,
        details,
        ip_address,
        user_agent,
        actor_id,
        users!audit_logs_actor_id_fkey(email, full_name)
      `)
      .order('timestamp', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error

    const { count: totalCount } = await supabase
      .from('audit_logs')
      .select('*', { count: 'exact', head: true })

    const totalPages = Math.ceil((totalCount || 0) / limit)

    res.json({
      logs: logs || [],
      totalCount: totalCount || 0,
      currentPage: page,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    })
  } catch (error) {
    console.error('Audit logs fetch error:', error)
    res.status(500).json({ error: 'Failed to fetch audit logs' })
  }
})

// Update job limits
router.put('/users/:userId/limits', async (req, res) => {
  try {
    const { userId } = req.params
    const { jobsPerWeek, jobsPerMonth } = req.body

    // Basic validation (allow null for unlimited)
    const week = jobsPerWeek === null ? null : parseInt(jobsPerWeek)
    const month = jobsPerMonth === null ? null : parseInt(jobsPerMonth)

    if ((week && week <= 0) || (month && month <= 0)) {
      return res.status(400).json({ error: 'Limits must be positive integers or null' })
    }

    // Upsert record
    const { error } = await supabase
      .from('user_limits')
      .upsert({
        user_id: userId,
        jobs_per_week: week || null,
        jobs_per_month: month || null,
      }, { onConflict: 'user_id' })

    if (error) throw error

    res.json({ message: 'User limits updated successfully' })
  } catch (error) {
    console.error('Update user limits error:', error)
    res.status(500).json({ error: 'Failed to update user limits' })
  }
})

// Reset password (send recovery email via Supabase)
router.post('/users/:userId/reset-password', async (req, res) => {
  try {
    const { userId } = req.params

    const { data: user } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single()

    if (!user) return res.status(404).json({ error: 'User not found' })

    // Trigger password reset email
    const { data: resetData, error } = await supabase.auth.resetPasswordForEmail(
      user.email,
      { redirectTo: process.env.FRONTEND_RESET_REDIRECT || 'http://localhost:3000/forgot-password' }
    )

    if (error) {
      console.error('Supabase reset error:', error)
      throw error
    }

    res.json({ message: 'Password reset email sent' })
  } catch (error) {
    console.error('Reset password error:', error)
    res.status(500).json({ error: 'Failed to send reset email' })
  }
})

// Update AI chat limits
router.put('/users/:userId/ai-limits', async (req, res) => {
  try {
    const { userId } = req.params
    const { aiLimits } = req.body // Expecting { hour: number|null, day: number|null, week: number|null, month: number|null }

    if (!aiLimits || typeof aiLimits !== 'object') {
      return res.status(400).json({ error: 'Invalid aiLimits payload' })
    }

    // Sanitize & validate values (positive integers or null)
    const cleanLimits: Record<string, number | null> = {}
    const allowedKeys = ['hour', 'day', 'week', 'month']
    for (const key of allowedKeys) {
      const val = aiLimits[key]
      if (val === null || val === undefined) {
        // Skip - unlimited
        continue
      }
      const numVal = parseInt(val)
      if (isNaN(numVal) || numVal <= 0) {
        return res.status(400).json({ error: `Invalid value for ${key}` })
      }
      cleanLimits[key] = numVal
    }

    // Upsert limits JSONB
    const { error } = await supabase
      .from('user_limits')
      .upsert({
        user_id: userId,
        ai_limits: cleanLimits,
      }, { onConflict: 'user_id' })

    if (error) throw error

    // Insert audit log
    await supabase.from('audit_logs').insert({
      action: 'update_ai_limits',
      target_type: 'user',
      target_id: userId,
      details: JSON.stringify(cleanLimits),
      actor_id: (req as any).user.id,
      ip_address: req.ip,
    })

    res.json({ message: 'AI limits updated successfully' })
  } catch (error) {
    console.error('Update AI limits error:', error)
    res.status(500).json({ error: 'Failed to update AI limits' })
  }
})

// Get system default ai limits
router.get('/defaults/:key', async (req, res) => {
  try {
    const { key } = req.params
    const { data, error } = await supabase
      .from('system_defaults')
      .select('value')
      .eq('key', key)
      .single()
    if (error || !data) {
      return res.status(404).json({ error: 'Default not found' })
    }
    res.json(data.value)
  } catch (err) {
    console.error('defaults fetch error', err)
    res.status(500).json({ error: 'Failed to fetch defaults' })
  }
})

// Get user AI usage stats
router.get('/users/:userId/ai-usage', async (req, res) => {
  try {
    const { userId } = req.params
    const now = new Date()
    
    // Calculate time boundaries
    const hourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)

    // Query usage counts
    const [hourlyResult, dailyResult, weeklyResult, monthlyResult] = await Promise.all([
      supabase
        .from('ai_usage_logs')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('request_timestamp', hourAgo.toISOString()),
      supabase
        .from('ai_usage_logs')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('request_timestamp', dayAgo.toISOString()),
      supabase
        .from('ai_usage_logs')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('request_timestamp', weekAgo.toISOString()),
      supabase
        .from('ai_usage_logs')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('request_timestamp', monthStart.toISOString()),
    ])

    res.json({
      hourly: hourlyResult.count || 0,
      daily: dailyResult.count || 0,
      weekly: weeklyResult.count || 0,
      monthly: monthlyResult.count || 0,
    })
  } catch (err) {
    console.error('AI usage stats error:', err)
    res.status(500).json({ error: 'Failed to fetch AI usage stats' })
  }
})

export default router 