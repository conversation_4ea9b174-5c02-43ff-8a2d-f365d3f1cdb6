// @ts-nocheck
import { Router, Request, Response } from 'express';
import { authenticateUser } from '../middleware/auth';
import { supabase } from '../config/supabase';

const router = Router();

// Apply authentication middleware to all notification routes
router.use(authenticateUser);


// GET /api/notifications - Get user's notifications with optional filters
router.get('/', async (req: Request, res: Response) => {
  try {
    const { user } = req;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    console.log(
        "USER:",
        user
    )

    // Check if user has any notifications, if not, create welcome notification
    const { count: notificationCount } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    if (notificationCount === 0) {
      // Create welcome notification for new users
      await supabase
        .from('notifications')
        .insert({
          user_id: user.id,
          type: 'info',
          title: 'Welcome to DeskBelt',
          message: 'Welcome to DeskBelt! Your workspace is set up and ready to go. Start by creating your first job or client.',
          action_url: '/dashboard',
          icon: 'information-circle',
          is_read: false
        });
    }

    const { 
      is_read, 
      type, 
      limit = '20', 
      offset = '0',
      include_expired = 'false'
    } = req.query;

    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    // Apply filters
    if (is_read !== undefined) {
      query = query.eq('is_read', is_read === 'true');
    }

    if (type) {
      query = query.eq('type', type);
    }

    // Filter out expired notifications unless specifically requested
    if (include_expired === 'false') {
      query = query.or('expires_at.is.null,expires_at.gt.now()');
    }

    // Apply pagination
    const limitNum = parseInt(limit as string);
    const offsetNum = parseInt(offset as string);
    query = query.range(offsetNum, offsetNum + limitNum - 1);

    const { data: notifications, error } = await query;

    if (error) {
      console.error('Error fetching notifications:', error);
      return res.status(500).json({ error: 'Failed to fetch notifications' });
    }

    res.json(notifications || []);
  } catch (error) {
    console.error('Notifications fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/notifications/stats - Get notification statistics
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const { user } = req;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Get total and unread count
    const { data: totalData, error: totalError } = await supabase
      .from('notifications')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id)
      .or('expires_at.is.null,expires_at.gt.now()');

    const { data: unreadData, error: unreadError } = await supabase
      .from('notifications')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id)
      .eq('is_read', false)
      .or('expires_at.is.null,expires_at.gt.now()');

    if (totalError || unreadError) {
      console.error('Error fetching notification stats:', totalError || unreadError);
      return res.status(500).json({ error: 'Failed to fetch notification stats' });
    }

    // Get breakdown by type
    const { data: typeData, error: typeError } = await supabase
      .from('notifications')
      .select('type')
      .eq('user_id', user.id)
      .or('expires_at.is.null,expires_at.gt.now()');

    if (typeError) {
      console.error('Error fetching notification types:', typeError);
      return res.status(500).json({ error: 'Failed to fetch notification types' });
    }

    const byType = (typeData || []).reduce((acc: Record<string, number>, notification: any) => {
      acc[notification.type] = (acc[notification.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const stats = {
      total: totalData?.length || 0,
      unread: unreadData?.length || 0,
      byType
    };

    res.json(stats);
  } catch (error) {
    console.error('Notification stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/notifications - Create new notification (system use)
router.post('/', async (req: Request, res: Response) => {
  try {
    const { user } = req;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { type, title, message, action_url, icon, expires_at } = req.body;

    if (!type || !title || !message) {
      return res.status(400).json({ error: 'Type, title, and message are required' });
    }

    const notificationData = {
      user_id: user.id,
      type,
      title,
      message,
      action_url,
      icon,
      expires_at
    };

    const { data: notification, error } = await supabase
      .from('notifications')
      .insert(notificationData)
      .select()
      .single();

    if (error) {
      console.error('Error creating notification:', error);
      return res.status(500).json({ error: 'Failed to create notification' });
    }

    res.status(201).json(notification);
  } catch (error) {
    console.error('Notification creation error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PATCH /api/notifications/:id - Update notification (mainly for marking as read)
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    const { user } = req;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { id } = req.params;
    const { is_read } = req.body;

    const { data: notification, error } = await supabase
      .from('notifications')
      .update({ is_read, updated_at: new Date().toISOString() })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating notification:', error);
      return res.status(500).json({ error: 'Failed to update notification' });
    }

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    res.json(notification);
  } catch (error) {
    console.error('Notification update error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/notifications/mark-all-read - Mark all notifications as read
router.post('/mark-all-read', async (req: Request, res: Response) => {
  try {
    const { user } = req;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { data: notifications, error } = await supabase
      .from('notifications')
      .update({ is_read: true, updated_at: new Date().toISOString() })
      .eq('user_id', user.id)
      .eq('is_read', false)
      .select();

    if (error) {
      console.error('Error marking all notifications as read:', error);
      return res.status(500).json({ error: 'Failed to mark notifications as read' });
    }

    res.json({ message: 'All notifications marked as read', count: notifications?.length || 0 });
  } catch (error) {
    console.error('Mark all read error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/notifications/:id - Delete notification
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { user } = req;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { id } = req.params;

    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error deleting notification:', error);
      return res.status(500).json({ error: 'Failed to delete notification' });
    }

    res.json({ message: 'Notification deleted successfully' });
  } catch (error) {
    console.error('Notification deletion error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/notifications/preferences - Get user notification preferences
router.get('/preferences', async (req: Request, res: Response) => {
  try {
    const { user } = req;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { data: preferences, error } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') { // Not found is OK
      console.error('Error fetching notification preferences:', error);
      return res.status(500).json({ error: 'Failed to fetch notification preferences' });
    }

    // If no preferences exist, create default ones
    if (!preferences) {
      const defaultPreferences = {
        user_id: user.id,
        email_notifications: true,
        push_notifications: true,
        in_app_notifications: true,
        job_updates: true,
        team_updates: true,
        payment_updates: true,
        marketing_emails: false,
        quiet_hours_start: '22:00',
        quiet_hours_end: '08:00',
        timezone: 'Europe/London'
      };

      const { data: newPreferences, error: createError } = await supabase
        .from('notification_preferences')
        .insert(defaultPreferences)
        .select()
        .single();

      if (createError) {
        console.error('Error creating notification preferences:', createError);
        return res.status(500).json({ error: 'Failed to create notification preferences' });
      }

      return res.json(newPreferences);
    }

    res.json(preferences);
  } catch (error) {
    console.error('Notification preferences fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/notifications/preferences - Update user notification preferences
router.put('/preferences', async (req: Request, res: Response) => {
  try {
    const { user } = req;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const preferences = req.body;

    const { data: updatedPreferences, error } = await supabase
      .from('notification_preferences')
      .upsert({
        user_id: user.id,
        ...preferences,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating notification preferences:', error);
      return res.status(500).json({ error: 'Failed to update notification preferences' });
    }

    res.json(updatedPreferences);
  } catch (error) {
    console.error('Notification preferences update error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router; 