// @ts-nocheck
import express from 'express'
import { body, validationResult } from 'express-validator'
import { authenticateUser } from '../middleware/auth'
import { reviewRequestEmailService } from '../services/reviewRequestEmailService'
import { supabase } from '../config/supabase'

const router = express.Router();

// Rate limiting: 20 review requests per hour per user
const reviewRequestAttempts = new Map();

const checkRateLimit = (userId: string): boolean => {
  const now = Date.now();
  const userAttempts = reviewRequestAttempts.get(userId) || [];
  
  // Remove attempts older than 1 hour
  const validAttempts = userAttempts.filter((time: number) => now - time < 60 * 60 * 1000);
  
  if (validAttempts.length >= 20) {
    return false; // Rate limit exceeded
  }
  
  validAttempts.push(now);
  reviewRequestAttempts.set(userId, validAttempts);
  return true;
};

// Validation rules
const reviewRequestValidation = [
  body('clientId').isUUID().withMessage('Valid client ID required'),
  body('clientEmail').isEmail().withMessage('Valid email address required'),
  body('clientName').isLength({ min: 1, max: 100 }).withMessage('Client name required (1-100 characters)'),
  body('message').isLength({ min: 10, max: 2000 }).withMessage('Message must be 10-2000 characters'),
  body('tone').isIn(['friendly', 'professional', 'casual', 'follow_up']).withMessage('Invalid tone. Must be: friendly, professional, casual, or follow_up'),
  body('businessName').optional().isLength({ max: 100 }).withMessage('Business name too long (max 100 characters)'),
  body('senderName').optional().isLength({ max: 100 }).withMessage('Sender name too long (max 100 characters)')
];

// Send review request email
router.post('/send', 
  authenticateUser,
  reviewRequestValidation,
  async (req: any, res: any): Promise<void> => {
    try {
      console.log('📬 Review request endpoint called');
      console.log('👤 User ID:', req.user.id);
      
      // Validate input
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      // Check rate limit
      if (!checkRateLimit(req.user.id)) {
        res.status(429).json({
          success: false,
          message: 'Rate limit exceeded. You can send up to 20 review requests per hour.',
          retryAfter: 3600
        });
        return;
      }

      const {
        clientId,
        clientEmail,
        clientName,
        message,
        tone,
        businessName,
        senderName
      } = req.body;

      console.log('📧 Sending review request to:', clientEmail);

      // Verify client belongs to authenticated user
      const { data: client, error: clientError } = await supabase
        .from('clients')
        .select('id, name, email, created_by')
        .eq('id', clientId)
        .eq('created_by', req.user.id)
        .single();

      if (clientError || !client) {
        console.error('❌ Client verification failed:');
        console.error('   Error:', clientError);
        console.error('   Client data:', client);
        
        // Let's also check if the client exists at all (for debugging)
        const { data: clientExists, error: existsError } = await supabase
          .from('clients')
          .select('id, name, email, created_by')
          .eq('id', clientId)
          .single();
        
        console.error('   Client exists check:', clientExists);
        console.error('   Exists error:', existsError);
        
        res.status(403).json({
          success: false,
          message: 'Client not found or access denied'
        });
        return;
      }

      // Verify the email matches the client record
      if (client.email !== clientEmail) {
        res.status(400).json({
          success: false,
          message: 'Email address does not match client record'
        });
        return;
      }

      console.log('✅ Client verified:', client.name);

      // Send review request email
      const result = await reviewRequestEmailService.sendReviewRequest({
        clientId,
        clientEmail,
        clientName,
        message,
        tone,
        businessName: businessName || req.user?.businessName || 'DeskBelt',
        senderName: senderName || req.user?.name || 'The Team',
        userId: req.user.id
      });

      if (result.success) {
        console.log('📧 Review request sent successfully');

        // Log the email activity to database
        try {
          const { error: logError } = await supabase
            .from('notes')
            .insert({
              client_id: clientId,
              user_id: req.user.id,
              content: `📬 Review request sent via email on ${new Date().toLocaleString('en-GB', {
                day: '2-digit',
                month: 'short', 
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              })}`,
              type: 'system'
            });

          if (logError) {
            console.error('⚠️ Failed to log review request:', logError);
          } else {
            console.log('📝 Review request logged to client notes');
          }
        } catch (logError) {
          console.error('⚠️ Error logging review request:', logError);
        }

        res.json({
          success: true,
          emailId: result.emailId,
          message: 'Review request sent successfully',
          timestamp: new Date().toISOString(),
          client: {
            id: client.id,
            name: client.name,
            email: client.email
          }
        });
      } else {
        console.error('❌ Review request failed:', result.error);
        res.status(500).json({
          success: false,
          message: result.message,
          error: result.error
        });
      }

    } catch (error) {
      console.error('💥 Review request endpoint error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

// Test review request connection
router.get('/test-connection', authenticateUser, async (req, res) => {
  try {
    console.log('🔍 Testing review request email connection...');
    
    const isConnected = await reviewRequestEmailService.verifyConnection();
    
    res.json({
      success: isConnected,
      message: isConnected ? 'Review request email service is operational' : 'Review request email service connection failed',
      service: 'Gmail SMTP',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Review request connection test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get review request statistics for user
router.get('/stats', authenticateUser, async (req, res) => {
  try {
    // Get review request count from client notes (system notes with review request content)
    const { data: reviewRequests, error } = await supabase
      .from('notes')
      .select('id, created_at, client_id')
      .eq('user_id', req.user?.id)
      .eq('type', 'system')
      .like('content', '%Review request sent%')
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Calculate stats
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const todayCount = reviewRequests?.filter(req => 
      new Date(req.created_at) >= today
    ).length || 0;

    const monthCount = reviewRequests?.filter(req => 
      new Date(req.created_at) >= thisMonth
    ).length || 0;

    const totalCount = reviewRequests?.length || 0;

    res.json({
      success: true,
      stats: {
        today: todayCount,
        thisMonth: monthCount,
        total: totalCount,
        dailyLimit: 20, // 20 per hour * 24 hours = 480, but we'll show 20 as practical limit
        monthlyLimit: 500 // Gmail limit
      },
      recentRequests: reviewRequests?.slice(0, 5).map(req => ({
        id: req.id,
        clientId: req.client_id,
        sentAt: req.created_at
      })) || []
    });

  } catch (error) {
    console.error('❌ Review request stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get review request statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;