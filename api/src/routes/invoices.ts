// @ts-nocheck
import express from 'express'
import { supabase } from '../config/supabase'
import { authenticateUser } from '../middleware/auth'

const router = express.Router()

// GET /api/invoices - List all invoices for the authenticated user
router.get('/', authenticateUser, async (req, res) => {
  try {
    const { search, status, limit = 20, offset = 0, include_relations } = req.query

    console.log('📋 Fetching invoices with params:', { search, status, limit, offset, include_relations })

    // Build the select query with optional relations
    let selectQuery = `
      id,
      job_id,
      amount,
      tax,
      details,
      line_items,
      due_date,
      status,
      created_at,
      updated_at,
      created_by
    `

    if (include_relations === 'true') {
      selectQuery += `,
        jobs!inner(
          id,
          title,
          client_id,
          clients(
            id,
            name,
            email,
            phone
          )
        )
      `
    }

    // Base query with user filtering through jobs relationship
    let query = supabase
      .from('invoices')
      .select(selectQuery, { count: 'exact' })

    // Filter by user through jobs relationship
    if (include_relations === 'true') {
      query = query.eq('jobs.created_by', req.user.id)
    } else {
      // If not including relations, we need to filter through a separate jobs query
      const { data: userJobs } = await supabase
        .from('jobs')
        .select('id')
        .eq('created_by', req.user.id)
      
      const jobIds = userJobs?.map(job => job.id) || []
      if (jobIds.length === 0) {
        return res.json({ invoices: [], stats: { totalRevenue: 0, pendingAmount: 0, overdueAmount: 0, draftCount: 0 } })
      }
      query = query.in('job_id', jobIds)
    }

    // Filter by search term
    if (search) {
      const searchTerm = search as string
      if (include_relations === 'true') {
        query = query.or(`details.ilike.%${searchTerm}%,jobs.title.ilike.%${searchTerm}%,jobs.clients.name.ilike.%${searchTerm}%`)
      } else {
        query = query.ilike('details', `%${searchTerm}%`)
      }
    }

    // Filter by status
    if (status) {
      const statusFilters = Array.isArray(status) ? status : [status]
      query = query.in('status', statusFilters)
    }

    // Add pagination and ordering
    query = query
      .order('created_at', { ascending: false })
      .range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1)

    const { data: invoices, error, count } = await query

    if (error) {
      console.error('Supabase error fetching invoices:', error)
      return res.status(500).json({ error: 'Failed to fetch invoices from database' })
    }

    // Calculate stats for all invoices (not just the paginated results)
    const statsQuery = supabase
      .from('invoices')
      .select('amount, status')

    // Filter stats by user through jobs relationship
    const { data: userJobs } = await supabase
      .from('jobs')
      .select('id')
      .eq('created_by', req.user.id)
    
    const jobIds = userJobs?.map(job => job.id) || []
    const { data: allInvoices } = await statsQuery.in('job_id', jobIds)

    const stats = {
      totalRevenue: 0,
      pendingAmount: 0,
      overdueAmount: 0,
      draftCount: 0
    }

    if (allInvoices) {
      allInvoices.forEach(invoice => {
        const amount = parseFloat(invoice.amount) || 0
        switch (invoice.status) {
          case 'paid':
            stats.totalRevenue += amount
            break
          case 'sent':
            stats.pendingAmount += amount
            break
          case 'overdue':
            stats.overdueAmount += amount
            break
          case 'draft':
            stats.draftCount += 1
            break
        }
      })
    }

    console.log('📋 Returning invoices:', {
      count: invoices?.length || 0,
      total: count,
      stats
    })

    res.json({
      invoices: invoices || [],
      stats,
      pagination: {
        total: count,
        offset: parseInt(offset as string),
        limit: parseInt(limit as string),
        hasMore: count ? (parseInt(offset as string) + parseInt(limit as string)) < count : false
      }
    })

  } catch (error) {
    console.error('Error fetching invoices:', error)
    res.status(500).json({ error: 'Failed to fetch invoices' })
  }
})

// POST /api/invoices - Create a new standalone invoice
router.post('/', authenticateUser, async (req, res) => {
  try {
    const { job_id, amount, tax, details, line_items, due_date, status = 'draft' } = req.body

    console.log('📝 Creating new invoice:', {
      job_id,
      amount,
      tax,
      status
    })

    // Verify job exists and belongs to user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', job_id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({ error: 'Job not found or access denied' })
    }

    // Create new invoice
    const { data: newInvoice, error } = await supabase
      .from('invoices')
      .insert({
        job_id,
        amount: parseFloat(amount),
        tax: parseFloat(tax) || 0,
        details,
        line_items,
        due_date,
        status,
        created_by: req.user.id
      })
      .select()
      .single()

    if (error) {
      console.error('Supabase error creating invoice:', error)
      return res.status(500).json({ error: 'Failed to create invoice' })
    }

    console.log('✅ Invoice created successfully:', newInvoice.id)
    res.status(201).json(newInvoice)

  } catch (error) {
    console.error('Error creating invoice:', error)
    res.status(500).json({ error: 'Failed to create invoice' })
  }
})

// GET /api/invoices/:id - Get specific invoice
router.get('/:id', authenticateUser, async (req, res) => {
  try {
    const { data: invoice, error } = await supabase
      .from('invoices')
      .select(`
        *,
        jobs!inner(
          id,
          title,
          client_id,
          created_by,
          clients(
            id,
            name,
            email,
            phone
          )
        )
      `)
      .eq('id', req.params.id)
      .eq('jobs.created_by', req.user.id)
      .single()

    if (error || !invoice) {
      return res.status(404).json({ error: 'Invoice not found' })
    }

    res.json(invoice)

  } catch (error) {
    console.error('Error fetching invoice:', error)
    res.status(500).json({ error: 'Failed to fetch invoice' })
  }
})

// PUT /api/invoices/:id - Update invoice
router.put('/:id', authenticateUser, async (req, res) => {
  try {
    const { amount, tax, details, line_items, due_date, status } = req.body

    // Verify invoice exists and user has access through job ownership
    const { data: existingInvoice, error: fetchError } = await supabase
      .from('invoices')
      .select(`
        id,
        job_id,
        jobs!inner(
          created_by
        )
      `)
      .eq('id', req.params.id)
      .eq('jobs.created_by', req.user.id)
      .single()

    if (fetchError || !existingInvoice) {
      return res.status(404).json({ error: 'Invoice not found' })
    }

    // Update invoice
    const { data: updatedInvoice, error } = await supabase
      .from('invoices')
      .update({
        amount: amount ? parseFloat(amount) : undefined,
        tax: tax !== undefined ? parseFloat(tax) : undefined,
        details,
        line_items,
        due_date,
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', req.params.id)
      .select()
      .single()

    if (error) {
      console.error('Supabase error updating invoice:', error)
      return res.status(500).json({ error: 'Failed to update invoice' })
    }

    console.log('✅ Invoice updated successfully:', updatedInvoice.id)
    res.json(updatedInvoice)

  } catch (error) {
    console.error('Error updating invoice:', error)
    res.status(500).json({ error: 'Failed to update invoice' })
  }
})

// DELETE /api/invoices/:id - Delete invoice
router.delete('/:id', authenticateUser, async (req, res) => {
  try {
    // Verify invoice exists and user has access through job ownership
    const { data: existingInvoice, error: fetchError } = await supabase
      .from('invoices')
      .select(`
        id,
        job_id,
        jobs!inner(
          created_by
        )
      `)
      .eq('id', req.params.id)
      .eq('jobs.created_by', req.user.id)
      .single()

    if (fetchError || !existingInvoice) {
      return res.status(404).json({ error: 'Invoice not found' })
    }

    // Delete invoice
    const { error } = await supabase
      .from('invoices')
      .delete()
      .eq('id', req.params.id)

    if (error) {
      console.error('Supabase error deleting invoice:', error)
      return res.status(500).json({ error: 'Failed to delete invoice' })
    }

    console.log('✅ Invoice deleted successfully:', req.params.id)
    res.json({ message: 'Invoice deleted successfully' })

  } catch (error) {
    console.error('Error deleting invoice:', error)
    res.status(500).json({ error: 'Failed to delete invoice' })
  }
})

export default router