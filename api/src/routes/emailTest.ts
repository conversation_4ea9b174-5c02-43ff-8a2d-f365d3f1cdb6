// @ts-nocheck
import express from 'express';
const router = express.Router();

// Test email sending functionality
router.get('/test-resend-email', async (req, res) => {
  try {
    console.log('🧪 Testing email service for resend functionality...');
    
    // Import the email service
    const { genericEmailService } = await import('../services/genericEmailService');
    
    const testEmailData = {
      recipientEmail: process.env.GMAIL_USER_EMAIL, // Send to self for testing
      recipientName: 'Test User',
      subject: 'Test Resend Invitation - DeskBelt',
      message: `This is a test of the resend invitation email functionality.
      
      If you receive this email, the email service is working correctly.
      
      Test invitation link: http://localhost:3000/invite/test-token-123
      
      This test email was sent at: ${new Date().toLocaleString()}`,
      emailType: 'custom',
      businessName: 'DeskBelt Test',
      senderName: 'Test System',
      userId: 'test-user-id'
    };
    
    console.log('🧪 Sending test email with data:', {
      to: testEmailData.recipientEmail,
      subject: testEmailData.subject,
      emailType: testEmailData.emailType
    });
    
    const result = await genericEmailService.sendEmail(testEmailData);
    
    console.log('🧪 Test email result:', result);
    
    res.json({
      success: true,
      message: 'Test email functionality completed',
      result: result
    });
    
  } catch (error) {
    console.error('🧪 Test email error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Unknown error',
      message: 'Test email failed'
    });
  }
});

export default router;