// @ts-nocheck
// Profile creation API endpoint to bypass RLS issues
import express from 'express';
import { supabase } from '../config/supabase';

const router = express.Router();

// POST /api/create-profile - Create user profile during registration
router.post('/', async (req, res) => {
  try {
    const { user_id, email, full_name } = req.body;

    if (!user_id || !email) {
      return res.status(400).json({ error: 'User ID and email are required' });
    }

    console.log('Creating profile via API for:', email);

    // Use service role to bypass RLS policies
    const { data, error } = await supabase
      .from('users')
      .insert({
        id: user_id,
        email: email,
        full_name: full_name || null,
        role: 'tradesperson',
        country: 'UK'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating profile via API:', error);
      return res.status(500).json({ error: error.message });
    }

    console.log('Profile created successfully via API:', data);
    res.json({ success: true, profile: data });

  } catch (error) {
    console.error('API endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;