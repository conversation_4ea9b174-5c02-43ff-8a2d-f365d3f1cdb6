import { Router } from 'express';
import { z } from 'zod';
import { supabase } from '../config/supabase';
import { authenticateUser } from '../middleware/auth';

const router = Router();

// In-memory cache for analytics data
const analyticsCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = process.env.NODE_ENV === 'production' ? 60 * 60 * 1000 : 5 * 60 * 1000; // 1 hour prod, 5 min dev

// Request validation schema
const analyticsQuerySchema = z.object({
  range: z.enum(['last_7d', 'last_30d', 'last_90d', 'last_6m', 'last_1y', 'custom']).default('last_30d'),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  include_comparison: z.string().transform(val => val === 'true').default('false'),
  'metrics[]': z.union([
    z.string(),
    z.array(z.string())
  ]).transform(val => {
    // Handle single string or array of strings
    const arr = Array.isArray(val) ? val : [val];
    return arr.filter(m => [
      'users_new',
      'users_active', 
      'clients_new',
      'jobs_created',
      'jobs_completed',
      'invoice_value',
      'quote_conversion',
      'quotes_created',
      'invoices_created',
      'contracts_created',
      'ai_messages'
    ].includes(m));
  }).default(['users_new', 'users_active', 'jobs_created', 'quotes_created', 'invoices_created', 'contracts_created'])
});

// Helper to get date range
function getDateRange(range: string, startDate?: string | undefined, endDate?: string | undefined) {
  const now = new Date();
  let start: Date;
  let end: Date = now;

  switch (range) {
    case 'last_7d':
      start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'last_30d':
      start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case 'last_90d':
      start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case 'last_6m':
      start = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
      break;
    case 'last_1y':
      start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    case 'custom':
      if (!startDate || !endDate) {
        throw new Error('Custom range requires start_date and end_date');
      }
      start = new Date(startDate);
      end = new Date(endDate);
      
      // Limit custom range to 1 year max
      const diffDays = (end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000);
      if (diffDays > 365) {
        throw new Error('Custom range cannot exceed 1 year');
      }
      break;
    default:
      start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  return { start, end };
}

// Helper to get comparison date range (previous period)
function getComparisonDateRange(range: string, startDate?: string | undefined, endDate?: string | undefined) {
  const { start, end } = getDateRange(range, startDate, endDate);
  const duration = end.getTime() - start.getTime();
  
  const comparisonEnd = new Date(start.getTime() - 1); // End 1ms before current period starts
  const comparisonStart = new Date(comparisonEnd.getTime() - duration);
  
  return { start: comparisonStart, end: comparisonEnd };
}

// Helper to calculate totals from time series data
function calculateTotals(data: Array<{date: string, value: number}>) {
  return data.reduce((sum, point) => sum + point.value, 0);
}

// Analytics aggregation helpers
async function getDailyNewUsers(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('users')
    .select('created_at')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString());

  if (error) throw error;

  // Group by day
  const dailyCounts = new Map<string, number>();
  data.forEach(user => {
    const date = new Date(user.created_at).toISOString().split('T')[0];
    dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyCounts.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getDailyActiveUsers(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('users')
    .select('last_login')
    .gte('last_login', start.toISOString())
    .lte('last_login', end.toISOString())
    .not('last_login', 'is', null);

  if (error) throw error;

  // Group by day
  const dailyCounts = new Map<string, number>();
  data.forEach(user => {
    if (user.last_login) {
      const date = new Date(user.last_login).toISOString().split('T')[0];
      dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
    }
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyCounts.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getDailyNewClients(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('clients')
    .select('created_at')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString());

  if (error) throw error;

  // Group by day
  const dailyCounts = new Map<string, number>();
  data.forEach(client => {
    const date = new Date(client.created_at).toISOString().split('T')[0];
    dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyCounts.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getDailyJobsCreated(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('jobs')
    .select('created_at')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString());

  if (error) throw error;

  // Group by day
  const dailyCounts = new Map<string, number>();
  data.forEach(job => {
    const date = new Date(job.created_at).toISOString().split('T')[0];
    dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyCounts.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getDailyJobsCompleted(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('jobs')
    .select('created_at, status')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString())
    .eq('status', 'completed');

  if (error) throw error;

  // Group by day
  const dailyCounts = new Map<string, number>();
  data.forEach(job => {
    const date = new Date(job.created_at).toISOString().split('T')[0];
    dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyCounts.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getDailyInvoiceValue(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('invoices')
    .select('created_at, amount')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString());

  if (error) throw error;

  // Group by day and sum amounts
  const dailyTotals = new Map<string, number>();
  data.forEach(invoice => {
    const date = new Date(invoice.created_at).toISOString().split('T')[0];
    const amount = parseFloat(invoice.amount) || 0;
    dailyTotals.set(date, (dailyTotals.get(date) || 0) + amount);
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyTotals.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getQuoteConversionRate(start: Date, end: Date) {
  // Get quotes in date range
  const { data: quotes, error: quotesError } = await supabase
    .from('quotes')
    .select('id, created_at')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString());

  if (quotesError) throw quotesError;

  // Get jobs that have quotes (converted quotes)
  const { data: jobs, error: jobsError } = await supabase
    .from('jobs')
    .select('id, created_at')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString())
    .not('id', 'is', null);

  if (jobsError) throw jobsError;

  // Simple conversion rate calculation
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    const dayQuotes = quotes.filter(q => new Date(q.created_at).toISOString().split('T')[0] === dateStr).length;
    const dayJobs = jobs.filter(j => new Date(j.created_at).toISOString().split('T')[0] === dateStr).length;
    
    result.push({
      date: dateStr,
      value: dayQuotes > 0 ? Math.round((dayJobs / dayQuotes) * 100) : 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getDailyQuotesCreated(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('quotes')
    .select('created_at')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString());

  if (error) throw error;

  // Group by day
  const dailyCounts = new Map<string, number>();
  data.forEach(quote => {
    const date = new Date(quote.created_at).toISOString().split('T')[0];
    dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyCounts.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getDailyInvoicesCreated(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('invoices')
    .select('created_at')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString());

  if (error) throw error;

  // Group by day
  const dailyCounts = new Map<string, number>();
  data.forEach(invoice => {
    const date = new Date(invoice.created_at).toISOString().split('T')[0];
    dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyCounts.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getDailyContractsCreated(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('contracts')
    .select('created_at')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString());

  if (error) throw error;

  // Group by day
  const dailyCounts = new Map<string, number>();
  data.forEach(contract => {
    const date = new Date(contract.created_at).toISOString().split('T')[0];
    dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyCounts.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

async function getDailyAiMessages(start: Date, end: Date) {
  const { data, error } = await supabase
    .from('chat_messages')
    .select('created_at')
    .gte('created_at', start.toISOString())
    .lte('created_at', end.toISOString());

  if (error) throw error;

  // Group by day
  const dailyCounts = new Map<string, number>();
  data.forEach(message => {
    const date = new Date(message.created_at).toISOString().split('T')[0];
    dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
  });

  // Fill missing days with 0
  const result = [];
  const current = new Date(start);
  while (current <= end) {
    const dateStr = current.toISOString().split('T')[0];
    result.push({
      date: dateStr,
      value: dailyCounts.get(dateStr) || 0
    });
    current.setDate(current.getDate() + 1);
  }

  return result;
}

// Main analytics endpoint
// @ts-ignore - TypeScript has issues with the middleware typing
router.get('/analytics', authenticateUser, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user || (!req.user.role || (req.user.role !== 'admin' && req.user.role !== 'super_admin'))) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Validate query parameters
    const query = analyticsQuerySchema.parse(req.query);
    
    // Extract metrics array from the parsed query
    const metrics = query['metrics[]'];
    
    // Generate cache key
    const cacheKey = JSON.stringify(query);
    const cached = analyticsCache.get(cacheKey);
    
    // Return cached data if valid
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      return res.json(cached.data);
    }

    // Get date range
    // @ts-ignore - Optional parameters handled in function
    const { start, end } = getDateRange(query.range, query.start_date, query.end_date);

    // Fetch requested metrics
    const results: any = {};
    
    // Also fetch comparison data if requested
    let comparisonResults: any = {};
    if (query.include_comparison) {
      const { start: compStart, end: compEnd } = getComparisonDateRange(query.range, query.start_date, query.end_date);
      comparisonResults = {};
    }

    if (metrics.includes('users_new')) {
      results.users_new = await getDailyNewUsers(start, end);
    }

    if (metrics.includes('users_active')) {
      results.users_active = await getDailyActiveUsers(start, end);
    }

    if (metrics.includes('clients_new')) {
      results.clients_new = await getDailyNewClients(start, end);
    }

    if (metrics.includes('jobs_created')) {
      results.jobs_created = await getDailyJobsCreated(start, end);
    }

    if (metrics.includes('jobs_completed')) {
      results.jobs_completed = await getDailyJobsCompleted(start, end);
    }

    if (metrics.includes('invoice_value')) {
      results.invoice_value = await getDailyInvoiceValue(start, end);
    }

    if (metrics.includes('quote_conversion')) {
      results.quote_conversion = await getQuoteConversionRate(start, end);
    }

    if (metrics.includes('quotes_created')) {
      results.quotes_created = await getDailyQuotesCreated(start, end);
    }

    if (metrics.includes('invoices_created')) {
      results.invoices_created = await getDailyInvoicesCreated(start, end);
    }

    if (metrics.includes('contracts_created')) {
      results.contracts_created = await getDailyContractsCreated(start, end);
    }

    if (metrics.includes('ai_messages')) {
      results.ai_messages = await getDailyAiMessages(start, end);
    }

    // Fetch comparison data if requested
    if (query.include_comparison) {
      const { start: compStart, end: compEnd } = getComparisonDateRange(query.range, query.start_date, query.end_date);
      
      if (metrics.includes('users_new')) {
        comparisonResults.users_new = await getDailyNewUsers(compStart, compEnd);
      }
      if (metrics.includes('users_active')) {
        comparisonResults.users_active = await getDailyActiveUsers(compStart, compEnd);
      }
      if (metrics.includes('clients_new')) {
        comparisonResults.clients_new = await getDailyNewClients(compStart, compEnd);
      }
      if (metrics.includes('jobs_created')) {
        comparisonResults.jobs_created = await getDailyJobsCreated(compStart, compEnd);
      }
      if (metrics.includes('quotes_created')) {
        comparisonResults.quotes_created = await getDailyQuotesCreated(compStart, compEnd);
      }
      if (metrics.includes('invoices_created')) {
        comparisonResults.invoices_created = await getDailyInvoicesCreated(compStart, compEnd);
      }
      if (metrics.includes('contracts_created')) {
        comparisonResults.contracts_created = await getDailyContractsCreated(compStart, compEnd);
      }
      if (metrics.includes('ai_messages')) {
        comparisonResults.ai_messages = await getDailyAiMessages(compStart, compEnd);
      }
    }

    // Calculate percentage changes
    const percentageChanges: any = {};
    if (query.include_comparison) {
      for (const metric of metrics) {
        if (results[metric] && comparisonResults[metric]) {
          const currentTotal = calculateTotals(results[metric]);
          const previousTotal = calculateTotals(comparisonResults[metric]);
          
          if (previousTotal === 0) {
            percentageChanges[metric] = currentTotal > 0 ? 100 : 0;
          } else {
            percentageChanges[metric] = ((currentTotal - previousTotal) / previousTotal) * 100;
          }
        }
      }
    }

    // Prepare response
    const responseData = {
      ...results,
      ...(query.include_comparison && { 
        comparison: comparisonResults,
        percentage_changes: percentageChanges 
      })
    };

    // Cache the results
    analyticsCache.set(cacheKey, {
      data: responseData,
      timestamp: Date.now()
    });

    // Clean up old cache entries (simple cleanup)
    if (analyticsCache.size > 100) {
      const oldestKey = analyticsCache.keys().next().value;
      if (oldestKey) {
        analyticsCache.delete(oldestKey);
      }
    }

    res.json(responseData);

  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({ 
      error: 'Failed to fetch analytics data',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router; 