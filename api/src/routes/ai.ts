// @ts-nocheck
import express from 'express'
import OpenAI from 'openai'
import { authenticateUser } from '../middleware/auth'
import { aiRateLimit } from '../middleware/aiRateLimit'

const router = express.Router()

// Apply auth and rate limiting to all AI routes
router.use(authenticateUser)
router.use(aiRateLimit)

// OpenRouter configuration  
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY

// Primary model (70B model - much more capable) - NO FALLBACKS
const PRIMARY_MODEL = 'meta-llama/llama-3.3-70b-instruct'

const OPENROUTER_MODEL = PRIMARY_MODEL // Using winner as primary

// Initialize OpenAI client with OpenRouter configuration (only if API key is available)
let openai: OpenAI | null = null
if (OPENROUTER_API_KEY) {
  openai = new OpenAI({
    baseURL: 'https://openrouter.ai/api/v1',
    apiKey: OPENROUTER_API_KEY,
    defaultHeaders: {
      'HTTP-Referer': 'https://deskbelt.app',
      'X-Title': 'DeskBelt - Tradesperson Management',
    },
  })
}

// Default prompt for job parsing
const JOB_PARSING_PROMPT = `You are an AI assistant specialized in parsing job descriptions for UK tradespeople.

Extract the following information from the job description and return it in JSON format:
- title: string (generate a clear job title)
- description: string (cleaned and formatted description)
- category: string (e.g., "Kitchen & Bathroom", "Electrical", "Plumbing", "General Building")
- estimatedDuration: number (in days)
- estimatedValue: number (if mentioned, in GBP)
- materials: string[] (list of materials needed)
- skills: string[] (required skills/trades)
- urgency: "low" | "medium" | "high"

Context will include:
- Client information (name, address, type)
- Previous job history for this client

Rules:
- Generate professional job titles based on work description
- Estimate realistic durations for UK trades
- Categorize work into standard trade categories
- Extract material lists when mentioned
- Assess urgency from language used
- Consider client location for travel time

Return valid JSON only, no other text.`

// Default prompt for client parsing
const CLIENT_PARSING_PROMPT = `You are an AI assistant specialized in parsing client information for UK tradespeople.

Extract the following information from the text and return it in JSON format:
- name: string (full name or business name)
- email: string (email address if found)
- phone: string (phone number if found, in UK format)
- address: string (full address if found)
- business_name: string (company/business name if different from name)
- type: "residential" | "commercial" (infer from context)

Rules:
- Extract UK phone numbers in proper format (e.g., "020 7946 0958", "************")
- Format addresses properly for UK postcodes
- Determine if residential or commercial based on context
- Clean and format names properly
- If business card text, extract all relevant contact details
- Return null for fields not found

Return valid JSON only, no other text.`

// Default prompt for quote parsing
const QUOTE_PARSING_PROMPT = `You are an AI assistant specialized in parsing quote information for UK tradespeople.

Extract line items from natural language input and return them in JSON format:
{
  "lineItems": [
    {
      "description": string,
      "quantity": number,
      "unitPrice": number,
      "unit": string (e.g., "day", "hour", "item", "m²")
    }
  ],
  "subtotal": number,
  "vatRate": number (default 20 for UK),
  "vatAmount": number,
  "total": number,
  "notes": string[]
}

Rules:
- Parse pricing amounts (£600, £1200, etc.) accurately
- Break down work into specific billable items
- Calculate VAT at 20% unless specified otherwise
- Use appropriate units (days for labour, items for materials)
- Extract any special notes or conditions
- Ensure all calculations are correct

Return valid JSON only, no other text.`

// Async handler wrapper
const asyncHandler = (fn: any) => (req: any, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next)
}

// POST /api/ai/parse-job
router.post('/parse-job', asyncHandler(async (req: any, res: any) => {
  const { input, clientContext, customPrompt } = req.body

  // Validate input
  if (!input || typeof input !== 'string' || input.trim().length === 0) {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Input text is required and must be a non-empty string'
    })
  }

  // Prepare context string
  const contextString = clientContext ? 
    `\n\nClient Context:\n${JSON.stringify(clientContext, null, 2)}` : ''

  // Use custom prompt if provided, otherwise use default
  const prompt = customPrompt || JOB_PARSING_PROMPT

  try {
    // Check if AI is available
    if (!openai) {
      return res.status(503).json({
        error: 'AI service unavailable',
        details: 'OpenRouter API key not configured'
      })
    }

    // Make request to OpenRouter
    const completion = await openai.chat.completions.create({
      model: OPENROUTER_MODEL,
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: `${input}${contextString}`
        }
      ],
      temperature: 0.1, // Low temperature for consistent, factual responses
      max_tokens: 2000,
    })

    const response = completion.choices[0]?.message?.content?.trim()
    
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI',
        details: 'The AI service did not return a response'
      })
    }

    // Parse JSON response
    let parsedResponse
    try {
      parsedResponse = JSON.parse(response)
    } catch (parseError) {
      // Try to extract JSON from response if direct parsing fails
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        try {
          parsedResponse = JSON.parse(jsonMatch[0])
        } catch (secondParseError) {
          return res.status(500).json({
            error: 'Invalid AI response format',
            details: `AI returned invalid JSON: ${response.substring(0, 200)}...`
          })
        }
      } else {
        return res.status(500).json({
          error: 'Invalid AI response format',
          details: `AI response does not contain valid JSON: ${response.substring(0, 200)}...`
        })
      }
    }

    // Validate the parsed response structure
    const requiredFields = ['title', 'description', 'category', 'estimatedDuration', 'urgency']
    const missingFields = requiredFields.filter(field => 
      parsedResponse[field] === undefined || 
      parsedResponse[field] === null ||
      parsedResponse[field] === ''
    )

    if (missingFields.length > 0) {
      return res.status(500).json({
        error: 'Incomplete AI response',
        details: `AI response missing required fields: ${missingFields.join(', ')}`
      })
    }

    // Return the parsed job information
    return res.json(parsedResponse)

  } catch (error) {
    console.error('AI job parsing error:', error)
    
    // Handle OpenRouter/API specific errors
    if (error instanceof Error) {
      if (error.message.includes('rate limit')) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          details: 'Too many requests. Please wait before trying again.'
        })
      }
      
      if (error.message.includes('API key')) {
        return res.status(401).json({
          error: 'Authentication failed',
          details: 'Invalid or missing API key'
        })
      }
    }

    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
}))

// POST /api/ai/parse-client
router.post('/parse-client', asyncHandler(async (req: any, res: any) => {
  const { input, customPrompt } = req.body

  // Validate input
  if (!input || typeof input !== 'string' || input.trim().length === 0) {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Input text is required and must be a non-empty string'
    })
  }

  try {
    // Check if AI is available
    if (!openai) {
      return res.status(503).json({
        error: 'AI service unavailable',
        details: 'OpenRouter API key not configured'
      })
    }

    // Use custom prompt if provided, otherwise use default
    const prompt = customPrompt || CLIENT_PARSING_PROMPT

    // Make request to OpenRouter
    const completion = await openai.chat.completions.create({
      model: OPENROUTER_MODEL,
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: input
        }
      ],
      temperature: 0.1,
      max_tokens: 1000,
    })

    const response = completion.choices[0]?.message?.content?.trim()
    
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI',
        details: 'The AI service did not return a response'
      })
    }

    // Parse JSON response
    let parsedResponse
    try {
      parsedResponse = JSON.parse(response)
    } catch (parseError) {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        try {
          parsedResponse = JSON.parse(jsonMatch[0])
        } catch (secondParseError) {
          return res.status(500).json({
            error: 'Invalid AI response format',
            details: `AI returned invalid JSON: ${response.substring(0, 200)}...`
          })
        }
      } else {
        return res.status(500).json({
          error: 'Invalid AI response format',
          details: `AI response does not contain valid JSON: ${response.substring(0, 200)}...`
        })
      }
    }

    return res.json(parsedResponse)

  } catch (error) {
    console.error('AI client parsing error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
}))

// POST /api/ai/parse-quote
router.post('/parse-quote', asyncHandler(async (req: any, res: any) => {
  const { input, jobContext, customPrompt } = req.body

  // Validate input
  if (!input || typeof input !== 'string' || input.trim().length === 0) {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Input text is required and must be a non-empty string'
    })
  }

  try {
    // Check if AI is available
    if (!openai) {
      return res.status(503).json({
        error: 'AI service unavailable',
        details: 'OpenRouter API key not configured'
      })
    }

    // Prepare context string
    const contextString = jobContext ? 
      `\n\nJob Context:\n${JSON.stringify(jobContext, null, 2)}` : ''

    // Use custom prompt if provided, otherwise use default
    const prompt = customPrompt || QUOTE_PARSING_PROMPT

    // Make request to OpenRouter
    const completion = await openai.chat.completions.create({
      model: OPENROUTER_MODEL,
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: `${input}${contextString}`
        }
      ],
      temperature: 0.1,
      max_tokens: 2000,
    })

    const response = completion.choices[0]?.message?.content?.trim()
    
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI',
        details: 'The AI service did not return a response'
      })
    }

    // Parse JSON response
    let parsedResponse
    try {
      parsedResponse = JSON.parse(response)
    } catch (parseError) {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        try {
          parsedResponse = JSON.parse(jsonMatch[0])
        } catch (secondParseError) {
          return res.status(500).json({
            error: 'Invalid AI response format',
            details: `AI returned invalid JSON: ${response.substring(0, 200)}...`
          })
        }
      } else {
        return res.status(500).json({
          error: 'Invalid AI response format',
          details: `AI response does not contain valid JSON: ${response.substring(0, 200)}...`
        })
      }
    }

    return res.json(parsedResponse)

  } catch (error) {
    console.error('AI quote parsing error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
}))

// POST /api/ai/generate-invoice
router.post('/generate-invoice', asyncHandler(async (req: any, res: any) => {
  const { jobContext, quoteContext, variations, customPrompt } = req.body

  // Validate input
  if (!jobContext) {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Job context is required'
    })
  }

  try {
    // Check if AI is available
    if (!openai) {
      return res.status(503).json({
        error: 'AI service unavailable',
        details: 'OpenRouter API key not configured'
      })
    }

    // Prepare context
    const contextString = `Job Context:\n${JSON.stringify(jobContext, null, 2)}`
    const quoteString = quoteContext ? `\n\nQuote Context:\n${JSON.stringify(quoteContext, null, 2)}` : ''
    const variationsString = variations ? `\n\nVariations:\n${variations}` : ''

    const defaultPrompt = `You are an AI assistant specialized in generating invoices for UK tradespeople.

Generate invoice details from job and quote data in JSON format:
{
  "lineItems": [
    {
      "description": string,
      "quantity": number,
      "unitPrice": number,
      "total": number
    }
  ],
  "subtotal": number,
  "vatRate": number,
  "vatAmount": number,
  "total": number,
  "dueDate": string (ISO date, default 30 days from now),
  "paymentTerms": string (e.g., "Net 30", "Due on receipt"),
  "notes": string[]
}

Rules:
- Inherit line items from quote if available
- Add variations as additional line items
- Calculate VAT at 20% for UK
- Use past tense descriptions (work completed)
- Generate realistic due dates and payment terms
- Ensure all calculations are correct

Return valid JSON only, no other text.`

    const prompt = customPrompt || defaultPrompt

    // Make request to OpenRouter
    const completion = await openai.chat.completions.create({
      model: OPENROUTER_MODEL,
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: `${contextString}${quoteString}${variationsString}`
        }
      ],
      temperature: 0.1,
      max_tokens: 2000,
    })

    const response = completion.choices[0]?.message?.content?.trim()
    
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI',
        details: 'The AI service did not return a response'
      })
    }

    // Parse JSON response
    let parsedResponse
    try {
      parsedResponse = JSON.parse(response)
    } catch (parseError) {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        try {
          parsedResponse = JSON.parse(jsonMatch[0])
        } catch (secondParseError) {
          return res.status(500).json({
            error: 'Invalid AI response format',
            details: `AI returned invalid JSON: ${response.substring(0, 200)}...`
          })
        }
      } else {
        return res.status(500).json({
          error: 'Invalid AI response format',
          details: `AI response does not contain valid JSON: ${response.substring(0, 200)}...`
        })
      }
    }

    return res.json(parsedResponse)

  } catch (error) {
    console.error('AI invoice generation error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
}))

// POST /api/ai/generate-contract
router.post('/generate-contract', asyncHandler(async (req: any, res: any) => {
  const { jobContext, quoteContext, clientContext, contractType, customPrompt } = req.body

  // Validate input
  if (!jobContext) {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Job context is required'
    })
  }

  try {
    // Check if AI is available
    if (!openai) {
      return res.status(503).json({
        error: 'AI service unavailable',
        details: 'OpenRouter API key not configured'
      })
    }

    // Prepare context
    const contextString = `Job Context:\n${JSON.stringify(jobContext, null, 2)}`
    const clientString = clientContext ? `\n\nClient Context:\n${JSON.stringify(clientContext, null, 2)}` : ''
    const quoteString = quoteContext ? `\n\nQuote Context:\n${JSON.stringify(quoteContext, null, 2)}` : ''
    const typeString = contractType ? `\n\nContract Type: ${contractType}` : ''

    const defaultPrompt = `You are an AI assistant specialized in generating contracts for UK tradespeople.

Generate comprehensive contract terms based on job details in JSON format:
{
  "terms": string (full contract text with UK legal compliance),
  "workDescription": string,
  "timeline": {
    "startDate": string,
    "expectedCompletion": string,
    "milestones": string[]
  },
  "pricing": {
    "totalValue": number,
    "paymentSchedule": string[],
    "variations": string
  },
  "warranties": string[],
  "liability": string,
  "healthSafety": string[]
}

Rules:
- Include UK building regulations compliance
- Add appropriate warranties for trade type
- Include health & safety requirements
- Set realistic timelines based on job scope
- Include payment milestone structure
- Add liability and insurance clauses
- Ensure legal compliance for UK trades

Return valid JSON only, no other text.`

    const prompt = customPrompt || defaultPrompt

    // Make request to OpenRouter
    const completion = await openai.chat.completions.create({
      model: OPENROUTER_MODEL,
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: `${contextString}${clientString}${quoteString}${typeString}`
        }
      ],
      temperature: 0.1,
      max_tokens: 3000,
    })

    const response = completion.choices[0]?.message?.content?.trim()
    
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI',
        details: 'The AI service did not return a response'
      })
    }

    // Parse JSON response
    let parsedResponse
    try {
      parsedResponse = JSON.parse(response)
    } catch (parseError) {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        try {
          parsedResponse = JSON.parse(jsonMatch[0])
        } catch (secondParseError) {
          return res.status(500).json({
            error: 'Invalid AI response format',
            details: `AI returned invalid JSON: ${response.substring(0, 200)}...`
          })
        }
      } else {
        return res.status(500).json({
          error: 'Invalid AI response format',
          details: `AI response does not contain valid JSON: ${response.substring(0, 200)}...`
        })
      }
    }

    return res.json(parsedResponse)

  } catch (error) {
    console.error('AI contract generation error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
}))

// GET /api/ai/smart-defaults
router.get('/smart-defaults', asyncHandler(async (req: any, res: any) => {
  const { type, context } = req.query

  // Validate input
  if (!type) {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Type parameter is required (client, job, quote, invoice, contract)'
    })
  }

  try {
    // Check if AI is available
    if (!openai) {
      return res.status(503).json({
        error: 'AI service unavailable',
        details: 'OpenRouter API key not configured'
      })
    }

    const contextString = context ? `\n\nContext:\n${context}` : ''

    const defaultPrompt = `You are an AI assistant specialized in providing smart defaults for UK tradespeople.

Generate context-aware default values in JSON format based on the type requested:

For "client":
{
  "paymentTerms": "Net 30",
  "preferredContact": "phone",
  "vatRegistered": false
}

For "job":
{
  "category": "General Building",
  "priority": "medium",
  "estimatedDuration": 1,
  "workingHours": "09:00-17:00"
}

For "quote":
{
  "validityPeriod": 30,
  "vatRate": 20,
  "paymentTerms": "50% deposit, 50% on completion"
}

For "invoice":
{
  "paymentTerms": "Net 30",
  "vatRate": 20,
  "dueDate": "+30 days"
}

For "contract":
{
  "warrantyPeriod": "12 months",
  "retentionPercentage": 5,
  "variationLimit": 10
}

Rules:
- Use UK trade standards
- Consider seasonal factors
- Base on similar job types
- Include regional variations
- Ensure compliance with UK regulations

Return valid JSON only, no other text.`

    // Make request to OpenRouter
    const completion = await openai.chat.completions.create({
      model: OPENROUTER_MODEL,
      messages: [
        {
          role: 'system',
          content: defaultPrompt
        },
        {
          role: 'user',
          content: `Type: ${type}${contextString}`
        }
      ],
      temperature: 0.1,
      max_tokens: 1000,
    })

    const response = completion.choices[0]?.message?.content?.trim()
    
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI',
        details: 'The AI service did not return a response'
      })
    }

    // Parse JSON response
    let parsedResponse
    try {
      parsedResponse = JSON.parse(response)
    } catch (parseError) {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        try {
          parsedResponse = JSON.parse(jsonMatch[0])
        } catch (secondParseError) {
          return res.status(500).json({
            error: 'Invalid AI response format',
            details: `AI returned invalid JSON: ${response.substring(0, 200)}...`
          })
        }
      } else {
        return res.status(500).json({
          error: 'Invalid AI response format',
          details: `AI response does not contain valid JSON: ${response.substring(0, 200)}...`
        })
      }
    }

    return res.json(parsedResponse)

  } catch (error) {
    console.error('AI smart defaults error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
}))

// POST /api/ai/quote-intelligence
router.post('/quote-intelligence', asyncHandler(async (req: any, res: any) => {
  const { description, location, clientAddress, customPrompt } = req.body

  // Validate input
  if (!description || typeof description !== 'string' || description.trim().length === 0) {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Job description is required and must be a non-empty string'
    })
  }

  try {
    // Check if AI is available
    if (!openai) {
      return res.status(503).json({
        error: 'AI service unavailable',
        details: 'OpenRouter API key not configured'
      })
    }

    const locationString = location ? `\nLocation: ${location}` : ''
    const clientLocationString = clientAddress ? `\nClient Location: ${clientAddress}` : ''

    const defaultPrompt = `You are an AI assistant specialized in providing intelligent market insights for UK tradespeople creating quotes.

Analyze the job description and provide actionable business intelligence in JSON format:
{
  "marketRate": {
    "workType": string,
    "estimatedRange": string,
    "suggestion": string,
    "confidence": number (0-100)
  },
  "materialAlert": string | null,
  "weatherAlert": string | null,
  "businessAdvice": string[],
  "riskFactors": string[]
}

Guidelines:
- Analyze the job description intelligently (no keyword matching)
- Provide realistic UK market rates based on current data
- Consider regional pricing variations (London typically 20-30% higher)
- Include material cost insights if relevant
- Weather alerts only for outdoor work
- Practical business advice for pricing and execution
- Identify potential risk factors or complications

Examples:
- "drain pipes" → analyze as plumbing work, consider access issues, material costs
- "rewiring house" → electrical work, consider property size, regulations, certification
- "kitchen tiles" → tiling work, consider prep work, material wastage, surface condition

Return valid JSON only, no other text.`

    const prompt = customPrompt || defaultPrompt

    // Make request to OpenRouter with LLaMA 3.3 70B for better analysis
    const completion = await openai.chat.completions.create({
      model: 'meta-llama/llama-3.3-70b-instruct', // Use larger model for better intelligence
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: `Job Description: "${description}"${locationString}${clientLocationString}`
        }
      ],
      temperature: 0.2, // Lower temperature for more consistent business advice
      max_tokens: 2000,
    })

    const response = completion.choices[0]?.message?.content?.trim()
    
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI',
        details: 'The AI service did not return a response'
      })
    }

    // Parse JSON response
    let parsedResponse
    try {
      parsedResponse = JSON.parse(response)
    } catch (parseError) {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        try {
          parsedResponse = JSON.parse(jsonMatch[0])
        } catch (secondParseError) {
          return res.status(500).json({
            error: 'Invalid AI response format',
            details: `AI returned invalid JSON: ${response.substring(0, 200)}...`
          })
        }
      } else {
        return res.status(500).json({
          error: 'Invalid AI response format',
          details: `AI response does not contain valid JSON: ${response.substring(0, 200)}...`
        })
      }
    }

    return res.json(parsedResponse)

  } catch (error) {
    console.error('AI quote intelligence error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
}))

// POST /api/ai/validate-pricing
router.post('/validate-pricing', asyncHandler(async (req: any, res: any) => {
  const { pricing, jobType, location, customPrompt } = req.body

  // Validate input
  if (!pricing || !jobType) {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Pricing and job type are required'
    })
  }

  try {
    // Check if AI is available
    if (!openai) {
      return res.status(503).json({
        error: 'AI service unavailable',
        details: 'OpenRouter API key not configured'
      })
    }

    const locationString = location ? `\n\nLocation: ${location}` : ''

    const defaultPrompt = `You are an AI assistant specialized in pricing validation for UK tradespeople.

Analyze the provided pricing and return market intelligence in JSON format:
{
  "isReasonable": boolean,
  "marketRange": {
    "low": number,
    "average": number,
    "high": number
  },
  "comparison": string ("below_market" | "market_rate" | "above_market"),
  "recommendations": string[],
  "factors": string[],
  "confidence": number (0-100)
}

Rules:
- Compare against typical UK trade rates
- Consider regional variations (London vs other areas)
- Factor in job complexity and materials
- Account for seasonal demand
- Include material cost fluctuations
- Consider trade specialization premiums

Return valid JSON only, no other text.`

    const prompt = customPrompt || defaultPrompt

    // Make request to OpenRouter
    const completion = await openai.chat.completions.create({
      model: OPENROUTER_MODEL,
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: `Pricing: £${pricing}\nJob Type: ${jobType}${locationString}`
        }
      ],
      temperature: 0.1,
      max_tokens: 1500,
    })

    const response = completion.choices[0]?.message?.content?.trim()
    
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI',
        details: 'The AI service did not return a response'
      })
    }

    // Parse JSON response
    let parsedResponse
    try {
      parsedResponse = JSON.parse(response)
    } catch (parseError) {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        try {
          parsedResponse = JSON.parse(jsonMatch[0])
        } catch (secondParseError) {
          return res.status(500).json({
            error: 'Invalid AI response format',
            details: `AI returned invalid JSON: ${response.substring(0, 200)}...`
          })
        }
      } else {
        return res.status(500).json({
          error: 'Invalid AI response format',
          details: `AI response does not contain valid JSON: ${response.substring(0, 200)}...`
        })
      }
    }

    return res.json(parsedResponse)

  } catch (error) {
    console.error('AI pricing validation error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
}))

// POST /api/ai/chat-response
router.post('/chat-response', asyncHandler(async (req: any, res: any) => {
  const { prompt, input } = req.body

  // Validate input
  if (!prompt || !input || typeof prompt !== 'string' || typeof input !== 'string') {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Both prompt and input are required and must be strings'
    })
  }

  // Check if AI is available
  if (!openai) {
    return res.status(503).json({
      error: 'AI service unavailable',
      details: 'OpenRouter API key not configured'
    })
  }

  // Try primary model first, fallback to secondary if needed
  const tryModel = async (modelName: string): Promise<string | null> => {
    try {
      const completion = await openai!.chat.completions.create({
        model: modelName,
        messages: [
          {
            role: 'system',
            content: prompt
          },
          {
            role: 'user',
            content: input
          }
        ],
        temperature: 0.7, // Higher temperature for more conversational responses
        max_tokens: 150, // Keep responses concise
      })

      return completion.choices[0]?.message?.content?.trim() || null
    } catch (error) {
      console.log(`Model ${modelName} failed:`, error instanceof Error ? error.message : 'Unknown error')
      return null
    }
  }

  // Try only the primary model - NO FALLBACKS
  let response = await tryModel(PRIMARY_MODEL)
  let usedModel = PRIMARY_MODEL

  if (!response) {
    return res.status(500).json({
      error: 'AI model failed',
      details: 'Unable to get response from AI model'
    })
  }

  return res.json({ 
    message: response,
    model: usedModel // Include which model was used
  })
}))

// GET /api/ai/test-connection
router.get('/test-connection', asyncHandler(async (req: any, res: any) => {
  // Check if AI is available
  if (!openai) {
    return res.status(503).json({
      connected: false,
      error: 'OpenRouter API key not configured'
    })
  }

  try {
    const completion = await openai.chat.completions.create({
      model: OPENROUTER_MODEL,
      messages: [
        {
          role: 'user',
          content: 'Respond with "OK" if you can receive this message.'
        }
      ],
      max_tokens: 10,
    })
    
    const response = completion.choices[0]?.message?.content?.trim()
    const isConnected = response?.includes('OK') || false
    
    return res.json({
      connected: isConnected,
      model: OPENROUTER_MODEL,
      response: response || 'No response'
    })
  } catch (error) {
    console.error('AI connection test failed:', error)
    return res.status(500).json({
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}))

// POST /api/ai/dex-chat - Trade-focused chatbot with strict topic filtering
router.post('/dex-chat', asyncHandler(async (req: any, res: any) => {
  const { message, context } = req.body

  // Validate input
  if (!message || typeof message !== 'string') {
    return res.status(400).json({
      error: 'Invalid input',
      details: 'Message is required and must be a string'
    })
  }

  if (message.length > 2000) {
    return res.status(400).json({
      error: 'Message too long',
      details: 'Message must be 2000 characters or less'
    })
  }

  // Check if AI is available
  if (!openai) {
    return res.status(503).json({
      error: 'AI service unavailable',
      details: 'OpenRouter API key not configured'
    })
  }

  try {
    // Build context string from provided context
    let contextString = ''
    if (context) {
      const { currentPage, module, jobId, clientId, teamId } = context
      if (currentPage) contextString += `\nCurrent page: ${currentPage}`
      if (module) contextString += `\nModule: ${module}`
      if (jobId) contextString += `\nJob ID: ${jobId}`
      if (clientId) contextString += `\nClient ID: ${clientId}`
      if (teamId) contextString += `\nTeam ID: ${teamId}`
    }

    // Dex system prompt - trade-focused with strict topic filtering
    const systemPrompt = `You are Dex, an AI business advisor specifically for UK tradespeople (plumbers, electricians, builders, carpenters, roofers, heating engineers, etc.).

CRITICAL RULES:
1. ONLY discuss topics related to trades, construction, building work, business advice for tradespeople, pricing, materials, regulations, tools, or customer management
2. If the user asks about anything else (weather, sports, politics, personal life, entertainment, general chit-chat), politely redirect them back to trade topics
3. Keep responses concise (2-3 sentences max), helpful, and professional
4. Use UK terminology and regulations (Building Regs, Gas Safe, NICEIC, etc.)
5. Focus on practical business advice, not just technical trade information

REDIRECT EXAMPLES:
- Weather chat → "I'm here to help with your trade business! How can I assist with pricing, scheduling, or customer management today?"
- Sports/entertainment → "Let's focus on your business instead! Do you have any jobs you need help pricing or any customer issues to discuss?"
- Personal topics → "I'm your business advisor, so let's talk trades! Any upcoming jobs or business challenges I can help with?"

GOOD TOPICS:
- Job pricing and quotes
- Customer communication and difficult clients  
- Material costs and suppliers
- Building regulations and compliance
- Tools and equipment advice
- Business growth and marketing
- Payment terms and cash flow
- Health & safety requirements
- Seasonal work planning
- Subcontractor management

Be friendly but firm about staying on-topic. Always try to steer conversations toward practical business advice for tradespeople.${contextString}`

    const completion = await openai.chat.completions.create({
      model: PRIMARY_MODEL,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: message
        }
      ],
      temperature: 0.7,
      max_tokens: 200, // Keep responses concise
    })

    const response = completion.choices[0]?.message?.content?.trim()
    
    if (!response) {
      return res.status(500).json({
        error: 'No response from AI',
        details: 'Dex is not available right now'
      })
    }

    return res.json({ 
      message: response,
      model: PRIMARY_MODEL
    })

  } catch (error) {
    console.error('Dex chat error:', error)
    
    // Check for rate limiting
    if (error instanceof Error && error.message.includes('Rate limit exceeded')) {
      return res.status(429).json({
        error: 'Rate limit exceeded',
        details: 'Dex is taking a break. Please try again in a few minutes.',
        fallback: "I'm here to help with your trade business! Try asking about job pricing, customer management, or business advice."
      })
    }
    
    return res.status(500).json({
      error: 'Internal server error',
      details: 'Dex encountered an error. Please try again.',
      fallback: "I'm here to help with your trade business! Try asking about job pricing, customer management, or business advice."
    })
  }
}))

// GET /api/ai/sample-questions - Generate random trade-focused sample questions
router.get('/sample-questions', asyncHandler(async (req: any, res: any) => {
  // Pool of trade-focused questions organized by category
  const questionPools = {
    pricing: [
      "How should I price a kitchen rewiring job?",
      "What's the going rate for bathroom electrical work?",
      "How do I calculate costs for a full house rewire?",
      "What markup should I use on materials?",
      "How much should I charge for emergency call-outs?",
      "What's fair pricing for garden lighting installation?",
      "How do I price commercial electrical work differently?",
      "What factors affect electrical installation pricing?"
    ],
    customer: [
      "How do I handle a difficult customer?",
      "What should I do when a client questions my quote?",
      "How can I improve customer communication?",
      "What's the best way to follow up after a job?",
      "How do I deal with payment delays?",
      "How should I handle customer complaints?",
      "What's the best way to upsell additional work?",
      "How do I build long-term customer relationships?"
    ],
    business: [
      "What are standard payment terms for electrical work?",
      "How do I protect myself with contracts?",
      "When should I require a deposit?",
      "What insurance do I need as an electrician?",
      "How do I manage cash flow effectively?",
      "What tools help with job scheduling?",
      "How do I handle warranty claims?",
      "What's the best way to track expenses?"
    ],
    technical: [
      "What safety checks should I include in every quote?",
      "How do I estimate time for complex installations?",
      "What's the latest on electrical regulations?",
      "How do I handle unexpected complications?",
      "What should I include in job documentation?",
      "How do I manage material ordering efficiently?",
      "What's best practice for cable routing?",
      "How do I ensure quality control on large jobs?"
    ]
  };

  try {
    // Randomly select one question from each category
    const categories = Object.keys(questionPools);
    const selectedQuestions = categories.map(category => {
      const questions = questionPools[category as keyof typeof questionPools];
      const randomIndex = Math.floor(Math.random() * questions.length);
      return {
        category,
        question: questions[randomIndex],
        icon: category === 'pricing' ? '💰' : 
              category === 'customer' ? '🤝' :
              category === 'business' ? '📋' : '🔧'
      };
    });

    // Shuffle and take 3 random questions
    const shuffled = selectedQuestions.sort(() => 0.5 - Math.random());
    const sampleQuestions = shuffled.slice(0, 3);

    return res.json({ 
      questions: sampleQuestions.map(q => ({
        text: q.question,
        icon: q.icon,
        category: q.category
      }))
    });

  } catch (error) {
    console.error('Sample questions error:', error);
    
    // Fallback to static questions if something goes wrong
    return res.json({
      questions: [
        { text: "How should I price a kitchen rewiring job?", icon: "💰", category: "pricing" },
        { text: "What are standard payment terms for electrical work?", icon: "📋", category: "business" },
        { text: "How do I handle a difficult customer?", icon: "🤝", category: "customer" }
      ]
    });
  }
}))

export default router 