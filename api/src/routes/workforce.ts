// @ts-nocheck
import express from 'express';
import { supabase } from '../config/supabase';
import { authenticateUser } from '../middleware/auth';
import crypto from 'crypto';

const router = express.Router();

// Test endpoint for debugging
router.get('/test-connection', (req, res) => {
  res.json({ message: 'Workforce route is working!' });
});

// Get all teams for the authenticated user
router.get('/', authenticateUser, async (req, res) => {
  if (!req.user) {
    void res.status(401).json({ error: 'Authentication required.' });
    return;
  }
  const userId = req.user.id;

  try {
    // Teams where user is member
    const { data: memberRows, error: memberError } = await supabase
      .from('workforce_members')
      .select(`
        workforce (
          id,
          name,
          owner_id
        )
      `)
      .eq('user_id', userId);

    if (memberError) throw memberError;

    // Teams where user is owner (in case not listed in workforce_members)
    const { data: ownerRows, error: ownerError } = await supabase
      .from('workforce')
      .select('id, name, owner_id')
      .eq('owner_id', userId);

    if (ownerError) throw ownerError;

    const teams = memberRows?.map(row => (row as any).workforce).filter(Boolean) || [];
    const allTeams = [...teams, ...(ownerRows || [])];

    // Remove duplicates by id
    const uniqueTeamsMap: Record<string, any> = {};
    allTeams.forEach((t) => {
      if (t && !uniqueTeamsMap[t.id]) {
        uniqueTeamsMap[t.id] = t;
      }
    });

    const uniqueTeams = Object.values(uniqueTeamsMap);

    res.json(uniqueTeams);
  } catch (error) {
    console.error('Error fetching user teams:', error);
    res.status(500).json({ error: 'An unexpected error occurred.' });
  }
});

// Get team members with their permissions
router.get('/:teamId/members', authenticateUser, async (req, res) => {
  if (!req.user) {
    void res.status(401).json({ error: 'Authentication required.' });
    return;
  }
  const { teamId } = req.params;
  const userId = req.user.id;

  try {
    // Verify user is a member of this team
    const { data: membership, error: membershipError } = await supabase
      .from('workforce_members')
      .select('role')
      .eq('workforce_id', teamId)
      .eq('user_id', userId)
      .single();

    if (membershipError || !membership) {
      void res.status(403).json({ error: 'Not authorized to access this team' });
      return;
    }

    // Get all team members with user details
    const { data: members, error } = await supabase
      .from('workforce_members')
      .select(`
        user_id,
        role,
        joined_at,
        can_manage_jobs,
        can_manage_clients,
        can_manage_invoices,
        can_manage_quotes,
        can_view_reports,
        can_manage_team,
        users (
          id,
          email,
          full_name,
          phone,
          company_name
        )
      `)
      .eq('workforce_id', teamId);

    if (error) throw error;

    res.json(members);
  } catch (error) {
    console.error('Error fetching team members:', error);
    res.status(500).json({ error: 'An unexpected error occurred.' });
  }
});

// Update team member permissions
router.put('/:teamId/members/:memberId/permissions', authenticateUser, async (req, res) => {
  if (!req.user) {
    void res.status(401).json({ error: 'Authentication required.' });
    return;
  }
  const { teamId, memberId } = req.params;
  const userId = req.user.id;
  const { can_manage_jobs, can_manage_clients, can_manage_invoices, can_manage_quotes, can_view_reports, can_manage_team } = req.body;

  try {
    // Check if user is owner or has team management permission  
    const { data: requestorMembership, error: requestorError } = await supabase
      .from('workforce_members')
      .select('role, can_manage_team')
      .eq('workforce_id', teamId)
      .eq('user_id', userId)
      .single();

    if (requestorError || !requestorMembership || 
        (requestorMembership.role !== 'owner' && !requestorMembership.can_manage_team)) {
      void res.status(403).json({ error: 'Not authorized to update team member permissions' });
      return;
    }

    // Don't allow non-owners to modify owner permissions
    const { data: targetMember, error: targetError } = await supabase
      .from('workforce_members')
      .select('role')
      .eq('workforce_id', teamId)
      .eq('user_id', memberId)
      .single();

    if (targetError || !targetMember) {
      void res.status(404).json({ error: 'Member not found' });
      return;
    }

    if (targetMember.role === 'owner' && requestorMembership.role !== 'owner') {
      void res.status(403).json({ error: 'Only owners can modify owner permissions' });
      return;
    }

    // Update the member's permissions
    const { error: updateError } = await supabase
      .from('workforce_members')
      .update({
        can_manage_jobs,
        can_manage_clients,
        can_manage_invoices,
        can_manage_quotes,
        can_view_reports,
        can_manage_team,
        updated_at: new Date().toISOString()
      })
      .eq('workforce_id', teamId)
      .eq('user_id', memberId);

    if (updateError) throw updateError;
    
    res.json({ message: 'Permissions updated successfully.' });
  } catch (error) {
    console.error('Error updating member permissions:', error);
    res.status(500).json({ error: 'An unexpected error occurred.' });
  }
});

// Get team settings
router.get('/:teamId/settings', authenticateUser, async (req, res) => {
  if (!req.user) {
    void res.status(401).json({ error: 'Authentication required.' });
    return;
  }
  const { teamId } = req.params;
  const userId = req.user.id;

  try {
    // First, verify the user is the owner of the team
    const { data: teamOwnerData, error: ownerError } = await supabase
      .from('workforce')
      .select('owner_id')
      .eq('id', teamId)
      .single();

    if (ownerError || !teamOwnerData) {
      res.status(404).json({ error: 'Team not found.' });
      return;
    }

    if (teamOwnerData.owner_id !== userId) {
      res.status(403).json({ error: 'You do not have permission to view these settings.' });
      return;
    }

    // Fetch the settings
    const { data, error } = await supabase
      .from('workforce')
      .select('name, auto_assign_jobs, require_job_approval, default_job_visibility, allow_invites')
      .eq('id', teamId)
      .single();

    if (error) throw error;
    
    res.json(data);
  } catch (error) {
    console.error('Error fetching team settings:', error);
    res.status(500).json({ error: 'An unexpected error occurred.' });
  }
});

// Update team settings
router.put('/:teamId/settings', authenticateUser, async (req, res) => {
  if (!req.user) {
    void res.status(401).json({ error: 'Authentication required.' });
    return;
  }
  const { teamId } = req.params;
  const userId = req.user.id;
  const updates = req.body;

  try {
    // Check if user is owner or has team management permission
    const { data: membership, error: membershipError } = await supabase
      .from('workforce_members')
      .select('role, can_manage_team')
      .eq('workforce_id', teamId)
      .eq('user_id', userId)
      .single();

    if (membershipError || !membership || (membership.role !== 'owner' && !membership.can_manage_team)) {
      void res.status(403).json({ error: 'Not authorized to update team settings' });
      return;
    }

    // Update the settings
    const { data, error } = await supabase
      .from('workforce')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', teamId)
      .select()
      .single();

    if (error) throw error;
    
    res.json({ message: 'Settings updated successfully.', data });
  } catch (error) {
    console.error('Error updating team settings:', error);
    res.status(500).json({ error: 'An unexpected error occurred.' });
  }
});

// Create new team
router.post('/', authenticateUser, async (req, res) => {
  if (!req.user) {
    void res.status(401).json({ error: 'Authentication required.' });
    return;
  }
  const userId = req.user.id;
  const { name } = req.body;
  const teamName = name && name.trim().length > 0 ? name.trim() : 'My Workforce';

  try {
    // Create team
    const { data: newTeam, error: teamError } = await supabase
      .from('workforce')
      .insert({
        name: teamName,
        owner_id: userId,
      })
      .select('id, name, owner_id')
      .single();

    if (teamError) throw teamError;

    // Add the owner as a team member with owner role
    const { error: memberError } = await supabase
      .from('workforce_members')
      .insert({
        user_id: userId,
        workforce_id: newTeam.id,
        role: 'owner',
        can_manage_jobs: true,
        can_manage_clients: true,
        can_manage_invoices: true,
        can_manage_quotes: true,
        can_view_reports: true,
        can_manage_team: true
      });

    void res.status(201).json(newTeam);
  } catch (error) {
    console.error('Error creating team:', error);
    void res.status(500).json({ error: 'Failed to create team' });
  }
});

// ==============================
// WORKFORCE INVITATION ENDPOINTS
// ==============================

// Send workforce invitation
router.post('/:teamId/invite', authenticateUser, async (req, res) => {
  if (!req.user) {
    void res.status(401).json({ error: 'Authentication required.' });
    return;
  }

  console.log('📧 Received invitation request:', {
    teamId: req.params.teamId,
      userId: req.user.id,
    body: req.body
  });


  const { teamId } = req.params;
  const userId = req.user.id;
  const { 
    email, 
    role = 'member', 
    personal_message = '', 
    invitation_method = 'email',
    permissions = {} 
  } = req.body;

  // Validate email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email || !emailRegex.test(email)) {
    void res.status(400).json({ error: 'Valid email address is required' });
    return;
  }

  try {
    // Check if user has permission to invite team members
    const { data: membership, error: membershipError } = await supabase
      .from('workforce_members')
      .select('role, can_manage_team')
      .eq('workforce_id', teamId)
      .eq('user_id', userId)
      .single();

    if (membershipError || !membership || 
        (membership.role !== 'owner' && !membership.can_manage_team)) {
      void res.status(403).json({ error: 'Not authorized to invite team members' });
      return;
    }

    console.log('📧 Membership check passed:', membership);

    // Check if email belongs to an existing member
    const { data: userByEmail } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (userByEmail) {
      const { data: existingMember } = await supabase
        .from('workforce_members')
        .select('user_id')
        .eq('workforce_id', teamId)
        .eq('user_id', userByEmail.id)
        .single();

      if (existingMember) {
        void res.status(409).json({ error: 'User is already a member of this workforce' });
        return;
      }
    }

    // Check for existing pending invitation
    const { data: existingInvitation } = await supabase
      .from('workforce_invitations')
      .select('id, status')
      .eq('workforce_id', teamId)
      .eq('email', email)
      .eq('status', 'pending')
      .single();

    if (existingInvitation) {
      void res.status(409).json({ error: 'Pending invitation already exists for this email' });
      return;
    }

    // Get team details for email
    const { data: teamData, error: teamError } = await supabase
      .from('workforce')
      .select('name, owner_id')
      .eq('id', teamId)
      .single();

    if (teamError || !teamData) {
      throw new Error('Team not found');
    }

    // Get team owner details
    const { data: teamOwnerData, error: ownerError } = await supabase
      .from('users')
      .select('full_name, email, company_name')
      .eq('id', teamData.owner_id)
      .single();

    if (ownerError || !teamOwnerData) {
      throw new Error('Team owner details not found');
    }

    // Get inviter details
    const { data: inviterData, error: inviterError } = await supabase
      .from('users')
      .select('full_name, email, company_name')
      .eq('id', userId)
      .single();

    if (inviterError || !inviterData) {
      throw new Error('Inviter details not found');
    }

    // Generate secure token
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

    // Extract permissions with defaults
    const {
      can_manage_jobs = false,
      can_manage_clients = false,
      can_manage_invoices = false,
      can_manage_quotes = false,
      can_view_reports = false,
      can_manage_team = false
    } = permissions;

    // Create invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('workforce_invitations')
      .insert({
        workforce_id: teamId,
        invited_by: userId,
        email,
        role,
        token,
        expires_at: expiresAt.toISOString(),
        personal_message,
        invitation_method,
        can_manage_jobs,
        can_manage_clients,
        can_manage_invoices,
        can_manage_quotes,
        can_view_reports,
        can_manage_team
      })
      .select()
      .single();


      console.log('📧 DB£ ::::: Sending invitation email...', );
      console.log('📧 DB£ ::::: Invitation Error::::', invitationError);
      console.log('📧 DB£ ::::: Invitation Status::::', invitation);

    if (invitationError) throw invitationError;

      console.log('📧 DB£ ::::: No Invitation Error::::');

    // Send invitation email using existing email service
    if (invitation_method === 'email') {

        //console.log('📧 DB£ ::::: Sending invitation email...');


      // Import the email service
      const { genericEmailService } = await import('../services/genericEmailService');
      
      // Generate invitation link (you may need to adjust this URL for your environment)
      const invitationLink = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/invite/${token}`;
      
      // Prepare email content
      const emailSubject = `You're invited to join ${teamData.name} on DeskBelt`;
      
      let emailMessage = `You've been invited to join "${teamData.name}" as a ${role} by ${inviterData.full_name || inviterData.email}.`;
      
      if (personal_message.trim()) {
        emailMessage += `\n\n"${personal_message.trim()}"`;
      }
      
      emailMessage += `\n\nClick the link below to accept your invitation:\n${invitationLink}`;
      emailMessage += `\n\nThis invitation will expire on ${expiresAt.toLocaleDateString()}.`;
      
      // Send the email
      const emailResult = await genericEmailService.sendEmail({
        recipientEmail: email,
        recipientName: email.split('@')[0], // Use email username as fallback name
        subject: emailSubject,
        message: emailMessage,
        emailType: 'custom',
        businessName: teamOwnerData.company_name || 'DeskBelt',
        senderName: inviterData.full_name || inviterData.email,
        userId: userId
      });

      if (!emailResult.success) {
        // If email fails, we should still return success for invitation creation
        // but log the email error
        console.error('Failed to send invitation email:', emailResult.error);
        
        // Log email failure
        await supabase
          .from('workforce_invitation_logs')
          .insert({
            invitation_id: invitation.id,
            action: 'email_failed',
            details: {
              error: emailResult.error,
              email_id: emailResult.emailId
            },
            user_id: userId
          });
      } else {
        // Log successful email send
        await supabase
          .from('workforce_invitation_logs')
          .insert({
            invitation_id: invitation.id,
            action: 'email_sent',
            details: {
              email_id: emailResult.emailId,
              sent_to: email
            },
            user_id: userId
          });
      }
    }

    // Log invitation creation
    await supabase
      .from('workforce_invitation_logs')
      .insert({
        invitation_id: invitation.id,
        action: 'created',
        details: {
          invited_by: userId,
          method: invitation_method,
          expires_at: expiresAt.toISOString()
        },
        user_id: userId
      });

    res.status(201).json({
      message: 'Invitation created and sent successfully',
      invitation: {
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        status: invitation.status,
        expires_at: invitation.expires_at,
        token: invitation.token // Include for frontend to generate invitation links
      }
    });
  } catch (error) {
    console.error('Error creating invitation:', error);
    res.status(500).json({ error: 'Failed to create invitation' });
  }
});

// Get workforce invitations
router.get('/:teamId/invitations', authenticateUser, async (req, res) => {
  if (!req.user) {
    void res.status(401).json({ error: 'Authentication required.' });
    return;
  }
  
  const { teamId } = req.params;
  const userId = req.user.id;

  try {
    // Verify user has permission to view invitations
    const { data: membership, error: membershipError } = await supabase
      .from('workforce_members')
      .select('role, can_manage_team')
      .eq('workforce_id', teamId)
      .eq('user_id', userId)
      .single();

    console.log('📧 Membership check:', {
        membership,
        membershipError
    });

    if (membershipError || !membership || 
        (membership.role !== 'owner' && !membership.can_manage_team)) {
      void res.status(403).json({ error: 'Not authorized to view invitations' });
      return;
    }

    // Get all invitations for this workforce
    const { data: invitations, error } = await supabase
      .from('workforce_invitations')
      .select(`
        id,
        email,
        role,
        status,
        sent_at,
        expires_at,
        personal_message,
        invitation_method,
        can_manage_jobs,
        can_manage_clients,
        can_manage_invoices,
        can_manage_quotes,
        can_view_reports,
        can_manage_team,
        invited_by
      `)
      .eq('workforce_id', teamId)
      .order('sent_at', { ascending: false });

    if (error) throw error;

    // Enhance invitations with inviter details
    const enhancedInvitations = [];
    for (const invitation of invitations || []) {
      let inviterDetails = null;
      
      // Try to get inviter details from auth.users
      try {
        const { data: inviter } = await supabase
          .from('users')
          .select('full_name, email')
          .eq('id', invitation.invited_by)
          .single();
        
        if (inviter) {
          inviterDetails = inviter;
        }
      } catch (inviterError) {
        console.log('Could not fetch inviter details:', inviterError);
      }

      enhancedInvitations.push({
        ...invitation,
        invited_by_user: inviterDetails
      });
    }

    res.json(enhancedInvitations);
  } catch (error) {
    console.error('Error fetching invitations:', error);
    res.status(500).json({ error: 'Failed to fetch invitations' });
  }
});

// Resend invitation
router.post('/:teamId/invitations/:invitationId/resend', authenticateUser, async (req, res) => {

    console.log('📧 DB3::::::::::::: RESEND: Route hit - teamId:', req.params.teamId, 'invitationId:', req.params.invitationId);


    if (!req.user) {
        void res.status(401).json({ error: 'Authentication required.' });
        return;
    }
  
  const { teamId, invitationId } = req.params;
  const userId = req.user.id;
  
  console.log('📧 RESEND: Route hit - teamId:', teamId, 'invitationId:', invitationId);
  console.log('📧 RESEND: User authenticated, userId:', userId);

  try {
    // Verify user has permission
    const { data: membership, error: membershipError } = await supabase
      .from('workforce_members')
      .select('role, can_manage_team')
      .eq('workforce_id', teamId)
      .eq('user_id', userId)
      .single();

    if (membershipError || !membership || 
        (membership.role !== 'owner' && !membership.can_manage_team)) {
      void res.status(403).json({ error: 'Not authorized to resend invitations' });
      return;
    }

    // Get invitation details
    const { data: invitation, error: invitationError } = await supabase
      .from('workforce_invitations')
      .select('*')
      .eq('id', invitationId)
      .eq('workforce_id', teamId)
      .single();

    if (invitationError || !invitation) {
      void res.status(404).json({ error: 'Invitation not found' });
      return;
    }

    if (invitation.status !== 'pending') {
      void res.status(400).json({ error: 'Can only resend pending invitations' });
      return;
    }

    // Generate new token and extend expiry
    const newToken = crypto.randomBytes(32).toString('hex');
    const newExpiresAt = new Date();
    newExpiresAt.setDate(newExpiresAt.getDate() + 7);

    // Update invitation
    const { error: updateError } = await supabase
      .from('workforce_invitations')
      .update({
        token: newToken,
        expires_at: newExpiresAt.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', invitationId);

    if (updateError) throw updateError;

    console.log('📧 RESEND: Checking invitation method:', invitation.invitation_method);
    
    // Send invitation email if method is email
    if (invitation.invitation_method === 'email') {
      console.log('📧 RESEND: Starting email sending process...');
      // Get team and inviter details for email
      const { data: teamData, error: teamError } = await supabase
        .from('workforce')
        .select(`
          name,
          owner_id
        `)
        .eq('id', teamId)
        .single();

      let ownerData = null;
      if (teamData && !teamError) {
        const { data: owner, error: ownerError } = await supabase
          .from('users')
          .select('full_name, email, company_name')
          .eq('id', teamData.owner_id)
          .single();
        
        if (!ownerError && owner) {
          ownerData = owner;
        }
      }

      const { data: inviterData, error: inviterError } = await supabase
        .from('users')
        .select('full_name, email, company_name')
        .eq('id', userId)
        .single();

      console.log('📧 RESEND: Team data retrieved:', !!teamData, 'Error:', !!teamError);
      if (teamError) console.log('📧 RESEND: Team error details:', teamError);
      console.log('📧 RESEND: Owner data retrieved:', !!ownerData);
      console.log('📧 RESEND: Inviter data retrieved:', !!inviterData, 'Error:', !!inviterError);
      if (inviterError) console.log('📧 RESEND: Inviter error details:', inviterError);

      console.log('📧 RESEND: Data validation - teamError:', !!teamError, 'inviterError:', !!inviterError, 'teamData:', !!teamData, 'inviterData:', !!inviterData, 'ownerData:', !!ownerData);

      if (!teamError && !inviterError && teamData && inviterData && ownerData) {
        console.log('📧 RESEND: Data validation passed, sending email...');
        try {
          // Import the email service
          const { genericEmailService } = await import('../services/genericEmailService');
          console.log('📧 RESEND: Email service imported successfully');
          
          // Generate invitation link with new token
          const invitationLink = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/invite/${newToken}`;
          
          // Prepare email content
          const emailSubject = `You're invited to join ${teamData.name} on DeskBelt (Resent)`;
          
          let emailMessage = `You've been invited to join "${teamData.name}" as a ${invitation.role} by ${inviterData.full_name || inviterData.email}.`;
          
          if (invitation.personal_message && invitation.personal_message.trim()) {
            emailMessage += `\n\n"${invitation.personal_message.trim()}"`;
          }
          
          emailMessage += `\n\nClick the link below to accept your invitation:\n${invitationLink}`;
          emailMessage += `\n\nThis invitation will expire on ${newExpiresAt.toLocaleDateString()}.`;
          emailMessage += `\n\nNote: This is a resent invitation with a new link.`;
          
          console.log('📧 RESEND: Sending email to:', invitation.email);
          console.log('📧 RESEND: Subject:', emailSubject);
          console.log('📧 RESEND: Link:', invitationLink);
          console.log('📧 RESEND: Business name:', ownerData.company_name || 'DeskBelt');
          console.log('📧 RESEND: Sender name:', inviterData.full_name || inviterData.email);
          
          // Send the email
          const emailResult = await genericEmailService.sendEmail({
            recipientEmail: invitation.email,
            recipientName: invitation.email.split('@')[0], // Use email username as fallback name
            subject: emailSubject,
            message: emailMessage,
            emailType: 'custom',
            businessName: ownerData.company_name || 'DeskBelt',
            senderName: inviterData.full_name || inviterData.email,
            userId: userId
          });
          
          console.log('📧 RESEND: Email send result:', emailResult.success, emailResult.message);

          // Log email result
          if (!emailResult.success) {
            console.error('Failed to send resend invitation email:', emailResult.error);
            
            // Log email failure
            await supabase
              .from('workforce_invitation_logs')
              .insert({
                invitation_id: invitationId,
                action: 'email_failed',
                details: {
                  error: emailResult.error,
                  email_id: emailResult.emailId,
                  action_type: 'resend'
                },
                user_id: userId
              });
          } else {
            // Log successful email send
            await supabase
              .from('workforce_invitation_logs')
              .insert({
                invitation_id: invitationId,
                action: 'email_sent',
                details: {
                  email_id: emailResult.emailId,
                  sent_to: invitation.email,
                  action_type: 'resend'
                },
                user_id: userId
              });
          }
        } catch (emailError) {
          console.error('📧 RESEND: Error sending resend invitation email:', emailError);
          
          // Log email error
          await supabase
            .from('workforce_invitation_logs')
            .insert({
              invitation_id: invitationId,
              action: 'email_failed',
              details: {
                error: emailError.message || 'Unknown email error',
                action_type: 'resend'
              },
              user_id: userId
            });
        }
      } else {
        console.log('📧 RESEND: Data validation failed - teamError:', !!teamError, 'inviterError:', !!inviterError, 'ownerData:', !!ownerData);
        console.log('📧 RESEND: Email skipped due to missing required data');
      }
    } else {
      console.log('📧 RESEND: Invitation method is not email:', invitation.invitation_method);
    }

    // Log resend action
    await supabase
      .from('workforce_invitation_logs')
      .insert({
        invitation_id: invitationId,
        action: 'resent',
        details: {
          resent_by: userId,
          new_expires_at: newExpiresAt.toISOString(),
          new_token: newToken
        },
        user_id: userId
      });

    res.json({
      message: 'Invitation resent successfully',
      token: newToken,
      expires_at: newExpiresAt.toISOString()
    });
  } catch (error) {
    console.error('📧 RESEND: Error resending invitation:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    res.status(500).json({ 
      error: 'Failed to resend invitation',
      message: errorMessage
    });
  }
});

// Cancel invitation
router.delete('/:teamId/invitations/:invitationId', authenticateUser, async (req, res) => {
  if (!req.user) {
    void res.status(401).json({ error: 'Authentication required.' });
    return;
  }
  
  const { teamId, invitationId } = req.params;
  const userId = req.user.id;

  try {
    // Verify user has permission
    const { data: membership, error: membershipError } = await supabase
      .from('workforce_members')
      .select('role, can_manage_team')
      .eq('workforce_id', teamId)
      .eq('user_id', userId)
      .single();

    if (membershipError || !membership || 
        (membership.role !== 'owner' && !membership.can_manage_team)) {
      void res.status(403).json({ error: 'Not authorized to cancel invitations' });
      return;
    }

    // Update invitation status to cancelled
    const { error: updateError } = await supabase
      .from('workforce_invitations')
      .update({
        status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('id', invitationId)
      .eq('workforce_id', teamId)
      .eq('status', 'pending');

    if (updateError) throw updateError;

    // Log cancellation
    await supabase
      .from('workforce_invitation_logs')
      .insert({
        invitation_id: invitationId,
        action: 'cancelled',
        details: {
          cancelled_by: userId,
          cancelled_at: new Date().toISOString()
        },
        user_id: userId
      });

    res.json({ message: 'Invitation cancelled successfully' });
  } catch (error) {
    console.error('Error cancelling invitation:', error);
    res.status(500).json({ error: 'Failed to cancel invitation' });
  }
});

// Accept invitation (public endpoint - no auth required)
router.post('/invitations/:token/accept', async (req, res) => {
  const { token } = req.params;
  const { user_id } = req.body; // User ID from registration/login

  if (!user_id) {
    void res.status(400).json({ error: 'User ID is required' });
    return;
  }

  try {
    // Find invitation by token
    const { data: invitation, error: invitationError } = await supabase
      .from('workforce_invitations')
      .select(`
        id,
        workforce_id,
        email,
        role,
        status,
        expires_at,
        can_manage_jobs,
        can_manage_clients,
        can_manage_invoices,
        can_manage_quotes,
        can_view_reports,
        can_manage_team
      `)
      .eq('token', token)
      .single();

    if (invitationError || !invitation) {
      void res.status(404).json({ error: 'Invalid invitation token' });
      return;
    }

    if (invitation.status !== 'pending') {
      void res.status(400).json({ error: 'Invitation is no longer valid' });
      return;
    }

    if (new Date(invitation.expires_at) < new Date()) {
      // Mark as expired
      await supabase
        .from('workforce_invitations')
        .update({ status: 'expired' })
        .eq('id', invitation.id);

      void res.status(400).json({ error: 'Invitation has expired' });
      return;
    }

    // Verify user email matches invitation
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', user_id)
      .single();

    if (userError || !user || user.email !== invitation.email) {
      void res.status(400).json({ error: 'Email address does not match invitation' });
      return;
    }

    // Check if user is already a member
    const { data: existingMember } = await supabase
      .from('workforce_members')
      .select('user_id')
      .eq('workforce_id', invitation.workforce_id)
      .eq('user_id', user_id)
      .single();

    if (existingMember) {
      void res.status(409).json({ error: 'User is already a member of this workforce' });
      return;
    }

    // Add user to workforce
    const { error: memberError } = await supabase
      .from('workforce_members')
      .insert({
        user_id,
        workforce_id: invitation.workforce_id,
        role: invitation.role,
        can_manage_jobs: invitation.can_manage_jobs,
        can_manage_clients: invitation.can_manage_clients,
        can_manage_invoices: invitation.can_manage_invoices,
        can_manage_quotes: invitation.can_manage_quotes,
        can_view_reports: invitation.can_view_reports,
        can_manage_team: invitation.can_manage_team
      });

    if (memberError) throw memberError;

    // Update invitation status
    const { error: statusError } = await supabase
      .from('workforce_invitations')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString(),
        accepted_by: user_id,
        updated_at: new Date().toISOString()
      })
      .eq('id', invitation.id);

    if (statusError) throw statusError;

    // Log acceptance
    await supabase
      .from('workforce_invitation_logs')
      .insert({
        invitation_id: invitation.id,
        action: 'accepted',
        details: {
          accepted_by: user_id,
          accepted_at: new Date().toISOString()
        },
        user_id: user_id
      });

    res.json({ 
      message: 'Invitation accepted successfully',
      workforce_id: invitation.workforce_id
    });
  } catch (error) {
    console.error('Error accepting invitation:', error);
    res.status(500).json({ error: 'Failed to accept invitation' });
  }
});

// Get invitation details by token (for acceptance page)
router.get('/invitations/:token', async (req, res) => {
  const { token } = req.params;

  try {
    const { data: invitation, error } = await supabase
      .from('workforce_invitations')
      .select(`
        id,
        email,
        role,
        status,
        expires_at,
        personal_message,
        workforce:workforce(
          name,
          owner_id
        )
      `)
      .eq('token', token)
      .single();

    // Get owner details separately if invitation exists
    let ownerData = null;
    if (invitation && !error && invitation.workforce) {
      const { data: owner } = await supabase
        .from('users')
        .select('full_name, email')
        .eq('id', invitation.workforce.owner_id)
        .single();
      
      if (owner) {
        ownerData = owner;
      }
    }

    if (error || !invitation) {
      void res.status(404).json({ error: 'Invalid invitation token' });
      return;
    }

    if (invitation.status !== 'pending') {
      void res.status(400).json({ error: 'Invitation is no longer valid' });
      return;
    }

    if (new Date(invitation.expires_at) < new Date()) {
      void res.status(400).json({ error: 'Invitation has expired' });
      return;
    }

    res.json({
      email: invitation.email,
      role: invitation.role,
      expires_at: invitation.expires_at,
      personal_message: invitation.personal_message,
      workforce: {
        ...invitation.workforce,
        owner: ownerData
      }
    });
  } catch (error) {
    console.error('Error fetching invitation:', error);
    res.status(500).json({ error: 'Failed to fetch invitation details' });
  }
});

export default router; 