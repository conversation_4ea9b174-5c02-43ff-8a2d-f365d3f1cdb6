import express from 'express';
import { optionalAuth } from '../middleware/auth';
import { supabase } from '../config/supabase';

const router = express.Router();

// Apply optional authentication middleware so req.user is available in dev
router.use(optionalAuth);

// Async handler wrapper
const asyncHandler = (fn: any) => (req: any, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// GET /api/chat/messages - Get chat messages for the current user
router.get('/messages', async (req: any, res: any) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { data: messages, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching chat messages:', error);
      return res.status(500).json({ error: 'Failed to fetch messages' });
    }

    res.json(messages || []);
  } catch (error) {
    console.error('Chat messages error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/chat/messages - Save a new chat message
router.post('/messages', async (req: any, res: any) => {
  try {
    const userId = req.user?.id;
    const { message, is_from_user, context } = req.body;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    if (!message || typeof message !== 'string') {
      return res.status(400).json({ error: 'Message is required' });
    }

    const { data, error } = await supabase
      .from('chat_messages')
      .insert({
        user_id: userId,
        message: message.trim(),
        is_from_user: !!is_from_user,
        context: context || {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error saving chat message:', error);
      return res.status(500).json({ error: 'Failed to save message' });
    }

    res.json(data);
  } catch (error) {
    console.error('Save message error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/chat/messages - Clear all chat messages for the current user
router.delete('/messages', async (req: any, res: any) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { error } = await supabase
      .from('chat_messages')
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error('Error clearing chat messages:', error);
      return res.status(500).json({ error: 'Failed to clear messages' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Clear messages error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/chat/archive - Archive current chat
router.post('/archive', async (req: any, res: any) => {
  try {
    const userId = req.user?.id;
    const { title, preview, messages } = req.body;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    if (!title || !messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: 'Title and messages are required' });
    }

    // First create the archived chat
    const { data: archivedChat, error: archiveError } = await supabase
      .from('archived_chats')
      .insert({
        user_id: userId,
        title: title.trim(),
        preview: preview || 'Business consultation',
        message_count: messages.length,
        archived_at: new Date().toISOString()
      })
      .select()
      .single();

    if (archiveError) {
      console.error('Error creating archived chat:', archiveError);
      return res.status(500).json({ error: 'Failed to archive chat' });
    }

    // Then save all the messages to the archive
    const archiveMessages = messages.map((msg: any) => ({
      archived_chat_id: archivedChat.id,
      message: msg.message,
      is_from_user: msg.is_from_user,
      created_at: msg.created_at
    }));

    const { error: messagesError } = await supabase
      .from('archived_chat_messages')
      .insert(archiveMessages);

    if (messagesError) {
      console.error('Error saving archived messages:', messagesError);
      // Clean up the archived chat if message saving fails
      await supabase.from('archived_chats').delete().eq('id', archivedChat.id);
      return res.status(500).json({ error: 'Failed to save archived messages' });
    }

    // Clear current chat messages
    await supabase
      .from('chat_messages')
      .delete()
      .eq('user_id', userId);

    res.json({ success: true, archived_chat: archivedChat });
  } catch (error) {
    console.error('Archive chat error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/chat/archived - Get archived chats for the current user
router.get('/archived', async (req: any, res: any) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { data: archivedChats, error } = await supabase
      .from('archived_chats')
      .select('*')
      .eq('user_id', userId)
      .order('archived_at', { ascending: false });

    if (error) {
      console.error('Error fetching archived chats:', error);
      return res.status(500).json({ error: 'Failed to fetch archived chats' });
    }

    res.json(archivedChats || []);
  } catch (error) {
    console.error('Archived chats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PATCH /api/chat/archived/:id - Update archive title
router.patch('/archived/:id', async (req: any, res: any) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;
    const { title } = req.body;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    if (!title || typeof title !== 'string') {
      return res.status(400).json({ error: 'Title is required' });
    }

    const { data, error } = await supabase
      .from('archived_chats')
      .update({ title: title.trim() })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating archive title:', error);
      return res.status(500).json({ error: 'Failed to update title' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Archive not found' });
    }

    res.json(data);
  } catch (error) {
    console.error('Update archive title error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/chat/archived/:id/restore - Restore archived chat to current
router.post('/archived/:id/restore', async (req: any, res: any) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Get the archived messages
    const { data: messages, error: messagesError } = await supabase
      .from('archived_chat_messages')
      .select('*')
      .eq('archived_chat_id', id)
      .order('created_at', { ascending: true });

    if (messagesError) {
      console.error('Error fetching archived messages:', messagesError);
      return res.status(500).json({ error: 'Failed to fetch archived messages' });
    }

    // Clear current chat first
    await supabase
      .from('chat_messages')
      .delete()
      .eq('user_id', userId);

    // Restore messages to current chat
    const currentMessages = messages.map((msg: any) => ({
      user_id: userId,
      message: msg.message,
      is_from_user: msg.is_from_user,
      context: {},
      created_at: msg.created_at,
      updated_at: new Date().toISOString()
    }));

    const { error: restoreError } = await supabase
      .from('chat_messages')
      .insert(currentMessages);

    if (restoreError) {
      console.error('Error restoring messages:', restoreError);
      return res.status(500).json({ error: 'Failed to restore messages' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Restore archive error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/chat/archived/:id - Delete archived chat permanently
router.delete('/archived/:id', async (req: any, res: any) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Delete archived messages first
    const { error: messagesError } = await supabase
      .from('archived_chat_messages')
      .delete()
      .eq('archived_chat_id', id);

    if (messagesError) {
      console.error('Error deleting archived messages:', messagesError);
      return res.status(500).json({ error: 'Failed to delete archived messages' });
    }

    // Delete the archived chat
    const { error: chatError } = await supabase
      .from('archived_chats')
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (chatError) {
      console.error('Error deleting archived chat:', chatError);
      return res.status(500).json({ error: 'Failed to delete archived chat' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Delete archive error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router; 