// @ts-nocheck
import express from 'express'
import { supabase, createUserClient } from '../config/supabase'
import { authenticateUser } from '../middleware/auth'

const router = express.Router()

// GET /api/jobs - List jobs from Supabase database
router.get('/', authenticateUser, async (req, res) => {
  try {
    const { search, status, limit = 20, offset = 0 } = req.query
    

    // Use explicit user filtering (like notifications route) - more reliable than RLS
    let query = supabase
      .from('jobs')
      .select(`
        id,
        title,
        description,
        status,
        scheduled_at,
        scheduled_start_time,
        scheduled_end_time,
        estimated_duration,
        scheduling_notes,
        created_at,
        updated_at,
        client_id,
        clients(
          id,
          name,
          phone,
          email
        )
      `,
      { count: 'exact' })
      .eq('created_by', req.user.id)  // Explicit user filtering
      .order('created_at', { ascending: false })

    // Filter by search term
    if (search) {
      const searchTerm = search as string
      // Search in job title, description, and client name
      // For client name, we need to search in the joined clients table differently
      query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
      
      // Additional filter for client name search - need to use a separate query approach
      if (searchTerm) {
        // Get client IDs that match the search term
        const { data: matchingClients } = await supabase
          .from('clients')
          .select('id')
          .ilike('name', `%${searchTerm}%`)
          .eq('created_by', req.user.id)
        
        if (matchingClients && matchingClients.length > 0) {
          const clientIds = matchingClients.map(client => client.id)
          // Modify the original query to also include jobs with matching client IDs
          const originalQuery = query
          const clientQuery = supabase
            .from('jobs')
            .select(`
              id,
              title,
              description,
              status,
              scheduled_at,
              scheduled_start_time,
              scheduled_end_time,
              estimated_duration,
              scheduling_notes,
              created_at,
              updated_at,
              client_id,
              clients(
                id,
                name,
                phone,
                email
              )
            `,
            { count: 'exact' })
            .eq('created_by', req.user.id)
            .in('client_id', clientIds)
            .order('created_at', { ascending: false })
          
          // Apply the same filters as the original query
          if (status) {
            const statusFilters = Array.isArray(status) ? status : [status]
            clientQuery = clientQuery.in('status', statusFilters)
          } else {
            clientQuery = clientQuery.neq('status', 'archived')
          }
          
          // Execute both queries and combine results
          const [originalResults, clientResults] = await Promise.all([
            originalQuery.range(offsetNum, offsetNum + limitNum - 1),
            clientQuery.range(offsetNum, offsetNum + limitNum - 1)
          ])
          
          if (originalResults.error && clientResults.error) {
            throw originalResults.error
          }
          
          // Combine and deduplicate results
          const allJobs = [
            ...(originalResults.data || []),
            ...(clientResults.data || [])
          ]
          
          // Remove duplicates based on job ID
          const uniqueJobs = allJobs.reduce((acc, job) => {
            if (!acc.find(existing => existing.id === job.id)) {
              acc.push(job)
            }
            return acc
          }, [])
          
          // Sort by created_at descending
          uniqueJobs.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          
          // Truncate to requested limit
          const jobs = uniqueJobs.slice(0, limitNum)
          
          // Transform and return early
          const jobsWithClients = jobs?.map(job => ({
            id: job.id,
            title: job.title,
            client: job.clients ? {
              id: job.clients.id,
              name: job.clients.name,
              phone: job.clients.phone,
              email: job.clients.email,
            } : {
              id: job.client_id,
              name: '',
              phone: '',
              email: '',
            },
            status: job.status,
            scheduled_at: job.scheduled_at,
            scheduled_start_time: job.scheduled_start_time,
            scheduled_end_time: job.scheduled_end_time,
            estimated_duration: job.estimated_duration,
            scheduling_notes: job.scheduling_notes,
            created_at: job.created_at,
            updated_at: job.updated_at,
            assigned_to: null,
            hasNotes: false,
          })) || []

          return res.json({
            jobs: jobsWithClients,
            total: uniqueJobs.length,
            hasMore: uniqueJobs.length > limitNum
          })
        }
      }
    }

    // Filter by status
    if (status) {
      const statusFilters = Array.isArray(status) ? status : [status]
      query = query.in('status', statusFilters)
    } else {
      // By default, exclude archived jobs unless specifically requested
      query = query.neq('status', 'archived')
    }

    // Pagination
    const limitNum = parseInt(limit as string)
    const offsetNum = parseInt(offset as string)
    query = query.range(offsetNum, offsetNum + limitNum - 1)

    const { data: jobs, error, count } = await query

    if (error) {
      console.error('Supabase error fetching jobs:', error)
      return res.status(500).json({ error: 'Failed to fetch jobs from database', details: error.message, hint: error.hint || null })
    }

    // Transform to frontend format - simplified without joins for now
    const jobsWithClients = jobs?.map(job => ({
      id: job.id,
      title: job.title,
      client: job.clients ? {
        id: job.clients.id,
        name: job.clients.name,
        phone: job.clients.phone,
        email: job.clients.email,
      } : {
        id: job.client_id,
        name: '',
        phone: '',
        email: '',
      },
      status: job.status,
      scheduled_at: job.scheduled_at,
      scheduled_start_time: job.scheduled_start_time,
      scheduled_end_time: job.scheduled_end_time,
      estimated_duration: job.estimated_duration,
      scheduling_notes: job.scheduling_notes,
      created_at: job.created_at,
      updated_at: job.updated_at,
      assigned_to: null,
      hasNotes: false,
    })) || []

    // Get total count for pagination (with explicit user filtering)
    const { count: totalCount } = await supabase
      .from('jobs')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', req.user.id)

    res.json({
      jobs: jobsWithClients,
      total: totalCount || 0,
      hasMore: offsetNum + limitNum < (totalCount || 0)
    })

  } catch (error) {
    console.error('Error fetching jobs:', error)
    res.status(500).json({ error: 'Failed to fetch jobs' })
  }
})

// POST /api/jobs - Create new job in Supabase database
router.post('/', authenticateUser, async (req, res) => {
  try {
    console.log('📝 Received job creation request:', {
      body: req.body,
      headers: req.headers,
      url: req.url
    })
    
    const { 
      title, 
      client_id, 
      description, 
      scheduled_at, 
      scheduled_start_time,
      scheduled_end_time,
      estimated_duration,
      scheduling_notes,
      workforce_id 
    } = req.body

    if (!title || !client_id) {
      console.log('❌ Validation failed - missing required fields:', { title, client_id })
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'title and client_id are required'
      })
    }

    const finalClientId = client_id;

    // Verify client exists and belongs to current user
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .select('id')
      .eq('id', finalClientId)
      .eq('created_by', req.user.id)

    if (clientError || !client) {
      console.log('❌ Client not found:', finalClientId)
      return res.status(400).json({
        error: 'Invalid client',
        details: 'Client ID does not exist'
      })
    }

    // Enforce per-user job limits (if set)
    const userId = req.user?.id || 'anonymous'

    // Fetch limits
    const { data: limits } = await supabase
      .from('user_limits')
      .select('jobs_per_week, jobs_per_month')
      .eq('user_id', userId)
      .single()

    if (limits) {
      const now = new Date()

      // Weekly limit (rolling 7 days)
      if (limits.jobs_per_week) {
        const since = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString()
        const { count } = await supabase
          .from('jobs')
          .select('*', { count: 'exact', head: true })
          .eq('created_by', userId)
          .gte('created_at', since)
        if ((count || 0) >= limits.jobs_per_week) {
          return res.status(403).json({ error: `Weekly job limit (${limits.jobs_per_week}) reached` })
        }
      }

      // Monthly limit (calendar month)
      if (limits.jobs_per_month) {
        const firstOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString()
        const { count } = await supabase
          .from('jobs')
          .select('*', { count: 'exact', head: true })
          .eq('created_by', userId)
          .gte('created_at', firstOfMonth)
        if ((count || 0) >= limits.jobs_per_month) {
          return res.status(403).json({ error: `Monthly job limit (${limits.jobs_per_month}) reached` })
        }
      }
    }

    // Create new job in Supabase
    const { data: newJob, error } = await supabase
      .from('jobs')
      .insert({
        title: title.trim(),
        description: description?.trim() || null,
        client_id: finalClientId,
        created_by: userId,
        workforce_id: workforce_id || null,
        scheduled_at: scheduled_at || null,
        scheduled_start_time: scheduled_start_time || null,
        scheduled_end_time: scheduled_end_time || null,
        estimated_duration: estimated_duration || null,
        scheduling_notes: scheduling_notes?.trim() || null,
        status: 'new'
      })
      .select()
      .single()

    if (error) {
      console.error('Supabase error creating job:', error)
      return res.status(500).json({ 
        error: 'Failed to create job in database',
        details: error.message 
      })
    }

    console.log('✅ Created new job in Supabase:', newJob)

    res.status(201).json({
      id: newJob.id,
      message: 'Job created successfully'
    })

  } catch (error) {
    console.error('Error creating job:', error)
    res.status(500).json({ error: 'Failed to create job' })
  }
})

// PUT /api/jobs/:id - Update job in Supabase
router.put('/:id', authenticateUser, async (req, res) => {
  try {
    console.log('📝 Received job update request:', {
      id: req.params.id,
      body: req.body
    })
    
    const { 
      title, 
      client_id, 
      description, 
      scheduled_at, 
      scheduled_start_time,
      scheduled_end_time,
      estimated_duration,
      scheduling_notes,
      workforce_id, 
      status 
    } = req.body

    // Verify job exists and belongs to current user
    const { data: existingJob, error: fetchError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (fetchError || !existingJob) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist'
      })
    }

    // Update job in Supabase with explicit user check
    const { data: updatedJob, error } = await supabase
      .from('jobs')
      .update({
        title: title?.trim(),
        description: description?.trim(),
        client_id,
        workforce_id,
        scheduled_at,
        scheduled_start_time,
        scheduled_end_time,
        estimated_duration,
        scheduling_notes: scheduling_notes?.trim(),
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .select()
      .single()

    if (error) {
      console.error('Supabase error updating job:', error)
      return res.status(500).json({ 
        error: 'Failed to update job in database',
        details: error.message 
      })
    }

    console.log('✅ Updated job in Supabase:', updatedJob)

    res.json({
      id: updatedJob.id,
      message: 'Job updated successfully'
    })

  } catch (error) {
    console.error('Error updating job:', error)
    res.status(500).json({ error: 'Failed to update job' })
  }
})

// PATCH /api/jobs/:id - Partially update job (e.g., status only)
router.patch('/:id', authenticateUser, async (req, res) => {
  try {
    console.log('📝 Received job patch request:', {
      id: req.params.id,
      body: req.body
    })
    
    // Only allow certain fields for patch operations
    const allowedFields = [
      'status', 
      'scheduled_at', 
      'scheduled_start_time',
      'scheduled_end_time',
      'estimated_duration',
      'scheduling_notes',
      'description', 
      'title'
    ]
    const updateData = {}
    
    // Build update object with only allowed fields
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key)) {
        updateData[key] = req.body[key]
      }
    })
    
    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        error: 'No valid fields to update',
        details: `Allowed fields: ${allowedFields.join(', ')}`
      })
    }

    // Verify job exists and belongs to current user
    const { data: existingJob, error: fetchError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (fetchError || !existingJob) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist'
      })
    }

    // Add updated timestamp
    updateData.updated_at = new Date().toISOString()

    // Update job in Supabase with explicit user check
    const { data: updatedJob, error } = await supabase
      .from('jobs')
      .update(updateData)
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .select()
      .single()

    if (error) {
      console.error('Supabase error patching job:', error)
      return res.status(500).json({ 
        error: 'Failed to update job in database',
        details: error.message 
      })
    }

    console.log('✅ Patched job in Supabase:', updatedJob)

    res.json({
      id: updatedJob.id,
      message: 'Job updated successfully'
    })

  } catch (error) {
    console.error('Error patching job:', error)
    res.status(500).json({ error: 'Failed to update job' })
  }
})

// GET /api/jobs/:id - Get single job from Supabase
router.get('/:id', authenticateUser, async (req, res) => {
  try {
    const { data: job, error } = await supabase
      .from('jobs')
      .select(`
        id,
        title,
        description,
        status,
        scheduled_at,
        created_at,
        updated_at,
        client_id,
        workforce_id,
        created_by,
        clients(
          id,
          name,
          phone,
          email,
          address
        )
      `)
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (error || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist'
      })
    }

    // Transform to frontend format
    const jobWithClient = {
      id: job.id,
      title: job.title,
      description: job.description,
      client: job.clients ? {
        id: job.clients.id,
        name: job.clients.name,
        phone: job.clients.phone,
        email: job.clients.email,
        address: job.clients.address
      } : {
        id: job.client_id,
        name: '',
        phone: '',
        email: '',
        address: ''
      },
      status: job.status,
      scheduled_at: job.scheduled_at,
      created_at: job.created_at,
      updated_at: job.updated_at,
      assigned_to: null,
      workforce_id: job.workforce_id,
      created_by: job.created_by
    }

    res.json(jobWithClient)

  } catch (error) {
    console.error('Error fetching job:', error)
    res.status(500).json({ error: 'Failed to fetch job' })
  }
})

// DELETE /api/jobs/:id - Delete job from Supabase
router.delete('/:id', authenticateUser, async (req, res) => {
  try {
    const { error } = await supabase
      .from('jobs')
      .delete()
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)

    if (error) {
      console.error('Supabase error deleting job:', error)
      return res.status(500).json({ 
        error: 'Failed to delete job from database',
        details: error.message 
      })
    }

    console.log('✅ Deleted job from Supabase:', req.params.id)

    res.json({
      message: 'Job deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting job:', error)
    res.status(500).json({ error: 'Failed to delete job' })
  }
})

// POST /api/jobs/:id/quotes - Create quote for job
router.post('/:id/quotes', authenticateUser, async (req, res) => {
  try {
    console.log('📝 Received quote creation request:', {
      jobId: req.params.id,
      body: req.body
    })
    
    const { 
      amount = 0, 
      details, 
      terms, 
      status = 'draft',
      market_rate_work_type,
      market_rate_suggestion,
      market_rate_estimated_range
    } = req.body

    if (!details) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'details is required'
      })
    }

    // Verify job exists and belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist'
      })
    }

    // Create new quote in Supabase
    const { data: newQuote, error } = await supabase
      .from('quotes')
      .insert({
        job_id: req.params.id,
        amount,
        details: details.trim(),
        terms: terms?.trim() || null,
        status,
        created_by: req.user.id,
        market_rate_work_type: market_rate_work_type?.trim() || null,
        market_rate_suggestion: market_rate_suggestion?.trim() || null,
        market_rate_estimated_range: market_rate_estimated_range?.trim() || null
      })
      .select()
      .single()

    if (error) {
      console.error('Supabase error creating quote:', error)
      return res.status(500).json({ 
        error: 'Failed to create quote in database',
        details: error.message 
      })
    }

    console.log('✅ Created new quote in Supabase:', newQuote)

    // Add system note to job
    try {
      const quoteAmount = parseFloat(amount)
      const noteMessage = `Quote created: £${quoteAmount.toFixed(2)}${status ? ` (${status})` : ''}`
      
      await supabase
        .from('job_notes')
        .insert({
          job_id: req.params.id,
          author_id: req.user.id,
          message: noteMessage,
          message_type: 'system'
        })
    } catch (noteError) {
      console.error('Failed to create system note:', noteError)
      // Don't fail the request if note creation fails
    }

    res.status(201).json({
      id: newQuote.id,
      message: 'Quote created successfully',
      quote: newQuote
    })

  } catch (error) {
    console.error('Error creating quote:', error)
    res.status(500).json({ error: 'Failed to create quote' })
  }
})

// GET /api/jobs/:id/quotes - Get quotes for job
router.get('/:id/quotes', authenticateUser, async (req, res) => {
  try {
    // First verify job belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist or access denied'
      })
    }

    const { data: quotes, error } = await supabase
      .from('quotes')
      .select('*')
      .eq('job_id', req.params.id)
      .eq('created_by', req.user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Supabase error fetching quotes:', error)
      return res.status(500).json({ error: 'Failed to fetch quotes from database' })
    }

    res.json({ quotes: quotes || [] })

  } catch (error) {
    console.error('Error fetching quotes:', error)
    res.status(500).json({ error: 'Failed to fetch quotes' })
  }
})

// PUT /api/jobs/:id/quotes/:quoteId - Update quote
router.put('/:id/quotes/:quoteId', authenticateUser, async (req, res) => {
  try {
    const { 
      amount = 0, 
      details, 
      terms, 
      status,
      market_rate_work_type,
      market_rate_suggestion,
      market_rate_estimated_range
    } = req.body

    // Verify job belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist or access denied'
      })
    }

    // Verify quote exists and belongs to user
    const { data: existingQuote, error: fetchError } = await supabase
      .from('quotes')
      .select('id')
      .eq('id', req.params.quoteId)
      .eq('job_id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (fetchError || !existingQuote) {
      return res.status(404).json({
        error: 'Quote not found',
        details: 'Quote ID does not exist for this job'
      })
    }

    // Update quote in Supabase
    const { data: updatedQuote, error } = await supabase
      .from('quotes')
      .update({
        amount,
        details: details?.trim(),
        terms: terms?.trim(),
        status,
        market_rate_work_type: market_rate_work_type?.trim() || null,
        market_rate_suggestion: market_rate_suggestion?.trim() || null,
        market_rate_estimated_range: market_rate_estimated_range?.trim() || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', req.params.quoteId)
      .select()
      .single()

    if (error) {
      console.error('Supabase error updating quote:', error)
      return res.status(500).json({ 
        error: 'Failed to update quote in database',
        details: error.message 
      })
    }

    console.log('✅ Updated quote in Supabase:', updatedQuote)

    // Add system note to job
    try {
      const quoteAmount = parseFloat(updatedQuote.amount)
      const noteMessage = `Quote updated: £${quoteAmount.toFixed(2)}${updatedQuote.status ? ` (${updatedQuote.status})` : ''}`
      
      await supabase
        .from('job_notes')
        .insert({
          job_id: req.params.id,
          author_id: req.user.id,
          message: noteMessage,
          message_type: 'system'
        })
    } catch (noteError) {
      console.error('Failed to create system note:', noteError)
      // Don't fail the request if note creation fails
    }

    res.json({
      id: updatedQuote.id,
      message: 'Quote updated successfully',
      quote: updatedQuote
    })

  } catch (error) {
    console.error('Error updating quote:', error)
    res.status(500).json({ error: 'Failed to update quote' })
  }
})

// DELETE /api/jobs/:id/quotes/:quoteId - Delete quote
router.delete('/:id/quotes/:quoteId', authenticateUser, async (req, res) => {
  try {
    // Verify job belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist or access denied'
      })
    }

    const { error } = await supabase
      .from('quotes')
      .delete()
      .eq('id', req.params.quoteId)
      .eq('job_id', req.params.id)
      .eq('created_by', req.user.id)

    if (error) {
      console.error('Supabase error deleting quote:', error)
      return res.status(500).json({ 
        error: 'Failed to delete quote from database',
        details: error.message 
      })
    }

    console.log('✅ Deleted quote from Supabase:', req.params.quoteId)

    // Add system note to job
    try {
      await supabase
        .from('job_notes')
        .insert({
          job_id: req.params.id,
          author_id: req.user.id,
          message: 'Quote deleted',
          message_type: 'system'
        })
    } catch (noteError) {
      console.error('Failed to create system note:', noteError)
      // Don't fail the request if note creation fails
    }

    res.json({
      message: 'Quote deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting quote:', error)
    res.status(500).json({ error: 'Failed to delete quote' })
  }
})

// POST /api/jobs/:id/invoices - Create invoice for job
router.post('/:id/invoices', authenticateUser, async (req, res) => {
  try {
    console.log('📝 Received invoice creation request:', {
      jobId: req.params.id,
      body: req.body
    })
    
    const { amount, tax = 0, details, line_items, due_date, quote_id, status = 'draft' } = req.body

    if (!amount || !details) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'amount and details are required'
      })
    }

    // Verify job exists and belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist'
      })
    }

    // If quote_id provided, verify it exists and belongs to this job
    if (quote_id) {
      const { data: quote, error: quoteError } = await supabase
        .from('quotes')
        .select('id')
        .eq('id', quote_id)
        .eq('job_id', req.params.id)
        .single()

      if (quoteError || !quote) {
        return res.status(400).json({
          error: 'Invalid quote',
          details: 'Quote ID does not exist for this job'
        })
      }
    }

    // Create new invoice in Supabase
    const { data: newInvoice, error } = await supabase
      .from('invoices')
      .insert({
        job_id: req.params.id,
        quote_id: quote_id || null,
        amount: parseFloat(amount),
        tax: parseFloat(tax),
        details: details.trim(),
        line_items: line_items || null,
        due_date: due_date || null,
        status,
        created_by: req.user.id
      })
      .select()
      .single()

    if (error) {
      console.error('Supabase error creating invoice:', error)
      return res.status(500).json({ 
        error: 'Failed to create invoice in database',
        details: error.message 
      })
    }

    console.log('✅ Created new invoice in Supabase:', newInvoice)

    // Add system note to job
    try {
      const totalAmount = parseFloat(amount) + parseFloat(tax)
      const noteMessage = `Invoice created: £${totalAmount.toFixed(2)}${due_date ? `, Due: ${due_date}` : ''}`
      
      await supabase
        .from('job_notes')
        .insert({
          job_id: req.params.id,
          author_id: req.user.id,
          message: noteMessage,
          message_type: 'system'
        })
    } catch (noteError) {
      console.error('Failed to create system note:', noteError)
      // Don't fail the request if note creation fails
    }

    res.status(201).json({
      id: newInvoice.id,
      message: 'Invoice created successfully',
      invoice: newInvoice
    })

  } catch (error) {
    console.error('Error creating invoice:', error)
    res.status(500).json({ error: 'Failed to create invoice' })
  }
})

// GET /api/jobs/:id/invoices - Get invoices for job
router.get('/:id/invoices', authenticateUser, async (req, res) => {
  try {
    const { data: invoices, error } = await supabase
      .from('invoices')
      .select('*')
      .eq('job_id', req.params.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Supabase error fetching invoices:', error)
      return res.status(500).json({ error: 'Failed to fetch invoices from database' })
    }

    res.json({ invoices: invoices || [] })

  } catch (error) {
    console.error('Error fetching invoices:', error)
    res.status(500).json({ error: 'Failed to fetch invoices' })
  }
})

// PUT /api/jobs/:id/invoices/:invoiceId - Update invoice
router.put('/:id/invoices/:invoiceId', authenticateUser, async (req, res) => {
  try {
    const { amount, tax, details, line_items, due_date, status } = req.body

    // Verify job belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist or access denied'
      })
    }

    // Verify invoice exists and belongs to user
    const { data: existingInvoice, error: fetchError } = await supabase
      .from('invoices')
      .select('id, status')
      .eq('id', req.params.invoiceId)
      .eq('job_id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (fetchError || !existingInvoice) {
      return res.status(404).json({
        error: 'Invoice not found',
        details: 'Invoice ID does not exist for this job'
      })
    }

    // Update invoice in Supabase
    const { data: updatedInvoice, error } = await supabase
      .from('invoices')
      .update({
        amount: amount ? parseFloat(amount) : undefined,
        tax: tax !== undefined ? parseFloat(tax) : undefined,
        details: details?.trim(),
        line_items: line_items || undefined,
        due_date,
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', req.params.invoiceId)
      .select()
      .single()

    if (error) {
      console.error('Supabase error updating invoice:', error)
      return res.status(500).json({ 
        error: 'Failed to update invoice in database',
        details: error.message 
      })
    }

    console.log('✅ Updated invoice in Supabase:', updatedInvoice)

    // Add system note if status changed to 'paid'
    if (status === 'paid' && existingInvoice.status !== 'paid') {
      try {
        await supabase
          .from('job_notes')
          .insert({
            job_id: req.params.id,
            author_id: req.user.id,
            message: 'Invoice marked paid',
            message_type: 'system'
          })
      } catch (noteError) {
        console.error('Failed to create system note:', noteError)
        // Don't fail the request if note creation fails
      }
    }

    // Add general system note for invoice update (if not already logged for paid status)
    if (!(status === 'paid' && existingInvoice.status !== 'paid')) {
      try {
        const totalAmount = parseFloat(updatedInvoice.amount) + parseFloat(updatedInvoice.tax || 0)
        const noteMessage = `Invoice updated: £${totalAmount.toFixed(2)}${updatedInvoice.status ? ` (${updatedInvoice.status})` : ''}`
        
        await supabase
          .from('job_notes')
          .insert({
            job_id: req.params.id,
            author_id: req.user.id,
            message: noteMessage,
            message_type: 'system'
          })
      } catch (noteError) {
        console.error('Failed to create system note:', noteError)
        // Don't fail the request if note creation fails
      }
    }

    res.json({
      id: updatedInvoice.id,
      message: 'Invoice updated successfully',
      invoice: updatedInvoice
    })

  } catch (error) {
    console.error('Error updating invoice:', error)
    res.status(500).json({ error: 'Failed to update invoice' })
  }
})

// DELETE /api/jobs/:id/invoices/:invoiceId - Delete invoice
router.delete('/:id/invoices/:invoiceId', authenticateUser, async (req, res) => {
  try {
    // Verify job belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist or access denied'
      })
    }

    const { error } = await supabase
      .from('invoices')
      .delete()
      .eq('id', req.params.invoiceId)
      .eq('job_id', req.params.id)
      .eq('created_by', req.user.id)

    if (error) {
      console.error('Supabase error deleting invoice:', error)
      return res.status(500).json({ 
        error: 'Failed to delete invoice from database',
        details: error.message 
      })
    }

    console.log('✅ Deleted invoice from Supabase:', req.params.invoiceId)

    // Add system note to job
    try {
      await supabase
        .from('job_notes')
        .insert({
          job_id: req.params.id,
          author_id: req.user.id,
          message: 'Invoice deleted',
          message_type: 'system'
        })
    } catch (noteError) {
      console.error('Failed to create system note:', noteError)
      // Don't fail the request if note creation fails
    }

    res.json({
      message: 'Invoice deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting invoice:', error)
    res.status(500).json({ error: 'Failed to delete invoice' })
  }
})

// ===== CONTRACT ENDPOINTS =====

// POST /api/jobs/:id/contracts - Create contract for job
router.post('/:id/contracts', authenticateUser, async (req, res) => {
  try {
    console.log('📝 Received contract creation request:', {
      jobId: req.params.id,
      body: req.body
    })
    
    const { terms, status = 'draft' } = req.body

    if (!terms || !terms.trim()) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'terms is required'
      })
    }

    // Verify job exists and belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist'
      })
    }

    // Create new contract in Supabase
    const { data: newContract, error } = await supabase
      .from('contracts')
      .insert({
        job_id: req.params.id,
        terms: terms.trim(),
        status,
        created_by: req.user.id
      })
      .select()
      .single()

    if (error) {
      console.error('Supabase error creating contract:', error)
      return res.status(500).json({ 
        error: 'Failed to create contract in database',
        details: error.message 
      })
    }

    console.log('✅ Created new contract in Supabase:', newContract)

    // Add system note to job
    try {
      await supabase
        .from('job_notes')
        .insert({
          job_id: req.params.id,
          author_id: req.user.id,
          message: 'Contract created',
          message_type: 'system'
        })
    } catch (noteError) {
      console.error('Failed to create system note:', noteError)
      // Don't fail the request if note creation fails
    }

    res.status(201).json({
      id: newContract.id,
      message: 'Contract created successfully',
      contract: newContract
    })

  } catch (error) {
    console.error('Error creating contract:', error)
    res.status(500).json({ error: 'Failed to create contract' })
  }
})

// GET /api/jobs/:id/contracts - Get contracts for job
router.get('/:id/contracts', authenticateUser, async (req, res) => {
  try {
    const { data: contracts, error } = await supabase
      .from('contracts')
      .select('*')
      .eq('job_id', req.params.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Supabase error fetching contracts:', error)
      return res.status(500).json({ error: 'Failed to fetch contracts from database' })
    }

    res.json({ contracts: contracts || [] })

  } catch (error) {
    console.error('Error fetching contracts:', error)
    res.status(500).json({ error: 'Failed to fetch contracts' })
  }
})

// PUT /api/jobs/:id/contracts/:contractId - Update contract
router.put('/:id/contracts/:contractId', authenticateUser, async (req, res) => {
  try {
    const { terms, status } = req.body

    // Verify job belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist or access denied'
      })
    }

    // Verify contract exists and belongs to user
    const { data: existingContract, error: fetchError } = await supabase
      .from('contracts')
      .select('id, status')
      .eq('id', req.params.contractId)
      .eq('job_id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (fetchError || !existingContract) {
      return res.status(404).json({
        error: 'Contract not found',
        details: 'Contract ID does not exist for this job'
      })
    }

    // Update contract in Supabase
    const { data: updatedContract, error } = await supabase
      .from('contracts')
      .update({
        terms: terms?.trim(),
        status,
        updated_at: new Date().toISOString(),
        ...(status === 'signed' && !existingContract.signed_at ? { signed_at: new Date().toISOString() } : {})
      })
      .eq('id', req.params.contractId)
      .select()
      .single()

    if (error) {
      console.error('Supabase error updating contract:', error)
      return res.status(500).json({ 
        error: 'Failed to update contract in database',
        details: error.message 
      })
    }

    console.log('✅ Updated contract in Supabase:', updatedContract)

    // Add system note if status changed to 'signed'
    if (status === 'signed' && existingContract.status !== 'signed') {
      try {
        await supabase
          .from('job_notes')
          .insert({
            job_id: req.params.id,
            author_id: req.user.id,
            message: 'Contract signed',
            message_type: 'system'
          })
      } catch (noteError) {
        console.error('Failed to create system note:', noteError)
        // Don't fail the request if note creation fails
      }
    }

    // Add general system note for contract update (if not already logged for signed status)
    if (!(status === 'signed' && existingContract.status !== 'signed')) {
      try {
        const noteMessage = `Contract updated${updatedContract.status ? ` (${updatedContract.status})` : ''}`
        
        await supabase
          .from('job_notes')
          .insert({
            job_id: req.params.id,
            author_id: req.user.id,
            message: noteMessage,
            message_type: 'system'
          })
      } catch (noteError) {
        console.error('Failed to create system note:', noteError)
        // Don't fail the request if note creation fails
      }
    }

    res.json({
      id: updatedContract.id,
      message: 'Contract updated successfully',
      contract: updatedContract
    })

  } catch (error) {
    console.error('Error updating contract:', error)
    res.status(500).json({ error: 'Failed to update contract' })
  }
})

// DELETE /api/jobs/:id/contracts/:contractId - Delete contract
router.delete('/:id/contracts/:contractId', authenticateUser, async (req, res) => {
  try {
    // Verify job belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', req.params.id)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist or access denied'
      })
    }

    const { error } = await supabase
      .from('contracts')
      .delete()
      .eq('id', req.params.contractId)
      .eq('job_id', req.params.id)
      .eq('created_by', req.user.id)

    if (error) {
      console.error('Supabase error deleting contract:', error)
      return res.status(500).json({ 
        error: 'Failed to delete contract from database',
        details: error.message 
      })
    }

    console.log('✅ Deleted contract from Supabase:', req.params.contractId)

    // Add system note to job
    try {
      await supabase
        .from('job_notes')
        .insert({
          job_id: req.params.id,
          author_id: req.user.id,
          message: 'Contract deleted',
          message_type: 'system'
        })
    } catch (noteError) {
      console.error('Failed to create system note:', noteError)
      // Don't fail the request if note creation fails
    }

    res.json({
      message: 'Contract deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting contract:', error)
    res.status(500).json({ error: 'Failed to delete contract' })
  }
})

// ===== JOB LOG ENDPOINTS =====

// GET /api/jobs/:id/log - Get job log/notes for job
router.get('/:id/log', authenticateUser, async (req, res) => {
  try {
    const jobId = req.params.id

    // First verify job belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id, title')
      .eq('id', jobId)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist or access denied'
      })
    }

    // Get job notes from database
    const { data: jobNotes, error } = await supabase
      .from('job_notes')
      .select(`
        id,
        job_id,
        author_id,
        message,
        message_type,
        created_at
      `)
      .eq('job_id', jobId)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Supabase error fetching job notes:', error)
      return res.status(500).json({ 
        error: 'Failed to fetch job log from database',
        details: error.message 
      })
    }

    // Get unique author IDs to fetch user names
    const authorIds = [...new Set(jobNotes?.map(note => note.author_id) || [])]
    let userLookup = {}

    if (authorIds.length > 0) {
      const { data: users } = await supabase
        .from('users')
        .select('id, full_name, email')
        .in('id', authorIds)
      
      userLookup = users?.reduce((acc, user) => {
        acc[user.id] = user
        return acc
      }, {}) || {}
    }

    // Transform job_notes to JobLogEntry format expected by frontend
    const logEntries = (jobNotes || []).map(note => {
      const user = userLookup[note.author_id]
      let userName = 'Unknown User'
      
      if (user) {
        if (user.full_name) {
          // Extract first name from full_name (everything before first space)
          userName = user.full_name.split(' ')[0]
        } else if (user.email) {
          // Use email username as fallback (everything before @)
          userName = user.email.split('@')[0]
        }
      }
      
      return {
        id: note.id,
        jobId: note.job_id,
        type: note.message_type === 'text' ? 'user' : note.message_type, // Map 'text' to 'user'
        action: note.message,
        details: undefined, // No details field in current schema
        userId: note.author_id,
        userName: userName,
        createdAt: note.created_at
      }
    })

    console.log(`✅ Fetched ${logEntries.length} job log entries for job ${jobId}`)

    res.json({
      success: true,
      jobId,
      jobTitle: job.title,
      logEntries
    })

  } catch (error) {
    console.error('Error fetching job log:', error)
    res.status(500).json({ error: 'Failed to fetch job log' })
  }
})

// POST /api/jobs/:id/log - Add entry to job log
router.post('/:id/log', authenticateUser, async (req, res) => {
  try {
    const jobId = req.params.id
    const { type, action, details } = req.body

    if (!type || !action) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'type and action are required'
      })
    }

    if (!['user', 'system', 'text'].includes(type)) {
      return res.status(400).json({
        error: 'Invalid type',
        details: 'type must be one of: user, system, text'
      })
    }

    // First verify job belongs to current user
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id')
      .eq('id', jobId)
      .eq('created_by', req.user.id)
      .single()

    if (jobError || !job) {
      return res.status(404).json({
        error: 'Job not found',
        details: 'Job ID does not exist or access denied'
      })
    }

    // Create new job note
    const { data: newNote, error } = await supabase
      .from('job_notes')
      .insert({
        job_id: jobId,
        author_id: req.user.id,
        message: action.trim(),
        message_type: type === 'user' ? 'text' : type // Map 'user' to 'text' for database
      })
      .select(`
        id,
        job_id,
        author_id,
        message,
        message_type,
        created_at
      `)
      .single()

    if (error) {
      console.error('Supabase error creating job note:', error)
      return res.status(500).json({ 
        error: 'Failed to create job log entry',
        details: error.message 
      })
    }

    // Get user info for the note author
    const { data: user } = await supabase
      .from('users')
      .select('full_name, email')
      .eq('id', newNote.author_id)
      .single()

    // Transform to JobLogEntry format
    let userName = 'Unknown User'
    if (user) {
      if (user.full_name) {
        // Extract first name from full_name (everything before first space)
        userName = user.full_name.split(' ')[0]
      } else if (user.email) {
        // Use email username as fallback (everything before @)
        userName = user.email.split('@')[0]
      }
    }
    
    const logEntry = {
      id: newNote.id,
      jobId: newNote.job_id,
      type: newNote.message_type === 'text' ? 'user' : newNote.message_type, // Map back to 'user'
      action: newNote.message,
      details: undefined,
      userId: newNote.author_id,
      userName: userName,
      createdAt: newNote.created_at
    }

    console.log('✅ Created job log entry:', logEntry)

    res.status(201).json({
      success: true,
      message: 'Job log entry created successfully',
      logEntry
    })

  } catch (error) {
    console.error('Error creating job log entry:', error)
    res.status(500).json({ error: 'Failed to create job log entry' })
  }
})

export default router 