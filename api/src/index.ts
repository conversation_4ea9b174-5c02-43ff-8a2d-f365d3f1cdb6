// @ts-nocheck
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import 'dotenv/config'
import OpenAI from 'openai'
// import testRoutes from './routes/test'
import aiRoutes from './routes/ai'
import jobsRoutes from './routes/jobs'
import clientsRoutes from './routes/clients'
import workforceRoutes from './routes/workforce'
import chatRoutes from './routes/chat'
import notificationsRoutes from './routes/notifications'
import adminRoutes from './routes/admin'
import analyticsRoutes from './routes/analytics'
import userAnalyticsRoutes from './routes/userAnalytics'
import emailTestRoutes from './routes/emailTest'
import reviewRequestRoutes from './routes/reviewRequests'
import jobEmailRoutes from './routes/jobEmails'
import invoicesRoutes from './routes/invoices'
import createProfileRoutes from './routes/createProfile.js'
import userProfileRoutes from './routes/userProfile'
import { supabase } from './config/supabase'

// Debug environment variables on startup
console.log('🔍 Environment Debug Info:')
console.log('   SUPABASE_URL:', process.env.SUPABASE_URL ? 'Set' : 'Missing')
console.log('   SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Missing')
console.log('   NODE_ENV:', process.env.NODE_ENV)
console.log('   PORT:', process.env.PORT)

const app = express()
const PORT = process.env.PORT || 4000

// Middleware
app.use(helmet())

// Build allowed origins list
const defaultOrigins = [
  'http://localhost:3000', // public front-end
  'http://localhost:3001', // public front-end testing if neded
  'http://localhost:4000', // old dev front-end / storybook etc.
  'http://localhost:3002', // admin portal
]

const envOrigins = process.env.FRONTEND_URL
  ? process.env.FRONTEND_URL.split(',').map(o => o.trim()).filter(Boolean)
  : []

const allowedOrigins = [...new Set([...defaultOrigins, ...envOrigins])]

app.use(cors({
  origin: (origin, callback) => {
    // Allow non-browser requests (like Postman) which have no origin
    if (!origin) return callback(null, true)

    if (allowedOrigins.includes(origin)) {
      return callback(null, true)
    }
    console.warn('🚫 CORS blocked origin:', origin)
    return callback(new Error('Not allowed by CORS'), false)
  },
  credentials: true,
}))
app.use(morgan('combined'))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'DeskBelt API is running',
    timestamp: new Date().toISOString()
  })
})


// Mount all API routes FIRST to ensure they take precedence
app.use('/api/jobs', jobEmailRoutes) // Job email endpoints (must be before main jobs routes)
app.use('/api/jobs', jobsRoutes)       // Full CRUD for jobs + quotes/invoices/contracts
app.use('/api/invoices', invoicesRoutes) // Standalone invoice management
app.use('/api/clients', clientsRoutes) // Full CRUD for clients + notes
app.use('/api/notifications', notificationsRoutes)  // Notifications
app.use('/api/workforce', workforceRoutes)  // Team/workforce management
app.use('/api/ai', aiRoutes)           // AI endpoints
app.use('/api/chat', chatRoutes)       // Chat endpoints
app.use('/api/admin', adminRoutes)     // Admin portal endpoints
app.use('/api/admin', analyticsRoutes) // Admin analytics endpoints
app.use('/api/user', userAnalyticsRoutes)   // User analytics endpoints
app.use('/api/user', userProfileRoutes)     // User profile endpoints
app.use('/api/email-test', emailTestRoutes) // Email testing endpoints
app.use('/api/review-requests', reviewRequestRoutes) // Review request endpoints
app.use('/api/create-profile', createProfileRoutes) // Profile creation endpoint

// Simple debug endpoint
app.get('/debug-db', async (req, res) => {
  try {
    console.log('🔍 Testing basic database connection...')
    
    // Test basic query without joins
    const { data, error } = await supabase
      .from('jobs')
      .select('id, title, status')
      .limit(3)

    if (error) {
      console.error('❌ Supabase error:', error)
      return res.status(500).json({ 
        error: 'Database query failed',
        details: error.message,
        code: error.code 
      })
    }

    console.log('✅ Database query successful:', data)
    res.json({
      success: true,
      jobCount: data?.length || 0,
      jobs: data || []
    })

  } catch (err) {
    console.error('❌ Debug endpoint error:', err)
    res.status(500).json({ 
      error: 'Debug test failed',
      details: err instanceof Error ? err.message : 'Unknown error'
    })
  }
})

// Test route right after health
app.get('/test-direct', (req, res) => {
  res.json({ message: 'Direct test route works!' })
})

// MAIN API ENDPOINTS - Simple and working
// Replace the complex routes with simple, working versions

// Removed conflicting simple endpoints - route files handle all /api/jobs and /api/clients requests

// Duplicate endpoints removed - now handled by proper route files

// Routes already mounted above to avoid conflicts

// Temporary simple AI routes for testing
app.get('/api/ai/test-connection', async (req, res) => {
  try {
    // Test actual OpenRouter connection
    const openai = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: process.env.OPENROUTER_API_KEY,
    })

    const response = await openai.chat.completions.create({
      model: "meta-llama/llama-3.3-8b-instruct:free",
      messages: [
        { role: "user", content: "Say 'Hello, API test successful!'" }
      ],
      max_tokens: 50
    })

    res.json({
      connected: true,
      message: 'AI route is working',
      model: 'meta-llama/llama-3.3-8b-instruct:free',
      testResponse: response.choices[0]?.message?.content || 'No response'
    })
  } catch (error) {
    console.error('OpenRouter test error:', error)
    res.status(500).json({
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'OpenRouter API test failed'
    })
  }
})

app.post('/api/ai/parse-job', async (req, res) => {
  const { input } = req.body
  
  if (!input || typeof input !== 'string' || input.trim().length === 0) {
    return res.status(400).json({
      error: 'Invalid input',
      message: 'Job description is required'
    })
  }

  try {
    // Initialize OpenRouter client
    const openai = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: process.env.OPENROUTER_API_KEY,
    })

    // Create AI prompt for job parsing - simple title and description only
    const prompt = `You're a tradesman's assistant. Given this rough work outline, extract:

- title: Clear job title (max 50 characters)
- description: Tasks to be done (max 150 words)

Fix obvious spelling errors (rewier → rewire, socets → sockets, lites → lights).

Job outline: "${input.trim()}"

Respond with JSON only: {"title": "...", "description": "..."}`

    const response = await openai.chat.completions.create({
      model: "meta-llama/llama-3.3-8b-instruct:free",
      messages: [
        { role: "user", content: prompt }
      ],
      max_tokens: 200,
      temperature: 0.3
    })

    const aiResponse = response.choices[0]?.message?.content
    
    if (!aiResponse) {
      throw new Error('No AI response received')
    }

    // Try to parse AI response as JSON
    let parsedResponse
    try {
      parsedResponse = JSON.parse(aiResponse)
    } catch (parseError) {
      // If JSON parsing fails, extract values manually
      const titleMatch = aiResponse.match(/"title":\s*"([^"]+)"/i)
      const descMatch = aiResponse.match(/"description":\s*"([^"]+)"/i)
      
      parsedResponse = {
        title: titleMatch?.[1] || input.split(' ').slice(0, 4).join(' '),
        description: descMatch?.[1] || input.trim()
      }
    }

    // Ensure we have required fields with fallbacks
    const result = {
      title: parsedResponse.title || input.split(' ').slice(0, 4).join(' '),
      description: parsedResponse.description || input.trim()
    }

    res.json(result)

  } catch (error) {
    console.error('AI parsing error:', error)
    
    // Fallback to simple logic if AI fails
    let title = input.trim()
    
    // Basic spelling fixes
    title = title.replace(/doroframes?/gi, 'doorframes')
    title = title.replace(/socets?/gi, 'sockets')
    title = title.replace(/lites?/gi, 'lights')
    title = title.replace(/rewier?/gi, 'rewire')
    
    // Generate basic title
    const lower = title.toLowerCase()
    let generatedTitle = ''
    
    if (lower.includes('rewire') || lower.includes('electrical')) {
      generatedTitle = lower.includes('kitchen') ? 'Kitchen Electrical Rewiring' :
                      lower.includes('bathroom') ? 'Bathroom Electrical Installation' :
                      lower.includes('house') ? 'House Rewiring' : 'Electrical Work'
    } else if (lower.includes('socket')) {
      const socketMatch = title.match(/(\d+)\s*socket/i)
      generatedTitle = socketMatch ? `Install ${socketMatch[1]} Electrical Sockets` : 'Socket Installation'
    } else if (lower.includes('light')) {
      const lightMatch = title.match(/(\d+)\s*light/i)
      generatedTitle = lightMatch ? `Install ${lightMatch[1]} Lights` : 'Lighting Installation'
    } else if (lower.includes('door')) {
      generatedTitle = 'Door Installation'
    } else {
      generatedTitle = title.split(' ').slice(0, 4).join(' ')
      generatedTitle = generatedTitle.charAt(0).toUpperCase() + generatedTitle.slice(1)
    }
    
    res.json({
      title: generatedTitle || 'New Job',
      description: title
    })
  }
})

app.get('/api', (req, res) => {
  res.json({
    message: 'Welcome to DeskBelt API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      api: '/api',
      ai: '/api/ai'
    }
  })
})

// Simple test endpoint to verify Supabase connection
app.get('/api/test-supabase', async (req, res) => {
  try {
    console.log('🔍 Testing direct Supabase connection...')
    console.log('   Using URL:', process.env.SUPABASE_URL)
    console.log('   Service role key present:', !!process.env.SUPABASE_SERVICE_ROLE_KEY)
    
    // Test with raw SQL first to bypass any RLS issues
    const { data: rawTest, error: rawError } = await supabase.rpc('get_jobs_count')
    
    if (rawError) {
      console.log('Raw RPC failed, trying direct query...')
      
      // Test a simple count query that doesn't require complex joins
      const { count, error } = await supabase
        .from('jobs')
        .select('*', { count: 'exact', head: true })

      if (error) {
        console.error('❌ Supabase test error:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        return res.status(500).json({ 
          success: false,
          error: 'Supabase connection failed',
          details: error.message,
          code: error.code 
        })
      }

      console.log('✅ Supabase connection successful, job count:', count)
      res.json({
        success: true,
        message: 'Supabase connection working',
        jobCount: count,
        timestamp: new Date().toISOString()
      })
    } else {
      console.log('✅ RPC function worked:', rawTest)
      res.json({
        success: true,
        message: 'RPC function working',
        result: rawTest,
        timestamp: new Date().toISOString()
      })
    }

  } catch (err) {
    console.error('❌ Test endpoint error:', err)
    res.status(500).json({ 
      success: false,
      error: 'Test failed',
      details: err instanceof Error ? err.message : 'Unknown error'
    })
  }
})

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack)
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  })
})

app.listen(PORT, () => {
  console.log(`🚀 DeskBelt API server running on localhost:${PORT}`)
  console.log(`📊 Health check: http://localhost:${PORT}/health`)
  console.log(`🔗 API endpoint: http://localhost:${PORT}/api`)
}) 