{"name": "@deskbelt/api", "version": "1.0.0", "description": "DeskBelt API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["deskbelt", "api", "freelance"], "author": "", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.49.9", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemailer": "^7.0.5", "openai": "^4.63.0", "pg": "^8.16.2", "resend": "^4.7.0", "web-push": "^3.6.7", "zod": "^3.25.49"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.29", "@types/web-push": "^3.6.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}