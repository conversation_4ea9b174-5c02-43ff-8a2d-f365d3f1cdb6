-- =============================================
-- Comprehensive Authentication and User Profile Fix
-- Description: Complete fix for authentication and profile creation issues
-- Issues: Profile creation failing, permission denied errors, missing user profiles
-- =============================================

-- Step 1: Ensure authenticated role has all necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.users TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Step 2: Grant permissions on auth schema (if needed for profile operations)
GRANT USAGE ON SCHEMA auth TO authenticated;

-- Step 3: Create a more permissive RLS policy for user registration
-- Drop all existing user policies to start fresh
DROP POLICY IF EXISTS "Allow user registration and profile creation" ON public.users;
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Super admins can view all users" ON public.users;
DROP POLICY IF EXISTS "Super admins can update any user" ON public.users;
DROP POLICY IF EXISTS "Super admins can insert users" ON public.users;

-- Create comprehensive user policies
CREATE POLICY "Users can manage their own profile" ON public.users
  FOR ALL USING (
    auth.uid() = id
    OR current_setting('role') = 'service_role'
  );

CREATE POLICY "Allow profile creation during registration" ON public.users
  FOR INSERT WITH CHECK (
    -- Standard case: authenticated user creating their own profile
    auth.uid() = id
    OR
    -- Service role case: system operations
    current_setting('role') = 'service_role'
    OR
    -- Invitation case: allow creation if invitation exists
    EXISTS (
      SELECT 1 FROM public.workforce_invitations wi
      WHERE wi.email = users.email
      AND wi.status = 'pending'
      AND wi.expires_at > now()
    )
    OR
    -- Auth user without profile case
    (
      EXISTS (
        SELECT 1 FROM auth.users au
        WHERE au.id = users.id
        AND au.email = users.email
      )
    )
  );

-- Step 4: Create a trigger function to automatically create user profiles
CREATE OR REPLACE FUNCTION auto_create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert user profile when auth.users record is created
  INSERT INTO public.users (
    id,
    email,
    full_name,
    role,
    country,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
    'tradesperson',
    'UK',
    now(),
    now()
  ) ON CONFLICT (id) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Create trigger to auto-create profiles (if it doesn't exist)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION auto_create_user_profile();

-- Step 6: Grant necessary permissions for the trigger function
GRANT EXECUTE ON FUNCTION auto_create_user_profile() TO authenticated;
GRANT EXECUTE ON FUNCTION auto_create_user_profile() TO service_role;

-- Step 7: Create missing profiles for existing auth users
INSERT INTO public.users (
  id,
  email,
  full_name,
  role,
  country,
  created_at,
  updated_at
)
SELECT
  au.id,
  au.email,
  COALESCE(au.raw_user_meta_data->>'full_name', au.raw_user_meta_data->>'name'),
  'tradesperson',
  'UK',
  now(),
  now()
FROM auth.users au
WHERE NOT EXISTS (
  SELECT 1 FROM public.users pu WHERE pu.id = au.id
)
ON CONFLICT (id) DO NOTHING;

-- Step 8: Ensure workforce_invitations table has proper permissions for invitation flow
GRANT SELECT ON public.workforce_invitations TO authenticated;
GRANT SELECT ON public.workforce_invitations TO anon;