# DeskBelt - Freelance Management Platform

A comprehensive freelance management platform built with modern web technologies to help freelancers manage clients, projects, invoices, and grow their business.

## 🚀 Features

- **Client Management**: Organize and manage client information, communication history, and relationships
- **Project Tracking**: Track project progress, deadlines, deliverables, and milestones
- **Time Tracking**: Log billable hours with detailed time entries and automatic calculations
- **Invoice Generation**: Create professional invoices with automated calculations and payment tracking
- **Financial Dashboard**: Monitor income, expenses, and business performance with interactive charts
- **Document Management**: Store and organize project files, contracts, and important documents
- **Reporting & Analytics**: Generate detailed reports on business performance and productivity

## 🏗️ Architecture

This is a monorepo containing:

- **`apps/web`**: Main client-facing web application (Next.js + React)
- **`apps/admin`**: Admin portal for system management (Next.js + React)
- **`api`**: Backend API server (Node.js + Express + TypeScript)
- **`libs/ui`**: Shared UI components library
- **`libs/db`**: Database schemas and utilities

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **UI Library**: React 19 with TypeScript
- **Styling**: Tailwind CSS + Headless UI
- **Icons**: Heroicons
- **State Management**: TanStack Query (React Query)
- **Forms**: React Hook Form + Zod validation
- **Charts**: Chart.js + React Chart.js 2

### Backend
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Validation**: Zod + Express Validator
- **Security**: Helmet, CORS, bcryptjs

### Development Tools
- **Language**: TypeScript
- **Linting**: ESLint + Prettier
- **Testing**: Cypress (E2E)
- **Build Tool**: Native TypeScript compiler
- **Dev Server**: Nodemon
- **Package Manager**: npm with workspaces

## 📋 Prerequisites

- Node.js 18+ and npm 8+
- Supabase account and project
- Git

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd deskbelt3
```

### 2. Install Dependencies
```bash
npm run install:all
```

### 3. Environment Setup

#### Web App Environment
Copy `apps/web/env.template` to `apps/web/.env.local`:
```bash
cp apps/web/env.template apps/web/.env.local
```

Fill in your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

#### API Environment
Copy `api/env.template` to `api/.env`:
```bash
cp api/env.template api/.env
```

Fill in your configuration:
```env
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
JWT_SECRET=your_jwt_secret_key
```

### 4. Start Development Servers
```bash
# Start both API and web app
npm run dev

# Or start individually
npm run dev:web    # Web app on http://localhost:3000
npm run dev:admin  # Admin app on http://localhost:3001
npm run dev:api    # API server on http://localhost:3001
```

## 📁 Project Structure

```
deskbelt3/
├── apps/
│   ├── web/                 # Main web application
│   │   ├── src/
│   │   │   ├── app/         # Next.js app router pages
│   │   │   │   ├── components/  # React components
│   │   │   │   ├── lib/         # Utility functions
│   │   │   │   ├── styles/      # Global styles
│   │   │   │   └── types/       # TypeScript type definitions
│   │   │   ├── next.config.js
│   │   │   ├── tailwind.config.js
│   │   │   └── package.json
│   │   └── admin/               # Admin portal
│   │       └── ...
│   ├── api/                     # Backend API
│   │   ├── src/
│   │   │   ├── config/          # Configuration files
│   │   │   ├── controllers/     # Route controllers
│   │   │   ├── middleware/      # Express middleware
│   │   │   ├── models/          # Data models
│   │   │   ├── routes/          # API routes
│   │   │   ├── utils/           # Utility functions
│   │   │   └── index.ts         # Server entry point
│   │   ├── tsconfig.json
│   │   └── package.json
│   └── libs/                    # Shared libraries
│       ├── ui/                  # Shared UI components
│       └── db/                  # Database utilities
│   ├── docs/                    # Documentation
│   ├── docker/                  # Docker configurations
│   └── package.json             # Root package.json
```

## 🔧 Available Scripts

### Root Level
- `npm run dev` - Start both API and web app in development mode
- `npm run build` - Build all applications for production
- `npm run start` - Start all applications in production mode
- `npm run lint` - Run linting on all applications
- `npm run test` - Run tests on all applications
- `npm run clean` - Clean all build artifacts

### Individual Apps
- `npm run dev:web` - Start web app development server
- `npm run dev:admin` - Start admin app development server
- `npm run dev:api` - Start API development server

## 🗄️ Database Setup

1. Create a new Supabase project
2. Set up the database schema (SQL files will be provided)
3. Configure Row Level Security (RLS) policies
4. Update environment variables with your Supabase credentials

## 🔐 Authentication

The application uses Supabase Auth for user authentication with support for:
- Email/password authentication
- Social login providers (Google, GitHub, etc.)
- JWT-based session management
- Role-based access control

## 🚀 Deployment

### Frontend (Vercel/Netlify)
1. Build the application: `npm run build:web`
2. Deploy the `apps/web` directory
3. Set environment variables in your hosting platform

### Backend (Railway/Heroku/DigitalOcean)
1. Build the API: `npm run build:api`
2. Deploy the `api` directory
3. Set environment variables
4. Ensure database connectivity

### Docker Deployment
Docker configurations are available in the `docker/` directory for containerized deployment.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the ISC License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the documentation in the `docs/` directory
- Review the API documentation at `/api/docs` when running locally

## 🔄 Development Workflow

1. **Setup**: Follow the Quick Start guide
2. **Development**: Use `npm run dev` for hot reloading
3. **Testing**: Run `npm run test` before committing
4. **Linting**: Ensure code quality with `npm run lint`
5. **Building**: Test production builds with `npm run build`

---

Built with ❤️ for freelancers who want to focus on their craft, not paperwork. 