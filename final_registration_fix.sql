-- =============================================
-- Final Registration Fix
-- Description: Ensure new user registration works through invitation links
-- =============================================

-- Step 1: Grant comprehensive permissions to anon role (for registration)
GRANT SELECT, INSERT ON public.users TO anon;
GRANT SELECT ON public.workforce_invitations TO anon;

-- Step 2: Create a very permissive registration policy
DROP POLICY IF EXISTS "Allow all user operations" ON public.users;

CREATE POLICY "Allow user registration and management" ON public.users
  FOR ALL USING (
    -- Own profile access
    auth.uid() = id
    OR
    -- Service role operations
    current_setting('role') = 'service_role'
    OR
    -- Allow anon role for registration (when no auth.uid() exists yet)
    auth.uid() IS NULL
    OR
    -- Allow if user has pending invitation
    EXISTS (
      SELECT 1 FROM public.workforce_invitations wi
      WHERE wi.email = users.email
      AND wi.status = 'pending'
      AND wi.expires_at > now()
    )
  ) WITH CHECK (
    -- Allow insertion for own ID or during registration
    auth.uid() = id
    OR
    current_setting('role') = 'service_role'
    OR
    -- Allow anon to insert during registration
    auth.uid() IS NULL
    OR
    -- Allow if invitation exists
    EXISTS (
      SELECT 1 FROM public.workforce_invitations wi
      WHERE wi.email = users.email
      AND wi.status = 'pending'
      AND wi.expires_at > now()
    )
  );

-- Step 3: Verify the invitation exists for the test email
SELECT
  email,
  status,
  expires_at,
  expires_at > now() as is_valid
FROM public.workforce_invitations
WHERE email = '<EMAIL>';